{"code": "AF", "descriptionEnglish": "Afghanistan", "description": "Afghanistan"}
{"code": "AX", "descriptionEnglish": "Åland Islands", "description": "Aland Isole"}
{"code": "AL", "descriptionEnglish": "Albania", "description": "Albania"}
{"code": "DZ", "descriptionEnglish": "Algeria", "description": "Algeria", "prefix": "213"}
{"code": "AS", "descriptionEnglish": "American Samoa", "description": "Samoa americane"}
{"code": "AD", "descriptionEnglish": "Andorra", "description": "Andorra", "prefix": "376"}
{"code": "AO", "descriptionEnglish": "Angola", "description": "Angola", "prefix": "244"}
{"code": "AI", "descriptionEnglish": "Anguilla", "description": "Anguilla", "prefix": "1264"}
{"code": "AQ", "descriptionEnglish": "Antarctica", "description": "Antartide"}
{"code": "AG", "descriptionEnglish": "Antigua and Barbuda", "description": "Antigua e Barbuda", "prefix": "1268"}
{"code": "AR", "descriptionEnglish": "Argentina", "description": "Argentina", "prefix": "54"}
{"code": "AM", "descriptionEnglish": "Armenia", "description": "Armenia", "prefix": "374"}
{"code": "AW", "descriptionEnglish": "Aruba", "description": "Aruba", "prefix": "294"}
{"code": "AU", "descriptionEnglish": "Australia", "description": "Australia", "prefix": "61"}
{"code": "AT", "descriptionEnglish": "Austria", "description": "Austria", "prefix": "43"}
{"code": "AZ", "descriptionEnglish": "Azerbaijan", "description": "Azerbaijan", "prefix": "994"}
{"code": "BS", "descriptionEnglish": "Bahamas", "description": "Bahamas", "prefix": "1242"}
{"code": "BH", "descriptionEnglish": "Bahrain", "description": "Bahrein", "prefix": "973"}
{"code": "BD", "descriptionEnglish": "Bangladesh", "description": "Bangladesh", "prefix": "880"}
{"code": "BB", "descriptionEnglish": "Barbados", "description": "Barbados", "prefix": "1246"}
{"code": "BY", "descriptionEnglish": "Belarus", "description": "Bielorussia", "prefix": "375"}
{"code": "BE", "descriptionEnglish": "Belgium", "description": "Belgio", "prefix": "32"}
{"code": "BZ", "descriptionEnglish": "Belize", "description": "Belize", "prefix": "501"}
{"code": "BJ", "descriptionEnglish": "Benin", "description": "Benin", "prefix": "229"}
{"code": "BM", "descriptionEnglish": "Bermuda", "description": "Bermuda", "prefix": "1441"}
{"code": "BT", "descriptionEnglish": "Bhutan", "description": "Bhutan", "prefix": "975"}
{"code": "BO", "descriptionEnglish": "Bolivia", "description": "Bolivia", "prefix": "591"}
{"code": "BA", "descriptionEnglish": "Bosnia and Herzegovina", "description": "Bosnia Erzegovina", "prefix": "387"}
{"code": "BW", "descriptionEnglish": "Botswana", "description": "Botswana", "prefix": "267"}
{"code": "BV", "descriptionEnglish": "Bouvet Island", "description": "Isola Bouvet"}
{"code": "BR", "descriptionEnglish": "Brazil", "description": "Brasile", "prefix": "55"}
{"code": "IO", "descriptionEnglish": "British Indian Ocean Territory", "description": "Territorio britannico dell'Oceano Indiano"}
{"code": "BN", "descriptionEnglish": "Brunei Darussalam", "description": "Brunei Darussalam", "prefix": "673"}
{"code": "BG", "descriptionEnglish": "Bulgaria", "description": "Bulgaria", "prefix": "359"}
{"code": "BF", "descriptionEnglish": "Burkina Faso", "description": "Burkina Faso", "prefix": "226"}
{"code": "BI", "descriptionEnglish": "Burundi", "description": "Burundi", "prefix": "257"}
{"code": "KH", "descriptionEnglish": "Cambodia", "description": "Cambogia", "prefix": "855"}
{"code": "CM", "descriptionEnglish": "Cameroon", "description": "Camerun", "prefix": "237"}
{"code": "CA", "descriptionEnglish": "Canada", "description": "Canada", "prefix": "1"}
{"code": "CV", "descriptionEnglish": "Cape Verde", "description": "Capo Verde", "prefix": "238"}
{"code": "KY", "descriptionEnglish": "Cayman Islands", "description": "Isole Cayman", "prefix": "1345"}
{"code": "CF", "descriptionEnglish": "Central African Republic", "description": "Repubblica Centrafricana", "prefix": "236"}
{"code": "TD", "descriptionEnglish": "Chad", "description": "Chad"}
{"code": "CL", "descriptionEnglish": "Chile", "description": "Chile", "prefix": "56"}
{"code": "CN", "descriptionEnglish": "China", "description": "Cina", "prefix": "86"}
{"code": "CX", "descriptionEnglish": "Christmas Island", "description": "Isola di Natale"}
{"code": "CC", "descriptionEnglish": "Cocos (Keeling) Islands", "description": "Isole Cocos (Keeling)"}
{"code": "CO", "descriptionEnglish": "Colombia", "description": "Colombia", "prefix": "57"}
{"code": "KM", "descriptionEnglish": "Comoros", "description": "Comoros", "prefix": "269"}
{"code": "CG", "descriptionEnglish": "Congo", "description": "Congo", "prefix": "242"}
{"code": "CD", "descriptionEnglish": "Congo, The Democratic Republic of the", "description": "Repubblica Democratica del Congo"}
{"code": "CK", "descriptionEnglish": "Cook Islands", "description": "Isole Cook", "prefix": "682"}
{"code": "CR", "descriptionEnglish": "Costa Rica", "description": "Costa Rica", "prefix": "506"}
{"code": "CI", "descriptionEnglish": "Cote d'Ivoire", "description": "Costa d'Avorio"}
{"code": "HR", "descriptionEnglish": "Croatia", "description": "Croazia", "prefix": "385"}
{"code": "CU", "descriptionEnglish": "Cuba", "description": "Cuba", "prefix": "53"}
{"code": "CY", "descriptionEnglish": "Cyprus", "description": "Cipro", "prefix": "357"}
{"code": "CZ", "descriptionEnglish": "Czech Republic", "description": "Repubblica Ceca", "prefix": "420"}
{"code": "DK", "descriptionEnglish": "Denmark", "description": "Danimarca", "prefix": "45"}
{"code": "DJ", "descriptionEnglish": "Djibouti", "description": "Gibuti", "prefix": "253"}
{"code": "DM", "descriptionEnglish": "Dominica", "description": "Dominica", "prefix": "1809"}
{"code": "DO", "descriptionEnglish": "Dominican Republic", "description": "Repubblica Dominicana", "prefix": "1809"}
{"code": "EC", "descriptionEnglish": "Ecuador", "description": "Ecuador", "prefix": "593"}
{"code": "EG", "descriptionEnglish": "Egypt", "description": "Egitto", "prefix": "20"}
{"code": "SV", "descriptionEnglish": "El Salvador", "description": "El Salvador", "prefix": "503"}
{"code": "GQ", "descriptionEnglish": "Equatorial Guinea", "description": "Guinea Equatoriale", "prefix": "240"}
{"code": "ER", "descriptionEnglish": "Eritrea", "description": "l'Eritrea", "prefix": "291"}
{"code": "EE", "descriptionEnglish": "Estonia", "description": "Estonia", "prefix": "372"}
{"code": "ET", "descriptionEnglish": "Ethiopia", "description": "Etiopia", "prefix": "251"}
{"code": "FK", "descriptionEnglish": "Falkland Islands (Malvinas)", "description": "Isole Falkland (Malvinas)", "prefix": "500"}
{"code": "FO", "descriptionEnglish": "Faroe Islands", "description": "Isole Faroe", "prefix": "298"}
{"code": "FJ", "descriptionEnglish": "Fiji", "description": "Fiji", "prefix": "679"}
{"code": "FI", "descriptionEnglish": "Finland", "description": "Finlandia", "prefix": "358"}
{"code": "FR", "descriptionEnglish": "France", "description": "Francia", "prefix": "33"}
{"code": "GF", "descriptionEnglish": "French Guiana", "description": "Guiana francese", "prefix": "594"}
{"code": "PF", "descriptionEnglish": "French Polynesia", "description": "Polinesia francese", "prefix": "689"}
{"code": "TF", "descriptionEnglish": "French Southern Territories", "description": "Territori della Francia del sud"}
{"code": "GA", "descriptionEnglish": "Gabon", "description": "Gabon", "prefix": "241"}
{"code": "GM", "descriptionEnglish": "Gambia", "description": "Gambia", "prefix": "220"}
{"code": "GE", "descriptionEnglish": "Georgia", "description": "Georgia", "prefix": "7880"}
{"code": "DE", "descriptionEnglish": "Germany", "description": "Germania", "prefix": "49"}
{"code": "GH", "descriptionEnglish": "Ghana", "description": "Ghana", "prefix": "233"}
{"code": "GI", "descriptionEnglish": "Gibraltar", "description": "Gibilterra", "prefix": "350"}
{"code": "GR", "descriptionEnglish": "Greece", "description": "Grecia", "prefix": "30"}
{"code": "GL", "descriptionEnglish": "Greenland", "description": "Groenlandia", "prefix": "299"}
{"code": "GD", "descriptionEnglish": "Grenada", "description": "Grenada", "prefix": "1473"}
{"code": "GP", "descriptionEnglish": "Guadeloupe", "description": "Guadeloupe", "prefix": "590"}
{"code": "GU", "descriptionEnglish": "Guam", "description": "Guam", "prefix": "671"}
{"code": "GT", "descriptionEnglish": "Guatemala", "description": "Guatemala", "prefix": "502"}
{"code": "GG", "descriptionEnglish": "Guernsey", "description": "Guernsey"}
{"code": "GN", "descriptionEnglish": "Guinea", "description": "Guinea", "prefix": "224"}
{"code": "GW", "descriptionEnglish": "Guinea-Bissau", "description": "Guinea-Bissau", "prefix": "245"}
{"code": "GY", "descriptionEnglish": "Guyana", "description": "Guyana", "prefix": "592"}
{"code": "HT", "descriptionEnglish": "Haiti", "description": "Haiti", "prefix": "509"}
{"code": "HM", "descriptionEnglish": "Heard Island and Mcdonald Islands", "description": "Isole Heard e isole Mcdonald"}
{"code": "VA", "descriptionEnglish": "Holy See (Vatican City State)", "description": "Stato della Città del Vaticano"}
{"code": "HN", "descriptionEnglish": "Honduras", "description": "Honduras", "prefix": "504"}
{"code": "HK", "descriptionEnglish": "Hong Kong", "description": "Hong Kong", "prefix": "852"}
{"code": "HU", "descriptionEnglish": "Hungary", "description": "Ungheria", "prefix": "36"}
{"code": "IS", "descriptionEnglish": "Iceland", "description": "Islanda", "prefix": "354"}
{"code": "IN", "descriptionEnglish": "India", "description": "India", "prefix": "91"}
{"code": "ID", "descriptionEnglish": "Indonesia", "description": "Indonesia", "prefix": "62"}
{"code": "IR", "descriptionEnglish": "Iran, Islamic Republic Of", "description": "Repubblica Islamica del Iran", "prefix": "98"}
{"code": "IQ", "descriptionEnglish": "Iraq", "description": "Iraq", "prefix": "964"}
{"code": "IE", "descriptionEnglish": "Ireland", "description": "Irlanda", "prefix": "353"}
{"code": "IM", "descriptionEnglish": "Isle of Man", "description": "Isola di Man"}
{"code": "IL", "descriptionEnglish": "Israel", "description": "Israele", "prefix": "972"}
{"code": "IT", "descriptionEnglish": "Italy", "description": "Italia", "prefix": "39"}
{"code": "JM", "descriptionEnglish": "Jamaica", "description": "Giamaica", "prefix": "1876"}
{"code": "JP", "descriptionEnglish": "Japan", "description": "Giappone", "prefix": "81"}
{"code": "JE", "descriptionEnglish": "Jersey", "description": "Isola di Jersey"}
{"code": "JO", "descriptionEnglish": "Jordan", "description": "Giordania", "prefix": "962"}
{"code": "KZ", "descriptionEnglish": "Kazakhstan", "description": "Kazakistan", "prefix": "7"}
{"code": "KE", "descriptionEnglish": "Kenya", "description": "Kenia", "prefix": "254"}
{"code": "KI", "descriptionEnglish": "Kiribati", "description": "Kiribati", "prefix": "686"}
{"code": "KP", "descriptionEnglish": "Korea, Democratic People's Republic of", "description": "Repubblica democratica popolare della Corea Nord", "prefix": "850"}
{"code": "KR", "descriptionEnglish": "Korea, Republic of", "description": "Repubblica di Corea", "prefix": "82"}
{"code": "KW", "descriptionEnglish": "Kuwait", "description": "Kuwait", "prefix": "965"}
{"code": "KG", "descriptionEnglish": "Kyrgyzstan", "description": "Kyrgyzstan", "prefix": "996"}
{"code": "LA", "descriptionEnglish": "Lao People's Democratic Republic", "description": "Laos", "prefix": "856"}
{"code": "LV", "descriptionEnglish": "Latvia", "description": "Lettonia", "prefix": "371"}
{"code": "LB", "descriptionEnglish": "Lebanon", "description": "Libano", "prefix": "961"}
{"code": "LS", "descriptionEnglish": "Lesotho", "description": "Lesotho", "prefix": "266"}
{"code": "LR", "descriptionEnglish": "Liberia", "description": "Liberia", "prefix": "231"}
{"code": "LY", "descriptionEnglish": "Libyan Arab Jamahiriya", "description": "Libia", "prefix": "218"}
{"code": "LI", "descriptionEnglish": "Liechtenstein", "description": "Liechtenstein", "prefix": "417"}
{"code": "LT", "descriptionEnglish": "Lithuania", "description": "Lituania", "prefix": "370"}
{"code": "LU", "descriptionEnglish": "Luxembourg", "description": "Lussemburgo", "prefix": "352"}
{"code": "MO", "descriptionEnglish": "Macao", "description": "Macao", "prefix": "853"}
{"code": "MK", "descriptionEnglish": "Macedonia, The Former Yugoslav Republic of", "description": "La Repubblica di Macedònia", "prefix": "389"}
{"code": "MG", "descriptionEnglish": "Madagascar", "description": "Madagascar", "prefix": "261"}
{"code": "MW", "descriptionEnglish": "Malawi", "description": "Malawi", "prefix": "265"}
{"code": "MY", "descriptionEnglish": "Malaysia", "description": "Malaysia", "prefix": "60"}
{"code": "MV", "descriptionEnglish": "Maldives", "description": "Maldive", "prefix": "960"}
{"code": "ML", "descriptionEnglish": "Mali", "description": "Mali", "prefix": "223"}
{"code": "MT", "descriptionEnglish": "Malta", "description": "Malta", "prefix": "356"}
{"code": "MH", "descriptionEnglish": "Marshall Islands", "description": "Isole Marshall", "prefix": "692"}
{"code": "MQ", "descriptionEnglish": "Martinique", "description": "Martinica", "prefix": "596"}
{"code": "MR", "descriptionEnglish": "Mauritania", "description": "Mauritania", "prefix": "222"}
{"code": "MU", "descriptionEnglish": "Mauritius", "description": "Mauritius"}
{"code": "YT", "descriptionEnglish": "Mayotte", "description": "Mayotte", "prefix": "269"}
{"code": "MX", "descriptionEnglish": "Mexico", "description": "Messico", "prefix": "52"}
{"code": "FM", "descriptionEnglish": "Micronesia, Federated States of", "description": "Gli Stati Federati di Micronesia", "prefix": "691"}
{"code": "MD", "descriptionEnglish": "Moldova, Republic of", "description": "Moldavia", "prefix": "373"}
{"code": "MC", "descriptionEnglish": "Monaco", "description": "Monaco", "prefix": "377"}
{"code": "MN", "descriptionEnglish": "Mongolia", "description": "Mongolia", "prefix": "976"}
{"code": "MS", "descriptionEnglish": "Montserrat", "description": "Montserrat", "prefix": "1664"}
{"code": "MA", "descriptionEnglish": "Morocco", "description": "Marocco", "prefix": "212"}
{"code": "MZ", "descriptionEnglish": "Mozambique", "description": "Mozambico", "prefix": "258"}
{"code": "MM", "descriptionEnglish": "Myanmar", "description": "Birmania", "prefix": "95"}
{"code": "NA", "descriptionEnglish": "Namibia", "description": "Namibia", "prefix": "264"}
{"code": "NR", "descriptionEnglish": "Nauru", "description": "Nauru", "prefix": "674"}
{"code": "NP", "descriptionEnglish": "Nepal", "description": "Nepal", "prefix": "977"}
{"code": "NL", "descriptionEnglish": "Netherlands", "description": "Paesi Bassi", "prefix": "31"}
{"code": "AN", "descriptionEnglish": "Netherlands Antilles", "description": "Paesi Bassi caraibici"}
{"code": "NC", "descriptionEnglish": "New Caledonia", "description": "Nuova Caledonia", "prefix": "687"}
{"code": "NZ", "descriptionEnglish": "New Zealand", "description": "Nuova Zelanda", "prefix": "64"}
{"code": "NI", "descriptionEnglish": "Nicaragua", "description": "Nicaragua", "prefix": "505"}
{"code": "NE", "descriptionEnglish": "Niger", "description": "Niger", "prefix": "227"}
{"code": "NG", "descriptionEnglish": "Nigeria", "description": "Nigeria", "prefix": "234"}
{"code": "NU", "descriptionEnglish": "Niue", "description": "Niue", "prefix": "683"}
{"code": "NF", "descriptionEnglish": "Norfolk Island", "description": "isola Norfolk", "prefix": "672"}
{"code": "MP", "descriptionEnglish": "Northern Mariana Islands", "description": "Isole Marianne Settentrionali", "prefix": "670"}
{"code": "NO", "descriptionEnglish": "Norway", "description": "Norvegia", "prefix": "47"}
{"code": "OM", "descriptionEnglish": "Oman", "description": "Oman", "prefix": "968"}
{"code": "PK", "descriptionEnglish": "Pakistan", "description": "Pakistan"}
{"code": "PW", "descriptionEnglish": "Palau", "description": "Palau", "prefix": "680"}
{"code": "PS", "descriptionEnglish": "Palestinian Territory, Occupied", "description": "Palestina"}
{"code": "PA", "descriptionEnglish": "Panama", "description": "Panama", "prefix": "507"}
{"code": "PG", "descriptionEnglish": "Papua New Guinea", "description": "Papua Nuova Guinea", "prefix": "675"}
{"code": "PY", "descriptionEnglish": "Paraguay", "description": "Paraguai", "prefix": "595"}
{"code": "PE", "descriptionEnglish": "Peru", "description": "Peru", "prefix": "51"}
{"code": "PH", "descriptionEnglish": "Philippines", "description": "Filippine", "prefix": "63"}
{"code": "PN", "descriptionEnglish": "Pitcairn", "description": "Pitcairn"}
{"code": "PL", "descriptionEnglish": "Poland", "description": "Polonia", "prefix": "48"}
{"code": "PT", "descriptionEnglish": "Portugal", "description": "Portogallo", "prefix": "351"}
{"code": "PR", "descriptionEnglish": "Puerto Rico", "description": "Porto rico", "prefix": "1787"}
{"code": "QA", "descriptionEnglish": "Qatar", "description": "Qatar", "prefix": "974"}
{"code": "RE", "descriptionEnglish": "Reunion", "description": "Riunione", "prefix": "262"}
{"code": "RO", "descriptionEnglish": "Romania", "description": "Romania", "prefix": "40"}
{"code": "RU", "descriptionEnglish": "Russian Federation", "description": "Russia", "prefix": "7"}
{"code": "RW", "descriptionEnglish": "RWANDA", "description": "Ruanda", "prefix": "250"}
{"code": "SH", "descriptionEnglish": "Saint Helena", "description": "Sant'Elena", "prefix": "290"}
{"code": "KN", "descriptionEnglish": "Saint Kitts and Nevis", "description": "Saint Kitts e Nevis", "prefix": "1869"}
{"code": "LC", "descriptionEnglish": "Saint Lucia", "description": "Saint Lucia", "prefix": "1758"}
{"code": "PM", "descriptionEnglish": "Saint Pierre and Miquelon", "description": "Saint-Pierre e Miquelon"}
{"code": "VC", "descriptionEnglish": "Saint Vincent and the Grenadines", "description": "Saint Vincent e Grenadine"}
{"code": "WS", "descriptionEnglish": "Samoa", "description": "Samoa"}
{"code": "SM", "descriptionEnglish": "San Marino", "description": "San Marino", "prefix": "378"}
{"code": "ST", "descriptionEnglish": "Sao Tome and Principe", "description": "São Tomé e Príncipe", "prefix": "239"}
{"code": "SA", "descriptionEnglish": "Saudi Arabia", "description": "Arabia Saudita", "prefix": "966"}
{"code": "SN", "descriptionEnglish": "Senegal", "description": "Senegal", "prefix": "221"}
{"code": "CS", "descriptionEnglish": "Serbia and Montenegro", "description": "Serbia", "prefix": "381"}
{"code": "SC", "descriptionEnglish": "Seychelles", "description": "Seychelles", "prefix": "248"}
{"code": "SL", "descriptionEnglish": "Sierra Leone", "description": "Sierra Leone", "prefix": "232"}
{"code": "SG", "descriptionEnglish": "Singapore", "description": "Singapore", "prefix": "65"}
{"code": "SK", "descriptionEnglish": "Slovakia", "description": "Slovacchia", "prefix": "421"}
{"code": "SI", "descriptionEnglish": "Slovenia", "description": "Slovenia", "prefix": "386"}
{"code": "SB", "descriptionEnglish": "Solomon Islands", "description": "Isole Salomone", "prefix": "677"}
{"code": "SO", "descriptionEnglish": "Somalia", "description": "Somalia", "prefix": "252"}
{"code": "ZA", "descriptionEnglish": "South Africa", "description": "Sudafrica", "prefix": "27"}
{"code": "GS", "descriptionEnglish": "South Georgia and the South Sandwich Islands", "description": "Georgia del Sud e Isole Sandwich Australi"}
{"code": "ES", "descriptionEnglish": "Spain", "description": "Spagna", "prefix": "34"}
{"code": "LK", "descriptionEnglish": "Sri Lanka", "description": "Sri Lanka", "prefix": "94"}
{"code": "SD", "descriptionEnglish": "Sudan", "description": "Sudan", "prefix": "249"}
{"code": "SR", "descriptionEnglish": "Suri", "description": "Suri", "prefix": "597"}
{"code": "SJ", "descriptionEnglish": "Svalbard and Jan Mayen", "description": "Svalbard e Jan Mayen"}
{"code": "SZ", "descriptionEnglish": "Swaziland", "description": "Swaziland", "prefix": "268"}
{"code": "SE", "descriptionEnglish": "Sweden", "description": "Svezia", "prefix": "46"}
{"code": "CH", "descriptionEnglish": "Switzerland", "description": "Svizzera", "prefix": "41"}
{"code": "SY", "descriptionEnglish": "Syrian Arab Republic", "description": "Siria", "prefix": "963"}
{"code": "TW", "descriptionEnglish": "Taiwan", "description": "Taiwan", "prefix": "886"}
{"code": "TJ", "descriptionEnglish": "Tajikistan", "description": "Tagikistan", "prefix": "7"}
{"code": "TZ", "descriptionEnglish": "Tanzania, United Republic of", "description": "Tanzania"}
{"code": "TH", "descriptionEnglish": "Thailand", "description": "Thailandia", "prefix": "66"}
{"code": "TL", "descriptionEnglish": "Timor-Leste", "description": "Timor Est"}
{"code": "TG", "descriptionEnglish": "Togo", "description": "Togo", "prefix": "228"}
{"code": "TK", "descriptionEnglish": "Tokelau", "description": "Tokelau"}
{"code": "TO", "descriptionEnglish": "Tonga", "description": "Tonga", "prefix": "676"}
{"code": "TT", "descriptionEnglish": "Trinidad and Tobago", "description": "Trinidad e Tobago", "prefix": "1868"}
{"code": "TN", "descriptionEnglish": "Tunisia", "description": "Tunisia", "prefix": "216"}
{"code": "TR", "descriptionEnglish": "Turkey", "description": "Turchia", "prefix": "90"}
{"code": "TM", "descriptionEnglish": "Turkmenistan", "description": "Turkmenistan", "prefix": "993"}
{"code": "TC", "descriptionEnglish": "Turks and Caicos Islands", "description": "Turks e Caicos", "prefix": "1649"}
{"code": "TV", "descriptionEnglish": "Tuvalu", "description": "Tuvalu", "prefix": "688"}
{"code": "UG", "descriptionEnglish": "Uganda", "description": "Uganda", "prefix": "256"}
{"code": "UA", "descriptionEnglish": "Ukraine", "description": "Ucraina", "prefix": "380"}
{"code": "AE", "descriptionEnglish": "United Arab Emirates", "description": "Emirati Arabi Uniti", "prefix": "971"}
{"code": "GB", "descriptionEnglish": "United Kingdom", "description": "Regno Unito", "prefix": "44"}
{"code": "US", "descriptionEnglish": "United States", "description": "Stati Uniti d'America", "prefix": "1"}
{"code": "UM", "descriptionEnglish": "United States Minor Outlying Islands", "description": "Isole Minori Esterne degli Stati Uniti d'America", "prefix": "246"}
{"code": "UY", "descriptionEnglish": "Uruguay", "description": "Uruguay", "prefix": "598"}
{"code": "UZ", "descriptionEnglish": "Uzbekistan", "description": "Uzbekistan", "prefix": "998"}
{"code": "VU", "descriptionEnglish": "Vanuatu", "description": "Vanuatu", "prefix": "678"}
{"code": "VE", "descriptionEnglish": "Venezuela", "description": "Venezuelano", "prefix": "58"}
{"code": "VN", "descriptionEnglish": "Viet Nam", "description": "Viet Nam", "prefix": "84"}
{"code": "VG", "descriptionEnglish": "Virgin Islands, British", "description": "Isole Vergini, inglesi", "prefix": "84"}
{"code": "VI", "descriptionEnglish": "Virgin Islands, U.S.", "description": "Isole Vergini americane", "prefix": "84"}
{"code": "WF", "descriptionEnglish": "Wallis and Futuna", "description": "Wallis e Futuna", "prefix": "681"}
{"code": "EH", "descriptionEnglish": "Western Sahara", "description": "Sahara occidentale"}
{"code": "YE", "descriptionEnglish": "Yemen", "description": "Yemen", "prefix": "969"}
{"code": "ZM", "descriptionEnglish": "Zambia", "description": "Zambia", "prefix": "260"}
{"code": "ZW", "descriptionEnglish": "Zimbabwe", "description": "Zimbabwe", "prefix": "263"}
