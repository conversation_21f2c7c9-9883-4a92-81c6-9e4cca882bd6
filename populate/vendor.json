{
    "creation" : ISODate("2019-07-23T12:00:00.000Z"),
    "lastUpdate" : ISODate("2019-07-23T12:00:00.000Z"),
    "lastname" : "Siteria",
    "name" : "",
    "genderType" : "male",
    "tin" : "04591370285",
    "vatNumber" : "04591370285",
    "email" : "<EMAIL>",
    "phoneNumber" : "+393406075503",
    "city" : "TREBASELEGHE",
    "address" : "Via Ostiglia 11",
    "provinceCode" : "PD",
    "postalCode" : "35010",
    "countryCode" : "IT",
    "statement" : false,
    "userId" : ObjectId("591d99797d80a2af3623ca39")
}
{
    "creation" : ISODate("2019-07-23T12:00:00.000Z"),
    "lastUpdate" : ISODate("2019-07-23T12:00:00.000Z"),
    "lastname" : "<PERSON><PERSON>",
    "name" : "",
    "genderType" : "male",
    "tin" : "03727640249",
    "vatNumber" : "03727640249",
    "email" : "<EMAIL>",
    "phoneNumber" : "+39 0424 525765",
    "city" : "Bassano del Grappa",
    "address" : "Vicolo Macello, 8",
    "provinceCode" : "VI",
    "postalCode" : "36061",
    "countryCode" : "IT",
    "statement" : false,
    "userId" : ObjectId("591d99797d80a2af3623ca40")
}
