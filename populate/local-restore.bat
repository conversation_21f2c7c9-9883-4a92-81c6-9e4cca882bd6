@echo off

:: converting date to day of week (1 = monday)
for /f "tokens=1-3 delims=/" %%a in ("%date%") do (
   set dd=%%a
   set mm=%%b
   set yy=%%c
)

if 1%dd:~0,1%==10 set dd=%dd:~1,1%
if 1%mm:~0,1%==10 set mm=%mm:~1,1%

set Days=WedThuFriSatSunMonTue
set /a z=14-mm,z/=12 
set /a jy=yy-z
set /a jm=12*z+mm-3
set /a mjd=153*jm+2,mjd/=5
set /a mjd=mjd+dd+365*jy+jy/4-jy/100+jy/400-678882
set /a "DoW=mjd%%7"
set /a DoW*=3
call set DoW=%%Days:~%DoW%,3%%

set daynumber=0
if [%DoW%]==[Mon] set daynumber=1
if [%DoW%]==[Tue] set daynumber=2
if [%DoW%]==[Wed] set daynumber=3
if [%DoW%]==[Thu] set daynumber=4
if [%DoW%]==[Fri] set daynumber=5
if [%DoW%]==[Sat] set daynumber=6
if [%DoW%]==[Sun] set daynumber=7


:: converting time to closest backup hour
for /f "tokens=1-2 delims=:" %%a in ("%time%") do (
   set hh=%%a
   set mm=%%b
)
:: trimming hours like 8, 9, etc...
for /f "tokens=* delims= " %%a in ("%hh%") do set hh=%%a

set hour=0
if [%hh%]==[24] set hour=23
if [%hh%]==[23] set hour=18
if [%hh%]==[22] set hour=18
if [%hh%]==[21] set hour=18
if [%hh%]==[20] set hour=18
if [%hh%]==[19] set hour=18
if [%hh%]==[18] set hour=10
if [%hh%]==[17] set hour=10
if [%hh%]==[16] set hour=10
if [%hh%]==[15] set hour=10
if [%hh%]==[14] set hour=10
if [%hh%]==[13] set hour=10
if [%hh%]==[12] set hour=10
if [%hh%]==[11] set hour=10
if [%hh%]==[10] set hour=24
if [%hh%]==[9] set hour=24
if [%hh%]==[8] set hour=24
if [%hh%]==[7] set hour=24
if [%hh%]==[6] set hour=24
if [%hh%]==[5] set hour=24
if [%hh%]==[4] set hour=24
if [%hh%]==[3] set hour=24
if [%hh%]==[2] set hour=24
if [%hh%]==[1] set hour=24
if [%hh%]==[0] set hour=24


:: previous day management
if [%hour%]==[24] (
	:: we MUST execute set command on a separate "if" in order to let subsequent "if" working
	set /a "daynumber=%daynumber%-1"
)
if [%hour%]==[24] (
	if [%daynumber%]==[0] set daynumber=7
)
if [%hour%]==[24] (
	set hour=23
)


:: computing backupname
set backupname=agora-mongodb-%daynumber%-%hour%.archive.gz


:: computing storagename
set storagename=agora-storage-%daynumber%-%hour%.archive.gz


:: computing archivename
set archivename=agora-storage-%daynumber%-%hour%.archive


:: with storage
set withstorage=false
if [%1] == [] goto :withoutstorage
if not [%1] == [--storage] goto :withoutstorage
set withstorage=true
echo local-restore with storage
shift
:withoutstorage

:: forced backupname
if [%1] == [] goto :dontforce
set backupname=%1
:dontforce


:: -----> MONGODB SESSION

:: downloading file
if exist c:\opt\WinSCP\WinSCP.com (
	c:\opt\WinSCP\WinSCP.com /command "open ftp://u332073-sub13:<EMAIL>/" "cd /backup/mongodb/agora" "get %backupname%" "exit"
	if errorlevel 1 (
		echo ERROR: unable to download file %backupname%
		goto :end
	)
) else (
	echo ERROR: unable to find ftp download tool
	goto :end
)


:: check backup presence
if not exist %backupname% (
	echo ERROR: unable to find file %backupname%
	goto :end
)


:: backup sanity check
if exist c:\opt\7-zip\7z.exe (
	c:\opt\7-zip\7z.exe t %backupname%
	if errorlevel 1 (
		echo ERROR: unable to gzip corrupted file %backupname%
		goto :end
	)
) else (
	echo ERROR: unable to find 7z tool
	goto :end
)


:: remove previous database (drop will remove only the collections that are in the backup)
if exist c:\opt\mongodb\server\bin\mongo.exe (
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.dropDatabase()"
	if errorlevel 1 (
		echo ERROR: unable to drop database
		goto :end
	)
) else (
	echo ERROR: unable to find mongo tool
	goto :end
)


:: restore backup
if exist c:\opt\mongodb\server\bin\mongorestore.exe (
	c:\opt\mongodb\server\bin\mongorestore.exe --gzip --drop --archive=%backupname% --db agora
	if errorlevel 1 (
		echo ERROR: unable to restore file %backupname%
		goto :end
	)
) else (
	echo ERROR: unable to find mongorestore tool
	goto :end
)


:: reset user collection to default password ("str")
if exist c:\opt\mongodb\server\bin\mongo.exe (
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('user').update({ username: { $regex: /./ } }, { $set: { password: 'uPsugy0yV5c6cBMi:p1a6xrYX3yRZtjREjSDtecWCQ/689wCk' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update password on user collection
		goto :end
	)
) else (
	echo ERROR: unable to find mongo tool
	goto :end
)


:: reset smtp collection to default gateway ("symmathesy")
if exist c:\opt\mongodb\server\bin\mongo.exe (
    c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('smtp').drop()"
	if errorlevel 1 (
		echo ERROR: unable to reset configuration on smtp collection
		goto :end
	)
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('smtp').insert({creation : ISODate('2019-06-06T18:05:22.204Z'), lastUpdate : ISODate('2019-06-06T18:09:32.189Z'), hostname : 'mail.symmathesy.org', port : 465, authentication : true, username : '<EMAIL>', password : '!midastouchSY', encryption : true, sender : '<EMAIL>'})"
	if errorlevel 1 (
		echo ERROR: unable to reset configuration on smtp collection
		goto :end
	)
) else (
	echo ERROR: unable to find mongo tool
	goto :end
)


:: reset notifications
if exist c:\opt\mongodb\server\bin\mongo.exe (
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('firm').update({}, { $set: { shopNotification: 'always' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update notification on firm collection
		goto :end
	)
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('firm').update({}, { $set: { vendorPortalNotification: 'never' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update notification on firm collection
		goto :end
	)
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('firm').update({}, { $set: { vendorNotification: 'never' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update notification on firm collection
		goto :end
	)
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('firm').update({}, { $set: { customerNotification: 'always' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update notification on firm collection
		goto :end
	)
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('firm').update({}, { $set: { siteEmail: '<EMAIL>' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update notification on firm collection
		goto :end
	)
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('firm').update({}, { $set: { shopEmail: '<EMAIL>' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update notification on firm collection
		goto :end
	)
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('firm').update({}, { $set: { shopNotificationEmail: '<EMAIL>' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update notification on firm collection
		goto :end
	)
	
) else (
	echo ERROR: unable to find mongo tool
	goto :end
)


:: reset meth integration
if exist c:\opt\mongodb\server\bin\mongo.exe (
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('firm').update({}, { $set: { methEnabled: false } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update meth integration on firm collection
		goto :end
	)
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('firm').update({}, { $set: { methEndpoint: 'http://agora.serviziocloud.com:888/iTnetagoraTest_0' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update meth integration on firm collection
		goto :end
	)
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('firm').update({}, { $set: { methController: 'iTnetC2_V1' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update meth integration on firm collection
		goto :end
	)
	
) else (
	echo ERROR: unable to find mongo tool
	goto :end
)


:: reset payment platforms
if exist c:\opt\mongodb\server\bin\mongo.exe (
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('payment_platform').update({ paymentType: 'paypal' }, { $set: { environmentType: 'sandbox' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update payment on payment_platform collection
		goto :end
	)
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('payment_platform').update({ paymentType: 'paypal' }, { $set: { alias: 'AY_fMbg_8peuRrhzOGGAadUwaqVFFz9MWHJCA9T7aEYuZ1Lsh--PGsjaseMNBrR6OeLfBz4fyZ0JwDqB' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update payment on payment_platform collection
		goto :end
	)
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('payment_platform').update({ paymentType: 'paypal' }, { $set: { secretKey: 'EEBc2l_Gwx9xu6BT7UXJjmDcw0cz2sH_gPwMVsJJLe7eQf6AdKXnYB2kv-4lrXOBjrFxn-4jqDtbU2uh' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update payment on payment_platform collection
		goto :end
	)
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('payment_platform').update({ paymentType: 'stripe' }, { $set: { environmentType: '' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update payment on payment_platform collection
		goto :end
	)
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('payment_platform').update({ paymentType: 'stripe' }, { $set: { alias: '' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update payment on payment_platform collection
		goto :end
	)
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('payment_platform').update({ paymentType: 'stripe' }, { $set: { secretKey: 'sk_test_51K6dScCB0BCRX18kXnxEvhER3IjlT1YkIOlrT2M2FyVUPyRAeCgPjurGkvDVWO6JBtaX7xt4y3AIdnEHjYpLE8WP00Q1i0VjjQ' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update payment on payment_platform collection
		goto :end
	)

	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('payment_platform').update({ paymentType: 'stripe' }, { $set: { priceKey1: 'price_1MFesKCB0BCRX18kO8jqAdJY' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update payment on payment_platform collection
		goto :end
	)
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('payment_platform').update({ paymentType: 'stripe' }, { $set: { priceKey2: 'price_1MFesrCB0BCRX18k5APNvdqR' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update payment on payment_platform collection
		goto :end
	)
	c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('payment_platform').update({ paymentType: 'stripe' }, { $set: { priceKey3: 'price_1MFetCCB0BCRX18kgOB07Ll5' } }, { upsert: false, multi: true })"
	if errorlevel 1 (
		echo ERROR: unable to update payment on payment_platform collection
		goto :end
	)


) else (
	echo ERROR: unable to find mongo tool
	goto :end
)


:: -----> STORAGE SESSION

if not [%withstorage%] == [true] goto :nostorage

:: downloading file
if exist c:\opt\WinSCP\WinSCP.com (
	c:\opt\WinSCP\WinSCP.com /command "open ftp://u332073-sub13:<EMAIL>/" "cd /backup/storage/agora" "get %storagename%" "exit"
	if errorlevel 1 (
		echo ERROR: unable to download file %storagename%
		goto :end
	)
) else (
	echo ERROR: unable to find ftp download tool
	goto :end
)


:: check backup presence
if not exist %storagename% (
	echo ERROR: unable to find file %storagename%
	goto :end
)


:: backup sanity check
if exist c:\opt\7-zip\7z.exe (
	c:\opt\7-zip\7z.exe t %storagename%
	if errorlevel 1 (
		echo ERROR: unable to gzip corrupted file %storagename%
		goto :end
	)
) else (
	echo ERROR: unable to find 7z tool
	goto :end
)


:: remove previous storage
if exist c:\opt\agora\storage\. (
	rmdir /S /Q c:\opt\agora\storage
	if errorlevel 1 (
		echo ERROR: unable to remove storage
		goto :end
	)
) else (
	echo no need to remove previous storage folder
)


:: restore backup (gzip)
if exist c:\opt\7-zip\7z.exe (
	c:\opt\7-zip\7z.exe x %storagename%
	if errorlevel 1 (
		echo ERROR: unable to gzip corrupted file %storagename%
		goto :end
	)
) else (
	echo ERROR: unable to find 7z tool
	goto :end
)


:: restore backup (tar)
if exist c:\opt\7-zip\7z.exe (
	c:\opt\7-zip\7z.exe x %archivename% -oc:\
	if errorlevel 1 (
		echo ERROR: unable to gzip corrupted file %archivename%
		goto :end
	)
) else (
	echo ERROR: unable to find 7z tool
	goto :end
)


:nostorage


:: remove backup
if exist %backupname% (
	del %backupname% /F /Q
)
if exist %storagename% (
	del %storagename% /F /Q
)
if exist %archivename% (
	del %archivename% /F /Q
)
goto :end


:: the end
:end
pause
