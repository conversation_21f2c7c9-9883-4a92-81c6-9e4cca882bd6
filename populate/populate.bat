@echo off

:: check for mongoimport presence
if not exist c:\opt\mongodb\server\bin\mongoimport.exe (
	echo ERROR: unable to find mongoimport tool
	goto :end
)


:: check for mongo presence
if not exist c:\opt\mongodb\server\bin\mongo.exe (
	echo ERROR: unable to find mongo tool
	goto :end
)


:: population
c:\opt\mongodb\server\bin\mongoimport.exe --db agora --collection country --drop --file \projects\agora-bundle\populate\country.json
c:\opt\mongodb\server\bin\mongoimport.exe --db agora --collection province --drop --file \projects\agora-bundle\populate\province.json
c:\opt\mongodb\server\bin\mongoimport.exe --db agora --collection city --drop --file \projects\agora-bundle\populate\city.json
c:\opt\mongodb\server\bin\mongoimport.exe --db agora --collection label --drop --file \projects\agora-bundle\populate\label.json

rem -- c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('user').remove({email:'<EMAIL>'})"
rem -- c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('user').remove({email:'<EMAIL>'})"
c:\opt\mongodb\server\bin\mongoimport.exe --db agora --collection user --drop --file \projects\agora-bundle\populate\user.json

rem -- c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('vendor').remove({email:'<EMAIL>'})"
rem -- c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('vendor').remove({email:'<EMAIL>'})"
c:\opt\mongodb\server\bin\mongoimport.exe --db agora --collection vendor --drop --file \projects\agora-bundle\populate\vendor.json

c:\opt\mongodb\server\bin\mongoimport.exe --db agora --collection area --drop --file \projects\agora-bundle\populate\area.json
c:\opt\mongodb\server\bin\mongoimport.exe --db agora --collection category --drop --file \projects\agora-bundle\populate\category.json

:: reset user collection to default password ("str")
c:\opt\mongodb\server\bin\mongo.exe agora --eval "db.getCollection('user').update({ name: { $regex: /./ } }, { $set: { password: 'uPsugy0yV5c6cBMi:p1a6xrYX3yRZtjREjSDtecWCQ/689wCk' } }, { upsert: false, multi: true })"
if errorlevel 1 (
	echo ERROR: unable to update password on user collection
	goto :end
)


:: the end
:end
pause
