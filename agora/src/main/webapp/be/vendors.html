{% extends "be/include/base.html" %}

{% block extrahead %}
<title>Lista agenti / amministratori</title>

<!-- SCRIPTS -->
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/datatables.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/jszip/jszip.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>    
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<script src="{{ contextPath }}/be/js/pages/vendors.js?{{ buildNumber }}"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>

{% endblock %}

{% block content %}

<!-- PAGE CONTENT -->
<div class="page-content">    

    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('VENDORS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('VENDORS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Control position -->
        <div class="panel panel-white">
            <div class="panel-heading">
                <h5 class="panel-title text-bold">Lista agenti / amministratori</h5>
                <div class="heading-elements">
                    <a href="{{ paths('VENDORS_ADD') }}" class="btn bg-danger heading-btn"><i class="icon-plus-circle2 position-left"></i>NUOVO AGENTE / AMM.TORE</a>
                </div>
            </div>

            <table class="table datatable-responsive-control-right">
                <thead>
                    <tr>
                        <th>Nome</th>
                        <th>Indirizzo</th>
                        <th>Contatti</th>
                        <th>Registrato il</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {% if vendorList is not empty %}
                        {% for entry in vendorList %}
                            <tr>
                                <td>
                                    <a href="{{ paths('VENDOR_VIEW') }}?oid={{ entry.vendor.id }}" class="text-bold">{{ entry.vendor.name }} {{ entry.vendor.lastName }}</a>
                                    <div class="text-muted text-size-small">
                                        {% set profileDescription = (entry.user.profileType == 'vendor') ? 'Agente' : 'Amministratore' %}
                                        {{ profileDescription }}
                                    </div>

                                </td>
                                <td>
                                    {{ entry.vendor.address }}, {{ entry.vendor.postalCode }}
                                    <div class="text-muted text-size-small">
                                        {{ entry.vendor.city }} ({{ entry.vendor.provinceCode }})
                                    </div>
                                </td>
                                <td>
                                    <ul class="list list-unstyled no-margin">
                                        <li class="no-margin truncate">
                                            <i class="icon-envelop5 text-size-base text-blue position-left"></i>
                                            <a href="mailto:{{ entry.vendor.email }}">{{ entry.vendor.email }}</a>
                                        </li>

                                        <li class="no-margin">
                                            <i class="icon-phone2 text-size-base text-success position-left"></i>
                                            <a href="tel:{{ entry.vendor.phoneNumber }}">{{ entry.vendor.phoneNumber }}</a>
                                        </li>
                                    </ul>
                                </td>
                                <td>
                                    <span sortable-value="{{ entry.vendor.sinceDate | date('yyyyMMddHHmm') }}">{{ entry.vendor.sinceDate | date('dd MMMM yyyy') }}</span>
                                </td>
                                <td></td>
                            </tr>
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
        <!-- /control position -->

    </div>
    <!-- END MAIN CONTENT -->

</div>
<!-- END PAGE CONTENT -->

{% endblock %}