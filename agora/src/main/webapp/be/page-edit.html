{% extends "be/include/base.html" %}
{% set pageActive = 'pages' %}
{% block extrahead %}
{% if page.id is not empty %}
<title>Modifica pagina {{ page.title | upper }}</title>
{% else %}
<title>Nuova pagina</title>
{% endif %}
<!-- Theme Custom CSS files -->
<link href="https://www.siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet">
<link href="https://www.siteria.it/libs/dropuploader/1.8.1/css/drop_uploader.min.css" rel="stylesheet">
<link href="{{ contextPath }}/fe/vendor/summernote/summernote-lite.min.css" rel="stylesheet">

<!-- Theme JS files -->
<!--
* Google Places API Web Service
* Google Maps Javascript API

-->
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBzbbAKQoJEad0bNS1qFMksTDfT60qJUnU&libraries=places&language=it-IT"></script>

<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/switchery.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/localization/messages_it.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
<script src="https://www.siteria.it/libs/autocomplete/1.0.4/auto-complete.min.js"></script>
<script src="https://www.siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="https://siteria.it/libs/sortable/1.7.0/Sortable.min.js"></script>
<script src="https://www.siteria.it/libs/dropuploader/1.8.1/js/drop_uploader.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/summernote/summernote-lite.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/summernote/lang/summernote-it-IT.js"></script>
<!-- /theme JS files -->
<script src="{{ contextPath }}/be/js/pages/page-edit.js?{{ buildNumber }}"></script>
<script src="{{ contextPath }}/be/js/lang/it-IT.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}
<!-- api call for products autocomplete -->
<a id="dataCustomersUri" style="display: none" href="{{ paths('DATA_CUSTOMERS') }}"></a>

<a id="imageUri" style="display: none;" href="{{ paths('IMAGE_SYSTEM') }}?oid=" rel="nofollow"></a>

<a id="pagesUri" style="display: none" href="{{ paths('BE_PAGES') }}"></a>
<a id="pagesSuccessUri" style="display: none;" href="{{ paths('BE_PAGES') }}/ok" rel="nofollow"></a>
<a id="pageRemoveUri" style="display: none;" href="{{ paths('BE_PAGE_REMOVE') }}?pageId={{ page.id }}" rel="nofollow"></a>

<!-- PAGE EDIT -->
{% if page.id is not empty %}
{% set saveUri = paths('BE_PAGE_EDIT_SAVE') + '?oid=' + page.id %}
{% else %}
{% set saveUri = paths('BE_PAGE_EDIT_SAVE') %}
{% endif %}
<a id="pageSaveUri" style="display: none" href="{{ saveUri }}"></a>

<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-success">
                    <a href="{{ paths('PAGE_EDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-danger">
                    <a href="{{ paths('PAGE_EDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- PAGE EDIT -->
        {% if page.id is not empty %}
        {% set saveUri = paths('BE_PAGE_EDIT_SAVE') + '?oid=' + page.id %}
        {% else %}
        {% set saveUri = paths('BE_PAGE_EDIT_SAVE') %}
        {% endif %}
        <div class="row">
            <div class="col-lg-8 col-lg-offset-2">
                <!-- Basic layout-->
                <form id="page-form" class="form-horizontal" method="post" action="{{ saveUri }}" autocomplete="off">
                    <div class="panel panel-white">

                        <div class="panel-heading">
                            {% if page.id is not empty %}
                            <h5 class="panel-title text-bold">Modifica pagina {{ page.title | upper }}</h5>
                            <div class="heading-elements visible-elements">
                                <div class="btn-group heading-btn">
                                    <a href="{{ paths('BE_PAGE_REMOVE_PROFILE_IMAGE') }}?oid={{ page.id }}" class="btn heading-btn btn-danger legitRipple"><i class="icon-trash-alt position-left"></i>ELIMINA IMMAGINE PROFILO</a>
                                    <a href="{{ paths('BE_PAGE_REMOVE_POSTER') }}?oid={{ page.id }}" class="btn heading-btn btn-danger legitRipple"><i class="icon-trash-alt position-left"></i>ELIMINA COPERTINA</a>
                                    <a href="{{ paths('BE_PAGE_QRCODE_GENERATE') }}?oid={{ page.id }}" class="btn heading-btn btn-primary legitRipple qrcode-print"><i class="icon-file-pdf position-left"></i>QRCODE</a>
                                </div>
                            </div>
                            {% else %}
                            <h5 class="panel-title text-bold">Nuova pagina</h5>
                            {% endif %}
                        </div>

                        <div class="panel-body">
                            <div class="row g-3 mb-3">
                                <!-- Profile phone -->
                                <div class="col-lg-3">
                                    <label class="form-label">Immagine del profilo</label>
                                    <div class="slim rounded"
                                         data-max-file-size="5"
                                         data-save-initial-image="{{ page.profileImageId is not empty ? 'true' : 'false'}}"
                                         data-push="false"
                                         data-post="output"
                                         data-label="Carica un'immagine"
                                         data-label-loading=" "
                                         data-ratio="1:1"
                                         data-jpeg-compression=100
                                         data-button-edit-label="Modifica"
                                         data-button-remove-label="Elimina"
                                         data-button-download-label="Scarica"
                                         data-button-upload-label="Carica"
                                         data-button-rotate-label="Ruota"
                                         data-button-cancel-label="Cancella"
                                         data-button-confirm-label="Conferma"
                                         data-status-file-size="Il file è troppo grande, il massimo consentito è $0 MB"
                                         data-status-file-type="Formato immagine non valido, formati consentiti: $0"
                                         data-status-no-support="Il tuo browser non supporta la modifica dell'immagine"
                                         data-status-image-too-small="Immagine troppo piccola, risoluzione minima: $0 pixel"
                                         data-status-content-length="Il server non supporta file così grandi"
                                         data-status-unknown-response="Errore sconosciuto, <NAME_EMAIL>"
                                         data-status-upload-success="Immagine salvata"
                                         data-size="100,100">
                                        {% if page.profileImageId is not empty %}
                                        <img src="{{ paths('IMAGE_SYSTEM') }}?oid={{ page.profileImageId }}" alt=""/>
                                        {% endif %}
                                        <input type="file" id="cropper" name="uploaded-profile" data-show-caption="false" data-show-remove="true">
                                    </div>
                                    <small class="form-text">Dimensioni consigliate 600x600 (ratio 1:1)</small>
                                </div>
                                <!-- Cover image -->
                                <div class="col-lg-12">
                                    <label class="form-label">Foto di copertina</label>
                                    <div class="slim rounded"
                                         data-max-file-size="5"
                                         data-save-initial-image="{{ page.coverImageId is not empty ? 'true' : 'false'}}"
                                         data-push="false"
                                         data-post="output"
                                         data-label="Carica un'immagine"
                                         data-label-loading=" "
                                         data-ratio="free"
                                         data-button-edit-label="Modifica"
                                         data-button-remove-label="Elimina"
                                         data-button-download-label="Scarica"
                                         data-button-upload-label="Carica"
                                         data-button-rotate-label="Ruota"
                                         data-button-cancel-label="Cancella"
                                         data-button-confirm-label="Conferma"
                                         data-status-file-size="Il file è troppo grande, il massimo consentito è $0 MB"
                                         data-status-file-type="Formato immagine non valido, formati consentiti: $0"
                                         data-status-no-support="Il tuo browser non supporta la modifica dell'immagine"
                                         data-status-image-too-small="Immagine troppo piccola, risoluzione minima: $0 pixel"
                                         data-status-content-length="Il server non supporta file così grandi"
                                         data-status-unknown-response="Errore sconosciuto, <NAME_EMAIL>"
                                         data-status-upload-success="Immagine salvata">
                                        {% if page.coverImageId is not empty %}
                                        <img src="{{ paths('IMAGE_SYSTEM') }}?oid={{ page.coverImageId }}" alt=""/>
                                        {% endif %}
                                        <input type="file" id="cropper" name="uploaded-cover" data-show-caption="false" data-show-remove="true">
                                    </div>
                                    <small class="form-text">Dimensioni consigliate 1116x280 (ratio 4:1)</small>
                                </div>
                                <!-- End Media -->
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Tipologia di pagina:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="select-search-standard form-control" id="pageType" name="pageType">
                                            {% for pageType in lookup('area') %}
                                            <option value="{{ pageType.code }}" {{ page.pageType == pageType.code ? 'selected' : '' }}>{{ pageType.description }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Categoria:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="select-search-standard form-control" id="category" name="category">
                                            {% for category in lookup('category') %}
                                            <option value="{{ category.code }}" {{ pageType == category.code ? 'selected' : '' }}>{{ category.description }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Nome:</label>
                                <div class="col-lg-9">
                                    <input role="presentation" autocomplete="off" type="text" name="name" class="form-control maxlength camel-case" maxlength="100" placeholder="Titolo dell'pagina" value="{{ page.name }}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Identificativo pagina:</label>
                                <div class="col-lg-9">
                                    <input type="text" class="form-control" placeholder="Identificativo pagina" id="identifier" name="identifier" value="{{ page.identifier }}" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Testo:</label>
                                <div class="col-lg-9">
                                    <textarea cols="18" rows="5" name="description" class="form-control summernote" placeholder="Inserisci una descrizione...">{{ page.description }}</textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Descrizione breve:</label>
                                <div class="col-lg-9">
                                    <textarea cols="18" rows="3" name="shortDescription" maxlength="50" class="form-control" placeholder="Inserisci una descrizione breve...">{{ page.shortDescription }}</textarea>
                                </div>
                            </div>
                            <legend class="text-bold">
                                <i class="icon-location4"></i>
                                Luogo
                                <a class="control-arrow collapsed" data-toggle="collapse" data-target="#page-place" aria-expanded="true">
                                    <i class="icon-circle-down2"></i>
                                </a>
                            </legend>
                            <!--
                            @LEO
                             SE TIPO = OFFLINE MOSTRO SOLO INDIRIZZI
                            SE TIPO = ONLINE MOSTRO SOLO LINK INDIRIZZO VIRTUALE
                            SE TIPO = MIXED MOSTRO ENTRAMBI
                            -->
                            <div class="collapse in" id="page-place" aria-expanded="true">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Indirizzo completo:</label>
                                    <div class="col-lg-9">
                                        <input role="presentation" autocomplete="off" type="text" name="fulladdress" id="fulladdress" class="form-control maxlength" maxlength="250" placeholder="Indirizzo" value="{{ page.fulladdress }}">
                                        <span class="help-block no-margin-bottom">Autocompleta i campi sottostanti</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Stato:</label>
                                    <select class="form-control select-search" name="countryCode" id="countryCode">
                                        <option value="">-</option>
                                        {% for item in lookup("country") %}
                                        <option value="{{ item.code }}" {{ item.code == page.countryCode ? 'selected' : ''}}>{{ item.description }}</option>
                                        {% endfor %}
                                    </select>                                    
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Indirizzo:</label>
                                    <div class="col-lg-9">
                                        <input role="presentation" autocomplete="off" type="text" name="address" id="address" class="form-control maxlength" maxlength="100" placeholder="Indirizzo" value="{{ page.address }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Città:</label>
                                    <div class="col-lg-9">
                                        <input role="presentation" autocomplete="off" type="text" name="city" id="city" class="form-control maxlength" maxlength="100" placeholder="Città" value="{{ page.city }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">CAP:</label>
                                    <div class="col-lg-9">
                                        <input role="presentation" autocomplete="off" type="text" name="postalCode" id="postalCode" class="form-control maxlength" maxlength="10" placeholder="CAP" value="{{ page.postalCode }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Provincia:</label>
                                    <div class="col-lg-9">
                                        <select class="form-control select-search-standard" name="provinceCode" id="provinceCode">
                                            <option value="">-</option>
                                            {% for item in lookup("province") %}
                                            <option value="{{ item.code }}" {{ item.code == page.provinceCode ? 'selected' : ''}}>{{ item.description }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Dettagli Indirizzo:</label>
                                    <div class="col-lg-9">
                                        <input role="presentation" autocomplete="off" type="text" name="extraAddress" id="extraAddress" class="form-control maxlength" maxlength="100" placeholder="Dettagli Indirizzo" value="{{ page.extraAddress }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Mostra numero follower:</label>
                                    <div class="col-lg-9">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" role="switch" id="showFollowers" name="showFollowers" {{ page.showFollowers == true ? 'checked' : '' }}>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Sito web:</label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" placeholder="Sito web" id="websiteUrl" name="websiteUrl" value="{{ page.websiteUrl }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Facebook:</label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" placeholder="Link Facebook" id="facebookUrl" name="facebookUrl" value="{{ page.facebookUrl }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Twitter:</label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" placeholder="Link Twitter" id="twitterUrl" name="twitterUrl" value="{{ page.twitterUrl }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Instagram:</label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" placeholder="Link Instagram" id="instagramUrl" name="instagramUrl" value="{{ page.instagramUrl }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Linkedin:</label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" placeholder="Link Linkedin" id="linkedinUrl" name="linkedinUrl" value="{{ page.linkedinUrl }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">YouTube:</label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" placeholder="Link YouTube" id="youtubeUrl" name="youtubeUrl" value="{{ page.youtubeUrl }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Reddit:</label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" placeholder="Link Reddit" id="redditUrl" name="redditUrl" value="{{ page.redditUrl }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Medium:</label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" placeholder="Link Medium" id="mediumUrl" name="mediumUrl" value="{{ page.mediumUrl }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">TikTok:</label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" placeholder="Link TikTok" id="tiktok" name="tiktok" value="{{ page.tiktok }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Spotify:</label>
                                    <div class="col-lg-9">
                                        <input type="text" class="form-control" placeholder="Link Spotify" id="spotifiyUrl" name="spotifiyUrl" value="{{ page.spotifiyUrl }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Proprietario pagina:</label>
                                    <div class="col-lg-9">
                                        {% set owner = get('user', page.ownerId) %}
                                        {% set email = 'n.d.' %}
                                        {% if owner.email is not empty %}
                                        {% set email  = owner.email %}
                                        {% endif %}

                                        {% set disabled = '' %}
                                        {% if page.isUserPage %}
                                        {% set disabled = 'disabled' %}
                                        {% endif %}

                                        <input id="autocomplete-owner" class="form-control" {{ disabled }} type="text" placeholder="proprietario" value="{{ owner.id is not empty ? email : '' }}">
                                        <input type="text" name="ownerId" id="ownerId" class="form-control hidden" placeholder="proprietario" value="{{ owner.id }}" >
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Chi può inserire eventi:</label>
                                    <div class="col-lg-9">
                                        <select class="form-control select" name="pageTagging" id="pageTagging">
                                            <option value="everyone" {{ page.pageTagging == 'everyone' ? 'selected' : '' }}>Chiunque</option>                                        
                                            <option value="owner" {{ page.pageTagging == 'owner' ? 'selected' : '' }}>Solo il proprietario</option>                                        
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Numero Followers:</label>
                                    <div class="col-lg-9">
                                        <input type="number" min="0" name="followers" id="followers" class="form-control maxlength" maxlength="10" value="{{ page.followers }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Abilita visualizzazione numero follower:</label>
                                    <div class="col-lg-9">
                                        <input type="checkbox" name="showFollowers" id="showFollowers" class="switchery" {{ page.showFollowers ? 'checked' : ''}}>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <div class="panel-footer has-visible-elements">
                            <div class="heading-elements visible-elements">
                                <span class="heading-text text-semibold">Azioni:</span>
                                <div class="pull-right">
                                    <button type="button" class="btn heading-btn btn-default btn-cancel">Annulla</button>
                                    {% if page.id is not empty %}
                                    <button id="btn-delete" type="button" class="btn heading-btn btn-danger">Elimina</button>
                                    {% endif %}
                                    <button id="btn-save" type="submit" class="btn heading-btn btn-primary">Salva<i class="icon-arrow-right14"></i></button>
                                </div>
                            </div>
                        </div>

                    </div>
                </form>
                <!-- /basic layout -->
            </div>
        </div>
    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

{% endblock %}
