{% extends "be/include/base.html" %}
{% set pageActive = 'sponsor-events' %}

{% block extrahead %}
<title>{% if sponsorEvent.id is not empty %}Modifica{% else %}Aggiungi{% endif %} Evento Sponsorizzato</title>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/localization/messages_it.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment_locales.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/core/app.js"></script>
<script src="{{ contextPath }}/be/js/pages/sponsor-event-edit.js?{{ buildNumber }}"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/ripple.min.js"></script>
{% endblock %}

{% block content %}

<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- Main content -->
    <div class="content-wrapper">
        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('SPONSOR_EVENT_EDIT') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Informazioni salvate correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('SPONSOR_EVENT_EDIT') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Hai bisogno di aiuto? <a href="mailto:<EMAIL>">Contatta il servizio clienti</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Horizontal form options -->
        {% if sponsorEvent.id is not empty %}
            {% set saveUri = paths('SPONSOR_EVENT_EDIT_SAVE') + '?sponsorEventId=' + sponsorEvent.id %}
        {% else %}
            {% set saveUri = paths('SPONSOR_EVENT_EDIT_SAVE') %}
        {% endif %}
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <!-- Basic layout-->
                <form id="sponsor-event-form" class="form-horizontal" method="post" action="{{ saveUri }}">
                    <div class="panel panel-white">
                        
                        <div class="panel-heading">
                            {% if sponsorEvent.id is not empty %}
                                <h5 class="panel-title text-bold">Modifica Evento Sponsorizzato</h5>
                            {% else %}
                                <h5 class="panel-title text-bold">Aggiungi Evento Sponsorizzato</h5>
                            {% endif %}
                        </div>

                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Evento:</label>
                                <div class="col-lg-9">
                                    <select name="eventId" id="eventId" class="form-control select-search-event" required>
                                        {% if sponsorEvent.eventId is not empty %}
                                            {% set selectedEvent = get('event', sponsorEvent.eventId) %}
                                            {% if selectedEvent is not empty %}
                                                <option value="{{ selectedEvent.id }}" selected>{{ selectedEvent.name }}</option>
                                            {% endif %}
                                        {% endif %}
                                    </select>
                                    <span class="help-block">Cerca e seleziona l'evento da sponsorizzare</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-lg-3">Data Scadenza <span class="text-danger">*</span></label>
                                <div class="col-lg-9">
                                    <input type="text" name="expirationDate" id="expirationDate" class="form-control daterange-single" placeholder="Seleziona data scadenza" value="{{ sponsorEvent.expirationDate | date('dd/MM/yyyy') }}" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-3 control-label">Ordinamento:</label>
                                <div class="col-lg-9">
                                    <input type="number" name="sort" class="form-control" 
                                           placeholder="Numero per ordinamento (es. 1, 2, 3...)" 
                                           value="{{ sponsorEvent.sort | default(1) }}" min="1" required>
                                    <span class="help-block">Numero per definire l'ordine di visualizzazione (1 = ultimo)</span>
                                </div>
                            </div>

                            {% if sponsorEvent.id is not empty %}
                                <input type="hidden" name="sponsorEventId" value="{{ sponsorEvent.id }}">
                            {% endif %}
                        </div>

                        <div class="panel-footer">
                            <div class="heading-elements">
                                <span class="heading-text text-semibold">Azioni:</span>
                                <div class="heading-btn pull-right">
                                    <button type="button" class="btn btn-default btn-cancel">Annulla</button>
                                    {% if sponsorEvent.id is not empty %}
                                        <button id="btn-delete" type="button" class="btn bg-danger-600">Rimuovi</button>
                                    {% endif %}
                                    <button id="btn-save" type="submit" class="btn btn-primary">
                                        {% if sponsorEvent.id is not empty %}Aggiorna evento sponsorizzato{% else %}Salva evento sponsorizzato{% endif %}
                                        <i class="icon-arrow-right14"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                    </div>
                </form>
                <!-- /basic layout -->

            </div>
        </div>

    </div>
    <!-- /main content -->
</div>
<!-- /page content -->

<!-- Hidden URIs for JavaScript -->
<a id="dataEventsUri" style="display: none;" href="{{ paths('DATA_SEARCH_EVENTS') }}" rel="nofollow"></a>
<a id="sponsorEventCollectionUri" style="display: none;" href="{{ paths('SPONSOR_EVENT_COLLECTION') }}" rel="nofollow"></a>
<a id="sponsorEventCollectionSuccessUri" style="display: none;" href="{{ paths('SPONSOR_EVENT_COLLECTION') }}/ok" rel="nofollow"></a>
{% if sponsorEvent.id is not empty %}
<a id="sponsorEventRemoveUri" style="display: none;" href="{{ paths('SPONSOR_EVENT_REMOVE') }}?sponsorEventId={{ sponsorEvent.id }}" rel="nofollow"></a>
{% endif %}

{% endblock %}
