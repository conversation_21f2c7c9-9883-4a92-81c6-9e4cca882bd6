{% extends "be/include/base.html" %}

{% block extrahead %}
{% if area.id is not empty %}
    <title>Modifica area {{ area.title | upper }}</title>
{% else %}
    <title>Nuova area</title>
{% endif %}
<!-- Theme Custom CSS files -->
<link href="https://www.siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet">
<!-- Theme JS files -->
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/localization/messages_it.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/wysihtml5/wysihtml5.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/wysihtml5/toolbar.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/wysihtml5/parsers.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/wysihtml5/locales/bootstrap-wysihtml5.ua-UA.js"></script>
<script src="https://www.siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/area-edit.js?{{ buildNumber }}"></script>

<!-- /theme JS files -->
{% endblock %}

{% block content %}

<a id="areasUri" style="display: none" href="{{ paths('AREAS') }}"></a>
<a id="areasSuccessUri" style="display: none;" href="{{ paths('AREAS') }}/ok" rel="nofollow"></a>
<a id="areaRemoveUri" style="display: none;" href="{{ paths('AREA_REMOVE') }}?areaId={{ area.id }}" rel="nofollow"></a>

<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-success">
                    <a href="{{ paths('AREA_EDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-danger">
                    <a href="{{ paths('AREA_EDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- AREA EDIT -->
        {% if area.id is not empty %}
            {% set saveUri = paths('AREA_EDIT_SAVE') + '?oid=' + area.id %}
        {% else %}
            {% set saveUri = paths('AREA_EDIT_SAVE') %}
        {% endif %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <!-- Basic layout-->
                <form id="area-form" class="form-horizontal" method="post" action="{{ saveUri }}">
                    <div class="panel panel-white">

                        <div class="panel-heading">
                            {% if area.id is not empty %}
                                <h5 class="panel-title text-bold">Modifica area {{ area.title | upper }}</h5>
                            {% else %}
                                <h5 class="panel-title text-bold">Nuova area</h5>
                            {% endif %}
                        </div>

                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Immagine:</label>
                                <div class="col-lg-9">
                                    <div class="row">
                                        <div class="col-xs-12">
                                            <div class="slim"
                                                 data-max-file-size="1"
                                                 data-save-initial-image="{{ area.imageIds[0] is not empty ? 'true' : 'false'}}"
                                                 data-push="false"
                                                 data-post="output"
                                                 data-label="Carica un'immagine"
                                                 data-label-loading=" "
                                                 data-ratio="free"
                                                 data-button-edit-label="Modifica"
                                                 data-button-remove-label="Elimina"
                                                 data-button-download-label="Scarica"
                                                 data-button-upload-label="Carica"
                                                 data-button-rotate-label="Ruota"
                                                 data-button-cancel-label="Cancella"
                                                 data-button-confirm-label="Conferma"
                                                 data-status-file-size="Il file è troppo grande, il massimo consentito è $0 MB"
                                                 data-status-file-type="Formato immagine non valido, formati consentiti: $0"
                                                 data-status-no-support="Il tuo browser non supporta la modifica dell'immagine"
                                                 data-status-image-too-small="Immagine troppo piccola, risoluzione minima: $0 pixel"
                                                 data-status-content-length="Il server non supporta file così grandi"
                                                 data-status-unknown-response="Errore sconosciuto, <NAME_EMAIL>"
                                                 data-status-upload-success="Immagine salvata">

                                                {% if area.imageIds[0] is not empty %}
                                                    <img src="{{ paths('IMAGE') }}?oid={{ area.imageIds[0] }}" alt=""/>
                                                {% endif %}
                                                <input type="file" id="cropper" name="uploaded-files" data-show-caption="false" data-show-remove="true">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Icona:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="icon" class="form-control maxlength" maxlength="50" placeholder="Codice icona" value="{{ area.icon }}" required {{ area.id is not empty ? 'readonly' : '' }}>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Codice:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="code" class="form-control maxlength" maxlength="50" placeholder="Codice area" value="{{ area.code }}" required {{ area.id is not empty ? 'readonly' : '' }}>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Titolo:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="title" class="form-control maxlength" maxlength="50" placeholder="Titolo dell' area" value="{{ area.title }}" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Descrizione:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <textarea cols="18" rows="5" name="description" class="wysihtml5 wysihtml5-min form-control" placeholder="Inserisci il testo ...">
                                            {{ area.description }}
                                        </textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Ordinamento:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="sorting" class="form-control maxlength" maxlength="50" placeholder="Es. 01" value="{{ area.sorting }}" {{ area.id is not empty ? 'readonly' : '' }}>
                                </div>
                            </div>
                        </div>
                        <div class="panel-footer has-visible-elements">
                            <div class="heading-elements visible-elements">
                                <span class="heading-text text-semibold">Azioni:</span>
                                <div class="pull-right">
                                    <button type="button" class="btn heading-btn btn-default btn-cancel">Annulla</button>
                                    {% if area.id is not empty %}
                                        <button id="btn-delete" type="button" class="btn heading-btn btn-danger">Elimina</button>
                                    {% endif %}
                                    <button id="btn-save" type="submit" class="btn heading-btn btn-primary">Salva<i class="icon-arrow-right14"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <!-- /basic layout -->
            </div>
        </div>
    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

{% endblock %}
