{% extends "be/include/base.html" %}
{% set sidebarDefaultVisible = 'true' %}
{% set sidebarMobileVisible = 'true' %}
{% block extrahead %}
<title>Agente</title>

<!-- SCRIPTS -->
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/switchery.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/autosize.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/datatables.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/jszip/jszip.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>    
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/moment/moment.min.js"></script>
<script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/fullcalendar/fullcalendar.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/fullcalendar/lang/it.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/editors/wysihtml5/wysihtml5.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/editors/wysihtml5/toolbar.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/editors/wysihtml5/parsers.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/editors/wysihtml5/locales/bootstrap-wysihtml5.ua-UA.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/localization/messages_it.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet" type="text/css" media="all">
<script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/vendor-view.js?{{ buildNumber }}"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>

{% endblock %}

{% block sidebarcontrol %}
<ul class="fab-menu fab-menu-top-left hidden-xs" data-fab-toggle="click">
    <li>
        <a class="fab-menu-btn btn btn-default btn-float btn-rounded btn-icon sidebar-control sidebar-main-hide">
            <i class="icon-paragraph-justify3"></i>
        </a>
    </li>
</ul>
{% endblock %}

{% block content %}

<!-- actions -->
<a id="vendorViewUri" style="display: none" href="{{ paths('VENDOR_VIEW') }}?oid={{ vendor.id }}" rel="nofollow"></a>
<a id="vendorViewSuccessUri" style="display: none" href="{{ paths('VENDOR_VIEW') }}/success?oid={{vendor.id}}" rel="nofollow"></a>
<a id="vendorsSuccessUri" style="display: none;" href="{{ paths('VENDORS') }}/success" rel="nofollow"></a>
<a id="vendorRemoveUri" style="display: none;" href="{{ paths('VENDOR_REMOVE') }}?vendorId={{ vendor.id }}" rel="nofollow"></a>

<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- LEFT SIDEBAR -->
    <div class="sidebar sidebar-main sidebar-default sidebar-separate">
        <div class="sidebar-content">

            <div class="content-group">
                <div class="panel-body bg-agora border-radius-top text-center" style="background-image: url({{ contextPath }}/be/imgs/bg.png); background-size: contain;">
                    <div class="content-group-sm">
                        <h5 class="text-semibold no-margin-bottom">
                            {% set fullname = '*Nome* *Cognome*' %}
                            {% if vendor.fullname is not empty %}
                                {% set fullname = vendor.fullname %}
                            {% elseif (vendor.name is not empty) or (vendor.lastname is not empty) %}
                                {% set fullname = (vendor.name | default('')) + ' ' + vendor.lastname | default('') %}
                            {% endif %}
                            {{ fullname }}
                        </h5>

                        <span class="display-block semi-transparent">agente dal {{ vendor.sinceDate | date('dd MMMM yyyy') }}</span>
                    </div>

                    <div class="display-inline-block content-group-sm">
                        {% if vendor.imageId is not empty %}
                            <img src="{{ paths('IMAGE') }}?oid={{ vendor.imageId }}" class="img-circle img-responsive" alt="" style="width: 120px; height: 120px;">
                        {% else %}
                            <img src="https://ui-avatars.com/api/?name={{ vendor.name }}+{{ vendor.lastname }}+{{ vendor.fullname }}&size=256" class="img-circle img-responsive" alt="" style="width: 120px; height: 120px;">
                        {% endif %}
                    </div>

                    <ul class="list-inline no-margin-bottom">
                        <li><a href="mailto:{{ vendor.email }}" data-popup="tooltip" title="Invia email" class="btn bg-blue btn-rounded btn-icon legitRipple"><i class="icon-envelop5"></i></a></li>
                        <li><a href="tel:{{ vendor.phoneNumber }}" data-popup="tooltip" title="Chiama" class="btn bg-success-700 btn-rounded btn-icon legitRipple"><i class="icon-phone2"></i></a></li>                                                
                    </ul>
                </div>

                <div class="panel panel-body no-border-top no-border-radius-top">
                    <div class="form-group mt-5">
                        <label class="text-semibold">Indirizzo:</label>
                        <span class="pull-right-sm truncate width-half-right" data-popup="tooltip" title="{{ vendor.address }}, {{ vendor.postalCode }}">{{ vendor.address }}, {{ vendor.postalCode }}</span>                        
                    </div>

                    <div class="form-group mt-5">
                        <label class="text-semibold">Città:</label>
                        <span class="pull-right-sm truncate width-half-right">{{ vendor.city }} ({{ vendor.provinceCode }})</span>
                    </div>

                    <div class="form-group">
                        <label class="text-semibold">Telefono:</label>
                        <span class="pull-right-sm truncate width-half-right">{{ vendor.phoneNumber }}</span>
                    </div>

                    <div class="form-group">
                        <label class="text-semibold">Email:</label>
                        <span class="pull-right-sm truncate width-half-right"><a href="mailto:{{ vendor.email }}">{{ vendor.email }}</a></span>
                    </div>

                </div>
            </div>

        </div>
    </div>
    <!-- END LEFT SIDEBAR -->

    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('VENDOR_VIEW') }}?oid={{ vendor.id }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('VENDOR_VIEW') }}?oid={{ vendor.id }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Toolbar -->
        <div class="navbar navbar-default navbar-component navbar-xs">
            <ul class="nav navbar-nav visible-xs-block">
                <li class="full-width text-center"><a data-toggle="collapse" data-target="#navbar-filter"><i class="icon-menu7"></i></a></li>
            </ul>

            <div class="navbar-collapse collapse" id="navbar-filter">
                <ul class="nav navbar-nav">
                    <li class="active"><a href="#info" data-toggle="tab"><i class="icon-info22 position-left"></i> Informazioni</a></li>
                    <li><a href="#notes" data-toggle="tab"><i class="icon-stack-text position-left"></i> Note</a></li>
                </ul>
            </div>
        </div>
        <!-- /toolbar -->


        <!-- User profile -->
        <div class="row">
            <div class="col-lg-12">
                <div class="tabbable">
                    <div class="tab-content">
                        <div class="tab-pane fade in active" id="info">

                            <!-- Profile info -->
                            <form id="form-edit-vendor" class="form-horizontal form-validate-jquery" method="post" action="{{ paths('VENDORS_EDIT_SAVE') }}?oid={{ vendor.id }}">
                                <div class="panel panel-white">
                                    <div class="panel-heading">
                                        <h5 class="panel-title text-bold">Informazioni</h5>
                                        <div class="heading-elements">
                                            <button type="submit" class="btn heading-btn btn-primary">Aggiorna agente <i class="icon-arrow-right14"></i></button>
                                        </div>                                        
                                    </div>

                                    <div class="panel-body">
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Foto profilo:</label>
                                            <div class="col-lg-9">
                                                <div class="row">
                                                    <div class="col-xs-6 col-xs-offset-3 col-sm-4 col-sm-offset-4 col-md-4 col-md-offset-4">
                                                        <div class="slim"
                                                             data-max-file-size="5"
                                                             data-save-initial-image="{{ vendor.imageId is not empty ? 'true' : 'false'}}"
                                                             data-push="false"
                                                             data-post="output"
                                                             data-label="Carica un'immagine"
                                                             data-label-loading=" "
                                                             data-ratio="1:1"
                                                             data-button-edit-label="Modifica"
                                                             data-button-remove-label="Elimina"
                                                             data-button-download-label="Scarica"
                                                             data-button-upload-label="Carica"
                                                             data-button-rotate-label="Ruota"
                                                             data-button-cancel-label="Cancella"
                                                             data-button-confirm-label="Conferma"
                                                             data-status-file-size="Il file è troppo grande, il massimo consentito è $0 MB"
                                                             data-status-file-type="Formato immagine non valido, formati consentiti: $0"
                                                             data-status-no-support="Il tuo browser non supporta la modifica dell'immagine"
                                                             data-status-image-too-small="Immagine troppo piccola, risoluzione minima: $0 pixel"
                                                             data-status-content-length="Il server non supporta file così grandi"
                                                             data-status-unknown-response="Errore sconosciuto, <NAME_EMAIL>"
                                                             data-status-upload-success="Immagine salvata">

                                                            {% if vendor.imageId is not empty %}
                                                                <img src="{{ paths('IMAGE') }}?oid={{ vendor.imageId }}" alt=""/>
                                                            {% endif %}
                                                            <input type="file" id="cropper" name="uploaded-files" data-show-caption="false" data-show-remove="true">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% set isVendor = (user.profileType != 'system') and (user.profileType != 'admin') %}
                                        {% if not isVendor %}
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Codice:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="code" class="form-control maxlength" maxlength="50" placeholder="Codice" value="{{ vendor.code }}" readonly>
                                                </div>
                                            </div>
                                        {% endif %}
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Nome:</label>
                                            <div class="col-lg-9">
                                                <input type="text" name="name" class="form-control maxlength" maxlength="50" placeholder="Nome" value="{{ vendor.name }}">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Cognome:</label>
                                            <div class="col-lg-9">
                                                <input type="text" name="lastname" class="form-control maxlength" maxlength="100" placeholder="Cognome" value="{{ vendor.lastname }}" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Ragione Sociale:</label>
                                            <div class="col-lg-9">
                                                <input type="text" name="fullname" class="form-control maxlength" maxlength="100" placeholder="Cognome" value="{{ vendor.fullname }}">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Sesso:</label>
                                            <div class="col-lg-9">
                                                <div class="form-group">
                                                    <select class="select" id="genderType" name="genderType" required>
                                                        <option value="">-</option>
                                                        <option value="male" {{ vendor.genderType == 'male' ? 'selected' : '' }}>Uomo</option>
                                                        <option value="female" {{ vendor.genderType == 'female' ? 'selected' : '' }}>Donna</option>
                                                        <option value="other" {{ vendor.genderType == 'other' ? 'selected' : '' }}>Altro</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Data di nascita:</label>
                                            <div class="col-lg-3">
                                                <input type="text" name="birthDay" class="form-control maxlength" maxlength="2" placeholder="Giorno" value="{{ vendor.birthDate | date('dd') }}">
                                            </div>
                                            <div class="col-lg-3">
                                                <div class="form-group">
                                                    {% set month = (vendor.birthDate | date('M')) + '' %}
                                                    <select class="form-control select" name="birthMonth" value='{{ month }}'>
                                                        <option value="1" {{ month == '1' ? 'selected' : '' }}>Gennaio</option>
                                                        <option value="2" {{ month == '2' ? 'selected' : '' }}>Febbraio</option>
                                                        <option value="3" {{ month == '3' ? 'selected' : '' }}>Marzo</option>
                                                        <option value="4" {{ month == '4' ? 'selected' : '' }}>Aprile</option>
                                                        <option value="5" {{ month == '5' ? 'selected' : '' }}>Maggio</option>
                                                        <option value="6" {{ month == '6' ? 'selected' : '' }}>Giugno</option>
                                                        <option value="7" {{ month == '7' ? 'selected' : '' }}>Luglio</option>
                                                        <option value="8" {{ month == '8' ? 'selected' : '' }}>Agosto</option>
                                                        <option value="9" {{ month == '9' ? 'selected' : '' }}>Settembre</option>
                                                        <option value="10" {{ month == '10' ? 'selected' : '' }}>Ottobre</option>
                                                        <option value="11" {{ month == '11' ? 'selected' : '' }}>Novembre</option>
                                                        <option value="12" {{ month == '12' ? 'selected' : '' }}>Dicembre</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-lg-3">
                                                <input type="text" name="birthYear" class="form-control maxlength" maxlength="4" minlength="4" placeholder="Anno" value="{{ vendor.birthDate | date('yyyy') }}">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Utente dal:</label>
                                            <div class="col-lg-3">
                                                <input type="text" name="sinceDay" class="form-control maxlength" maxlength="2" placeholder="Giorno" value="{{ vendor.sinceDate | date('dd') }}">
                                            </div>
                                            <div class="col-lg-3">
                                                <div class="form-group">
                                                    {% set sinceMonth = (vendor.sinceDate | date('M')) + '' %}
                                                    <select class="form-control select" name="sinceMonth" value='{{ sinceMonth }}'>
                                                        <option value="1" {{ sinceMonth == '1' ? 'selected' : '' }}>Gennaio</option>
                                                        <option value="2" {{ sinceMonth == '2' ? 'selected' : '' }}>Febbraio</option>
                                                        <option value="3" {{ sinceMonth == '3' ? 'selected' : '' }}>Marzo</option>
                                                        <option value="4" {{ sinceMonth == '4' ? 'selected' : '' }}>Aprile</option>
                                                        <option value="5" {{ sinceMonth == '5' ? 'selected' : '' }}>Maggio</option>
                                                        <option value="6" {{ sinceMonth == '6' ? 'selected' : '' }}>Giugno</option>
                                                        <option value="7" {{ sinceMonth == '7' ? 'selected' : '' }}>Luglio</option>
                                                        <option value="8" {{ sinceMonth == '8' ? 'selected' : '' }}>Agosto</option>
                                                        <option value="9" {{ sinceMonth == '9' ? 'selected' : '' }}>Settembre</option>
                                                        <option value="10" {{ sinceMonth == '10' ? 'selected' : '' }}>Ottobre</option>
                                                        <option value="11" {{ sinceMonth == '11' ? 'selected' : '' }}>Novembre</option>
                                                        <option value="12" {{ sinceMonth == '12' ? 'selected' : '' }}>Dicembre</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-lg-3">
                                                <input type="text" name="sinceYear" class="form-control maxlength" maxlength="4" minlength="4" placeholder="Anno" value="{{ vendor.sinceDate | date('yyyy') }}">
                                            </div>
                                        </div>
                                        <legend class="text-bold"><i class="icon-address-book"></i> CONTATTI</legend>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Telefono:</label>
                                            <div class="col-lg-9">
                                                <input type="tel" name="phoneNumber" class="form-control maxlength" maxlength="13" placeholder="Telefono o cellulare" value="{{ vendor.phoneNumber }}" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Telefono secondario:</label>
                                            <div class="col-lg-9">
                                                <input type="tel" name="phoneNumberAdditional" class="form-control maxlength" maxlength="13" placeholder="Altro numero di telefono" value="{{ vendor.phoneNumberAdditional }}">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Email:</label>
                                            <div class="col-lg-9">
                                                <input type="email" name="email" class="form-control maxlength" maxlength="100" placeholder="Email" value="{{ vendor.email }}" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Email secondaria:</label>
                                            <div class="col-lg-9">
                                                <input type="email" name="emailAdditional" class="form-control maxlength" maxlength="100" placeholder="Email secondaria" value="{{ vendor.emailAdditional }}">
                                            </div>
                                        </div>
                                        <legend class="text-bold"><i class="icon-location4"></i> INDIRIZZO PRINCIPALE/CONSEGNA</legend>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Indirizzo:</label>
                                            <div class="col-lg-9">
                                                <input type="text" name="address" class="form-control maxlength" maxlength="100" placeholder="Indirizzo" value="{{ vendor.address }}" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Città:</label>
                                            <div class="col-lg-9">
                                                <input type="text" name="city" class="form-control maxlength" maxlength="100" placeholder="Città" value="{{ vendor.city }}" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">CAP:</label>
                                            <div class="col-lg-9">
                                                <input type="text" name="postalCode" class="form-control maxlength" maxlength="10" placeholder="CAP" value="{{ vendor.postalCode }}" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Provincia:</label>
                                            <div class="col-lg-9">
                                                <select class="form-control select-search" name="provinceCode" required>
                                                    <option value="">-</option>
                                                    {% for item in lookup("province") %}
                                                        <option value="{{ item.code }}" {{ item.code == vendor.provinceCode ? 'selected' : ''}}>{{ item.description }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                        <legend class="text-bold">
                                            <i class="icon-location4"></i>
                                            DATI DI FATTURAZIONE
                                            <a class="control-arrow" data-toggle="collapse" data-target="#address2">
                                                <i class="icon-circle-down2"></i>
                                            </a>
                                        </legend>
                                        <div class="collapse" id="address2">
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Ragione Sociale:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="invoiceFullname" class="form-control maxlength" maxlength="100" placeholder="Ragione Sociale" value="{{ vendor.invoiceFullname }}">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Stato:</label>
                                                <div class="col-lg-3">
                                                    <select class="form-control select" name="invoiceCountryCode">
                                                        <option value="">-</option>
                                                        {% for item in lookup("country") %}
                                                            <option value="{{ item.code }}" {{ item.code == vendor.invoiceCountryCode ? 'selected' : ''}}>{{ item.description }}</option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Indirizzo:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="invoiceAddress" class="form-control maxlength" maxlength="100" placeholder="Indirizzo" value="{{ vendor.invoiceAddress }}">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Città:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="invoiceCity" class="form-control maxlength" maxlength="100" placeholder="Città" value="{{ vendor.invoiceCity }}">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">CAP:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="invoicePostalCode" class="form-control maxlength" maxlength="10" placeholder="CAP" value="{{ vendor.invoicePostalCode }}">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Provincia:</label>
                                                <div class="col-lg-3">
                                                    <select class="form-control select-search" name="invoiceProvinceCode">
                                                        <option value="">-</option>
                                                        {% for item in lookup("province") %}
                                                            <option value="{{ item.code }}" {{ item.code == customer.invoiceProvinceCode ? 'selected' : ''}}>{{ item.description }}</option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Cod. Fiscale:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="tin" class="tin form-control maxlength" minlength="1" maxlength="20" placeholder="Codice Fiscale" value="{{ vendor.tin }}">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">P.IVA:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="vatNumber" class="form-control maxlength" maxlength="20" placeholder="Partita IVA" value="{{ vendor.vatNumber }}">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Codice univoco SDI:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="sdiNumber" class="form-control maxlength" maxlength="7" placeholder="Codice univoco SDI" value="{{ vendor.sdiNumber }}">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Pec:</label>
                                                <div class="col-lg-9">
                                                    <input type="email" name="pec" class="form-control maxlength" maxlength="100" placeholder="Pec" value="{{ vendor.pec }}">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">IBAN:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" id="lastname" name="bankAccount" class="form-control maxlength" maxlength="100" value="{{ vendor.bankAccount }}">
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Tassazione:</label>
                                                <div class="col-lg-9">
                                                    {% if not isVendor %}
                                                        <select class="form-control select-search" name="tax" id="tax">
                                                            <option value="">-</option>
                                                            {% for item in lookup("tax") %}
                                                                <option value="{{ item.code }}" {{ item.code == vendor.tax ? 'selected' : ''}}>{{ item.name }}</option>
                                                            {% endfor %}
                                                        </select>
                                                    {% else %}
                                                        <input type="text" name="tax" class="form-control" value="{{ decode('tax', vendor.tax) }}" readonly>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
<!--                                        <legend class="text-semibold">
                                            <i class="icon-pie-chart2"></i>
                                            GESTIONE ECONOMICA
                                            <a class="control-arrow" data-toggle="collapse" data-target="#economic">
                                                <i class="icon-circle-down2"></i>
                                            </a>
                                        </legend>
                                        <div class="collapse" id="economic">
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Estratto conto:</label>
                                                <div class="col-lg-9">
                                                    <div class="checkbox checkbox-switchery switchery-sm">
                                                        <label>
                                                            <input type="checkbox" class="switchery" {{ vendor.statement ? 'checked' : '' }} name="statement">
                                                        </label>
                                                    </div>
                                                </div>                                    
                                            </div>                                            
                                        </div>-->
                                        <legend class="text-bold">
                                            <i class="icon-key"></i>
                                            CREDENZIALI
                                            <a class="control-arrow" data-toggle="collapse" data-target="#credential">
                                                <i class="icon-circle-down2"></i>
                                            </a>
                                        </legend>
                                        <div class="collapse" id="credential">                                            
                                            {% if (user.profileType == 'system') or (user.profileType == 'admin') %}
                                                <div class="form-group">
                                                    <label class="col-lg-3 control-label">Profilo:</label>
                                                    <div class="col-lg-9">
                                                        <div class="form-group">
                                                            <select class="select" name="profileType" required>                                            
                                                                <option value="vendor" {{ userVendor.profileType == 'vendor' ? 'selected' : '' }}>Utente</option>                                            
                                                                <option value="admin" {{ userVendor.profileType == 'admin' ? 'selected' : '' }}>Amministratore</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            {% else %}
                                                <div class="form-group">
                                                    <label class="col-lg-3 control-label">Profilo:</label>
                                                    <div class="col-lg-9">
                                                        <input type="email" id="profileType" name="profileType" class="form-control" value="{{ userVendor.profileType == 'vendor' ? 'Agente' : 'Amministratore' }}" required disabled>
                                                    </div>
                                                </div>
                                            {% endif %}
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Email:</label>
                                                <div class="col-lg-9">
                                                    <input type="email" id="username" name="username" class="form-control" value="{{ userVendor.username }}" required disabled>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Password:</label>
                                                <div class="col-lg-9">
                                                    <input type="password" name="password" class="form-control" placeholder="Password" value="{{ userVendor.password }}" required disabled>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Conferma password:</label>
                                                <div class="col-lg-9">
                                                    <input type="password" name="password-confirm" class="form-control" placeholder="Conferma password" value="{{ userVendor.password }}" required data-parsley-equalto="#password" disabled>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="panel-footer has-visible-elements">
                                        <div class="heading-elements visible-elements">
                                            <span class="heading-text text-semibold">Azioni:</span>
                                            <div class="pull-right">
                                                <button type="button" class="btn heading-btn btn-default btn-cancel">Annulla</button>
                                                <button id="deleteVendor" type="button" class="btn heading-btn btn-danger">Elimina</button>
                                                <button type="submit" class="btn heading-btn btn-primary">Aggiorna agente <i class="icon-arrow-right14"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- /profile info -->
                            </form>

                        </div>
                        
                        <div class="tab-pane fade" id="notes">
                            <div class="panel panel-white">
                                <div class="panel-heading">
                                    <h5 class="panel-title text-bold">Note</h5>
                                </div>
                                <div class="panel-body">
                                    <form id="form-edit-note" class="form-horizontal form-validate-jquery" method="post" action="{{ paths('VENDORS_EDIT_SAVE') }}?oid={{ vendor.id }}&isNote=true">
                                        <div class="form-group">
                                            <textarea cols="18" rows="18" name="note" class="wysihtml5 wysihtml5-min form-control" placeholder="Inserisci delle note ...">
                                                {{ vendor.note }}
                                            </textarea>
                                        </div>

                                        <div class="text-right">
                                            <button type="button" class="btn btn-cancel">Annulla</button>
                                            <button type="submit" class="btn btn-primary">Salva note <i class="icon-arrow-right14"></i></button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                        </div>

                    </div>
                </div>
            </div>

        </div>
        <!-- /user profile -->

    </div>
    <!-- END MAIN CONTENT -->

</div>
<!-- END PAGE CONTENT -->

{% endblock %}
