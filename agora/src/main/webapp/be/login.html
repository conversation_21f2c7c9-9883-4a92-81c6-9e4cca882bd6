<!DOCTYPE html>
<html lang="it">
    <head>
        <!-- Links -->
        {% include "be/include/snippets/head.html" %}
        {% block extrahead %}
        <title>Login</title>        
        <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
        <script src="{{ contextPath }}/be/js/pages/login.js?{{ buildNumber }}"></script>
        <!-- Custom CSS -->        
        <link href="{{ contextPath }}/be/css/custom.css?{{ buildNumber }}" rel="stylesheet" type="text/css">        
        {% endblock %}
    </head>
    <body class="navbar-bottom login-container">
        <!-- Main navbar -->
        <div class="navbar navbar-inverse bg-agora has-shadow">
            <div class="navbar-header">
                <a class="navbar-brand" href="">
                    <img src="{{ contextPath }}/be/imgs/logo-w.svg">
                </a>
                <ul class="nav navbar-nav pull-right visible-xs-block">
                    <li><a data-toggle="collapse" data-target="#navbar-mobile"><i class="icon-grid3"></i></a></li>
                </ul>
            </div>
            <div class="navbar-collapse collapse" id="navbar-mobile">
                <ul class="nav navbar-nav">
                    <li><a href="mailto:<EMAIL>">Assistenza</a></li>
                </ul>
            </div>
        </div>
        <!-- /main navbar -->
        <!-- Page container -->
        <div class="page-container">
            <!-- PAGE CONTENT -->
            <div class="page-content">
                <!-- MAIN CONTENT -->
                <div class="content-wrapper">
                    <!-- Simple login form -->
                    <form method="post" action="{{ paths('LOGIN_DO') }}" class="login-form">
                        <div class="panel panel-body">
                            <div class="text-center">
                                <div class="icon-object border-agora text-agora"><i class="icon-key"></i></div>
                                <h5 class="content-group">Login <small class="display-block">Inserisci le credenziali</small></h5>
                            </div>
                            {% if error %}
                                <div class="alert bg-danger-400 alert-styled-left">
                                    <button type="button" class="close" data-dismiss="alert"><span>×</span><span class="sr-only">Close</span></button>
                                    Credenziali errate
                                </div>
                            {% endif %}
                            <div class="form-group has-feedback has-feedback-left">
                                <input type="email" id="email" name="email" class="form-control" required>
                                <div class="form-control-feedback">
                                    <i class="icon-user text-muted"></i>
                                </div>
                            </div>
                            <div class="form-group has-feedback has-feedback-left">
                                <input type="password" id="password" name="password" class="form-control" required>
                                <div class="form-control-feedback">
                                    <i class="icon-lock2 text-muted"></i>
                                </div>
                            </div>
                            <div class="form-group login-options">
                                <div class="row">
                                    <div class="col-xs-4 col-sm-4 control-label">
                                        <div class="checkbox">
                                            <label>
                                                <input id="remember" class="styled" name="remember" type="checkbox" checked="checked">
                                                Ricordami
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-xs-8 col-sm-8 text-right">
                                        <a href="{{ paths('FORGOT') }}" class="checkbox">Password dimenticata?</a>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary btn-block">ACCEDI <i class="icon-circle-right2 position-right"></i></button>
                            </div>
                        </div>
                    </form>
                    <!-- /simple login form -->
                </div>
                <!-- END MAIN CONTENT -->
            </div>
            <!-- END PAGE CONTENT -->
        </div>
        <!-- /page container -->
        <!-- Footer -->
        {% include "be/include/snippets/footer.html" %}
        <!-- /footer -->
    </body>
</html>