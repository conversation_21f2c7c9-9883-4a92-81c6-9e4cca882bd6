{% extends "be/include/base.html" %}

{% block extrahead %}
<title>Analisi</title>

<!-- SCRIPTS -->
<link href="https://siteria.it/libs/tabulator/4.6.2/dist/css/tabulator.min.css" rel="stylesheet">
<script type="text/javascript" src="https://siteria.it/libs/moment/2.20.1/min/moment.min.js"></script>
<script type="text/javascript" src="https://siteria.it/libs/sheetjs/0.15.5/dist/xlsx.full.min.js"></script>
<script type="text/javascript" src="https://siteria.it/libs/tabulator/4.6.2/dist/js/tabulator.min.js"></script>

<script src="{{ contextPath }}/be/js/pages/analysis-data.js?{{ buildNumber }}"></script>

{% endblock %}

{% block content %}

<!-- <PERSON>GE CONTENT -->
<div class="page-content">

    <div class="panel panel-white">
        <div class="panel-heading">
            
            <h5 class="panel-title text-bold">Prodotti ordinati</h5>
            <div class="heading-elements">
                <div class="form-group">
                    <div class="btn-group heading-btn mr-10">
                        <a href="{{ paths('ANALYSIS_DATA') }}" class="btn btn-primary legitRipple">Analisi dati</a>
                        <button class="btn btn-primary dropdown-toggle legitRipple" data-toggle="dropdown" aria-expanded="false"><span class="caret"></span></button>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li><a href="{{ paths('ANALYSIS_DATA') }}">Analisi dati</a></li>
                            <li><a href="{{ paths('ANALYSIS_PIVOT') }}">Analisi pivot</a></li>
                        </ul>
                    </div>
                </div>
            </div>        
        </div>

        <div class="panel-body">
            {% if orderList is not empty %}
                <div>
                    <a id="download-csv" class="btn type--uppercase">
                        <span class="btn__text">
                            Scarica CSV
                        </span>
                    </a>
                    <a id="download-xlsx" class="btn type--uppercase">
                        <span class="btn__text">
                            Scarica XLSX
                        </span>
                    </a>
                </div>                
                <table id="orders" class="mt--1" style="display: none;">
                    <thead>
                        <tr>
                            <th>Anno</th>
                            <th>Mese</th>
                            <th>Giorno</th>
                            <th>Data</th>
                            <th>Numero</th>
                            <th>Cliente</th>
                            <th>Indirizzo</th>
                            <th>Città</th>
                            <th>CAP</th>
                            <th>Provincia</th>
                            <th>Stato</th>
                            <th>E-commerce</th>
                            <th>Codice</th>
                            <th>Area</th>
                            <th>Categoria</th>
                            <th>Colore</th>
                            <th>Taglia</th>
                            <th>Tessuto</th>
                            <th>Descrizione</th>
                            <th>Prezzo unitario</th>
                            <th>Quantità</th>
                            <th>Valore totale</th>
                            <th>Totale ordine </th>
                            <th>Coupon</th>
                            <th>Totale ordine Finale</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for entry in orderList %}
                            {% for item in entry.items %}
                                <tr>
                                    <td>{{ entry.order.date | date('yyyy') }}</td>
                                    <td>{{ entry.order.date | date('MM') }}</td>
                                    <td>{{ entry.order.date | date('dd') }}</td>
                                    <td>{{ entry.order.date | date('dd/MM/yyyy') }}</td>
                                    <td>{{ entry.order.protocol }}</td>

                                    {% set fullname = '*Nome* *Cognome*' %}
                                    {% if entry.customer.fullname is not empty %}
                                        {% set fullname = entry.customer.fullname %}
                                    {% elseif (entry.customer.name is not empty) and (entry.customer.lastname is not empty) %}
                                        {% set fullname = (entry.customer.name | default('*Nome*')) + ' ' + entry.customer.lastname | default('*Cognome*') %}
                                    {% endif %}
                                    <td>{{ fullname }}</td>

                                    <td>{{ entry.customer.address }}</td>
                                    <td>{{ entry.customer.city }}</td>
                                    <td>{{ entry.customer.postalCode }}</td>
                                    <td>{{ entry.customer.provinceCode }}</td>
                                    <td>{{ entry.customer.countryCode }}</td>
                                    
                                    <td>{{ entry.order.shop ? 'Si' : 'No' }}</td>

                                    <td>{{ item.product.code }}</td>
                                    <td>{{ item.product.area }}</td>
                                    <td>{{ item.product.category }}</td>
                                    <td>{{ item.product.color }}</td>
                                    <td>{{ item.product.size }}</td>
                                    <td>{{ item.product.fabric }}</td>
                                    <td>{{ item.product.name }}</td>
                                    
                                    <td>{{ ((item.item.price | default(0)) - (item.item.discount | default(0))) | numberformat("#0.00") }}</td>
                                    <td>{{ item.item.quantity }}</td>
                                    <td>{{ ((item.item.itemPrice | default(0)) - (item.item.itemDiscount | default(0))) | numberformat("#0.00") }}</td>
                                    <td>{{ (entry.order.totalPrice | default(0)) | numberformat("#0.00")}}</td>
                                    <td>{{ entry.order.couponCode }}</td>
                                    <td>{{ (entry.order.finalPrice | default(0)) | numberformat("#0.00")}}</td>
                                </tr>
                            {% endfor %}
                        {% endfor %}
                    </tbody>
                </table>                
            {% else %}
                NON CI SONO DATI DA VISUALIZZARE
            {% endif %}

            <script>
                $(function() {
                    
                    var table = new Tabulator("#orders", {
                        pagination: 'local',
                        paginationSize: 20,
                        langs:{
                            "it":{ //Italian language definition
                                "pagination":{
                                    "first":"Inizio",
                                    "first_title":"Pagina iniziale",
                                    "last":"Fine",
                                    "last_title":"Pagina finale",
                                    "prev":"Precedente",
                                    "prev_title":"Pagina precedente",
                                    "next":"Successiva",
                                    "next_title":"Pagina successiva",
                                },
                            }
                        },
                        columns:[
                            //{title:"Anno", sorter:"number"},
                            {title:"Anno", sorter:"number"},
                            {title:"Mese", sorter:"number"},
                            {title:"Giorno", sorter:"number"},
                            {title:"Data", sorter:"date", sorterParams:{
                                    format:"DD/MM/YYYY",
                                    alignEmptyValues:"top",
                            }},
                            {title:"Numero"},
                            {title:"Cliente"},
                            {title:"Indirizzo"},
                            {title:"Città"},
                            {title:"CAP"},
                            {title:"Provincia"},
                            {title:"Stato"},
                            {title:"E-commerce"},
                            {title:"Codice"},
                            {title:"Area"},
                            {title:"Categoria"},
                            {title:"Colore"},
                            {title:"Taglia"},
                            {title:"Tessuto"},
                            {title:"Descrizione"},
                            {title:"Prezzo unitario", sorter:"number"},
                            {title:"Quantità", sorter:"number"},
                            {title:"Valore totale", sorter:"number"},
                        ],
                    });
                    table.setLocale("it");
                    
                    //trigger download of orders.csv file
                    document.getElementById("download-csv").addEventListener("click", function(){
                        table.download("csv", "orders.csv", {delimiter:";", bom:true});
                    });
                    
                    //trigger download of orders.xlsx file
                    document.getElementById("download-xlsx").addEventListener("click", function(){
                        table.download("xlsx", "orders.xlsx", {sheetName:"Attività"});
                    });
                    
                });

            </script>
            
        </div>
    </div>

</div>
<!-- END PAGE CONTENT -->


{% endblock %}