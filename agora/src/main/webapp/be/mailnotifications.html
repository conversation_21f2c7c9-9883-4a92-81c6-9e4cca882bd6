{% extends "be/include/base.html" %}
{#{% set sidebarMobileVisible = 'true' %}#}
{% set pageActive = 'mailnotifications' %}
{% block extrahead %}
    <title>Notifiche mail</title>

    <!-- SCRIPTS -->
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment_locales.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/datatables.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/jszip/jszip.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>    
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/mailnotifications.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}
<!-- actions -->
<a id="mailnotificationStatusUri" style="display: none" href="{{ paths('MAILNOTIFICATION_STATUS') }}" rel="nofollow"></a>

<!-- js deposit -->
<div id="startDate" style="display: none">{{ startDate | date("yyyy-MM-dd") }}</div>
<div id="endDate" style="display: none">{{ endDate | date("yyyy-MM-dd") }}</div>
<div id="mailnotificationAlignmentProcessing" style="display: none">{{ mailnotification.status == 'processing' }}</div>
<div id="mailnotificationAlignmentUser" style="display: none">{{ decode('user', mailnotification.userId) }}</div>

<!-- PAGE CONTENT -->
<div class="page-content">    
    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        {% if success %}
            <div class="row">
                <div class="col-md-12">
                    <div class="alert alert-success">
                        <a href="{{ paths('MAILNOTIFICATIONS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                        <span>Dati memorizzati correttamente</span>
                    </div>
                </div>
            </div>
        {% elseif error %}
            <div class="row">
                <div class="col-md-12">
                    <div class="alert alert-danger">
                        <a href="{{ paths('MAILNOTIFICATIONS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                        <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Control position -->
        <div class="panel panel-white">
            <div class="panel-heading">
                <h5 class="panel-title text-bold">Notifiche email</h5>
                <small id="progress-label" style="display: none;">Elaborazione in corso: <div id="progress-counter"></div></small>
                <div class="heading-elements">                    
                    <a href="{{ paths('MAILNOTIFICATIONS_DATA_SEND') }}?frequency=day" class="btn heading-btn bg-dark-blue mailnotification-send {{ ((not firm.mailNotificationEnabled) or (mailnotification.status == 'processing')) ? 'disabled' : '' }}"><i class="icon-mail5 position-left"></i>INVIA NOTIFICHE (DAY)</a>
                    <a href="{{ paths('MAILNOTIFICATIONS_DATA_SEND') }}?frequency=week" class="btn heading-btn bg-dark-blue mailnotification-send {{ ((not firm.mailNotificationEnabled) or (mailnotification.status == 'processing')) ? 'disabled' : '' }}"><i class="icon-mail5 position-left"></i>INVIA NOTIFICHE (WEEK)</a>
                    <button type="button" class="btn heading-btn btn-default daterange-predefined">
                        <i class="icon-calendar22 position-left"></i>
                        <span></span>
                        <b class="caret"></b>
                    </button>                    
                </div>
            </div>

            <table class="table datatable-responsive-control-right">
                <thead>
                    <tr>
                        <th>Data</th>
                        <th>Nome</th>
                        <th>Operazione</th>
                        <th>Durata</th>
                        <th>Stato</th>
                        <th>Elementi</th>
                        <th>KO</th>
                        <th>Azioni</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {% if mailnotificationList is not empty %}
                        {% for entry in mailnotificationList %}
                        
                            {% set error = '' %}
                            {% if entry.errorCount > 0 %}
                                {% set error = 'text-danger' %}
                            {% endif %}
                            
                            <tr>
                                <td>
                                    <div class="{{ error }}">{{ entry.creation | date('yyyy-MM-dd HH:mm:ss:SSS') }}</div>
                                </td>
                                <td>
                                    <div class="{{ error }}">{{ decode('user', entry.userId) }}</div>
                                </td>
                                <td>
                                    {% set operationDescription = '' %}
                                    {% if entry.operation == 'sendnotification' %}
                                        {% set operationDescription = 'Invio notifiche' %}
                                    {% else %}
                                        {% set operationDescription = '?!?' %}
                                    {% endif %}
                                    <div class="{{ error }}">{{ operationDescription }}</div>
                                </td>
                                <td>
                                    <div class="{{ error }}">{{ (entry.millies | default(0) / 1000.0) | numberformat('#') }}s</div>
                                </td>
                                <td>
                                    {% set statusDescription = '' %}
                                    {% if entry.status == 'processing' %}
                                        {% set statusDescription = 'In elaborazione' %}
                                    {% elseif entry.status == 'done' %}
                                        {% set statusDescription = 'Completato' %}
                                    {% elseif entry.status == 'error' %}
                                        {% set statusDescription = 'In errore' %}
                                    {% elseif entry.status == 'abort' %}
                                        {% set statusDescription = 'Interrotto' %}
                                    {% else %}
                                        {% set statusDescription = '?!?' %}
                                    {% endif %}
                                    <div class="{{ error }}">{{ statusDescription }}</div>
                                </td>
                                <td>
                                    <div class="{{ error }}">{{ entry.count | default(0) | numberformat('#') }}</div>
                                </td>
                                <td>
                                    <div class="{{ error }}">{{ entry.errorCount | default(0) | numberformat('#') }}</div>
                                </td>
                                <td>
                                    <a data-popup="tooltip" title="Dettaglio" href="{{ paths('MAILNOTIFICATION_VIEW') }}?mailnotificationId={{ entry.id }}" class="text-bold"><i class="icon-search4"></i></a>
                                </td>
                                <td></td>
                            </tr>
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
        <!-- /control position -->

    </div>
    <!-- END MAIN CONTENT -->

</div>
<!-- END PAGE CONTENT -->

{% endblock %}