{% extends "be/include/base.html" %}
{% set pageActive = 'eventreports' %}

{% block extrahead %}
    <title>Segnalazioni eventi</title>

    <!-- SCRIPTS -->
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment_locales.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/datatables.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/jszip/jszip.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/eventreports.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}

<!-- js deposit -->
<div id="startDate" style="display: none">{{ startDate | date("yyyy-MM-dd") }}</div>
<div id="endDate" style="display: none">{{ endDate | date("yyyy-MM-dd") }}</div>


<!-- EVENT CONTENT -->
<div class="page-content">

    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('BE_EVENT_REPORTS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('BE_EVENT_REPORTS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Control position -->
        <div class="panel panel-white">
            <div class="panel-heading has-visible-elements">
                <h5 class="panel-title text-bold">Ricerche</h5>
                <div class="heading-elements visible-elements">
                    <button type="button" class="btn heading-btn btn-default daterange-predefined">
                        <i class="icon-calendar22 position-left"></i>
                        <span></span>
                        <b class="caret"></b>
                    </button>
                </div>
            </div>
            {% if (user.profileType == 'system') or (user.profileType == 'admin') %}
                <input type="hidden" id="isHead" value="true">
            {% else %}
                <input type="hidden" id="isHead" value="false">
            {% endif %}
            <table class="table datatable-responsive-control-right">
                <thead>
                    <tr>
                        <th>Evento</th>
                        <th>Utente</th>
                        <th>Motivo</th>
                        <th>Messaggio</th>
                        <th>Data</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {% if eventreportList is not empty %}
                        {% for entry in eventreportList %}
                            {% set userRequest = get('user', entry.userId) %}
                            {% set eventRequest = get('event', entry.eventId) %}
                        
                            <tr>
                                <td>
                                    <div><a href="{{ paths('EVENT_BASE') }}/{{ eventRequest.identifier }}" target="_blank">{{ eventRequest.name }}</a></div>
                                </td>
                                <td>
                                    <div>{{ userRequest.username }}</div>
                                </td>
                                <td>
                                    <div>{{ entry.why }}</div>
                                </td>
                                <td>
                                    <div>{{ entry.message }}</div>
                                </td>
                                <td>
                                    <div>{{ entry.creation | date('yyyy-MM-dd HH:mm:ss:SSS') }}</div>
                                </td>
                                <td></td>
                            </tr>
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
        <!-- /control position -->

    </div>
    <!-- END MAIN CONTENT -->

</div>
<!-- END EVENT CONTENT -->

{% endblock %}