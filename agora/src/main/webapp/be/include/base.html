<!DOCTYPE html>
<html lang="it">

    <head>

        <!-- Links -->
        {% include "be/include/snippets/head.html" %}

        <!-- Extra head -->
        {% block extrahead %}{% endblock %}

        <!-- Custom CSS -->
        <link href="{{ contextPath }}/be/css/custom.css?{{ buildNumber }}" rel="stylesheet" type="text/css">
    </head>

    <body class="navbar-bottom {{ sidebarDefaultVisible == 'true' ? '' : 'sidebar-main-hidden'}}">

        <!-- Header -->
        <div class="page-header page-header-inverse bg-primary">
            <!-- Main navbar -->
            <div class="navbar navbar-inverse navbar-transparent">
                <div class="navbar-header">
                    <a class="navbar-brand" href="{{ paths('DASHBOARD') }}"><img src="{{ contextPath }}/be/imgs/logo-w.svg" alt="Logo"></a>
                    <ul class="nav navbar-nav pull-right visible-xs-block">
                        <li><a data-toggle="collapse" data-target="#navbar-mobile"><i class="icon-user"></i></a></li>
                        {% if sidebarMobileVisible == 'true' %}
                            <li><a class="sidebar-mobile-main-toggle legitRipple"><i class="icon-indent-increase"></i></a></li>
                        {% endif %}
                    </ul>
                </div>
                <div class="navbar-collapse collapse" id="navbar-mobile">
                    {% if user is not empty %}
                        {% set profileDescription = user.profileType %}
                        {% if user.profileType == 'system' %}
                            {% set profileDescription = 'sistema' %}
                        {% elseif user.profileType == 'admin' %}
                            {% set profileDescription = 'amministratore' %}
                        {% elseif user.profileType == 'head' %}
                            {% set profileDescription = 'sede' %}
                        {% elseif user.profileType == 'vendor' %}
                            {% set profileDescription = 'agente' %}
                        {% else %}
                            {# ...keep default #}
                        {% endif %}
                    {% endif %}

                    {% if user is not empty %}
                        <div class="navbar-right">
                            <ul class="nav navbar-nav">
                                <li class="dropdown dropdown-user">
                                    <a class="dropdown-toggle" data-toggle="dropdown">
                                        {% if user.imageId is not empty %}
                                            <img src="{{ paths('IMAGE') }}?oid={{ user.imageId }}" alt="">
                                        {% else %}                                            
                                            <img src="https://ui-avatars.com/api/?name={{ user.name }}+{{ user.lastname }}&size=256">
                                        {% endif %}
                                        <span>{{ user.name }}</span>
                                        <i class="caret"></i>
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-right">
                                        <li><a href="{{ paths('PROFILE') }}"><i class="icon-user"></i> Il mio profilo</a></li>
                                        {% if (user.profileType == 'system') or (user.profileType == 'admin') %}
                                            <li><a href="{{ paths('FIRM') }}"><i class="icon-gear"></i> Conf. ambiente</a></li>
                                            <li><a href="{{ paths('SMTP') }}"><i class="icon-mail5"></i> Conf. posta</a></li>
                                        {% endif %}
                                        <li class="divider"></li>
                                        <li><a href="{{ paths('LOGOUT_DO') }}"><i class="icon-switch2"></i> Logout</a></li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    {% endif %}
                </div>
            </div>
            <!-- /main navbar -->

            <!-- Second navbar -->
            <div class="navbar navbar-inverse navbar-transparent" id="navbar-second">
                <ul class="nav navbar-nav visible-xs-block">
                    <li><a class="text-center collapsed" data-toggle="collapse" data-target="#navbar-second-toggle"><i class="icon-paragraph-justify3"></i></a></li>
                </ul>
                <div class="navbar-collapse collapse" id="navbar-second-toggle">
                    {% if user is not empty %}
                        <ul class="nav navbar-nav navbar-nav-material">
                            <li class="{{ pageActive == 'dashboard' ? 'active' : ''}}">
                                <a href="{{ paths('DASHBOARD') }}">Dashboard</a>
                            </li>                                                                                    
                            <li class="{{ pageActive == 'customers' ? 'active' : ''}}">
                                <a href="{{ paths('CUSTOMERS') }}">Clienti</a>
                            </li>                                                                                    
                            <li class="{{ pageActive == 'pages' ? 'active' : ''}}">
                                <a href="{{ paths('BE_PAGES') }}">Pagine</a>
                            </li>
                            <li class="{{ pageActive == 'events' ? 'active' : ''}}">
                                <a href="{{ paths('BE_EVENTS') }}">Eventi</a>
                            </li>
                            <li class="dropdown {{ (pageActive == 'sponsor-events' or pageActive == 'sponsor-pages') ? 'active' : ''}}">
                                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                    SPONSORIZZAZIONI <span class="caret"></span>
                                </a>
                                <ul class="dropdown-menu width-200">
                                    <li>
                                        <a href="{{ paths('SPONSOR_EVENT_COLLECTION') }}"><i class="icon-calendar"></i> Eventi Sponsorizzati</a>
                                    </li>
                                    <li>
                                        <a href="{{ paths('SPONSOR_PAGE_COLLECTION') }}"><i class="icon-magazine"></i> Pagine Sponsorizzate</a>
                                    </li>
                                </ul>
                            </li>
                            <li class="{{ pageActive == 'searches' ? 'active' : ''}}">
                                <a href="{{ paths('BE_SEARCHES') }}">Ricerche</a>
                            </li>
                            <li class="{{ pageActive == 'eventrequests' ? 'active' : ''}}">
                                <a href="{{ paths('BE_EVENT_REQUESTS') }}">Richieste eventi</a>
                            </li> 
                            <li class="{{ pageActive == 'pagereports' ? 'active' : ''}}">
                                <a href="{{ paths('BE_PAGE_REPORTS') }}">Segnalazioni pagine</a>
                            </li> 
                            <li class="{{ pageActive == 'pageclaims' ? 'active' : ''}}">
                                <a href="{{ paths('BE_PAGE_CLAIMS') }}">Rivendica pagine</a>
                            </li> 
                            <li class="{{ pageActive == 'eventreports' ? 'active' : ''}}">
                                <a href="{{ paths('BE_EVENT_REPORTS') }}">Segnalazioni eventi</a>
                            </li> 
                            <li class="{{ pageActive == 'mailnotifications' ? 'active' : ''}}">
                                <a href="{{ paths('MAILNOTIFICATIONS') }}">Notifiche mail</a>
                            </li> 
                            <li class="dropdown">
                                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                    Upload <span class="caret"></span>
                                </a>
                                <ul class="dropdown-menu width-200">                                   
                                    <li>
                                        <a href="{{ paths('PAGES_UPLOAD') }}"><i class="icon-magazine"></i> Upload pagine</a>
                                    </li>
                                    <li>
                                        <a href="{{ paths('PAGES_UPLOAD_FOTO') }}"><i class="icon-magazine"></i> Upload pagine (foto)</a>
                                    </li>
                                    <li>
                                        <a href="{{ paths('EVENTS_UPLOAD') }}"><i class="icon-newspaper"></i> Upload eventi</a>
                                    </li>
                                </ul>
                            </li>
                            <li class="{{ pageActive == 'translation' ? 'active' : ''}}">
                                <a href="{{ paths('LABELS') }}"><i class="icon-spell-check"></i> Testi e traduzioni</a>
                            </li>
                        </ul>
                    {% endif %}
                </div>
            </div>
            <!-- /second navbar -->

            <!-- Sidebar controls -->
            {% block sidebarcontrol %}{% endblock %}
        </div>


        <div class="page-container">

            <!-- Content -->
            {% block content %}{% endblock %}

        </div>

        <!-- Footer -->
        {% include "be/include/snippets/footer.html" %}
        <script>
            $.blockUI.defaults  = {
                message:  '<img src="{{ contextPath }}/be/imgs/loader.svg" width="64">',
                css: {
                    padding:        0,
                    margin:         0,
                    width:          '30%',
                    top:            '40%',
                    left:           '35%',
                    textAlign:      'center',
                    color:          '#0F1628',
                    border:         'none',
                    backgroundColor:'transparent',
                    cursor:         'wait'
                },
                overlayCSS:  {
                    backgroundColor: '#000',
                    opacity:         0.3,
                    cursor:          'wait'
                },
                baseZ: 1000,
                showOverlay: true
            };
        </script>
    </body>

</html>