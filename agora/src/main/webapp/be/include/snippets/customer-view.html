<div class="panel-body">
    <div class="form-group">
        <label class="col-lg-3 control-label">Foto:</label>
        <div class="col-lg-9">
            <div class="row">
                <div class="col-xs-6 col-xs-offset-3 col-sm-2 col-sm-offset-0">
                    <div class="slim"
                         data-max-file-size="5"
                         data-save-initial-image="{{ customer.imageId is not empty ? 'true' : 'false'}}"
                         data-push="false"
                         data-post="output"
                         data-label="Carica un'immagine"
                         data-label-loading=" "
                         data-ratio="1:1"
                         data-button-edit-label="Modifica"
                         data-button-remove-label="Elimina"
                         data-button-download-label="Scarica"
                         data-button-upload-label="Carica"
                         data-button-rotate-label="Ruota"
                         data-button-cancel-label="Cancella"
                         data-button-confirm-label="Conferma"
                         data-status-file-size="Il file è troppo grande, il massimo consentito è $0 MB"
                         data-status-file-type="Formato immagine non valido, formati consentiti: $0"
                         data-status-no-support="Il tuo browser non supporta la modifica dell'immagine"
                         data-status-image-too-small="Immagine troppo piccola, risoluzione minima: $0 pixel"
                         data-status-content-length="Il server non supporta file così grandi"
                         data-status-unknown-response="Errore sconosciuto, <NAME_EMAIL>"
                         data-status-upload-success="Immagine salvata">

                        {% if customer.imageId is not empty %}
                        <img src="{{ paths('IMAGE') }}?oid={{ customer.imageId }}" alt=""/>
                        {% endif %}
                        <input type="file" id="cropper" name="uploaded-files" data-show-caption="false" data-show-remove="true">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="col-lg-3 control-label">Codice:</label>
        <div class="col-lg-3">
            <input type="text" name="code" class="form-control maxlength" maxlength="50" placeholder="Codice" value="{{ customer.code }}" readonly>
        </div>
    </div>    
    <div class="form-group">
        <label class="col-lg-3 control-label">Tipo:</label>
        <div class="col-lg-9">
            <div class="form-group">
                <select class="form-control" id="genderType" name="genderType" required>
                    <option value="">-</option>
                    <option value="other" {{ customer.genderType == 'other' ? 'selected' : '' }}>Azienda</option>
                    <option value="male" {{ customer.genderType == 'male' ? 'selected' : '' }}>Uomo</option>
                    <option value="female" {{ customer.genderType == 'female' ? 'selected' : '' }}>Donna</option>
                </select>
            </div>
        </div>
    </div>                                        
    <div class="form-group">
        <label class="col-lg-3 control-label">Nome:</label>
        <div class="col-lg-9">
            <input type="text" name="name" class="form-control maxlength" maxlength="50" placeholder="Nome" value="{{ customer.name }}">
        </div>
    </div>
    <div class="form-group">
        <label class="col-lg-3 control-label">Cognome:</label>
        <div class="col-lg-9">
            <input type="text" id="lastname" name="lastname" class="form-control maxlength fullname" maxlength="100" placeholder="Cognome" value="{{ customer.lastname }}">
        </div>
    </div>        
    <div class="form-group">
        <label class="col-lg-3 control-label">Bio:</label>
        <div class="col-lg-9">
            <div class="form-group">
                <textarea rows="15" name="bio" class="summernote" placeholder="Inserisci il testo...">{{ customer.bio }}</textarea>                                        
            </div>
        </div>
    </div>                                                 
    <div class="form-group">
        <label class="col-lg-3 control-label">Email:</label>
        <div class="col-lg-9">
            <input type="email" id="email" name="email" class="form-control maxlength contacts" maxlength="100" placeholder="Email" value="{{ customer.email }}">
        </div>
    </div>                                                                        
    <div class="form-group">
        <label class="col-lg-3 control-label">Parola chiave antifrode:</label>
        <div class="col-lg-9">
            <input type="text" id="secretKey" name="secretKey" class="form-control maxlength fullname" maxlength="100" placeholder="Parola chiave antifrode" value="{{ customer.secretKey }}">
        </div>
    </div>        
</div>