{% extends "be/include/base.html" %}
{% set pageActive = 'eventrequests' %}

{% block extrahead %}
    <title><PERSON><PERSON> eventi</title>

    <!-- SCRIPTS -->
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment_locales.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/datatables.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/jszip/jszip.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/eventrequests.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}

<!-- js deposit -->
<div id="startDate" style="display: none">{{ startDate | date("yyyy-MM-dd") }}</div>
<div id="endDate" style="display: none">{{ endDate | date("yyyy-MM-dd") }}</div>


<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('BE_EVENT_REQUESTS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('BE_EVENT_REQUESTS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Control position -->
        <div class="panel panel-white">
            <div class="panel-heading has-visible-elements">
                <h5 class="panel-title text-bold">Ricerche</h5>
                <div class="heading-elements visible-elements">
                    <button type="button" class="btn heading-btn btn-default daterange-predefined">
                        <i class="icon-calendar22 position-left"></i>
                        <span></span>
                        <b class="caret"></b>
                    </button>
                </div>
            </div>
            {% if (user.profileType == 'system') or (user.profileType == 'admin') %}
                <input type="hidden" id="isHead" value="true">
            {% else %}
                <input type="hidden" id="isHead" value="false">
            {% endif %}
            <table class="table datatable-responsive-control-right">
                <thead>
                    <tr>
                        <th>Pagina</th>
                        <th>Utente</th>
                        <th>Location</th>
                        <th>Messaggio</th>
                        <th>Data</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {% if eventrequestList is not empty %}
                        {% for entry in eventrequestList %}
                            {% set userRequest = get('user', entry.userId) %}
                            {% set pageRequest = get('page', entry.pageId) %}
                        
                            <tr>
                                <td>
                                    <div><a href="{{ paths('PAGE_BASE') }}/{{ pageRequest.identifier }}" target="_blank">{{ pageRequest.name }}</a></div>
                                </td>
                                <td>
                                    <div>{{ userRequest.username }}</div>
                                </td>
                                <td>
                                    <div>{{ entry.location }}</div>
                                </td>
                                <td>
                                    <div>{{ entry.message }}</div>
                                </td>
                                <td>
                                    <div>{{ entry.creation | date('yyyy-MM-dd HH:mm:ss:SSS') }}</div>
                                </td>
                                <td></td>
                            </tr>
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
        <!-- /control position -->

    </div>
    <!-- END MAIN CONTENT -->

</div>
<!-- END PAGE CONTENT -->

{% endblock %}