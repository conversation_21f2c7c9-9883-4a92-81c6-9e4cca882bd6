{% extends "be/include/base.html" %}

{% block extrahead %}
    <title>Aree</title>

    <!-- SCRIPTS -->
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/datatables.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/jszip/jszip.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>    
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/areas.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}

<!-- data -->

<!-- js deposit -->
<div id="userProfileType" style="display: none;">{{ user.profileType }}</div>

<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- LEFT SIDEBAR -->
    <div class="sidebar sidebar-main sidebar-default sidebar-separate">
    </div>
    <!-- END LEFT SIDEBAR -->

    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        {% if success %}
            <div class="row">
                <div class="col-md-12">
                    <div class="alert alert-success">
                        <a href="{{ paths('AREAS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                        <span>Dati memorizzati correttamente</span>
                    </div>
                </div>
            </div>
        {% elseif error %}
            <div class="row">
                <div class="col-md-12">
                    <div class="alert alert-danger">
                        <a href="{{ paths('AREAS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                        <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- AREAS -->
        <div class="panel panel-white">
            <div class="panel-heading has-visible-elements">
                <h5 class="panel-title text-bold">Aree</h5>                
            </div>

            <div id="productsTable">
                <table class="table datatable-responsive-control-right">
                    <thead>
                        <tr>
                            <th>Titolo</th>
                            <th>Codice</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if areaList is not empty %}
                            {% for entry in areaList %}
                                <tr>                                    
                                    <td>
                                        <a href="{{ paths('AREA_EDIT') }}?oid={{ entry.id }}" class="text-bold">{{ entry.title }}</a>                                        
                                    </td>
                                    <td>
                                        {{ entry.code }}
                                    </td>
                                    <td></td>
                                </tr>
                            {% endfor %}
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
        <!-- /control position -->

    </div>
    <!-- END MAIN CONTENT -->

</div>
<!-- END PAGE CONTENT -->

{% endblock %}
