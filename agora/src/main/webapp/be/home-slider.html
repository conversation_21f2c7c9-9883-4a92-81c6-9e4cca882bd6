{% extends "be/include/base.html" %}

{% set currentPage = 'HOME_SLIDER' %}

{% block extrahead %}
<title>Lista HOME SLIDER</title>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/datatables.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/home-slider.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}

<!-- Page content -->
<div class="page-content">

    <!-- Main content -->
    <div class="content-wrapper">
        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('HOME_SLIDERS') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Informazioni salvate correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('HOME_SLIDERS') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Hai bisogno di aiuto? <a href="mailto:<EMAIL>">Contatta il servizio clienti</a></span>
                </div>
            </div>
        </div>
        {% endif %}
        

        <!-- State saving -->
        <div class="panel panel-white">
            <div class="panel-heading">
                <h5 class="panel-title text-bold">Lista HOME SLIDER</h5>
                <div class="heading-elements">
                    <div class="form-group">
                        <a href="{{ paths('HOME_SLIDER_EDIT') }}" class="btn btn-primary legitRipple"><i class="icon-plus-circle2 position-left"></i>Aggiungi home slider</a>                        
                    </div>
                </div>
            </div>

            <table class="table datatable-home-sliders">
                <thead>
                    <tr>
                        <th>Nome</th>
                        <th>Creato il</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {% if homeSliderList is not empty %}
                        {% for entry in homeSliderList %}
                            <tr>
                                <td>
                                    <a href="{{ paths('HOME_SLIDER_EDIT') }}?homeSliderId={{ entry.id }}" class="text-bold">{{ entry.title }} </a>
                                </td>
                                <td>
                                    <span sortable-value="{{ entry.creation | date('yyyyMMddHHmm') }}">{{ entry.creation | date('dd MMMM yyyy HH:mm') }}</span>
                                </td>
                                <td></td>
                            </tr>
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
        <!-- /state saving -->

    </div>
    <!-- /main content -->
</div>
<!-- /page content -->
{% endblock %}