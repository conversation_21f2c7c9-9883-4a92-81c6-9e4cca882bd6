{% extends "be/include/base.html" %}

{% block extrahead %}
<title>Nuovo agente / amministratore</title>

<!-- CSS -->
<link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet" type="text/css" media="all">

<!-- SCRIPTS -->
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/switchery.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/autosize.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/formatter.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/typeahead.bundle.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/handlebars.min.js"></script>
<script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/localization/messages_it.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/vendors-add.js?{{ buildNumber }}"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>

{% endblock %}

{% block content %}

<!-- data api -->
<a id="dataCitiesUri" style="display: none" href="{{ paths('DATA_CITIES') }}" rel="nofollow"></a>

<!-- actions -->
<a id="vendorsUri" style="display: none" href="{{ paths('VENDORS') }}" rel="nofollow"></a>

<!-- PAGE CONTENT -->
<div class="page-content">
    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-success">
                    <a href="{{ paths('VENDORS_ADD') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-danger">
                    <a href="{{ paths('VENDORS_ADD') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Horizontal form options -->
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <!-- Basic layout-->
                <form id="form-add-vendor" class="form-horizontal form-validate-jquery" method="post" action="{{ paths('VENDORS_ADD_SAVE') }}">
                    <div class="panel panel-white">
                        <div class="panel-heading">
                            <h5 class="panel-title text-bold">Nuovo agente / amministratore</h5>                            
                            <div class="heading-elements">                                    
                                <button type="submit" class="btn heading-btn btn-primary">Salva agente / amministratore <i class="icon-arrow-right14"></i></button>
                            </div>
                        </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Foto profilo:</label>
                                <div class="col-lg-9">
                                    <div class="row">
                                        <div class="col-xs-6 col-xs-offset-3 col-sm-4 col-sm-offset-4 col-md-4 col-md-offset-4">
                                            <div class="slim"
                                                data-max-file-size="5"
                                                data-save-initial-image="false"
                                                data-push="false"
                                                data-post="output"
                                                data-label="Carica un'immagine"
                                                data-label-loading=" "
                                                data-ratio="1:1"
                                                data-button-edit-label="Modifica"
                                                data-button-remove-label="Elimina"
                                                data-button-download-label="Scarica"
                                                data-button-upload-label="Carica"
                                                data-button-rotate-label="Ruota"
                                                data-button-cancel-label="Cancella"
                                                data-button-confirm-label="Conferma"
                                                data-status-file-size="Il file è troppo grande, il massimo consentito è $0 MB"
                                                data-status-file-type="Formato immagine non valido, formati consentiti: $0"
                                                data-status-no-support="Il tuo browser non supporta la modifica dell'immagine"
                                                data-status-image-too-small="Immagine troppo piccola, risoluzione minima: $0 pixel"
                                                data-status-content-length="Il server non supporta file così grandi"
                                                data-status-unknown-response="Errore sconosciuto, <NAME_EMAIL>"
                                                data-status-upload-success="Immagine salvata">

                                                <input type="file" name="uploaded-files" multiple="multiple" data-show-caption="false" data-show-remove="true">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Nome:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="name" class="form-control maxlength" maxlength="50" placeholder="Nome">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Cognome:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="lastname" class="form-control maxlength" maxlength="100" placeholder="Cognome" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Ragione Sociale:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="fullname" class="form-control maxlength" maxlength="100" placeholder="Ragione Sociale">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Sesso:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="select" name="genderType" required>
                                            <option value="male">Uomo</option>
                                            <option value="female">Donna</option>
                                            <option value="other">Altro</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Data di nascita:</label>
                                <div class="col-lg-3">
                                    <input type="text" name="birthDay" class="form-control maxlength" maxlength="2" placeholder="Giorno">
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <select class="select" name="birthMonth">
                                            <option value="1">Gennaio</option>
                                            <option value="2">Febbraio</option>
                                            <option value="3">Marzo</option>
                                            <option value="4">Aprile</option>
                                            <option value="5">Maggio</option>
                                            <option value="6">Giugno</option>
                                            <option value="7">Luglio</option>
                                            <option value="8">Agosto</option>
                                            <option value="9">Settembre</option>
                                            <option value="10">Ottobre</option>
                                            <option value="11">Novembre</option>
                                            <option value="12">Dicembre</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <input type="text" name="birthYear" class="form-control maxlength" maxlength="4" minlength="4" placeholder="Anno">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Utente dal:</label>
                                <div class="col-lg-3">
                                    <input type="text" name="sinceDay" class="form-control maxlength" maxlength="2" placeholder="Giorno">
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <select class="select"  name="sinceMonth">
                                            <option value="1">Gennaio</option>
                                            <option value="2">Febbraio</option>
                                            <option value="3">Marzo</option>
                                            <option value="4">Aprile</option>
                                            <option value="5">Maggio</option>
                                            <option value="6">Giugno</option>
                                            <option value="7">Luglio</option>
                                            <option value="8">Agosto</option>
                                            <option value="9">Settembre</option>
                                            <option value="10">Ottobre</option>
                                            <option value="11">Novembre</option>
                                            <option value="12">Dicembre</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <input type="text" name="sinceYear" class="form-control maxlength" maxlength="4" minlength="4" placeholder="Anno">
                                </div>
                            </div>
                            <legend class="text-bold"><i class="icon-address-book"></i> CONTATTI</legend>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Telefono:</label>
                                <div class="col-lg-9">
                                    <input type="tel" name="phoneNumber" class="form-control maxlength" maxlength="13" placeholder="Telefono o cellulare" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Telefono secondario:</label>
                                <div class="col-lg-9">
                                    <input type="tel" name="phoneNumberAdditional" class="form-control maxlength" maxlength="13" placeholder="Altro numero di telefono">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Email:</label>
                                <div class="col-lg-9">
                                    <input type="email" name="email" class="form-control maxlength" maxlength="100" placeholder="Email" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Email secondaria:</label>
                                <div class="col-lg-9">
                                    <input type="email" name="emailAdditional" class="form-control maxlength" maxlength="100" placeholder="Email secondaria">
                                </div>
                            </div>
                            <legend class="text-bold"><i class="icon-location4"></i> INDIRIZZO</legend>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Stato:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="form-control select-search" id="countryCode" name="countryCode" required>
                                            <option value="">-</option>
                                            {% for item in lookup("country") %}
                                                <option value="{{ item.code }}" {{ item.code == 'IT' ? 'selected' : '' }}>{{ item.description }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>                            
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Indirizzo:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="address" class="form-control maxlength" maxlength="100" placeholder="Indirizzo" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Città:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="city" class="form-control maxlength" maxlength="100" placeholder="Città" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">CAP:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="postalCode" class="form-control maxlength" maxlength="10" placeholder="CAP" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Provincia:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="form-control select-search" name="provinceCode" required>
                                            <option value="" selected>-</option>
                                            {% for item in lookup("province") %}
                                                <option value="{{ item.code }}">{{ item.description }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <legend class="text-bold">
                                <i class="icon-office"></i>
                                DATI DI FATTURAZIONE
                                <a class="control-arrow" data-toggle="collapse" data-target="#address2">
                                    <i class="icon-circle-down2"></i>
                                </a>
                            </legend>
                            <div class="collapse in" id="address2">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Ragione Sociale:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="invoiceFullname" class="form-control maxlength" maxlength="100" placeholder="Ragione Sociale">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Stato:</label>
                                    <div class="col-lg-9">
                                        <select class="form-control select" name="invoiceCountryCode">
                                            <option value="">-</option>
                                            {% for item in lookup("country") %}
                                                <option value="{{ item.code }}" {{ item.code == 'IT' ? 'selected' : '' }}>{{ item.description }}</option>                                                
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Indirizzo:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="invoiceAddress" class="form-control maxlength" maxlength="100" placeholder="Indirizzo">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Città:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="invoiceCity" class="form-control maxlength" maxlength="100" placeholder="Città">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">CAP:</label>
                                    <div class="col-lg-9">
                                        <input type="number" name="invoicePostalCode" class="form-control maxlength" maxlength="10" placeholder="CAP">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Provincia:</label>
                                    <div class="col-lg-9">
                                        <select class="form-control select-search" name="invoiceProvinceCode">
                                            <option value="">-</option>
                                            {% for item in lookup("province") %}
                                                <option value="{{ item.code }}">{{ item.description }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Cod. Fiscale:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="tin" class="tin form-control maxlength" minlength="1" maxlength="20" placeholder="Codice Fiscale">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">P.IVA:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="vatNumber" class="form-control maxlength" maxlength="20" placeholder="Partita IVA">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Codice univoco SDI:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="sdiNumber" class="form-control maxlength" maxlength="7" placeholder="Codice univoco SDI">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Pec:</label>
                                    <div class="col-lg-9">
                                        <input type="email" name="pec" class="form-control maxlength" maxlength="100" placeholder="Pec">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">IBAN:</label>
                                    <div class="col-lg-9">
                                        <input type="text" id="lastname" name="bankAccount" class="form-control maxlength" maxlength="100">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Tassazione:</label>
                                    <div class="col-lg-9">
                                        {% if not isVendor %}
                                            <select class="form-control select-search" name="tax" id="tax">
                                                <option value="">-</option>
                                                {% for item in lookup("tax") %}
                                                    <option value="{{ item.code }}">{{ item.name }}</option>
                                                {% endfor %}
                                            </select>
                                        {% else %}
                                            <input type="text" name="tax" class="form-control" value="" readonly>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <legend class="text-semibold">
                                <i class="icon-pie-chart2"></i>
                                GESTIONE ECONOMICA                                
                            </legend>                            
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Estratto conto:</label>
                                <div class="col-lg-9">
                                    <div class="checkbox checkbox-switchery switchery-sm">
                                        <label>
                                            <input type="checkbox" class="switchery" checked name="statement">
                                        </label>
                                    </div>
                                </div>                                    
                            </div>                            
                            
                            <legend class="text-bold"><i class="icon-key"></i> CREDENZIALI</legend>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Profilo:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="select" name="profileType" required>                                            
                                            <option value="vendor">Utente</option>                                            
                                            <option value="admin">Amministratore</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Email:</label>
                                <div class="col-lg-9">
                                    <input type="email" id="username" name="username" class="form-control" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Password:</label>
                                <div class="col-lg-9">
                                    <input type="password" name="password" class="form-control" placeholder="Password" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Conferma password:</label>
                                <div class="col-lg-9">
                                    <input type="password" name="password-confirm" class="form-control" placeholder="Conferma password" required data-parsley-equalto="#password">
                                </div>
                            </div>                                            
                        </div>
                        <div class="panel-footer has-visible-elements">
                            <div class="heading-elements visible-elements">
                                <span class="heading-text text-semibold">Azioni:</span>
                                <div class="pull-right">
                                    <button type="button" class="btn heading-btn btn-default btn-cancel">Annulla</button>
                                    <button type="submit" class="btn heading-btn btn-primary">Salva agente / amministratore <i class="icon-arrow-right14"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <!-- /basic layout -->
            </div>
        </div>
    </div>
    <!-- END MAIN CONTENT -->

</div>
<!-- END PAGE CONTENT -->

{% endblock %}
