{% extends "be/include/base.html" %}

{% set currentPage = 'HOME_SLIDER' %}

{% block extrahead %}
{% if homeSlider.id is not empty %}
    <title>Modifica HOME SLIDER</title>
{% else %}
    <title>Aggiungi HOME SLIDER</title>
{% endif %}
<!-- Theme Custom CSS files -->
<link href="https://www.siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet">
<!-- Theme JS files -->
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/autosize.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/formatter.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/typeahead.bundle.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/handlebars.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://www.siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<script src="{{ contextPath }}/be/js/pages/home-slider-edit.js?{{ buildNumber }}"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
<!-- /theme JS files -->
{% endblock %}

{% block content %}

<a id="homeSlidersUri" style="display: none" href="{{ paths('HOME_SLIDERS') }}"></a>
<a id="homeSlidersSuccessUri" style="display: none;" href="{{ paths('HOME_SLIDERS') }}/ok" rel="nofollow"></a>
<a id="homeSliderRemoveUri" style="display: none;" href="{{ paths('HOME_SLIDER_REMOVE') }}?homeSliderId={{ homeSlider.id }}" rel="nofollow"></a>

<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('HOME_SLIDER_EDIT') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Informazioni salvate correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('HOME_SLIDER_EDIT') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Hai bisogno di aiuto? <a href="mailto:<EMAIL>">Contatta il servizio clienti</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Horizontal form options -->
        {% if homeSlider.id is not empty %}
            {% set saveUri = paths('HOME_SLIDER_EDIT_SAVE') + '?homeSliderId=' + homeSlider.id %}
        {% else %}
            {% set saveUri = paths('HOME_SLIDER_EDIT_SAVE') %}
        {% endif %}
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <!-- Basic layout-->
                <form id="homeSlider-form" class="form-horizontal" method="post" action="{{ saveUri }}">
                    <div class="panel panel-white">
                        
                        <div class="panel-heading">
                            {% if homeSlider.id is not empty %}
                                <h5 class="panel-title text-bold">Modifica {{ homeSlider.title | upper }}</h5>
                            {% else %}
                                <h5 class="panel-title text-bold">Aggiungi Home Slider</h5>
                            {% endif %}
                        </div>
                        
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Immagine:</label>
                                <div class="col-lg-9">
                                    <div class="row">
                                        <div class="col-xs-12">
                                            <div class="slim"
                                                data-max-file-size="1"
                                                data-label="Carica un'immagine"
                                                data-save-initial-image="{{ homeSlider.imageId is not empty ? 'true' : 'false'}}"
                                                data-push="false"
                                                data-post="output"
                                                data-jpeg-compression="75"
                                                data-label-loading="Caricamento immagine..."
                                                data-button-edit-label="Modifica"
                                                data-button-remove-label="Elimina"
                                                data-button-download-label="Scarica"
                                                data-button-upload-label="Carica"
                                                data-button-rotate-label="Ruota"
                                                data-button-cancel-label="Cancella"
                                                data-button-confirm-label="Conferma"
                                                data-status-file-size="Il file è troppo grande, il massimo consentito è $0 MB"
                                                data-status-file-type="Formato immagine non valido, formati consentiti: $0"
                                                data-status-no-support="Il tuo browser non supporta la modifica dell'immagine"
                                                data-status-image-too-small="Immagine troppo piccola, risoluzione minima: $0 pixel"
                                                data-status-content-length="Il server non supporta file così grandi"
                                                data-status-unknown-response="Errore sconosciuto, <NAME_EMAIL>"
                                                data-status-upload-success="Immagine salvata"
                                                data-ratio="16:9"
                                                data-force-size="1920,1080">

                                                {% if homeSlider.imageId is not empty %}
                                                    <img src="{{ paths('IMAGE') }}?oid={{ homeSlider.imageId }}" alt=""/>
                                                {% endif %}
                                                <input type="file" id="cropper" name="uploaded-files" data-show-caption="false" data-show-remove="true" required>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Id Video Vimeo:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="linkVimeo" class="form-control maxlength" maxlength="200" placeholder="Es. 601002664" value="{{ homeSlider.linkVimeo }}">
                                    <span class="help-block">Inserire solo l'ID e non l'url completo quindi un numero di 9 cifre.</span>
                                </div>                                
                            </div>
                            <legend class="text-bold">Slider in italiano</legend>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Titolo:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="title" class="form-control maxlength" maxlength="50" placeholder="Titolo" value="{{ homeSlider.title }}" required>
                                </div>
                            </div>                            
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Sottotitolo:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="subtitle" class="form-control maxlength" maxlength="400" placeholder="Sottotitolo" value="{{ homeSlider.subtitle }}" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Testo CTA:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="ctaCaption" class="form-control maxlength" maxlength="30" placeholder="Testo CTA" value="{{ homeSlider.ctaCaption }}" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Link CTA:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="link" class="form-control maxlength" maxlength="300" placeholder="Url" value="{{ homeSlider.link }}" required>
                                </div>
                            </div>
                            <legend class="text-bold">Slider in inglese</legend>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Titolo Inglese:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="titleEnglish" class="form-control maxlength" maxlength="50" placeholder="Titolo Inglese" value="{{ homeSlider.titleEnglish }}" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Sottotitolo Inglese:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="subtitleEnglish" class="form-control maxlength" maxlength="400" placeholder="Sottotitolo Inglese" value="{{ homeSlider.subtitleEnglish }}" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Testo CTA Inglese:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="ctaCaptionEnglish" class="form-control maxlength" maxlength="30" placeholder="Testo CTA Inglese" value="{{ homeSlider.ctaCaptionEnglish }}" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Link CTA Inglese:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="linkEnglish" class="form-control maxlength" maxlength="300" placeholder="Url" value="{{ homeSlider.linkEnglish }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="panel-footer">
                            <div class="heading-elements">
                                <span class="heading-text text-semibold">Azioni:</span>
                                <div class="heading-btn pull-right">
                                    <button type="button" class="btn btn-default btn-cancel">Annulla</button>
                                    {% if homeSlider.id is not empty %}                                    
                                        <button id="btn-delete" type="button" class="btn bg-danger-600">Rimuovi</button>
                                    {% endif %}
                                    <button id="btn-save" type="submit" class="btn btn-primary">Pubblica home slider<i class="icon-arrow-right14"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <!-- /basic layout -->
            </div>
        </div>
    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

{% endblock %}
