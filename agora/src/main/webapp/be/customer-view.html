{% extends "be/include/base.html" %}
{% set pageActive = 'customers' %}
{% set sidebarDefaultVisible = 'true' %}
{% set sidebarMobileVisible = 'true' %}
{% block extrahead %}
    <title>Cliente</title>

    <!-- CSS -->
    <link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet" type="text/css" media="all">

    <!-- SCRIPTS -->
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/switchery.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/core/libraries/jasny_bootstrap.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/autosize.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/datatables.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/jszip/jszip.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
    <script src="{{ contextPath }}/be/js/picker.js?{{ buildNumber }}"></script>
    <script src="{{ contextPath }}/be/js/picker.date.js?{{ buildNumber }}"></script>
    <script src="{{ contextPath }}/be/js/picker.time.js?{{ buildNumber }}"></script>
    <script src="{{ contextPath }}/be/js/legacy.js?{{ buildNumber }}"></script>
    <script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.css" rel="stylesheet">
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/summernote/summernote.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/summernote/lang/summernote-it-IT.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/wysihtml5/wysihtml5.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/wysihtml5/toolbar.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/wysihtml5/parsers.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/wysihtml5/locales/bootstrap-wysihtml5.ua-UA.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/localization/messages_it.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
    <script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/customer-view.js?{{ buildNumber }}"></script>
{% endblock %}

{% block sidebarcontrol %}
<ul class="fab-menu fab-menu-top-left hidden-xs" data-fab-toggle="click">
    <li>
        <a class="fab-menu-btn btn btn-default btn-float btn-rounded btn-icon sidebar-control sidebar-main-hide">
            <i class="icon-paragraph-justify3"></i>
        </a>
    </li>
</ul>
{% endblock %}

{% block content %}

<!-- actions -->
<a id="customersSuccessUri" style="display: none" href="{{ paths('CUSTOMERS') }}/ok" rel="nofollow"></a>
<a id="customerViewUri" style="display: none" href="{{ paths('CUSTOMER_VIEW') }}?oid={{customer.id}}" rel="nofollow"></a>
<a id="customerAddressSaveUri" style="display: none" href="{{ paths('CUSTOMER_ADDRESS_SAVE') }}" rel="nofollow"></a>
<a id="customerViewSuccessUri" style="display: none" href="{{ paths('CUSTOMER_VIEW') }}/ok?oid={{customer.id}}" rel="nofollow"></a>
<a id="customerViewAbortUri" style="display: none" href="{{ paths('CUSTOMER_VIEW') }}?oid={{customer.id}}" rel="nofollow"></a>
<a id="customerRemoveUri" style="display: none" href="{{ paths('CUSTOMER_REMOVE') }}?customerId={{ customer.id }}" rel="nofollow"></a>
<a id="privacySendUri" style="display: none" href="{{ paths('CUSTOMER_PRIVACY_SEND') }}?customerId={{ customer.id }}" rel="nofollow"></a>
<a id="downloadSendUri" style="display: none" href="{{ paths('CUSTOMER_DOWNLOAD_SEND') }}?customerId={{ customer.id }}" rel="nofollow"></a>

{% set isVendor = (user.profileType != 'system') and (user.profileType != 'admin') %}

<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- LEFT SIDEBAR -->
    <div class="sidebar sidebar-main sidebar-default sidebar-separate">
        <div class="sidebar-content">

            <div class="content-group">
                <div class="panel-body bg-agora border-radius-top text-center" style="background-image: url({{ contextPath }}/be/imgs/bg.png); background-size: contain;">
                    <div class="content-group-sm">
                        <h5 class="text-semibold no-margin-bottom">
                            {% set fullname = '*Nome* *Cognome*' %}
                            {% if customer.fullname is not empty %}
                                {% set fullname = customer.fullname %}
                            {% elseif (customer.name is not empty) and (customer.lastname is not empty) %}
                                {% set fullname = (customer.name | default('*Nome*')) + ' ' + customer.lastname | default('*Cognome*') %}
                            {% endif %}
                            {{ fullname }}
                        </h5>

                        <span class="display-block semi-transparent">cliente dal {{ customer.sinceDate | date('dd MMMM yyyy') }}</span>
                    </div>

                    <div class="display-inline-block content-group-sm">
                        {% if customer.imageId is not empty %}
                            <img src="{{ paths('IMAGE') }}?oid={{ customer.imageId }}" class="img-circle img-responsive img-entity" alt="Cliente">
                        {% else %}
                            {#<!--<img src="{{ contextPath }}/be/imgs/placeholder.jpg" class="img-circle img-responsive img-entity" alt="Cliente">-->#}
                            <img src="https://ui-avatars.com/api/?name={{ customer.name }}+{{ customer.lastname }}+{{ customer.fullname }}&size=256" class="img-circle img-responsive" alt="" style="width: 120px; height: 120px;">
                        {% endif %}
                    </div>

                </div>

                <div class="panel panel-body no-border-top no-border-radius-top">

                    <div class="form-group">
                        <label class="text-semibold">Email:</label>
                        <span class="pull-right-sm truncate width-half-right" data-popup="tooltip" title="{{ customer.email }}"><a href="mailto:{{ customer.email }}">{{ customer.email }}</a></span>
                    </div>

                </div>
            </div>

        </div>
    </div>
    <!-- END LEFT SIDEBAR -->

    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('CUSTOMER_VIEW') }}?oid={{ customer.id }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('CUSTOMER_VIEW') }}?oid={{ customer.id }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Toolbar -->
        <div class="navbar navbar-default navbar-component navbar-xs">
            <ul class="nav navbar-nav visible-xs-block">
                <li class="full-width text-center"><a data-toggle="collapse" data-target="#navbar-filter"><i class="icon-menu7"></i></a></li>
            </ul>

            <div class="navbar-collapse collapse" id="navbar-filter">
                <ul class="nav navbar-nav">
                    <li class="active"><a href="#pages" data-toggle="tab"><i class="icon-magazine position-left"></i> Pagine create<span class="badge badge-danger badge-inline position-right">{{ pageList | length }}</span></a></li>
                    <li><a href="#pagesfollow" data-toggle="tab"><i class="icon-magazine position-left"></i> Pagine seguite<span class="badge badge-danger badge-inline position-right">{{ pageFollowList | length }}</span></a></li>
                    <li><a href="#events" data-toggle="tab"><i class="icon-newspaper position-left"></i> Eventi<span class="badge badge-danger badge-inline position-right">{{ eventList | length }}</span></a></li>
                    <li><a href="#eventsfollow" data-toggle="tab"><i class="icon-magazine position-left"></i> Eventi seguite<span class="badge badge-danger badge-inline position-right">{{ eventFollowList | length }}</span></a></li>
                    <li><a href="#emailconfig" data-toggle="tab"><i class="icon-mail5 position-left"></i> Email config.<span class="badge badge-danger badge-inline position-right">{{ customerNotificationList | length }}</span></a></li>
                    <li><a href="#info" data-toggle="tab"><i class="icon-info22 position-left"></i> Informazioni</a></li>
                </ul>
            </div>
        </div>
        <!-- /toolbar -->


        <!-- User profile -->
        <div class="row">
            <div class="col-lg-12">
                <div class="tabbable">
                    <div class="tab-content">
                        <div class="tab-pane fade in active" id="pages">

                            <!-- Control position -->
                            <div class="panel panel-white">
                                <div class="panel-heading">
                                    <h5 class="panel-title text-bold">Pagine create</h5>
                                    <div class="heading-elements">

                                    </div>
                                </div>

                                <table class="table datatable-responsive-control-right">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Indirizzo</th>
                                            <th>Provincia</th>
                                            <th>Creato il</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for entry in pageList %}
                                            {% if entry.status != 'draft' %}
                                                <tr>
                                                    <td>
                                                        <a href="{{ paths('BE_PAGE_EDIT') }}?oid={{ entry.id }}" class="text-bold" sortable-value="{{ entry.name }}">#{{ entry.name | default('n.d.') }}</a>
                                                    </td>
                                                    <td>
                                                        <span>{{ entry.address }}</span>
                                                    </td>
                                                    <td>
                                                        <span>{{ entry.provinceCode }}</span>
                                                    </td>
                                                    <td>
                                                        <span sortable-value="{{ entry.creation | date('yyyyMMddHHmm') }}">{{ entry.creation | date('dd/MM/yyyy') }}</span>
                                                    </td>                                                    
                                                    <td></td>
                                                </tr>
                                            {% endif %}
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <!-- /control position -->

                        </div>
                        
                        <div class="tab-pane fade" id="pagesfollow">

                            <!-- Control position -->
                            <div class="panel panel-white">
                                <div class="panel-heading">
                                    <h5 class="panel-title text-bold">Pagine seguite</h5>
                                    <div class="heading-elements">

                                    </div>
                                </div>

                                <table class="table datatable-responsive-control-right">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Follower</th>
                                            <th>Indirizzo</th>
                                            <th>Provincia</th>
                                            <th>Creata il</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for entry in pageFollowList %}
                                            {% if entry.status != 'draft' %}
                                                <tr>
                                                    <td>
                                                        <a href="{{ paths('BE_PAGE_EDIT') }}?oid={{ entry.page.id }}" class="text-bold" sortable-value="{{ entry.page.name }}">#{{ entry.page.name | default('n.d.') }}</a>
                                                    </td>
                                                    <td>
                                                        <span>{{ entry.followerCount }}</span>
                                                    </td>
                                                    <td>
                                                        <span>{{ entry.page.address }}</span>
                                                    </td>
                                                    <td>
                                                        <span>{{ entry.page.provinceCode }}</span>
                                                    </td>
                                                    <td>
                                                        <span sortable-value="{{ entry.page.creation | date('yyyyMMddHHmm') }}">{{ entry.page.creation | date('dd/MM/yyyy') }}</span>
                                                    </td>                                                    
                                                    <td></td>
                                                </tr>
                                            {% endif %}
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <!-- /control position -->

                        </div>
                        
                        <div class="tab-pane fade" id="events">

                            <!-- Control position -->
                            <div class="panel panel-white">
                                <div class="panel-heading">
                                    <h5 class="panel-title text-bold">Eventi</h5>
                                    <div class="heading-elements">

                                    </div>
                                </div>

                                <table class="table datatable-responsive-control-right">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Indirizzo</th>
                                            <th>Provincia</th>
                                            <th>Creato il</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for entry in eventList %}
                                            {% if entry.status != 'draft' %}
                                                <tr>
                                                    <td>
                                                        <a href="{{ paths('BE_EVENT_EDIT') }}?oid={{ entry.id }}" class="text-bold" sortable-value="{{ entry.name }}">#{{ entry.name | default('n.d.') }}</a>
                                                    </td>
                                                    <td>
                                                        <span>{{ entry.address }}</span>
                                                    </td>
                                                    <td>
                                                        <span>{{ entry.provinceCode }}</span>
                                                    </td>
                                                    <td>
                                                        <span sortable-value="{{ entry.creation | date('yyyyMMddHHmm') }}">{{ entry.creation | date('dd/MM/yyyy') }}</span>
                                                    </td>                                                    
                                                    <td></td>
                                                </tr>
                                            {% endif %}
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <!-- /control position -->

                        </div>
                        
                        <div class="tab-pane fade" id="eventsfollow">

                            <!-- Control position -->
                            <div class="panel panel-white">
                                <div class="panel-heading">
                                    <h5 class="panel-title text-bold">Eventi seguiti</h5>
                                    <div class="heading-elements">

                                    </div>
                                </div>

                                <table class="table datatable-responsive-control-right">
                                    <thead>
                                        <tr>
                                            <th>Nome</th>
                                            <th>Follower</th>
                                            <th>Indirizzo</th>
                                            <th>Provincia</th>
                                            <th>Creata il</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for entry in eventFollowList %}
                                            {% if entry.status != 'draft' %}
                                                <tr>
                                                    <td>
                                                        <a href="{{ paths('BE_EVENT_EDIT') }}?oid={{ entry.event.id }}" class="text-bold" sortable-value="{{ entry.event.name }}">#{{ entry.event.name | default('n.d.') }}</a>
                                                    </td>
                                                    <td>
                                                        <span>{{ entry.followerCount }}</span>
                                                    </td>
                                                    <td>
                                                        <span>{{ entry.event.address }}</span>
                                                    </td>
                                                    <td>
                                                        <span>{{ entry.event.provinceCode }}</span>
                                                    </td>
                                                    <td>
                                                        <span sortable-value="{{ entry.event.creation | date('yyyyMMddHHmm') }}">{{ entry.event.creation | date('dd/MM/yyyy') }}</span>
                                                    </td>                                                    
                                                    <td></td>
                                                </tr>
                                            {% endif %}
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <!-- /control position -->

                        </div>
                        
                        <div class="tab-pane fade" id="emailconfig">

                            <!-- Control position -->
                            <div class="panel panel-white">
                                <div class="panel-heading">
                                    <h5 class="panel-title text-bold">Configurazione notifiche mail</h5>
                                    <div class="heading-elements">

                                    </div>
                                </div>

                                <table class="table datatable-responsive-control-right">
                                    <thead>
                                        <tr>
                                            <th>Città</th>
                                            <th>Raggio</th>
                                            <th>Stato</th>
                                            <th>Creata il</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for entry in customerNotificationList %}
                                            <tr>
                                                <td>
                                                    {{ entry.city }}
                                                </td>
                                                <td>
                                                    <span>{{ entry.rangeKm }}</span>
                                                </td>
                                                <td>
                                                    <span>{{ entry.emailActive ? 'ATTIVA' : 'DISATTIVA' }}</span>
                                                </td>
                                                <td>
                                                    <span sortable-value="{{ entry.creation | date('yyyyMMddHHmm') }}">{{ entry.creation | date('dd/MM/yyyy') }}</span>
                                                </td>                                                    
                                                <td></td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <!-- /control position -->

                        </div>
                        
                        <div class="tab-pane fade" id="info">

                            <!-- Profile info -->
                            <form id="form-add-customer" class="form-horizontal form-validate-jquery" method="post" action="{{ paths('CUSTOMERS_EDIT_SAVE') }}?oid={{ customer.id }}">
                                <div class="panel panel-white">
                                    <div class="panel-heading">
                                        <h5 class="panel-title text-bold">Informazioni</h5>
                                        {% if customer.channel == 'B2B' %}
                                            <div class="heading-elements">
                                                <div class="form-group">
                                                    <div class="btn-group heading-btn">
                                                        {% set passwordSendLabel = 'INVIA CREDENZIALI B2B' %}
                                                        {% if userCustomer.profileType == 'customer' %}
                                                            {% set passwordSendLabel = 'REINVIA CREDENZIALI B2B' %}
                                                        {% endif %}
                                                        <a href="{{ paths('CUSTOMER_B2B_PASSWORD_SEND') }}?customerId={{ customer.id }}" class="btn bg-blue legitRipple b2b-password-send"><i class="icon-key position-left"></i>{{ passwordSendLabel }}</a>                                                        
                                                    </div>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    {% include "be/include/snippets/customer-view.html" %}                                    

                                    <div class="panel-footer has-visible-elements">
                                        <div class="heading-elements visible-elements">
                                            <span class="heading-text text-semibold">Azioni:</span>
                                            <div class="pull-right">
                                                <button type="button" class="btn heading-btn btn-default btn-cancel">Annulla</button>
                                                <button id="deleteCustomer" type="button" class="btn heading-btn btn-danger">Elimina</button>
                                                <button type="submit" class="btn heading-btn btn-primary">Aggiorna informazioni <i class="icon-arrow-right14"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- /profile info -->
                            </form>

                        </div>

                    </div>
                </div>
            </div>

        </div>
        <!-- /user profile -->

    </div>
    <!-- END MAIN CONTENT -->

    <!-- Horizontal form modal -->
    <div id="address-insert-popup" class="modal fade">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h5 class="modal-title text-bold">Aggiungi indirizzo di {{ customer.fullname }}</h5>
                </div>

                <form id="address-insert-popup-form" class="form-horizontal" action="{{ paths('CUSTOMER_ADDRESS_SAVE') }}">
                    <div class="modal-body">
                        <div id="address-insert-popup-panel">

                            <input type="hidden" name="addressId" required value="{{ customer.id }}">
                            <input type="hidden" name="addressIndex" required value="-1">

                            <div class="form-group">
                                <label class="col-lg-3 control-label">Nome:</label>
                                <div class="col-lg-9">
                                    <input name="fullname" class="form-control maxlength" maxlength="100" value="{{ address.fullname }}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Stato:</label>
                                <div class="col-lg-3">
                                    <select class="form-control select" id="addressInsertCountryCode" name="countryCode">
                                        <option value="">-</option>
                                        {% for item in lookup("country") %}
                                            <option value="{{ item.code }}" {{ item.code == address.countryCode ? 'selected' : ''}}>{{ item.description }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Indirizzo:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="address" class="form-control maxlength" maxlength="100" placeholder="Indirizzo" value="{{ address.address }}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Città:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="city" class="form-control maxlength" maxlength="100" placeholder="Città" value="{{ address.city }}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">CAP:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="postalCode" class="form-control maxlength" maxlength="10" placeholder="CAP" value="{{ address.postalCode }}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Provincia:</label>
                                <div class="col-lg-3">
                                    <select class="form-control select-search" id="addressInsertProvinceCode" name="provinceCode">
                                        <option value="">-</option>
                                        {% for item in lookup("province") %}
                                            <option value="{{ item.code }}" {{ item.code == address.provinceCode ? 'selected' : ''}}>{{ item.description }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Telefono:</label>
                                <div class="col-lg-9">
                                    <input type="tel" name="phoneNumber" class="form-control maxlength" maxlength="13" placeholder="Telefono o cellulare" value="{{ address.phoneNumber }}">
                                </div>
                            </div>

                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-link" data-dismiss="modal">Chiudi</button>
                        <button type="submit" class="btn btn-primary">Aggiungi</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- /horizontal form modal -->

    <!-- Horizontal form modal -->
    <div id="address-edit-popup" class="modal fade">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h5 class="modal-title text-bold">Modifica indirizzo di {{ customer.fullname }}</h5>
                </div>

                <form id="address-edit-popup-form" class="form-horizontal" action="{{ paths('CUSTOMER_ADDRESS_SAVE') }}">
                    <div class="modal-body">
                        <div id="address-edit-popup-panel">

                            <input type="hidden" name="addressId" required value="{{ customer.id }}">
                            <input type="hidden" name="addressIndex" required value="0">

                            <div class="form-group">
                                <label class="col-lg-3 control-label">Nome:</label>
                                <div class="col-lg-9">
                                    <input name="fullname" class="form-control maxlength" maxlength="100" required value="{{ address.fullname }}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Stato:</label>
                                <div class="col-lg-3">
                                    <select class="form-control select" id="addressCountryCode" name="countryCode" required>
                                        <option value="">-</option>
                                        {% for item in lookup("country") %}
                                            <option value="{{ item.code }}" {{ item.code == address.countryCode ? 'selected' : ''}}>{{ item.description }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Indirizzo:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="address" class="form-control maxlength" maxlength="100" placeholder="Indirizzo" value="{{ address.address }}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Città:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="city" class="form-control maxlength" maxlength="100" placeholder="Città" value="{{ address.city }}" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">CAP:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="postalCode" class="form-control maxlength" maxlength="10" placeholder="CAP" value="{{ address.postalCode }}" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Provincia:</label>
                                <div class="col-lg-3">
                                    <select class="form-control select-search" id="addressProvinceCode" name="provinceCode" required>
                                        <option value="">-</option>
                                        {% for item in lookup("province") %}
                                            <option value="{{ item.code }}" {{ item.code == address.provinceCode ? 'selected' : ''}}>{{ item.description }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Telefono:</label>
                                <div class="col-lg-9">
                                    <input type="tel" name="phoneNumber" class="form-control maxlength" maxlength="13" placeholder="Telefono o cellulare" value="{{ address.phoneNumber }}" required>
                                </div>
                            </div>

                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-link" data-dismiss="modal">Chiudi</button>
                        <button type="submit" class="btn btn-primary">Aggiorna</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- /horizontal form modal -->

</div>
<!-- END PAGE CONTENT -->
{% endblock %}
