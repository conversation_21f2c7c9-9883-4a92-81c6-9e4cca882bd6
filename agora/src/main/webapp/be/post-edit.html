{% extends "be/include/base.html" %}
{% set pageActive = 'magazine' %}
{% block extrahead %}
{% if post.id is not empty %}
    <title>Modifica articolo {{ post.title | upper }}</title>
{% else %}
    <title>Nuovo articolo</title>
{% endif %}
<!-- Theme Custom CSS files -->
<link href="https://www.siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet">
<link href="https://www.siteria.it/libs/dropuploader/1.8.1/css/drop_uploader.min.css" rel="stylesheet">

<!-- Theme JS files -->
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/switchery.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/localization/messages_it.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.css" rel="stylesheet">
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/summernote/summernote.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/summernote/lang/summernote-it-IT.js"></script>

<script src="https://www.siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="https://siteria.it/libs/sortable/1.7.0/Sortable.min.js"></script>
<script src="https://www.siteria.it/libs/dropuploader/1.8.1/js/drop_uploader.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/post-edit.js?{{ buildNumber }}"></script>
<script src="{{ contextPath }}/be/js/summernote-image-attributes.js?{{ buildNumber }}"></script>
<script src="{{ contextPath }}/be/js/lang/it-IT.js?{{ buildNumber }}"></script>

<!-- /theme JS files -->
{% endblock %}

{% block content %}
<a id="imageUri" style="display: none;" href="{{ paths('IMAGE') }}?oid=" rel="nofollow"></a>

<a id="postsUri" style="display: none" href="{{ paths('POSTS') }}"></a>
<a id="postsSuccessUri" style="display: none;" href="{{ paths('POSTS') }}/ok" rel="nofollow"></a>
<a id="postRemoveUri" style="display: none;" href="{{ paths('POST_REMOVE') }}?postId={{ post.id }}" rel="nofollow"></a>
<a id="postImageSaveUri" style="display: none;" href="{{ paths('POST_IMAGE_SAVE') }}" rel="nofollow"></a>
<!-- POST EDIT -->
{% if post.id is not empty %}
    {% set saveUri = paths('POST_EDIT_SAVE') + '?oid=' + post.id + '&published=' %}
{% else %}
    {% set saveUri = paths('POST_EDIT_SAVE') + '?published='  %}
{% endif %}
<a id="postSaveUri" style="display: none" href="{{ saveUri }}"></a>

<div id="imageIds" style="display: none;">
    {% if post.id is not empty %}
        {% if post.imageIds is not empty %}
            {% for imageId in post.imageIds %}{{ imageId }}|{% endfor %}
        {%endif%}
    {%endif%}
</div>

<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-success">
                    <a href="{{ paths('POST_EDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-danger">
                    <a href="{{ paths('POST_EDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="row">
            <div class="col-lg-8 col-lg-offset-2">
                <!-- Basic layout-->
                <form id="post-form" class="form-horizontal" method="post" action="{{ saveUri }}">
                    <div class="panel panel-white">

                        <div class="panel-heading">
                            {% if post.id is not empty %}
                                <h5 class="panel-title text-bold">Modifica articolo {{ post.title | upper }}</h5>
                            {% else %}
                                <h5 class="panel-title text-bold">Nuovo articolo</h5>
                            {% endif %}
                        </div>

                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Immagini:</label>
                                <div class="col-lg-9">
                                    <div class="file-drop-area">
                                        <label for="files">Trascina qui le foto</label>
                                        <input name="uploaded-files" id="files" type="file" multiple>
                                    </div>
                                    <span class="help-block">La prima immagine sarà l'immagine di copertina. Puoi ordinare le foto trascinandole.</span>
                                    <br>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Titolo:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="title" class="form-control maxlength" maxlength="80" placeholder="Titolo dell'articolo" value="{{ post.title }}" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Testo dell'articolo:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <textarea rows="50" name="description" class="summernote" placeholder="Inserisci il testo...">
                                            {{ post.description }}
                                        </textarea>                                        
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Categoria:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="select-search-standard form-control" id="category" name="category" required>
                                            <option value="">-</option>
                                            <option value="attualita" {{ post.category == 'attualita' ? 'selected' : '' }}>Attualità</option>                                            
                                            <option value="benessere" {{ post.category == 'benessere' ? 'selected' : '' }}>Benessere</option>
                                            <option value="cibo" {{ post.category == 'cibo' ? 'selected' : '' }}>Food & Drink</option>
                                            <option value="tempo-libero" {{ post.category == 'tempo-libero' ? 'selected' : '' }}>Tempo libero</option>
                                            <option value="cultura" {{ post.category == 'cultura' ? 'selected' : '' }}>Cultura</option>                                            
                                        </select>
                                    </div>
                                </div>
                            </div>
                           
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Tags:</label>
                                <div class="col-lg-9">
                                    <select class="select-search-multiple" id="taglist" name="taglist" data-placeholder="Inserisci uno o più tag..." multiple required>
                                        <option value="">-</option>
                                        {% for tag in tagList %}
                                            <option value="{{ tag }}" {{ (post.tags contains tag) ? 'selected' : '' }}>{{ tag }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>       
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Scelto da Agorapp:</label>
                                <div class="col-lg-9">
                                    <div class="checkbox checkbox-switchery switchery-sm">
                                        <label>
                                            <input type="checkbox" class="switchery" name="editorChoice" value="{{ post.editorChoice }}">
                                        </label>
                                    </div>                                                
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Sponsorizzato:</label>
                                <div class="col-lg-9">
                                    <div class="checkbox checkbox-switchery switchery-sm">
                                        <label>
                                            <input type="checkbox" class="switchery" name="sponsored" value="{{ post.sponsored }}">
                                        </label>
                                    </div>                                                
                                </div>
                            </div>
                        </div>
                        <div class="panel-footer has-visible-elements">
                            <div class="heading-elements visible-elements">
                                <span class="heading-text text-semibold">Azioni:</span>
                                <div class="pull-right">
                                    <button type="button" class="btn heading-btn btn-default btn-cancel">Annulla</button>
                                    {% if post.id is not empty %}
                                        <button id="btn-delete" type="button" class="btn heading-btn btn-danger">Elimina</button>
                                    {% endif %}
                                    <button id="btn-draft" type="submit" class="btn heading-btn bg-info">Salva come bozza</button>
                                    <button id="btn-save" type="submit" class="btn heading-btn btn-primary">Salva<i class="icon-arrow-right14"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <!-- /basic layout -->
            </div>
        </div>
    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

{% endblock %}
