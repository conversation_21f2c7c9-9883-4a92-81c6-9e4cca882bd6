{% extends "be/include/base.html" %}
{% set pageActive = 'sponsor-events' %}

{% block extrahead %}
<title><PERSON>i Sponsorizzati</title>
<!-- SCRIPTS -->
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment_locales.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/datatables.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/jszip/jszip.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/ripple.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/sponsor-event-collection.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}

<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- Main content -->
    <div class="content-wrapper">
        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('SPONSOR_EVENT_COLLECTION') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Informazioni salvate correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('SPONSOR_EVENT_COLLECTION') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Hai bisogno di aiuto? <a href="mailto:<EMAIL>">Contatta il servizio clienti</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- State saving -->
        <div class="panel panel-white">
            <div class="panel-heading">
                <h5 class="panel-title text-bold">Eventi Sponsorizzati</h5>
                <div class="heading-elements">
                    <a href="{{ paths('SPONSOR_EVENT_EDIT') }}" class="btn btn-primary legitRipple"><i class="icon-plus-circle2 position-left"></i>Aggiungi evento sponsorizzato</a>
                </div>
            </div>

            <table class="table datatable-responsive-control-right">
                <thead>
                    <tr>
                        <th>Nome Evento</th>
                        <th>Data Scadenza</th>
                        <th>Ordinamento</th>
                        <th>Creato il</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {% if sponsorEventEntries is not empty %}
                        {% for entry in sponsorEventEntries %}
                            {% set isExpired = false %}
                            {% if entry.sponsorEvent.expirationDate.getTime() < today().getTime() %}
                                {% set isExpired = true %}
                            {% endif %}
                            <tr class="{{ isExpired ? 'text-danger' : '' }}">
                                <td>
                                    <a href="{{ paths('SPONSOR_EVENT_EDIT') }}?sponsorEventId={{ entry.sponsorEvent.id }}" class="text-bold">{{ entry.event.name }}</a>
                                </td>
                                <td>
                                    <span sortable-value="{{ entry.sponsorEvent.expirationDate | date('yyyyMMddHHmm') }}">{{ entry.sponsorEvent.expirationDate | date('dd/MM/yyyy') }}</span>
                                </td>
                                <td>
                                    <span>{{ entry.sponsorEvent.sort | default(0) }}</span>
                                </td>
                                <td>
                                    <span sortable-value="{{ entry.sponsorEvent.creation | date('yyyyMMddHHmm') }}">{{ entry.sponsorEvent.creation | date('dd MMMM yyyy HH:mm') }}</span>
                                </td>
                                <td></td>
                            </tr>
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
        <!-- /state saving -->

    </div>
    <!-- /main content -->
</div>
<!-- /page content -->
{% endblock %}
