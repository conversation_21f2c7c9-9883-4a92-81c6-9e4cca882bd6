{% extends "be/include/base.html" %}
{% set pageActive = 'sponsor-pages' %}

{% block extrahead %}
<title>{% if sponsorPage.id is not empty %}Modifica{% else %}Aggiungi{% endif %} Pagina Sponsorizzata</title>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/localization/messages_it.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment_locales.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/core/app.js"></script>
<script src="{{ contextPath }}/be/js/pages/sponsor-page-edit.js?{{ buildNumber }}"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/ripple.min.js"></script>
{% endblock %}

{% block content %}

<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- Main content -->
    <div class="content-wrapper">
        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('SPONSOR_PAGE_EDIT') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Informazioni salvate correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('SPONSOR_PAGE_EDIT') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Hai bisogno di aiuto? <a href="mailto:<EMAIL>">Contatta il servizio clienti</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Horizontal form options -->
        {% if sponsorPage.id is not empty %}
            {% set saveUri = paths('SPONSOR_PAGE_EDIT_SAVE') + '?sponsorPageId=' + sponsorPage.id %}
        {% else %}
            {% set saveUri = paths('SPONSOR_PAGE_EDIT_SAVE') %}
        {% endif %}
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                <!-- Basic layout-->
                <form id="sponsor-page-form" class="form-horizontal" method="post" action="{{ saveUri }}">
                    <div class="panel panel-white">
                        
                        <div class="panel-heading">
                            {% if sponsorPage.id is not empty %}
                                <h5 class="panel-title text-bold">Modifica Pagina Sponsorizzata</h5>
                            {% else %}
                                <h5 class="panel-title text-bold">Aggiungi Pagina Sponsorizzata</h5>
                            {% endif %}
                        </div>

                        <div class="panel-body">
                            <div class="form-group">
                                <label class="control-label col-lg-3">Pagina <span class="text-danger">*</span></label>
                                <div class="col-lg-9">
                                    <select name="pageId" id="pageId" class="form-control select-search" data-placeholder="Seleziona una pagina" required>
                                        {% if sponsorPage.pageId is not empty %}
                                            {% set selectedPage = get('page', sponsorPage.pageId) %}
                                            <option value="{{ sponsorPage.pageId }}" selected>{{ selectedPage.name }}</option>
                                        {% endif %}
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-lg-3">Data Scadenza <span class="text-danger">*</span></label>
                                <div class="col-lg-9">
                                    <input type="text" name="expirationDate" id="expirationDate" class="form-control daterange-single" placeholder="Seleziona data scadenza" value="{{ sponsorPage.expirationDate | date('dd/MM/yyyy') }}" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="control-label col-lg-3">Ordinamento</label>
                                <div class="col-lg-9">
                                    <input type="number" name="sort" class="form-control" placeholder="Ordinamento (0 = primo)" value="{{ sponsorPage.sort | default(0) }}">
                                    <span class="help-block">Numero per definire l'ordine di visualizzazione (1 = ultimo)</span>
                                </div>
                            </div>

                            {% if sponsorPage.id is not empty %}
                                <input type="hidden" name="sponsorPageId" value="{{ sponsorPage.id }}">
                            {% endif %}
                        </div>

                        <div class="panel-footer">
                            <div class="heading-elements">
                                <span class="heading-text text-semibold">Azioni:</span>
                                <div class="heading-btn pull-right">
                                    <button type="button" class="btn btn-default btn-cancel">Annulla</button>
                                    {% if sponsorPage.id is not empty %}
                                        <button id="btn-delete" type="button" class="btn bg-danger-600">Rimuovi</button>
                                    {% endif %}
                                    <button id="btn-save" type="submit" class="btn btn-primary">
                                        {% if sponsorPage.id is not empty %}Aggiorna pagina sponsorizzata{% else %}Salva pagina sponsorizzata{% endif %}
                                        <i class="icon-arrow-right14"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                    </div>
                </form>
                <!-- /basic layout -->

            </div>
        </div>

    </div>
    <!-- /main content -->
</div>
<!-- /page content -->

<!-- Hidden URIs for JavaScript -->
<a id="dataPagesUri" style="display: none;" href="{{ paths('DATA_PAGES') }}" rel="nofollow"></a>
<a id="sponsorPageCollectionUri" style="display: none;" href="{{ paths('SPONSOR_PAGE_COLLECTION') }}" rel="nofollow"></a>
<a id="sponsorPageCollectionSuccessUri" style="display: none;" href="{{ paths('SPONSOR_PAGE_COLLECTION') }}/ok" rel="nofollow"></a>
{% if sponsorPage.id is not empty %}
<a id="sponsorPageRemoveUri" style="display: none;" href="{{ paths('SPONSOR_PAGE_REMOVE') }}?sponsorPageId={{ sponsorPage.id }}" rel="nofollow"></a>
{% endif %}

{% endblock %}
