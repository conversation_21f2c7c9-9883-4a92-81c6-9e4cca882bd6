{% extends "be/include/base.html" %}
{% set pageActive = 'translations' %}

{% block extrahead %}
<title>Testi e traduzioni</title>
<script src="https://www.siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/handsontable/handsontable.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/2.1/layout_5/LTR/material/full/assets/js/app.js"></script>
<script src="https://www.siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/ripple.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/labels.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}

<!-- Page content -->
<div class="page-content">
    
    <a id="beLabelsDataUri" style="display: none" href="{{ paths('LABELS_DATA') }}" rel="nofollow"></a>
    <a id="beLabelsSaveUri" style="display: none" href="{{ paths('LABELS_SAVE') }}" rel="nofollow"></a>
    
    <!-- Main content -->
    <div class="content-wrapper">
        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('LABELS') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('LABELS') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Basic functionality -->
        <div class="panel panel-white">
            <div class="panel-heading">
                <h5 class="panel-title text-bold">Testi e traduzioni</h5>
                <div class="heading-elements">
                    <div class="heading-btn">
                        <a id="hot_save" href="#" class="btn heading-btn btn-primary legitRipple">Salva traduzioni <i class="icon-arrow-right14"></i></a>
                    </div>
                </div>
            </div>

            <div class="panel-body">
                <div class="form-group has-feedback has-feedback-left">
                    <input type="text" id="hot_search_basic_input" class="form-control" placeholder="Cerca per chiave o descrizione...">
                    <div class="form-control-feedback">
                        <i class="icon-search4 text-size-small"></i>
                    </div>
                </div>

                <div class="hot-container">
                    <div id="hot_search_basic"></div>
                </div>
            </div>
        </div>
        <!-- /basic functionality -->

    </div>
    <!-- /main content -->
</div>
<!-- /page content -->
{% endblock %}
