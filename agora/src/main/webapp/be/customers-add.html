{% extends "be/include/base.html" %}

{% block extrahead %}
<title>Nuovo cliente</title>

<!-- CSS -->
<link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet" type="text/css" media="all">

<!-- SCRIPTS -->
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/switchery.min.js"></script>
<script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/localization/messages_it.js"></script>
<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.css" rel="stylesheet">
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/formatter.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
<script src="{{ contextPath }}/be/js/picker.js?{{ buildNumber }}"></script>
<script src="{{ contextPath }}/be/js/picker.date.js?{{ buildNumber }}"></script>
<script src="{{ contextPath }}/be/js/picker.time.js?{{ buildNumber }}"></script>
<script src="{{ contextPath }}/be/js/legacy.js?{{ buildNumber }}"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/anytime.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/summernote/summernote.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/summernote/lang/summernote-it-IT.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/customers-add.js?{{ buildNumber }}"></script>
<script src="{{ contextPath }}/be/js/summernote-image-attributes.js?{{ buildNumber }}"></script>


{% endblock %}

{% block content %}

<!-- actions -->
<a id="customersAddAbortUri" style="display: none" href="{{ paths('CUSTOMERS') }}" rel="nofollow"></a>

<!-- PAGE CONTENT -->
<div class="page-content">
    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-success">
                    <a href="{{ paths('CUSTOMERS_ADD') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-danger">
                    <a href="{{ paths('CUSTOMERS_ADD') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Horizontal form options -->
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <!-- Basic layout-->
                <form class="form-horizontal" method="post" action="{{ paths('CUSTOMERS_ADD_SAVE') }}">
                    <div class="panel panel-white">
                        <div class="panel-heading">
                            <h5 class="panel-title text-bold">Nuovo cliente</h5>
                        </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Foto copertina:</label>
                                <div class="col-lg-9">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="slim"
                                                data-max-file-size="5"
                                                data-save-initial-image="false"
                                                data-push="false"
                                                data-post="output"
                                                data-label="Carica un'immagine di copertina"
                                                data-label-loading=" "
                                                data-ratio="4:1"
                                                data-button-edit-label="Modifica"
                                                data-button-remove-label="Elimina"
                                                data-button-download-label="Scarica"
                                                data-button-upload-label="Carica"
                                                data-button-rotate-label="Ruota"
                                                data-button-cancel-label="Cancella"
                                                data-button-confirm-label="Conferma"
                                                data-status-file-size="Il file è troppo grande, il massimo consentito è $0 MB"
                                                data-status-file-type="Formato immagine non valido, formati consentiti: $0"
                                                data-status-no-support="Il tuo browser non supporta la modifica dell'immagine"
                                                data-status-image-too-small="Immagine troppo piccola, risoluzione minima: $0 pixel"
                                                data-status-content-length="Il server non supporta file così grandi"
                                                data-status-unknown-response="Errore sconosciuto, <NAME_EMAIL>"
                                                data-status-upload-success="Immagine salvata">

                                                <input type="file" name="uploaded-files" multiple="multiple" data-show-caption="false" data-show-remove="true">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Logo:</label>
                                <div class="col-lg-9">
                                    <div class="row">
                                        <div class="col-xs-6 col-xs-offset-3 col-sm-4 col-sm-offset-4 col-md-4 col-md-offset-4">
                                            <div class="slim"
                                                data-max-file-size="5"
                                                data-save-initial-image="false"
                                                data-push="false"
                                                data-post="output"
                                                data-label="Carica un logo"
                                                data-label-loading=" "
                                                data-ratio="1:1"
                                                data-button-edit-label="Modifica"
                                                data-button-remove-label="Elimina"
                                                data-button-download-label="Scarica"
                                                data-button-upload-label="Carica"
                                                data-button-rotate-label="Ruota"
                                                data-button-cancel-label="Cancella"
                                                data-button-confirm-label="Conferma"
                                                data-status-file-size="Il file è troppo grande, il massimo consentito è $0 MB"
                                                data-status-file-type="Formato immagine non valido, formati consentiti: $0"
                                                data-status-no-support="Il tuo browser non supporta la modifica dell'immagine"
                                                data-status-image-too-small="Immagine troppo piccola, risoluzione minima: $0 pixel"
                                                data-status-content-length="Il server non supporta file così grandi"
                                                data-status-unknown-response="Errore sconosciuto, <NAME_EMAIL>"
                                                data-status-upload-success="Immagine salvata">
                                                <input type="file" name="logo-uploaded-files" multiple="multiple" data-show-caption="false" data-show-remove="true">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>   
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Categoria:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="form-control select" name="area">
                                            {% for item in lookup('area') %}
                                                <option value="{{ item.code }}">{{ item.title }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Settore:</label>
                                <div class="col-lg-9">
                                    <select class="select-search-pills" name="sector" data-placeholder="Seleziona un settore..." multiple>
                                        <option value="">-</option>
                                        {% for item in sectorList %}
                                            <option value="{{ item }}">{{ item }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Data fondazione:</label>
                                <div class="col-lg-9">
                                    <div class="input-group">
                                        <span class="input-group-addon"><i class="icon-calendar5"></i></span>
                                        <input name="establishmentDate" id="establishmentDate" type="text" class="form-control pickadate" placeholder="Seleziona una data">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Nome attività:</label>
                                <div class="col-lg-9">
                                    <input type="text" id="fullname" name="fullname" class="form-control maxlength" maxlength="100" placeholder="Nome attività">
                                    <span class="help-block no-margin-bottom">Può differire dalla ragione sociale di fatturazione, da inserire più sotto</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Email accesso:</label>
                                <div class="col-lg-9">
                                    <input type="email" id="email" name="email" class="form-control maxlength" maxlength="100" placeholder="Email">
                                </div>
                            </div>
                            <!--
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Password:</label>
                                <div class="col-lg-9">
                                    <input type="password" disabled class="form-control maxlength contacts" maxlength="100" placeholder="Password">
                                    <span class="help-block no-margin-bottom">Sarà possibile creare e inviare le credenziali per il nuovo cliente dopo averlo salvato</span>
                                </div>
                            </div>
                            -->
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Telefono:</label>
                                <div class="col-lg-9">
                                    <input type="tel" id="phoneNumber" name="phoneNumber" class="form-control maxlength" maxlength="13" placeholder="Telefono o cellulare">
                                </div>
                            </div>
                                                                                    
                            <legend class="text-bold"><i class="icon-address-book"></i> CONTATTI PERSONA DI RIFERIMENTO</legend>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Nome referente:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="name" class="form-control maxlength" maxlength="50" placeholder="Nome">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Cognome referente:</label>
                                <div class="col-lg-9">
                                    <input type="text" id="lastname" name="lastname" class="form-control maxlength" maxlength="100" placeholder="Cognome">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Telefono referente:</label>
                                <div class="col-lg-9">
                                    <input type="tel" name="phoneNumberAdditional" class="form-control maxlength" maxlength="13" placeholder="Numero telefono referente">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Email referente:</label>
                                <div class="col-lg-9">
                                    <input type="email" name="emailAdditional" class="form-control maxlength" maxlength="100" placeholder="Email referente">
                                </div>
                            </div>
                            
                            <legend class="text-bold"><i class="icon-location4"></i> INFO</legend>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Sito web:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="websiteUrl" class="form-control maxlength" maxlength="100" placeholder="Link sito web">
                                    <span class="help-block no-margin-bottom">Completo di protocollo https://</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Facebook link:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="facebookUrl" class="form-control maxlength" maxlength="100" placeholder="Link pagina Facebook">
                                    <span class="help-block no-margin-bottom">Completo di protocollo https://</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Linkedin link:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="linkedinUrl" class="form-control maxlength" maxlength="200" placeholder="Link pagina Linkedin">
                                    <span class="help-block no-margin-bottom">Completo di protocollo https://</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Instagram username:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="instagramUsername" class="form-control maxlength" maxlength="100" placeholder="Instagram username">                                    
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Twitter username:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="twitterUsername" class="form-control maxlength" maxlength="100" placeholder="Twitter username">                                    
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Descrizione attività:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <textarea rows="15" name="bio" class="summernote" placeholder="Inserisci il testo..."></textarea>                                        
                                    </div>
                                </div>
                            </div>
                            <legend class="text-bold"><i class="icon-watch"></i> ORARI APERTURA</legend>                            
                            <div class="form-group">
                                <label class="col-lg-3 control-label">
                                    Lunedì:
                                    <div class="form-group">
                                        <div class="checkbox checkbox-switchery switchery-sm">
                                            <label>
                                                <input type="checkbox" class="switchery" id="openingMonday" checked name="openingMonday">
                                            </label>
                                        </div>                                                
                                    </div>
                                </label>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-opening" name="openingMondayOpen" placeholder="Orario di apertura">
                                        <span class="help-block">Apertura</span>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-openings" name="openingMondayLunch" placeholder="Orari pausa pranzo">
                                        <span class="help-block">Pausa pranzo (lasciare vuoto se orario continuato)</span>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-opening" name="openingMondayClose" placeholder="Orario di chiusura">
                                        <span class="help-block">Chiusura</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">
                                    Martedì:
                                    <div class="form-group">
                                        <div class="checkbox checkbox-switchery switchery-sm">
                                            <label>
                                                <input type="checkbox" class="switchery" id="openingTuesday" checked name="openingTuesday">
                                            </label>
                                        </div>                                                
                                    </div>
                                </label>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-opening" name="openingTuesdayOpen" placeholder="Orario di apertura">
                                        <span class="help-block">Apertura</span>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-openings" name="openingTuesdayLunch" placeholder="Orari pausa pranzo">
                                        <span class="help-block">Pausa pranzo (lasciare vuoto se orario continuato)</span>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-opening" name="openingTuesdayClose" placeholder="Orario di chiusura">
                                        <span class="help-block">Chiusura</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">
                                    Mercoledì:
                                    <div class="form-group">
                                        <div class="checkbox checkbox-switchery switchery-sm">
                                            <label>
                                                <input type="checkbox" class="switchery" id="openingWednesday" checked name="openingWednesday">
                                            </label>
                                        </div>                                                
                                    </div>
                                </label>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-opening" name="openingWednesdayOpen" placeholder="Orario di apertura">
                                        <span class="help-block">Apertura</span>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-openings" name="openingWednesdayLunch" placeholder="Orari pausa pranzo">
                                        <span class="help-block">Pausa pranzo (lasciare vuoto se orario continuato)</span>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-opening" name="openingWednesdayClose" placeholder="Orario di chiusura">
                                        <span class="help-block">Chiusura</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">
                                    Giovedì:
                                    <div class="form-group">
                                        <div class="checkbox checkbox-switchery switchery-sm">
                                            <label>
                                                <input type="checkbox" class="switchery" id="openingThursday" checked name="openingThursday">
                                            </label>
                                        </div>                                                
                                    </div>
                                </label>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-opening" name="openingThursdayOpen" placeholder="Orario di apertura">
                                        <span class="help-block">Apertura</span>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-openings" name="openingThursdayLunch" placeholder="Orari pausa pranzo">
                                        <span class="help-block">Pausa pranzo (lasciare vuoto se orario continuato)</span>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-opening" name="openingThursdayClose" placeholder="Orario di chiusura">
                                        <span class="help-block">Chiusura</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">
                                    Venerdì:
                                    <div class="form-group">
                                        <div class="checkbox checkbox-switchery switchery-sm">
                                            <label>
                                                <input type="checkbox" class="switchery" id="openingFriday" checked name="openingFriday">
                                            </label>
                                        </div>                                                
                                    </div>
                                </label>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-opening" name="openingFridayOpen" placeholder="Orario di apertura">
                                        <span class="help-block">Apertura</span>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-openings" name="openingFridayLunch" placeholder="Orari pausa pranzo">
                                        <span class="help-block">Pausa pranzo (lasciare vuoto se orario continuato)</span>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-opening" name="openingFridayClose" placeholder="Orario di chiusura">
                                        <span class="help-block">Chiusura</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">
                                    Sabato:
                                    <div class="form-group">
                                        <div class="checkbox checkbox-switchery switchery-sm">
                                            <label>
                                                <input type="checkbox" class="switchery" id="openingSaturday" checked name="openingSaturday">
                                            </label>
                                        </div>                                                
                                    </div>
                                </label>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-opening" name="openingSaturdayOpen" placeholder="Orario di apertura">
                                        <span class="help-block">Apertura</span>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-openings" name="openingSaturdayLunch" placeholder="Orari pausa pranzo">
                                        <span class="help-block">Pausa pranzo (lasciare vuoto se orario continuato)</span>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-opening" name="openingSaturdayClose" placeholder="Orario di chiusura">
                                        <span class="help-block">Chiusura</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">
                                    Domenica:
                                    <div class="form-group">
                                        <div class="checkbox checkbox-switchery switchery-sm">
                                            <label>
                                                <input type="checkbox" class="switchery" id="openingSunday" checked name="openingSunday">
                                            </label>
                                        </div>                                                
                                    </div>
                                </label>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-opening" name="openingSundayOpen" placeholder="Orario di apertura">
                                        <span class="help-block">Apertura</span>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-openings" name="openingSundayLunch" placeholder="Orari pausa pranzo">
                                        <span class="help-block">Pausa pranzo (lasciare vuoto se orario continuato)</span>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <input type="text" class="form-control" format="format-opening" name="openingSundayClose" placeholder="Orario di chiusura">
                                        <span class="help-block">Chiusura</span>
                                    </div>
                                </div>
                            </div>
                            <legend class="text-bold"><i class="icon-location4"></i> INDIRIZZO PRINCIPALE</legend>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Stato:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="form-control select-search" id="countryCode" name="countryCode">
                                            <option value="">-</option>
                                            {% for item in lookup("country") %}
                                                <option value="{{ item.code }}" {{ item.code == 'IT' ? 'selected' : '' }}>{{ item.description }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>                            
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Indirizzo:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="address" id="address" class="form-control maxlength" maxlength="100" placeholder="Indirizzo">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Città:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="city" id="city" class="form-control maxlength" maxlength="100" placeholder="Città">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">CAP:</label>
                                <div class="col-lg-9">
                                    <input type="number" name="postalCode" id="postalCode" class="form-control maxlength" maxlength="10" placeholder="CAP">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Provincia:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="form-control select-search" id="provinceCode" name="provinceCode">
                                            <option value="" selected>-</option>
                                            {% for item in lookup("province") %}
                                                <option value="{{ item.code }}">{{ item.description }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <legend class="text-bold">
                                <i class="icon-office"></i>
                                DATI DI FATTURAZIONE
                                <a class="control-arrow" data-toggle="collapse" data-target="#address2">
                                    <i class="icon-circle-down2"></i>
                                </a>
                            </legend>
                            <div class="form-group text-center">
                                <button type="button" class="btn btn-default btn-copy-address">Copia da indirizzo principale</button>
                            </div>
                            
                            <div class="collapse in" id="address2">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Fatturazione Elettronica:</label>
                                    <div class="col-lg-9">
                                        <div class="checkbox checkbox-switchery switchery-sm">
                                            <label>
                                                <input type="checkbox" class="switchery" id="einvoice" checked name="einvoice">
                                            </label>
                                        </div>                                                
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Ragione Sociale:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="invoiceFullname" id="invoiceFullname" class="form-control maxlength" maxlength="100" placeholder="Ragione Sociale">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Stato:</label>
                                    <div class="col-lg-9">
                                        <select class="form-control select" name="invoiceCountryCode" id="invoiceCountryCode">
                                            <option value="">-</option>
                                            {% for item in lookup("country") %}
                                                <option value="{{ item.code }}" {{ item.code == 'IT' ? 'selected' : '' }}>{{ item.description }}</option>                                                
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Indirizzo:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="invoiceAddress" id="invoiceAddress" class="form-control maxlength" maxlength="100" placeholder="Indirizzo">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Città:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="invoiceCity" id="invoiceCity" class="form-control maxlength" maxlength="100" placeholder="Città">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">CAP:</label>
                                    <div class="col-lg-9">
                                        <input type="number" name="invoicePostalCode" id="invoicePostalCode" class="form-control maxlength" maxlength="10" placeholder="CAP">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Provincia:</label>
                                    <div class="col-lg-9">
                                        <select class="form-control select-search" name="invoiceProvinceCode" id="invoiceProvinceCode">
                                            <option value="">-</option>
                                            {% for item in lookup("province") %}
                                                <option value="{{ item.code }}">{{ item.description }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Cod. Fiscale:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="tin" class="tin form-control maxlength" minlength="1" maxlength="20" placeholder="Codice Fiscale">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">P.IVA:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="vatNumber" class="form-control maxlength" maxlength="11" placeholder="Partita IVA">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Codice univoco SDI:</label>
                                    <div class="col-lg-9">
                                        <input type="text" id="sdiNumber" name="sdiNumber" class="form-control maxlength einvoice-control" maxlength="7" placeholder="Codice univoco SDI">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Pec:</label>
                                    <div class="col-lg-9">
                                        <input type="email" id="pec" name="pec" class="form-control maxlength einvoice-control" maxlength="100" placeholder="Pec">
                                    </div>
                                </div>                                
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">IBAN:</label>
                                    <div class="col-lg-9">
                                        <input type="text" id="bankAccount" name="bankAccount" class="form-control maxlength" maxlength="27" placeholder="IBAN" >
                                    </div>
                                </div>
                            </div>                                                        
                            <input type="hidden" name="channel" value="B2B">                            
                            <input type="hidden" name="vendorId" value="{{ vendorList[0].id }}">                                                        
                        </div>
                        <div class="panel-footer has-visible-elements">
                            <div class="heading-elements visible-elements">
                                <span class="heading-text text-semibold">Azioni:</span>
                                <div class="heading-btn pull-right">
                                    <button type="button" class="btn heading-btn btn-default btn-cancel">Annulla</button>
                                    <button type="submit" class="btn heading-btn btn-primary">Inserisci <i class="icon-arrow-right14"></i></button>                                    
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <!-- /basic layout -->
            </div>
        </div>
    </div>
    <!-- END MAIN CONTENT -->

</div>
<!-- END PAGE CONTENT -->
{% endblock %}
