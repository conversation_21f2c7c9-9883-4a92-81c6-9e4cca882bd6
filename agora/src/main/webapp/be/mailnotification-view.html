{% extends "be/include/base.html" %}

{% block extrahead %}

    {% set operationDescription = '' %}
    {% if mailnotification.operation == 'sendnotification' %}
        {% set operationDescription = 'Invio notifiche' %}
    {% else %}
        {% set operationDescription = '?!?' %}
    {% endif %}

    <title>Chiamata {{ operationDescription | upper }}</title>

    <!-- SCRIPTS -->
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/switchery.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/localization/messages_it.js"></script>
    <script src="{{ contextPath }}/be/js/pages/mailnotification-view.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}

<!-- actions -->
<!-- ... -->

<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        {% if success %}
            <div class="row">
                <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                    <div class="alert alert-success">
                        <a href="{{ paths('MAILNOTIFICATION_VIEW') }}?mailnotificationId={{ mailnotification.id }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                        <span>Dati memorizzati correttamente</span>
                    </div>
                </div>
            </div>
        {% elseif error %}
            <div class="row">
                <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                    <div class="alert alert-danger">
                        <a href="{{ paths('MAILNOTIFICATION_VIEW') }}?mailnotificationId={{ mailnotification.id }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                        <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- MAILNOTIFICATION VIEW -->
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="tabbable">
                    <div class="tab-content">
                        <div class="tab-pane fade in active" id="activity">

                            <!-- Profile info -->
                            <form id="form-mailnotification" class="form-horizontal form-validate-jquery">
                                <div class="panel panel-white">
                                    <div class="panel-heading">
                                        <h5 class="panel-title text-bold">Chiamata {{ mailnotification.name | upper }}</h5>
                                        <div class="heading-elements">
                                            <a href="{{ paths('MAILNOTIFICATIONS') }}" class="btn heading-btn btn-default btn-cancel">Torna alla lista</a>
                                        </div>
                                    </div>

                                    <div class="panel-body">
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Data:</label>
                                            <div class="col-lg-9">
                                                <input type="text" class="form-control" value="{{ mailnotification.creation | date('yyyy-MM-dd HH:mm:ss:SSS') }}" readonly>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Nome:</label>
                                            <div class="col-lg-9">
                                                <input type="text" class="form-control" value="{{ decode('user', mailnotification.userId) }}" readonly>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Operazione:</label>
                                            <div class="col-lg-9">
                                                <input type="text" class="form-control" value="{{ operationDescription }}" readonly>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Durata:</label>
                                            <div class="col-lg-9">
                                                <input type="text" class="form-control" value="{{ (mailnotification.millies | default(0) / 1000.0) | numberformat('#') }}s" readonly>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Esito:</label>
                                            <div class="col-lg-9">
                                                {% set statusDescription = '' %}
                                                {% if mailnotification.status == 'processing' %}
                                                    {% set statusDescription = 'In elaborazione' %}
                                                {% elseif mailnotification.status == 'done' %}
                                                    {% set statusDescription = 'Completato' %}
                                                {% elseif mailnotification.status == 'error' %}
                                                    {% set statusDescription = 'In errore' %}
                                                {% elseif mailnotification.status == 'abort' %}
                                                    {% set statusDescription = 'Interrotto' %}
                                                {% else %}
                                                    {% set statusDescription = '?!?' %}
                                                {% endif %}
                                                <input type="text" class="form-control" value="{{ statusDescription }}" readonly>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Elementi:</label>
                                            <div class="col-lg-9">
                                                <input type="text" class="form-control" value="{{ mailnotification.count | default(0) }}" readonly>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Ko:</label>
                                            <div class="col-lg-9">
                                                <input type="text" class="form-control" value="{{ mailnotification.errorCount | default(0) }}" readonly>
                                            </div>
                                        </div>
                                        {% if mailnotification.errors is not empty %}
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Errori:</label>
                                                <div class="col-lg-9">
                                                    <div class="form-control-static">
                                                        {% for error in mailnotification.errors %}
                                                            {{ error }}</br>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>
                                        {% endif %}
                                        {% if mailnotification.inOuts is not empty %}
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Parametri:</label>
                                                <div class="col-lg-9">
                                                    <div class="form-control-static">
                                                        {% for inout in mailnotification.inOuts %}
                                                            {{ inout.in }}</br>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>
                                        {% endif %}
                                        {% if mailnotification.inOuts is not empty %}
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Esito:</label>
                                                <div class="col-lg-9">
                                                    <div class="form-control-static">
                                                        {% for inout in mailnotification.inOuts %}
                                                            {{ inout.out }}</br>
                                                        {% endfor %}
                                                    </div>
                                                </div>
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="panel-footer has-visible-elements">
                                        <div class="heading-elements">
                                            <span class="heading-text text-semibold">Azioni:</span>
                                            <div class="pull-right">
                                                <a href="{{ paths('MAILNOTIFICATIONS') }}" class="btn heading-btn btn-default btn-cancel">Torna alla lista</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- /profile info -->
                            </form>

                        </div>

                    </div>
                </div>
            </div>

        </div>
        <!-- /user profile -->

    </div>
    <!-- END MAIN CONTENT -->

</div>
<!-- END PAGE CONTENT -->
{% endblock %}
