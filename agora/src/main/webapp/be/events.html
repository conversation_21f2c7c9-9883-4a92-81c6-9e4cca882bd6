{% extends "be/include/base.html" %}
{% set pageActive = 'events' %}
{% block extrahead %}
<title>Eventi</title>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment_locales.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/datatables.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/jszip/jszip.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/events.js?{{ buildNumber }}"></script>
{% endblock %}

{% block sidebarcontrol %}
<ul class="fab-menu fab-menu-top-left hidden-xs" data-fab-toggle="click">
    <li>
        <a class="fab-menu-btn btn btn-default btn-float btn-rounded btn-icon sidebar-control sidebar-main-hide">
            <i class="icon-paragraph-justify3"></i>
        </a>
    </li>
</ul>
{% endblock %}

{% block content %}

<!-- actions -->
<a id="eventRemoveAllUri" style="display: none;" href="{{ paths('BE_EVENT_REMOVE_MULTIPLE') }}" rel="nofollow"></a>
<a id="eventRemoveUri" style="display: none;" href="{{ paths('EVENT_REMOVE') }}?eventId=" rel="nofollow"></a>
<a id="eventsSuccessUri" style="display: none;" href="{{ paths('BE_EVENTS') }}/ok" rel="nofollow"></a>
<a id="eventsIndesignUri" style="display: none" href="{{ paths('EVENTS_INDESIGN') }}" rel="nofollow"></a>
<a id="printTempTxtUri" style="display: none" href="{{ paths('PRINT_TEMP_TXT') }}?filename=" rel="nofollow"></a>
<a id="eventsAlignmentStatusUri" style="display: none" href="{{ paths('EVENTS_ALIGNMENT_STATUS') }}" rel="nofollow"></a>
<a id="dataUri" style="display: none;" href="{{ paths('BE_EVENTS_DATA') }}" rel="nofollow"></a>

<!-- js deposit -->
<div id="startDate" style="display: none">{{ startDate | date("yyyy-MM-dd") }}</div>
<div id="endDate" style="display: none">{{ endDate | date("yyyy-MM-dd") }}</div>
<div id="alignmentRunning" style="display: none">{{ alignmentRunning }}</div>

<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- LEFT SIDEBAR -->
    <div class="sidebar sidebar-main sidebar-default sidebar-separate">
        <div class="sidebar-content">

            <!-- Filter -->
            <div class="sidebar-category">
                <div class="category-title">
                    <span>Filtra eventi</span>
                    <ul class="icons-list">
                        <li><a href="#" data-action="collapse"></a></li>
                    </ul>
                </div>

                <div class="category-content">
                    <form>
                        {% if provinceList is not empty %}
                        <div class="form-group">
                            <legend class="text-size-mini text-muted no-border no-padding">Provincia</legend>
                            <div class="has-scroll">
                                {% for province in provinceList %}
                                <div class="checkbox">
                                    <label class="display-block text-capitalize">
                                        <input type="checkbox" class="styled event-province-filter" event-province-value="{{ province.provinceCode }}" {{ selectedProvinces contains province.provinceCode ? 'checked' : ''}}>
                                        {{ decode("province", province.provinceCode) }}
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                        <button id="filter-apply" type="button" class="btn btn-primary btn-block"><i class="icon-filter3 position-left"></i>Filtra</button>
                    </form>
                </div>
            </div>
            <!-- /categories -->

        </div>
    </div>
    <!-- END LEFT SIDEBAR -->

    <!-- MAIN CONTENT -->
    <div class="content-wrapper">
        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('BE_EVENTS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('BE_EVENTS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}


        <!-- EVENTS -->
        <div class="panel panel-white">
            <div class="panel-heading has-visible-elements">
                <h5 class="panel-title text-bold">Eventi</h5>
                {% if (user.profileType == 'system') or (user.profileType == 'admin') %}
                <div class="heading-elements visible-elements">                        
                    <button type="button" class="btn btn-danger heading-btn" onclick="deleteEvents();">
                        <i class="icon-trash-alt position-left"></i>
                        Cancella eventi
                    </button>
                    <button type="button" class="btn heading-btn btn-default daterange-predefined">
                        <i class="icon-calendar22 position-left"></i>
                        <span></span>
                        <b class="caret"></b>
                    </button>
                </div>
                {% endif %}
            </div>

            <table class="table datatable-events">
                <thead>
                    <tr>
                        <th>Immagine</th>
                        <th>Creato da</th>
                        <th>Titolo</th>
                        <th>Descrizione</th>
                        <th>Link</th>
                        <th>Identificativo</th>
                        <th>Luogo</th>
                        <th>Data inizio</th>
                        <th>Data fine</th>
                        <th>Data inserimento</th>
                        <th>Azioni</th>
                        <th></th>
                    </tr>
                </thead>
            </table>
        </div>
        <!-- /state saving -->

    </div>
    <!-- /main content -->
</div>
<!-- /page content -->
{% endblock %}