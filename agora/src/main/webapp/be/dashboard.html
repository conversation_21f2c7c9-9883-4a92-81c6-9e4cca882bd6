{% extends "be/include/base.html" %}
{% set pageActive = 'dashboard' %}
{% block extrahead %}
<title>Statistiche</title>

<!-- SCRIPTS -->
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/datatables.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/visualization/echarts/echarts.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/dashboard.js?{{ buildNumber }}"></script>


{% endblock %}

{% block content %}

<!-- PAGE CONTENT -->
<div class="page-content">

    <div class="row">                
        {% if (user.profileType == 'system') or (user.profileType == 'admin') %}
            <div class="col-sm-4">
                <a href="{{ paths('BE_PAGES') }}?startDate={{ today() | date('dd/MM/yyyy') }}&endDate={{ today() | date('dd/MM/yyyy') }}" data-popup="tooltip" title="Visualizza pagine">
                    <div class="panel panel-body">
                        <div class="media no-margin">                    

                            <div class="media-body">
                                <h3 class="no-margin text-semibold">{{ pageCount }}</h3>
                                <span class="text-uppercase text-size-mini text-muted">pagine create oggi</span>
                            </div>

                            <div class="media-left media-middle">
                                <i class="icon-magazine icon-3x text-orange-400"></i>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-sm-4">
                <a href="{{ paths('BE_EVENTS') }}?startDate={{ today() | date('dd/MM/yyyy') }}&endDate={{ today() | date('dd/MM/yyyy') }}" data-popup="tooltip" title="Visualizza eventi">
                    <div class="panel panel-body">
                        <div class="media no-margin">                    

                            <div class="media-body">
                                <h3 class="no-margin text-semibold">{{ eventCount }}</h3>
                                <span class="text-uppercase text-size-mini text-muted">eventi creati oggi</span>
                            </div>

                            <div class="media-left media-middle">
                                <i class="icon-calendar2 icon-3x text-info-400"></i>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

            <div class="col-sm-4">
                <a href="{{ paths('CUSTOMERS') }}?startDate={{ today() | date("dd/MM/yyyy") }}&endDate={{ today() | date("dd/MM/yyyy") }}" data-popup="tooltip" title="Visualizza clienti">
                    <div class="panel panel-body">
                        <div class="media no-margin">
                            <div class="media-body">
                                <h3 class="no-margin text-semibold">{{ customerCount }}</h3>
                                <span class="text-uppercase text-size-mini text-muted">clienti registrati oggi</span>
                            </div>

                            <div class="media-right media-middle">
                                <i class="icon-user-plus icon-3x text-success-700"></i>
                            </div>
                        </div>
                    </div>
                </a>
            </div>

        </div>
    {% endif %}

    <div class="panel panel-white">
        <div class="panel-heading">
            
            <h5 class="panel-title text-bold">{{ analysisDescription }}</h5>
            <div class="heading-elements">
                <div class="form-group">
                    <div class="btn-group heading-btn">
                        <a href="{{ paths('DASHBOARD') }}?analysis={{ analysis }}&value={{ value }}&year={{ year }}" class="btn btn-primary legitRipple">{{ year }}</a>
                        {% if yearList is not empty %}
                            <button class="btn btn-primary dropdown-toggle legitRipple" data-toggle="dropdown" aria-expanded="false"><span class="caret"></span></button>
                            <ul class="dropdown-menu dropdown-menu-right">
                                {% for y in yearList %}
                                    <li><a href="{{ paths('DASHBOARD') }}?year={{ y }}">{{ y }}</a></li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </div>
                    <div class="btn-group heading-btn mr-10">
                        <a href="{{ paths('DASHBOARD') }}?analysis={{ analysis }}&year={{ year }}" class="btn btn-primary legitRipple">{{ analysisDescription }}</a>
                        <button class="btn btn-primary dropdown-toggle legitRipple" data-toggle="dropdown" aria-expanded="false"><span class="caret"></span></button>
                        <ul class="dropdown-menu dropdown-menu-right">
                            <li><a href="{{ paths('DASHBOARD') }}?analysis=trend&year={{ year }}">Andamento clienti e pagine</a></li>
                            <li><a href="{{ paths('DASHBOARD') }}?analysis=bestPerformerCustomer&year={{ year }}">Miglior performer / clienti</a></li>
                        </ul>
                    </div>
                </div>
            </div>        
        </div>

        <div id="analysis" style="display: none;">{{ analysis }}</div>
        <div id="analysisValue" style="display: none;">{{ value }}</div>
        <div id="analysisValueDescription" style="display: none;">{{ valueDescription }}</div>
        <div id="customer-new-counters" style="display: none;">{% for value in customerNewCounters %}{{ value }},{% endfor %}</div>
        <div id="customer-old-counters" style="display: none;">{% for value in customerOldCounters %}{{ value }},{% endfor %}</div>
        <div id="page-new-counters" style="display: none;">{% for value in pageNewCounters %}{{ value }},{% endfor %}</div>
        <div id="best-names" style="display: none;">{% for value in bestNames %}{{ value }},{% endfor %}</div>
        <div id="best-values" style="display: none;">{% for value in bestValues %}{{ value }},{% endfor %}</div>
        
        <div class="panel-body">
            <div class="chart-container">
                <div class="chart has-fixed-height" id="customers_chart"></div>
            </div>
        </div>
    </div>

</div>
<!-- END PAGE CONTENT -->

{% endblock %}