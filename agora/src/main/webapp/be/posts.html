{% extends "be/include/base.html" %}
{% set pageActive = 'magazine' %}
{% block extrahead %}
    <title>Magazine</title>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment_locales.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/datatables.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/jszip/jszip.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/posts.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}
    <!-- js deposit -->
    <div id="startDate" style="display: none">{{ startDate | date("yyyy-MM-dd") }}</div>
    <div id="endDate" style="display: none">{{ endDate | date("yyyy-MM-dd") }}</div>

<!-- PAGE CONTENT -->
<div class="page-content">


    <!-- MAIN CONTENT -->
    <div class="content-wrapper">
        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('POSTS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('POSTS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}


        <!-- POSTS -->
        <div class="panel panel-white">
            <div class="panel-heading has-visible-elements">
                <h5 class="panel-title text-bold">Magazine</h5>
                {% if (user.profileType == 'system') or (user.profileType == 'admin') %}
                    <div class="heading-elements visible-elements">
                        <a href="{{ paths('POST_EDIT') }}" class="btn heading-btn btn-primary legitRipple"><i class="icon-plus-circle2 position-left"></i>NUOVO ARTICOLO</a>
                        <button type="button" class="btn heading-btn btn-default daterange-predefined">
                            <i class="icon-calendar22 position-left"></i>
                            <span></span>
                            <b class="caret"></b>
                        </button>
                    </div>
                {% endif %}
            </div>

            <table class="table datatable-posts">
                <thead>
                    <tr>
                        <th>Titolo</th>
                        <th>Link</th>
                        <th>Categoria</th>
                        <th>Inserito da</th>
                        <th>Data</th>
                        <th>Sponsorizzato</th>
                        <th>Stato</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {% if postList is not empty %}
                        {% for entry in postList %}
                            <tr>                                
                                <td>
                                    <a href="{{ paths('POST_EDIT') }}?oid={{ entry.id }}" class="text-bold" target="_blank">{{ entry.title }}</a>
                                </td>
                                <td>
                                    <a href="{{ paths('MAGAZINE_DETAIL_BASE') }}/{{ entry.category }}/{{ entry.identifier }}" target="_blank">Link ↗</a>
                                </td>
                                <td>
                                    {% if entry.category == 'attualita' %}
                                        {% set categoryBg = 'bg-agora' %}
                                        {% set categoryDescription = 'ATTUALITÀ' %}                                    
                                    {% elseif entry.category == 'benessere' %}
                                        {% set categoryBg = 'bg-success' %}
                                        {% set categoryDescription = 'BENESSERE' %}
                                    {% elseif entry.category == 'cibo' %}
                                        {% set categoryBg = 'bg-orange' %}
                                        {% set categoryDescription = 'FOOD & DRINK' %}                                    
                                    {% elseif entry.category == 'tempo-libero' %}
                                        {% set categoryBg = 'bg-pink' %}
                                        {% set categoryDescription = 'TEMPO LIBERO' %}
                                    {% elseif entry.category == 'cultura' %}
                                        {% set categoryBg = 'bg-brown' %}
                                        {% set categoryDescription = 'CULTURA' %}                                    
                                    {% endif %}
                                    <div class="label {{ categoryBg }}">{{ categoryDescription }}</div>
                                </td>
                                <td>
                                    {{ decode('user', entry.userId) }}
                                </td>
                                <td>
                                    <span sortable-value="{{ entry.creation | date('yyyyMMddHHmm') }}">{{ entry.creation | date('dd MMMM yyyy HH:mm') }}</span>
                                </td>
                                <td>
                                    {{ entry.sponsored ? 'SI' : 'NO' }}
                                </td>
                                <td>
                                    {% if entry.published %}
                                        <div class="label bg-success">PUBBLICATO</div>
                                    {% else %}
                                        <div class="label bg-info">BOZZA</div>
                                    {% endif %}
                                </td>
                                <td></td>
                            </tr>
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
        <!-- /state saving -->

    </div>
    <!-- /main content -->
</div>
<!-- /page content -->
{% endblock %}