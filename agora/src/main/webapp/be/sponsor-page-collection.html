{% extends "be/include/base.html" %}
{% set pageActive = 'sponsor-pages' %}

{% block extrahead %}
<title><PERSON><PERSON><PERSON>ponsorizzate</title>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/datatables.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/core/app.js"></script>
<script src="{{ contextPath }}/be/js/pages/sponsor-page-collection.js?{{ buildNumber }}"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/ripple.min.js"></script>
{% endblock %}

{% block content %}

<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- Main content -->
    <div class="content-wrapper">
        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('SPONSOR_PAGE_COLLECTION') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Informazioni salvate correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('SPONSOR_PAGE_COLLECTION') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Hai bisogno di aiuto? <a href="mailto:<EMAIL>">Contatta il servizio clienti</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- State saving -->
        <div class="panel panel-white">
            <div class="panel-heading has-visible-elements">
                <h5 class="panel-title text-bold">Pagine Sponsorizzate</h5>
                <div class="heading-elements visible-elements">
                    <a href="{{ paths('SPONSOR_PAGE_EDIT') }}" class="btn heading-btn btn-primary">
                        <i class="icon-plus3 position-left"></i>
                        Aggiungi Pagina Sponsorizzata
                    </a>
                </div>
            </div>

            <table class="table datatable-sponsor-pages">
                <thead>
                    <tr>
                        <th>Nome Pagina</th>
                        <th>Data Scadenza</th>
                        <th>Ordinamento</th>
                        <th>Creato il</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {% if sponsorPageEntries is not empty %}
                        {% for entry in sponsorPageEntries %}
                            <tr>
                                <td>
                                    <a href="{{ paths('SPONSOR_PAGE_EDIT') }}?sponsorPageId={{ entry.sponsorPage.id }}" class="text-bold">{{ entry.page.name }}</a>
                                </td>
                                <td>
                                    <span sortable-value="{{ entry.sponsorPage.expirationDate | date('yyyyMMddHHmm') }}">{{ entry.sponsorPage.expirationDate | date('dd/MM/yyyy') }}</span>
                                </td>
                                <td>
                                    <span>{{ entry.sponsorPage.sort | default(0) }}</span>
                                </td>
                                <td>
                                    <span sortable-value="{{ entry.sponsorPage.creation | date('yyyyMMddHHmm') }}">{{ entry.sponsorPage.creation | date('dd MMMM yyyy HH:mm') }}</span>
                                </td>
                                <td></td>
                            </tr>
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
        <!-- /state saving -->

    </div>
    <!-- /main content -->
</div>
<!-- /page content -->

<!-- Hidden URIs for JavaScript -->
<a id="sponsorPageRemoveUri" style="display: none;" href="{{ paths('SPONSOR_PAGE_REMOVE') }}?sponsorPageId=" rel="nofollow"></a>
<a id="sponsorPageSuccessUri" style="display: none;" href="{{ paths('SPONSOR_PAGE_COLLECTION') }}/ok" rel="nofollow"></a>

{% endblock %}
