{% extends "be/include/base.html" %}
{% set pageActive = 'customers' %}
{% set sidebarMobileVisible = 'true' %}
{% block extrahead %}
    <title>Clienti</title>

    <!-- SCRIPTS -->
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment_locales.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/datatables.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/jszip/jszip.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/customers.js?{{ buildNumber }}"></script>
{% endblock %}

{% block sidebarcontrol %}
<ul class="fab-menu fab-menu-top-left hidden-xs" data-fab-toggle="click">
    <li>
        <a class="fab-menu-btn btn btn-default btn-float btn-rounded btn-icon sidebar-control sidebar-main-hide">
            <i class="icon-paragraph-justify3"></i>
        </a>
    </li>
</ul>
{% endblock %}

{% block content %}

<!-- data -->
<a id="customersDataUri" style="display: none;" href="{{ paths('CUSTOMERS_DATA') }}" rel="nofollow"></a>

<!-- actions -->
<a id="customerViewUri" style="display: none;" href="{{ paths('CUSTOMER_VIEW') }}?oid=" rel="nofollow"></a>
<a id="ordersAddUri" style="display: none;" href="{{ paths('ORDERS_ADD') }}?customerId=" rel="nofollow"></a>
<a id="ordersAddInitUri" style="display: none;" href="{{ paths('ORDERS_ADD_INITCART') }}?customerId=" rel="nofollow"></a>
<a id="shopperUrl" style="display: none;" href="{{ contextPath }}/be/imgs/shopper.png" rel="nofollow"></a>

<!-- js deposit -->
<div id="startDate" style="display: none">{{ startDate | date("yyyy-MM-dd") }}</div>
<div id="endDate" style="display: none">{{ endDate | date("yyyy-MM-dd") }}</div>


<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- LEFT SIDEBAR -->
    <div class="sidebar sidebar-main sidebar-default sidebar-separate">
        <div class="sidebar-content">

            <!-- Filter -->
            <div class="sidebar-category">
                <div class="category-title">
                    <span>Filtra clienti</span>
                    <ul class="icons-list">
                        <li><a href="#" data-action="collapse"></a></li>
                    </ul>
                </div>

                <div class="category-content">
                    <form>                        
                        {% if cityList is not empty %}
                            <div class="form-group">
                                <legend class="text-size-mini text-muted no-border no-padding">Comune</legend>
                                <div class="has-scroll">
                                    {% for city in cityList %}
                                        <div class="checkbox">
                                            <label class="display-block text-capitalize">
                                                <input type="checkbox" class="styled agora-city-filter" agora-city-value="{{ city.city }}" {{ selectedCities contains city.city ? 'checked' : ''}}>
                                                {{ city.city | lower | title }}
                                            </label>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                        <button id="filter-apply" type="button" class="btn btn-primary btn-block"><i class="icon-filter3 position-left"></i>Filtra</button>
                    </form>
                </div>
            </div>
            <!-- /filter -->

        </div>
    </div>
    <!-- END LEFT SIDEBAR -->

    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('CUSTOMERS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('CUSTOMERS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Control position -->
        <div class="panel panel-white">
            <div class="panel-heading has-visible-elements">
                <h5 class="panel-title text-bold">Clienti</h5>
                <div class="heading-elements visible-elements">
                    <button type="button" class="btn heading-btn btn-default daterange-predefined">
                        <i class="icon-calendar22 position-left"></i>
                        <span></span>
                        <b class="caret"></b>
                    </button>
                </div>
            </div>
            {% if (user.profileType == 'system') or (user.profileType == 'admin') %}
                <input type="hidden" id="isHead" value="true">
            {% else %}
                <input type="hidden" id="isHead" value="false">
            {% endif %}
            <table class="table datatable-responsive-control-right">
                <thead>
                    <tr>
                        <th>Nome</th>
                        <th>Tipo</th>
                        <th>Indirizzo</th>
                        <th>Contatti</th>
                        <th>Email</th>
                        <th>Telefono</th>
                        <th>Registrato il</th>
                        <th>Stato</th>
                        <th></th>
                    </tr>
                </thead>
            </table>
        </div>
        <!-- /control position -->

    </div>
    <!-- END MAIN CONTENT -->

</div>
<!-- END PAGE CONTENT -->

{% endblock %}