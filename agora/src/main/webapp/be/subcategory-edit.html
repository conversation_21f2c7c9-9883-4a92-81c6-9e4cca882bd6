{% extends "be/include/base.html" %}

{% block extrahead %}
{% if subcategory.id is not empty %}
    <title>Modifica sottocategoria {{ subcategory.title | upper }}</title>
{% else %}
    <title>Nuova sottocategoria</title>
{% endif %}
<!-- Theme Custom CSS files -->
<link href="https://www.siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet">
<!-- Theme JS files -->
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/localization/messages_it.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/wysihtml5/wysihtml5.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/wysihtml5/toolbar.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/wysihtml5/parsers.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/editors/wysihtml5/locales/bootstrap-wysihtml5.ua-UA.js"></script>
<script src="https://www.siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/subcategory-edit.js?{{ buildNumber }}"></script>

<!-- /theme JS files -->
{% endblock %}

{% block content %}

<a id="subcategoriesUri" style="display: none" href="{{ paths('SUBCATEGORIES') }}"></a>
<a id="subcategoriesSuccessUri" style="display: none;" href="{{ paths('SUBCATEGORIES') }}/ok" rel="nofollow"></a>
<a id="subcategoryRemoveUri" style="display: none;" href="{{ paths('SUBCATEGORY_REMOVE') }}?subcategoryId={{ subcategory.id }}" rel="nofollow"></a>

<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-success">
                    <a href="{{ paths('SUBCATEGORY_EDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-danger">
                    <a href="{{ paths('SUBCATEGORY_EDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- SUBCATEGORY EDIT -->
        {% if subcategory.id is not empty %}
            {% set saveUri = paths('SUBCATEGORY_EDIT_SAVE') + '?oid=' + subcategory.id %}
        {% else %}
            {% set saveUri = paths('SUBCATEGORY_EDIT_SAVE') %}
        {% endif %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <!-- Basic layout-->
                <form id="subcategory-form" class="form-horizontal" method="post" action="{{ saveUri }}">
                    <div class="panel panel-white">
                        
                        <div class="panel-heading">
                            {% if subcategory.id is not empty %}
                                <h5 class="panel-title text-bold">Modifica sottocategoria {{ subcategory.title | upper }}</h5>
                            {% else %}
                                <h5 class="panel-title text-bold">Nuova sottocategoria</h5>
                            {% endif %}
                        </div>
                        
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Immagine:</label>
                                <div class="col-lg-9">
                                    <div class="row">
                                        <div class="col-xs-12">
                                            <div class="slim"
                                                 data-max-file-size="1"
                                                 data-save-initial-image="{{ subcategory.imageIds[0] is not empty ? 'true' : 'false'}}"
                                                 data-push="false"
                                                 data-post="output"
                                                 data-label="Carica un'immagine"
                                                 data-label-loading=" "
                                                 data-ratio="free"
                                                 data-button-edit-label="Modifica"
                                                 data-button-remove-label="Elimina"
                                                 data-button-download-label="Scarica"
                                                 data-button-upload-label="Carica"
                                                 data-button-rotate-label="Ruota"
                                                 data-button-cancel-label="Cancella"
                                                 data-button-confirm-label="Conferma"
                                                 data-status-file-size="Il file è troppo grande, il massimo consentito è $0 MB"
                                                 data-status-file-type="Formato immagine non valido, formati consentiti: $0"
                                                 data-status-no-support="Il tuo browser non supporta la modifica dell'immagine"
                                                 data-status-image-too-small="Immagine troppo piccola, risoluzione minima: $0 pixel"
                                                 data-status-content-length="Il server non supporta file così grandi"
                                                 data-status-unknown-response="Errore sconosciuto, <NAME_EMAIL>"
                                                 data-status-upload-success="Immagine salvata">

                                                {% if subcategory.imageIds[0] is not empty %}
                                                    <img src="{{ paths('IMAGE') }}?oid={{ subcategory.imageIds[0] }}" alt=""/>
                                                {% endif %}
                                                <input type="file" id="cropper" name="uploaded-files" data-show-caption="false" data-show-remove="true">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Icona:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="icon" class="form-control maxlength" maxlength="50" placeholder="Codice icona" value="{{ subcategory.icon }}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Codice:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="code" class="form-control maxlength" maxlength="50" placeholder="Codice sottocategoria" value="{{ subcategory.code }}" required  {{ subcategory.id is not empty ? 'readonly' : '' }}>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Area:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="select" name="area" value="{{ subcategory.area }}" required>
                                            <option value="">-</option>
                                            {% if areaList is not empty %}
                                                {% for area in areaList %}
                                                    <option {{ area.code == subcategory.area ? 'selected' : '' }} value="{{ area.code }}" >{{ area.title }}</option>
                                                {% endfor %}
                                            {% endif %}
                                        </select>
                                    </div>
                                </div>
                            </div>                            
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Categoria:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="select" name="category" value="{{ subcategory.category }}" required>
                                            <option value="">-</option>
                                            {% if categoryList is not empty %}
                                                {% for category in categoryList %}
                                                    <option {{ category.code == subcategory.category ? 'selected' : '' }} value="{{ category.code }}" >{{ decode('area', category.area) }} - {{ category.title }}</option>
                                                {% endfor %}
                                            {% endif %}
                                        </select>
                                    </div>
                                </div>
                            </div>                            
                            <div class="tabbable">
                                <div class="form-group no-margin-bottom">
                                    <label class="col-lg-3 control-label">Inserisci sottocategoria in:</label>
                                    <div class="col-lg-9">
                                        <ul class="nav nav-tabs language bg-slate-300 nav-tabs-component">                                                                                        
                                            <li class="active" id="tab-it"><a href="#it" data-toggle="tab">Italiano</a></li>
                                            <li id="tab-en"><a href="#en" data-toggle="tab">English</a></li>                                            
                                        </ul>
                                    </div>                                            
                                </div>
                                <div class="tab-content">
                                    <div class="tab-pane active" id="it">
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Titolo:</label>
                                            <div class="col-lg-9">
                                                <input type="text" name="title" class="form-control maxlength" maxlength="50" placeholder="Titolo della sottocategoria" value="{{ subcategory.title }}" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Descrizione:</label>
                                            <div class="col-lg-9">
                                                <div class="form-group">
                                                    <textarea cols="18" rows="5" name="description" class="wysihtml5 wysihtml5-min form-control" placeholder="Inserisci il testo ...">
                                                        {{ subcategory.description }}
                                                    </textarea>
                                                </div>                                    
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="tab-pane" id="en">
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Titolo inglese:</label>
                                            <div class="col-lg-9">
                                                <input type="text" name="titleEnglish" class="form-control maxlength" maxlength="50" placeholder="Titolo inglese della sottocategoria" value="{{ subcategory.titleEnglish }}" required>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Descrizione inglese:</label>
                                            <div class="col-lg-9">
                                                <div class="form-group">
                                                    <textarea cols="18" rows="5" name="descriptionEnglish" class="wysihtml5 wysihtml5-min form-control" placeholder="Inserisci il testo in inglese...">
                                                        {{ subcategory.descriptionEnglish }}
                                                    </textarea>
                                                </div>                                    
                                            </div>
                                        </div>
                                    </div>    
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Gruppo:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="group" class="form-control maxlength" maxlength="50" placeholder="Es. Audio\Video" value="{{ subcategory.group }}">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Ordinamento:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="sorting" class="form-control maxlength" maxlength="50" placeholder="Es. 01" value="{{ subcategory.sorting }}">
                                </div>
                            </div>
                        </div>
                        <div class="panel-footer has-visible-elements">
                            <div class="heading-elements visible-elements">
                                <span class="heading-text text-semibold">Azioni:</span>
                                <div class="pull-right">
                                    <button type="button" class="btn heading-btn btn-default btn-cancel">Annulla</button>
                                    {% if subcategory.id is not empty %}                                    
                                        <button id="btn-delete" type="button" class="btn heading-btn btn-danger">Elimina</button>
                                    {% endif %}
                                    <button id="btn-save" type="submit" class="btn heading-btn btn-primary">Salva<i class="icon-arrow-right14"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <!-- /basic layout -->
            </div>
        </div>
    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

{% endblock %}
