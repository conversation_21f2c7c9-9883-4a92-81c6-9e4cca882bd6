{% extends "be/include/base.html" %}

{% block extrahead %}
    <title>Profilo</title>
    <link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet">
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script> 
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/localization/messages_it.js"></script>
    <script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
    <script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/profile.js?{{ buildNumber }}"></script>
    
{% endblock %}

{% block content %}
    <!-- Page content -->
    <div class="page-content">

        <!-- Main content -->
        <div class="content-wrapper">

            <!-- User profile -->
            <div class="row">
                <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                    {% if success %}
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-success">
                                <a href="{{ paths('PROFILE') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                                <span>Dati memorizzati correttamente</span>
                            </div>
                        </div>
                    </div>
                    {% elseif error %}
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-danger">
                                <a href="{{ paths('PROFILE') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                                <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Profile info -->
                    <form id="form-edit-profile" class="form-horizontal form-validate-jquery" method="post" action="{{ paths('PROFILE_SAVE') }}?oid={{ user.id }}">
                        <div class="panel panel-white">
                            <div class="panel-heading">
                                <h5 class="panel-title text-bold">Informazioni</h5>                                        
                            </div>

                            <div class="panel-body">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Foto profilo:</label>
                                    <div class="col-lg-9">
                                        <div class="row">
                                            <div class="col-xs-6 col-xs-offset-3 col-sm-4 col-sm-offset-4 col-md-4 col-md-offset-4">
                                                <div class="slim"
                                                     data-max-file-size="5"
                                                     data-save-initial-image="{{ user.imageId is not empty ? 'true' : 'false'}}"
                                                     data-push="false"
                                                     data-post="output"
                                                     data-label="Carica un'immagine"
                                                     data-label-loading="Caricamento immagine..."                                                     
                                                     data-button-edit-label="Modifica"
                                                     data-button-remove-label="Elimina"
                                                     data-button-download-label="Scarica"
                                                     data-button-upload-label="Carica"
                                                     data-button-rotate-label="Ruota"
                                                     data-button-cancel-label="Cancella"
                                                     data-button-confirm-label="Conferma"
                                                     data-status-file-size="Il file è troppo grande, il massimo consentito è $0 MB"
                                                     data-status-file-type="Formato immagine non valido, formati consentiti: $0"
                                                     data-status-no-support="Il tuo browser non supporta la modifica dell'immagine"
                                                     data-status-image-too-small="Immagine troppo piccola, risoluzione minima: $0 pixel"
                                                     data-status-content-length="Il server non supporta file così grandi"
                                                     data-status-unknown-response="Errore sconosciuto, <NAME_EMAIL>"
                                                     data-status-upload-success="Immagine salvata"
                                                     data-ratio="1:1">

                                                    {% if user.imageId is not empty %}
                                                    <img src="{{ paths('IMAGE') }}?oid={{ user.imageId }}" alt=""/>
                                                    {% endif %}
                                                    <input type="file" id="cropper" name="uploaded-files" data-show-caption="false" data-show-remove="true">
                                                </div>
                                            </div>
                                        </div>
                                    </div> 
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Nome:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="name" class="form-control maxlength" maxlength="50" placeholder="Nome" value="{{ user.name }}">
                                    </div>
                                </div>
                                <legend class="text-bold">
                                    <i class="icon-key"></i>
                                    CREDENZIALI
                                    <a class="control-arrow" data-toggle="collapse" data-target="#credential">
                                        <i class="icon-circle-down2"></i>
                                    </a>
                                </legend>
                                <div class="collapse in" id="credential">
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Email:</label>
                                        <div class="col-lg-9">
                                            <input type="email" id="username" name="username" class="form-control" value="{{ user.username }}" required disabled>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Password:</label>
                                        <div class="col-lg-9">
                                            <input type="password" name="password" class="form-control" placeholder="Password" value="{{ user.password }}" required>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Conferma password:</label>
                                        <div class="col-lg-9">
                                            <input type="password" name="password-confirm" class="form-control" placeholder="Conferma password" value="{{ user.password }}" required data-parsley-equalto="#password">
                                        </div>
                                    </div>                                            
                                </div>
                                                        
                            </div>
                            <div class="panel-footer has-visible-elements">
                                <div class="heading-elements visible-elements">
                                    <span class="heading-text text-semibold">Azioni:</span>
                                    <div class="pull-right">                                        
                                        <button type="button" class="btn heading-btn btn-default btn-cancel">Annulla</button>
                                        <button type="submit" class="btn heading-btn btn-primary">Aggiorna informazioni <i class="icon-arrow-right14"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- /profile info -->
                    </form>
                </div>

            </div>
            <!-- /user profile -->

        </div>
        <!-- /main content -->

    </div>
    <!-- /page content -->
    <script>
        $(function () {
            $('.btn-cancel').click(function () {
                $.confirm({
                    theme: 'supervan',
                    escapeKey: true,
                    animation: 'top',
                    closeAnimation: 'bottom',
                    backgroundDismiss: true,
                    title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
                    content: "Le modifiche fatte fin'ora verranno perse.",
                    buttons: {
                        annulla: {
                            btnClass: 'btn-default',
                            action: function () {
                                //
                            }
                        },
                        conferma: {
                            btnClass: 'bg-agora',
                            action: function () {
                                window.location.href = "{{ paths('PROFILE') }}";
                            }
                        }
                    }
                });
            });
        });
    </script>
{% endblock %}
