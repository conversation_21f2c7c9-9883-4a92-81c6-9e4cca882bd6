{% extends "be/include/base.html" %}
{% set pageActive = 'pages' %}
{% block extrahead %}
    <title>Pagine</title>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment_locales.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/datatables.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/jszip/jszip.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/pages.js?{{ buildNumber }}"></script>
{% endblock %}

{% block sidebarcontrol %}
    <ul class="fab-menu fab-menu-top-left hidden-xs" data-fab-toggle="click">
        <li>
            <a class="fab-menu-btn btn btn-default btn-float btn-rounded btn-icon sidebar-control sidebar-main-hide">
                <i class="icon-paragraph-justify3"></i>
            </a>
        </li>
    </ul>
{% endblock %}

{% block content %}

    <!-- actions -->
<a id="pageRemoveUri" style="display: none;" href="{{ paths('BE_PAGE_REMOVE') }}?pageId=" rel="nofollow"></a>
<a id="pagesSuccessUri" style="display: none;" href="{{ paths('BE_PAGES') }}/ok" rel="nofollow"></a>
<a id="dataUri" style="display: none;" href="{{ paths('BE_PAGES_DATA') }}" rel="nofollow"></a>

<!-- js deposit -->
<div id="startDate" style="display: none">{{ startDate | date("yyyy-MM-dd") }}</div>
<div id="endDate" style="display: none">{{ endDate | date("yyyy-MM-dd") }}</div>

<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- LEFT SIDEBAR -->
    <div class="sidebar sidebar-main sidebar-default sidebar-separate">
        <div class="sidebar-content">

            <!-- Filter -->
            <div class="sidebar-category">
                <div class="category-title">
                    <span>Filtra pagine</span>
                    <ul class="icons-list">
                        <li><a href="#" data-action="collapse"></a></li>
                    </ul>
                </div>

                <div class="category-content">
                    <form>
                        {% if (user.profileType == 'system') or (user.profileType == 'admin') %}
                            <div class="form-group">
                                <legend class="text-size-mini text-muted no-border no-padding">Ghost page</legend>
                                <div class="">
                                    <div class="checkbox">
                                        <label class="display-block text-capitalize">
                                            <input type="checkbox" class="styled page-ghost-filter" page-ghost-value="true" {{ selectedGhosts contains 'true' ? 'checked' : ''}}>
                                            Si
                                        </label>
                                    </div>
                                    <div class="checkbox">
                                        <label class="display-block text-capitalize">
                                            <input type="checkbox" class="styled page-ghost-filter" page-ghost-value="false" {{ selectedGhosts contains 'false' ? 'checked' : ''}}>
                                            No
                                        </label>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                        {% if provinceList is not empty %}
                            <div class="form-group">
                                <legend class="text-size-mini text-muted no-border no-padding">Provincia</legend>
                                <div class="has-scroll">
                                    {% for province in provinceList %}
                                        <div class="checkbox">
                                            <label class="display-block text-capitalize">
                                                <input type="checkbox" class="styled page-province-filter" page-province-value="{{ province.provinceCode }}" {{ selectedProvinces contains province.provinceCode ? 'checked' : ''}}>
                                                {{ decode("province", province.provinceCode) }}
                                            </label>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                        <button id="filter-apply" type="button" class="btn btn-primary btn-block"><i class="icon-filter3 position-left"></i>Filtra</button>
                    </form>
                </div>
            </div>
            <!-- /categories -->

        </div>
    </div>
    <!-- END LEFT SIDEBAR -->

    <!-- MAIN CONTENT -->
    <div class="content-wrapper">
        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('BE_PAGES') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('BE_PAGES') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}


        <!-- PAGES -->
        <div class="panel panel-white">
            <div class="panel-heading has-visible-elements">
                <h5 class="panel-title text-bold">Pagine</h5>
                {% if (user.profileType == 'system') or (user.profileType == 'admin') %}
                    <div class="heading-elements visible-elements">                        
                        <button type="button" class="btn heading-btn btn-default daterange-predefined">
                            <i class="icon-calendar22 position-left"></i>
                            <span></span>
                            <b class="caret"></b>
                        </button>
                    </div>
                {% endif %}
            </div>
            
            <table class="table datatable-posts">
                <thead>
                    <tr>
                        <th>Immagine</th>
                        <th>Inserito da</th>
                        <th>Nome</th>
                        <th>Link</th>
                        <th>Tipologia</th>
                        <th>Indirizzo</th>
                        <th>Data inserimento</th>
                        <th>Identificativo</th>
                        <th>Azioni</th>
                        <th></th>
                    </tr>
                </thead>
                
            </table>
        </div>
        <!-- /state saving -->

    </div>
    <!-- /main content -->
</div>
<!-- /page content -->
{% endblock %}