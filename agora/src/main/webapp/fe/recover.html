{% extends "fe/include/base-no-header.html" %}

{% block title %}{{ label('forgot.title.meta') | raw }} | Agorapp{% endblock %}

{% block canonical %}    
    <meta name="description" content="{{ label('forgot.description.meta') | raw }}">
    <link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block content %}
    <a id="homeUri" style="display: none" href="{{ paths('HOME') }}" rel="nofollow"></a>
    <a id="loginUri" style="display: none" href="{{ paths('ACCESS') }}" rel="nofollow"></a>
    <a id="forgotSendUri" style="display: none" href="{{ paths('RECOVER_SEND') }}" rel="nofollow"></a>
    
    <!-- Container START -->
    <div class="container">
        <div class="row justify-content-center align-items-center vh-100 py-5">
            <!-- Main content START -->
            <div class="col-sm-10 col-md-8 col-lg-7 col-xl-6 col-xxl-5">
                <div class="text-center">
                    <a href="{{ paths('HOME') }}"><img class="mb-3" src="{{ contextPath }}/fe/images/logo.svg" width="150" alt="Agorapp"></a>
                </div>
                <!-- Forgot password START -->
                <div class="card card-body p-4 p-sm-5 border-lg">
                    <!-- Title -->
                    <h1 class="mb-2">{{ label('password.recovery') | raw }}</h1>
                    <p>{{ label('forgot.receive.instructions') | raw }}</p>
                    <!-- form START -->
                    <form class="mt-3" id="form-recover" method="post" novalidate="novalidate">                    
                        <!-- Email -->
                        <div class="mb-3 input-group-lg">
                            <input type="email" class="form-control" name="email" id="username" placeholder="{{ label('common.insert.email') | raw }}" aria-label="{{ label('common.insert.email') | raw }}" required>                            
                        </div>
                        <!-- Back to sign in -->
                        <div class="mb-3 text-center">
                            <p>{{ label('common.return.at') | raw }} <a href="{{ paths('ACCESS') }}">Login</a></p>
                        </div>
                        <!-- Button -->
                        <div class="d-grid "><button type="submit" class="btn btn-lg btn-primary">{{ label('password.recovery') | raw }}</button></div>
                        <!-- Copyright -->
                        <p class="mb-0 mt-3 text-center">{{ label('common.copyright') | raw }}</p>
                    </form>
                    <!-- form END -->
                </div>
                <!-- Forgot password END -->
            </div>
        </div> <!-- Row END -->
    </div>
    <!-- Container END -->
    
{% endblock %}
{% block pagescripts %}
    <script src="{{ contextPath }}/fe/js/pages/recover.js?{{ buildNumber }}"></script>
{% endblock %}
