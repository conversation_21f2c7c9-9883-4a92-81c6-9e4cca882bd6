{% extends "fe/include/base.html" %}

{% set metaDescription = '' %}

{% block title %}{{ label('results.title.meta') | raw }} | Agorapp{% endblock %}

{% block canonical %}
    
    <meta name="description" content="{{ metaDescription }}">
    <link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block socialcards %}
    <meta property="og:url"                content="{{ publicUrl }}" />
    <meta property="og:type"               content="website" />
    <meta property="og:title"              content="{{ label('results.title.meta') | raw }} | Agorapp" />
    <meta property="og:description"        content="{{ metaDescription }}" />
    <meta property="og:image"              content="" />
    <meta property="og:image:width"        content="1200" />
    <meta property="og:image:height"       content="630" />
    <meta property="og:image:alt"          content="{{ label('results.title.meta') | raw }} | Agorapp" />
{% endblock %}
{% block pagecss %}
    <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/tiny-slider/dist/tiny-slider.css">
    <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/zuck.js/dist/zuck.min.css">
{% endblock %}
{% block content %}
    <!-- some hidden stuff -->
    <div id="pagination" style="display: none;">{{ pagination }}</div>
    <a id="homeUri" style="display: none" href="{{ paths('HOME') }}" rel="nofollow"></a>
    <a id="pageFollowToggleUri" style="display: none" href="{{ paths('PAGE_FOLLOWER_TOGGLE') }}" rel="nofollow"></a>
    <a id="eventFollowToggleUri" style="display: none" href="{{ paths('EVENT_FOLLOWER_TOGGLE') }}" rel="nofollow"></a>  
        
    <!-- CLIPBOARD TOAST -->
    {% include "fe/include/snippets/clipboard-toast.html" %}

    <!-- Container START -->
    <div class="container">
        <div class="row g-0">

            <!-- Sidenav START -->
            <div class="col-lg-3">
                {% include "fe/include/snippets/sidenav-results-left.html" %}
            </div>
            <!-- Sidenav END -->

            <!-- Main content START -->
            <div id="resultsAll" class="col-md-8 col-lg-6 vstack g-0 mt-3">  
                
                <!-- Card feed item START -->
                {% if pageList is not empty or eventList is not empty %}
                    <div class="containerResults">
                        {% if q == 'all' %}
                            <div class="resultsEntity">
                                {% for entry in pageList %}
                                    <div class="card border border-agora {{ pageList.size > 1 and not loop.last ? 'border-bottom-0' : '' }}">
                                        <div class="card-header border-0">

                                            <div class="d-flex flex-column flex-sm-row align-items-start">
                                                <div class="d-flex flex-nowrap flex-fill">
                                                    <div class="avatar avatar-lg me-3 flex-shrink-0">
                                                        <a href="{{ paths('PAGE_BASE') }}/{{ entry.page.identifier }}" class="resultslink" data-results-back="resultslink-{{ entry.page.identifier }}">                                                         
                                                            {% if entry.page.profileImageId is not empty %}
                                                                <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ entry.page.profileImageId }}" alt="{{ entry.page.name }}"> 
                                                            {% else %}
                                                                <img class="avatar-img border border-white border-3" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ entry.page.name }}"> 
                                                            {% endif %}
                                                        </a>
                                                    </div>
                                                    <!-- Info -->
                                                    <div class="flex-grow-1 mb-2">
                                                        <div class="d-sm-flex align-items-start">
                                                            <h6 class="mb-0"><a href="{{ paths('PAGE_BASE') }}/{{ entry.page.identifier }}" class="resultslink" data-results-back="resultslink-{{ entry.page.identifier }}">{{ entry.page.name }} </a></h6>
                                                        </div>
                                                        <ul class="nav nav-stack small gap-0 gap-sm-2">
                                                            <li class="nav-item">
                                                                <i class="bi bi-person-vcard-fill pe-1"></i> {{ decode('category', entry.page.category) }}
                                                            </li>
                                                            {% if entry.page.city is not empty %}
                                                            <li class="nav-item">
                                                                <i class="bi bi-geo-alt pe-1"></i> {{ entry.page.city }}
                                                            </li>
                                                            {% endif %}                                                          
                                                            {% if entry.followerCount > 0 and (entry.page.showFollowers == true) %}
                                                                <li class="nav-item">
                                                                    <i class="bi bi-people pe-1"></i> {{ entry.followerCount }} follower
                                                                </li>                                                                                                                            
                                                            {% endif %}
                                                        </ul>
                                                        {% if entry.page.tags is not empty %}
                                                            <ul class="nav nav-stack small gap-0 gap-sm-2">
                                                                <li class="nav-item">
                                                                    <i class="bi bi-tags-fill"></i>
                                                                    {% for tag in entry.page.tags %}
                                                                        {{ tag | lower }}
                                                                        {% if not loop.last %}, {% endif %}
                                                                    {% endfor %}
                                                                </li>
                                                            </ul>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-start justify-content-md-end order-3 mt-2 mt-md-0 ">
                                                    <a class="btn btn-primary-soft btn-sm mb-0 me-2 text-nowrap resultslink" href="{{ paths('PAGE_BASE') }}/{{ entry.page.identifier }}" data-results-back="resultslink-{{ entry.page.identifier }}"><i class="bi bi-box-arrow-up-right pe-1"></i> {{ label('common.view') | raw }}</a>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Card feed item END -->         
                                {% endfor %}
                                {% for entry in eventList %}
                                    <div class="card border border-agora {{ eventList.size > 1 and not loop.last ? 'border-bottom-0' : '' }}">
                                        <div class="card-header border-0">
                                            <!-- Connections Item -->
                                            <div class="d-flex flex-column flex-sm-row align-items-start">                                                
                                                <div class="d-flex flex-nowrap flex-fill">
                                                    <div class="avatar avatar-lg me-3 mb-3 mb-md-0">
                                                        <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="resultslink" data-results-back="resultslink-{{ entry.event.identifier }}"> 

                                                            {% if entry.event.coverImageId is not empty %}
                                                                <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ entry.event.coverImageId }}" alt="{{ entry.event.name }}">
                                                            {% else %}
                                                                <img class="avatar-img border border-white border-3" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="{{ entry.event.name }}"> 
                                                            {% endif %}                                                                                                                                                                                                                                                                                                                              

                                                        </a>
                                                    </div>
                                                    <!-- Info -->
                                                    <div class="w-100">
                                                        <div class="d-sm-flex align-items-start">
                                                            <h6 class="mb-0"><a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="resultslink" data-results-back="resultslink-{{ entry.event.identifier }}">{{ entry.event.name }} </a></h6>
                                                        </div>
                                                        <ul class="nav nav-stack small gap-0 gap-sm-2">
                                                            <li class="nav-item me-2">

                                                                <i class="bi bi-calendar-check"></i> {{ formatDate(entry.event.startDate, "EEE d MMM yyyy", language) | capitalize }} {{ entry.event.startHour }}
                                                            </li>
                                                            <li class="nav-item me-2">
                                                                <i class="bi bi-geo-alt"></i> {{ entry.event.city }}
                                                            </li>
                                                            {% if entry.followerCount > 0 and (entry.event.showFollowers == true) %}
                                                                <li class="nav-item me-2">
                                                                    <i class="bi bi-people"></i>
                                                                    {% if entry.followerCount > 1 %}
                                                                        {{ entry.followerCount }} {{ label('common.interested') | raw }}
                                                                    {% else %}
                                                                        {{ entry.followerCount }} {{ label('common.interested.sing') | raw }}
                                                                    {% endif %}
                                                                </li>
                                                            {% endif %}
                                                        </ul>
                                                        {% if entry.event.tags is not empty %}
                                                            <ul class="nav nav-stack small gap-0 gap-sm-2">
                                                                <li class="nav-item">
                                                                    <i class="bi bi-tags-fill"></i>
                                                                {% for tag in entry.event.tags %}
                                                                        {{ tag | lower }}{% if not loop.last %}, {% endif %}
                                                                {% endfor %}
                                                                </li>
                                                            </ul>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-start justify-content-md-end order-3 mt-2 mt-md-0">
                                                    <a class="btn btn-primary-soft btn-sm mb-0 me-2 text-nowrap resultslink" href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" data-results-back="resultslink-{{ entry.event.identifier }}"><i class="bi bi-box-arrow-up-right pe-1"></i>{{ label('common.view') | raw }}</a>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    <!-- Card feed item END -->         
                                {% endfor %}
                            </div>
                            <div class="row m-t-sm">
                                <div class="col-xs-12 text-center">
                                    <div id="loading" class="spinner">
                                        <img src="{{ contextPath }}/fe/images/loader.svg" width="64" alt="loading...">
                                    </div>
                                </div>
                            </div>                
                        {% endif %}
                        {% if q == 'pages' %}
                            <div class="resultsEntity">
                                {% for entry in pageList %}
                                    <div class="card border border-agora {{ pageList.size > 1 and not loop.last ? 'border-bottom-0' : '' }}">
                                        <div class="card-header border-0">

                                            <div class="d-flex flex-column flex-sm-row align-items-start">
                                                <div class="d-flex flex-nowrap flex-fill">
                                                    <div class="avatar avatar-lg me-3 flex-shrink-0">
                                                        <a href="{{ paths('PAGE_BASE') }}/{{ entry.page.identifier }}" class="resultslink" data-results-back="resultslink-{{ entry.page.identifier }}">                                                         
                                                            {% if entry.page.profileImageId is not empty %}
                                                                <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ entry.page.profileImageId }}" alt="{{ entry.page.name }}"> 
                                                            {% else %}
                                                                <img class="avatar-img border border-white border-3" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ entry.page.name }}"> 
                                                            {% endif %}
                                                        </a>
                                                    </div>
                                                    <!-- Info -->
                                                    <div class="flex-grow-1 mb-2">
                                                        <div class="d-sm-flex align-items-start">
                                                            <h6 class="mb-0"><a href="{{ paths('PAGE_BASE') }}/{{ entry.page.identifier }}" class="resultslink" data-results-back="resultslink-{{ entry.page.identifier }}">{{ entry.page.name }} </a></h6>
                                                        </div>
                                                        <ul class="nav nav-stack small gap-0 gap-sm-2">
                                                            <li class="nav-item">
                                                                <i class="bi bi-person-vcard-fill pe-1"></i> {{ decode('category', entry.page.category) }}
                                                            </li>
                                                            {% if entry.page.city is not empty %}
                                                            <li class="nav-item">
                                                                <i class="bi bi-geo-alt pe-1"></i> {{ entry.page.city }}
                                                            </li>
                                                            {% endif %}                                                          
                                                            {% if entry.followerCount > 0 and (entry.page.showFollowers == true) %}
                                                                <li class="nav-item">
                                                                    <i class="bi bi-people pe-1"></i> {{ entry.followerCount }} follower
                                                                </li>                                                                                                                            
                                                            {% endif %}
                                                        </ul>
                                                        {% if entry.page.tags is not empty %}
                                                            <ul class="nav nav-stack small gap-0 gap-sm-2">
                                                                <li class="nav-item">
                                                                    <i class="bi bi-tags-fill"></i>
                                                                    {% for tag in entry.page.tags %}
                                                                        {{ tag | lower }}
                                                                        {% if not loop.last %}, {% endif %}
                                                                    {% endfor %}
                                                                </li>
                                                            </ul>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-start justify-content-md-end order-3 mt-2 mt-md-0">
                                                    <a class="btn btn-primary-soft btn-sm mb-0 me-2 text-nowrap resultslink" href="{{ paths('PAGE_BASE') }}/{{ entry.page.identifier }}" data-results-back="resultslink-{{ entry.page.identifier }}"><i class="bi bi-box-arrow-up-right pe-1"></i> {{ label('common.view') | raw }}</a>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Card feed item END -->         
                                {% endfor %}
                            </div>
                            <div class="row m-t-sm">
                                <div class="col-xs-12 text-center">
                                    <div id="loading" class="spinner">
                                        <img src="{{ contextPath }}/fe/images/loader.svg" width="64" alt="loading...">
                                    </div>
                                </div>
                            </div>                
                        {% endif %}
                        {% if q == 'events' %}
                            <div class="resultsEntity">
                                {% for entry in eventList %}
                                    <div class="card border border-agora {{ eventList.size > 1 and not loop.last ? 'border-bottom-0' : '' }}">
                                        <div class="card-header border-0">
                                            <!-- Connections Item -->
                                            <div class="d-flex flex-column flex-sm-row align-items-start">
                                                <div class="d-flex flex-nowrap flex-fill">
                                                    <div class="avatar avatar-lg me-3 mb-3 mb-md-0">
                                                        <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="resultslink" data-results-back="resultslink-{{ entry.event.identifier }}"> 

                                                            {% if entry.event.coverImageId is not empty %}
                                                                <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ entry.event.coverImageId }}" alt="{{ entry.event.name }}">
                                                            {% else %}
                                                                <img class="avatar-img border border-white border-3" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="{{ entry.event.name }}"> 
                                                            {% endif %}                                                                                                                                                                                                                                                                                                                              

                                                        </a>
                                                    </div>
                                                    <!-- Info -->
                                                    <div class="w-100">
                                                        <div class="d-sm-flex align-items-start">
                                                            <h6 class="mb-0"><a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="resultslink" data-results-back="resultslink-{{ entry.event.identifier }}">{{ entry.event.name }} </a></h6>
                                                        </div>
                                                        <ul class="nav nav-stack small gap-0 gap-sm-2">
                                                            <li class="nav-item me-2">

                                                                <i class="bi bi-calendar-check"></i> {{ formatDate(entry.event.startDate, "EEE d MMM yyyy", language) | capitalize }} {{ entry.event.startHour }}
                                                            </li>
                                                            <li class="nav-item me-2">
                                                                <i class="bi bi-geo-alt"></i> {{ entry.event.city }}
                                                            </li>
                                                            {% if entry.followerCount > 0 and (entry.event.showFollowers == true) %}
                                                                <li class="nav-item me-2">
                                                                    <i class="bi bi-people"></i>
                                                                    {% if entry.followerCount > 1 %}
                                                                        {{ entry.followerCount }} {{ label('common.interested') | raw }}
                                                                    {% else %}
                                                                        {{ entry.followerCount }} {{ label('common.interested.sing') | raw }}
                                                                    {% endif %}
                                                                </li>
                                                            {% endif %}
                                                        </ul>
                                                        {% if entry.event.tags is not empty %}
                                                            <ul class="nav nav-stack small gap-0 gap-sm-2">
                                                                <li class="nav-item">
                                                                    <i class="bi bi-tags-fill"></i>
                                                                {% for tag in entry.event.tags %}
                                                                        {{ tag | lower }}{% if not loop.last %}, {% endif %}
                                                                {% endfor %}
                                                                </li>
                                                            </ul>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-start justify-content-md-end order-3 mt-2 mt-md-0">
                                                    <a class="btn btn-primary-soft btn-sm mb-0 me-2 text-nowrap resultslink" href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" data-results-back="resultslink-{{ entry.event.identifier }}"><i class="bi bi-box-arrow-up-right pe-1"></i>{{ label('common.view') | raw }}</a>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    <!-- Card feed item END -->         
                                {% endfor %}
                            </div>
                            <div class="row m-t-sm">
                                <div class="col-xs-12 text-center">
                                    <div id="loading" class="spinner">
                                        <img src="{{ contextPath }}/fe/images/loader.svg" width="64" alt="loading...">
                                    </div>
                                </div>
                            </div>                
                        {% endif %}

                        <!-- Card feed item END -->
                    </div>
                {% else %}
                    {% if q == 'all' %}
                        <div class="card ms-3 mt-3 me-3 mb-3 mb-md-0">
                                {{ label('common.no.results') | raw }}
                        </div>
                        {% if user.profileType != 'unconfirmed' %}
                            <div class="card ms-3 mt-3 me-3 mb-3 mb-md-0">
                                <a class="btn btn-primary" href="{{ paths('PAGE_ADD') }}?name={{ text }}"> <i class="fa-solid fa-plus pe-1"></i> {{ label('account.pages.create') | raw }} {{ text }}</a>
                            </div>
                        {% endif %}
                    {% elseif q == 'pages' %}
                        <div class="card ms-3 mt-3 me-3 mb-3 mb-md-0">
                                {{ label('common.pages.no.results') | raw }}
                        </div>
                        {% if user.profileType != 'unconfirmed' %}
                            <div class="card ms-3 mt-3 me-3 mb-3 mb-md-0">
                                <a class="btn btn-primary" href="{{ paths('PAGE_ADD') }}?name={{ text }}"> <i class="fa-solid fa-plus pe-1"></i> {{ label('account.pages.create') | raw }} {{ text }}</a>
                            </div>
                        {% endif %}
                    {% elseif q == 'events' %}
                        <div class="card ms-3 mt-3 me-3 mb-3 mb-md-0">
                                {{ label('common.events.no.results') | raw }}
                        </div>
                    {% endif %}
                {% endif %}
                
                <div class="pager">
                    {% if resultUrl contains '?' %}
                        {% if skip > 12 %}
                            <a href="{{ resultUrl }}&skip={{ skip - limit }}&limit={{ limit }}" class="pager__prev">&leftarrow; {{ label('common.prev.article') | raw }}</a>
                            {% if loadmore %}
                                <a href="{{ resultUrl }}&skip={{ skip + limit }}&limit={{ limit }}" class="pager__next">{{ label('common.next.article') | raw }} &rightarrow;</a>
                            {% endif %}
                        {% else %}
                            {% if skip == 12 %}
                                <a href="{{ resultUrl }}" class="pager__prev">&leftarrow; {{ label('common.prev.article') | raw }}</a>
                            {% endif %}
                            {% if loadmore %}
                            <a href="{{ resultUrl }}&skip={{ skip + limit }}&limit={{ limit }}" class="pager__next">{{ label('common.next.article') | raw }} &rightarrow;</a>
                            {% endif %}
                        {% endif %}
                    {% else %}
                        {% if skip > 12 %}
                            <a href="{{ resultUrl }}?skip={{ skip - limit }}&limit={{ limit }}" class="pager__prev">&leftarrow; {{ label('common.prev.article') | raw }}</a>
                            {% if loadmore %}
                                <a href="{{ resultUrl }}?skip={{ skip + limit }}&limit={{ limit }}" class="pager__next">{{ label('common.next.article') | raw }} &rightarrow;</a>
                            {% endif %}
                        {% else %}
                            {% if skip == 12 %}
                                <a href="{{ resultUrl }}" class="pager__prev">&leftarrow; {{ label('common.prev.article') | raw }}</a>
                            {% endif %}
                            {% if loadmore %}
                            <a href="{{ resultUrl }}?skip={{ skip + limit }}&limit={{ limit }}" class="pager__next">{{ label('common.next.article') | raw }} &rightarrow;</a>
                            {% endif %}
                        {% endif %}                    
                    {% endif %}
                </div>
            </div>
            <!-- Main content END -->
        </div> <!-- Row END -->
    </div>
    <!-- Container END -->
{% endblock %}       

{% block pagescripts %}     
    <script src="{{ contextPath }}/fe/js/pages/results.js?{{ buildNumber }}"></script>
{% endblock %}