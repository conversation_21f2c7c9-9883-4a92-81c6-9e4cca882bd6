{% extends "fe/include/base.html" %}

{% set metaDescription = label('home.description.meta') | raw %}

{% block title %}{{ label('home.title.meta') | raw }} | Agorapp{% endblock %}

{% block canonical %}
<meta name="robots" content="index, follow">
<meta name="description" content="{{ seoDescription }}">
<link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block socialcards %}
<meta property="og:url"                content="{{ publicUrl }}" />
<meta property="og:type"               content="website" />
<meta property="og:title"              content="{{ label('home.title.meta') | raw }} | Agorapp" />
<meta property="og:description"        content="{{ metaDescription }}" />
<meta property="og:image"              content="{{ contextPath }}/fe/images/about/cover.png" />
<meta property="og:image:width"        content="1200" />
<meta property="og:image:height"       content="630" />
<meta property="og:image:alt"          content="{{ label('home.title.meta') | raw }} | Agorapp" />
{% endblock %}

{% block pagecss %}
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/tiny-slider/dist/tiny-slider.css">
{% endblock %}

{% block content %}
<!-- some hidden stuff -->
<div id="pagination" style="display: none;">{{ pagination }}</div>
<a id="homeUri" style="display: none" href="{{ paths('HOME') }}" rel="nofollow"></a>
<a id="pageFollowToggleUri" style="display: none" href="{{ paths('PAGE_FOLLOWER_TOGGLE') }}" rel="nofollow"></a>
<a id="eventFollowToggleUri" style="display: none" href="{{ paths('EVENT_FOLLOWER_TOGGLE') }}" rel="nofollow"></a>  

<!-- CLIPBOARD TOAST -->
{% include "fe/include/snippets/clipboard-toast.html" %}

{% include "fe/include/snippets/modal-report-event.html" %}   

<!-- Container START -->
<div class="container">
    <div class="row">

        <!-- Sidenav START -->
        {% if user is not empty and user.profileType != 'unconfirmed' %}
        <div class="col-lg-3">
            {% include "fe/include/snippets/sidenav-left.html" %}
        </div>
        {% endif %}
        <!-- Sidenav END -->

        <!-- Main content START -->
        <div class="col-md-8 col-lg-6 vstack gap-4">  

            <!-- Card feed item START -->
            <div class="containerWallEvents">
                {% if user is not empty and user.profileType != 'unconfirmed' %}
                {% if not existCustomerNotification %}
                <div class="alert alert-agora alert-dismissible fade show mb-0 text-center" role="alert">
                    {{ label('alert.notifications.title') | raw }}<br>
                    <a href="{{ paths('ACCOUNT_NOTIFICATIONS') }}#customernotification-tab" class="btn btn-xs btn-primary ms-md-4 mt-2">{{ label('alert.notifications.cta') | raw }}</a>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Chiudi"></button>
                </div>
                {% endif %}
                {% endif %}

                {% if wallEventList is not empty %}
                <div class="wallEvent border-lg-lr">
                    {% for entry in wallEventList %}

                    {% set isVertical = false %}
                    {% if entry.event.coverImageId is not empty %}
                    {% set coverImage = get('documentdescriptor', entry.event.coverImageId) %}
                    {% if coverImage is not empty and coverImage.isVertical() %}
                    {% set isVertical = true %}
                    {% endif %}
                    {% endif %}

                    {% if isVertical == true %}
                    <div class="card border-lg-b">
                        <!-- Card header START -->
                        <div class="card-header border-0 pb-0">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <!-- Avatar -->
                                    {% set pageEvent = '' %}
                                    {% if entry.event.validPageIds is not empty %}
                                    {% set pageEvent = get('page', entry.event.validPageIds[0]) %}
                                    {% else %}
                                    {% set pageEvent = get('page', entry.event.pageIds[0]) %}
                                    {% endif %}

                                    <div class="avatar me-2">
                                        {% if pageEvent.profileImageId is not empty %}
                                        <a href="{{ paths('PAGE_BASE') }}/{{ pageEvent.identifier }}" class="walllink" data-wall-back="walllink-{{ entry.event.identifier }}"> <img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageEvent.profileImageId }}" alt="{{ pageEvent.name }}"> </a>
                                        {% else %}
                                        <a href="{{ paths('PAGE_BASE') }}/{{ pageEvent.identifier }}" class="walllink" data-wall-back="walllink-{{ entry.event.identifier }}"><img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ pageEvent.name }}"> </a>
                                        {% endif %}
                                    </div>

                                    <!-- Title -->
                                    <div>
                                        <h6 class="card-title mb-0"> <a href="{{ paths('PAGE_BASE') }}/{{ pageEvent.identifier }}" class="walllink" data-wall-back="walllink-{{ pageEvent.identifier }}"> {{ pageEvent.name }} {{ (entry.event.pageIds | length) > 1 ? (' ' + (label('common.other.pages') | raw) + ' ') : ''}} </a></h6>
                                        <ul class="avatar-group list-unstyled align-items-sm-center mb-0">
                                            {% if entry.walletExtraPages is not empty %}
                                            {% set extraPages = entry.walletExtraPages %}
                                            {% for extraPage in extraPages %}
                                            <li class="avatar avatar-xs">
                                                <a href="{{ paths('PAGE_BASE') }}/{{ extraPage.identifier }}" class="walllink" data-wall-back="walllink-{{ entry.event.identifier }}">
                                                    <img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ extraPage.profileImageId }}" title="{{ extraPage.name }}" onerror="this.src = '{{ contextPath }}/fe/images/avatar/placeholder.jpg'">
                                                </a>
                                            </li>
                                            {% endfor %}
                                            {% set extraPagesAmount = (entry.event.pageIds.size - entry.walletExtraPages.size) %}
                                            {% if extraPagesAmount > 1 %}
                                            <li class="avatar avatar-xs">
                                                <div class="avatar-img rounded-circle bg-primary"><span class="text-white position-absolute top-50 start-50 translate-middle">+{{ extraPagesAmount -1 }}</span></div>
                                            </li>
                                            {% endif %}
                                            {% endif %}
                                            <li class="small ms-3">
                                                {{ formatDate(entry.event.startDate, "EEE dd MMMM yyyy", language) | capitalize }}
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <!-- Card share action menu -->
                                <a href="#" class="text-secondary btn btn-secondary-soft-hover py-1 px-2" id="cardShareAction5" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-three-dots"></i>
                                </a>
                                <!-- Card share action dropdown menu -->
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="cardShareAction5">
                                    {% if user is not empty and user.profileType != 'unconfirmed' %}
                                    <li><a class="dropdown-item" href="#"> <i class="bi bi-person-x fa-fw pe-2"></i>{{ label('common.unfollow') | raw }} {{ pageEvent.name }}</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    {% endif %}
                                    <li><a class="dropdown-item" href="" data-bs-toggle="modal" data-eventId="{{ entry.event.id }}" data-bs-target="#modalReportEvent"> <i class="bi bi-flag fa-fw pe-2"></i>{{ label('common.report.post') | raw }}</a></li>
                                </ul>
                            </div>
                            <!-- Card share action END -->
                            <hr>
                        </div>
                        <!-- Card header START -->

                        <!-- Card body START -->
                        <div class="card-body pb-0 pt-0">
                            <p><b class="text-decoration-underline"><a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" data-wall-back="walllink-{{ entry.event.identifier }}" class="walllink h4 text-link">{{ entry.event.name }}</a></b></p>
                            <div class="d-flex flex-column flex-md-row gap-3">
                                <div class="flex-shrink-0 img-vertical">
                                    {% if entry.event.coverImageId is not empty %}
                                    <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="walllink" data-wall-back="walllink-{{ entry.event.identifier }}"> <img class="card-img pb-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ entry.event.coverImageId }}" alt="Post"></a>
                                    {% else %}
                                    {% if pageEvent.coverImageId is not empty %}
                                    <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="walllink" data-wall-back="walllink-{{ entry.event.identifier }}"> <img class="card-img pb-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageEvent.coverImageId }}" alt="Post"></a>
                                    {% else %}
                                    <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="walllink" data-wall-back="walllink-{{ entry.event.identifier }}"> <img class="card-img pb-3" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="Post"></a>
                                    {% endif %}
                                    {% endif %}
                                </div>
                                <div>
                                    <p>
                                        {% autoescape false %}{{ newline(entry.event.description) | striphtml | abbreviate(200) }}{% endautoescape %}
                                        <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" data-wall-back="walllink-{{ entry.event.identifier }}" class="text-link ms-2">{{ label('common.continue') | raw }}</a>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <!-- Card body END -->
                        <!-- Card Footer START -->
                        {% if entry.event.type is not empty and entry.event.type == 'container' %}
                        {% if entry.childEvents is not empty %}
                            <div class="oneEvent px-3">
                                {% for childEvent in entry.childEvents %}
                                <hr>
                                <!-- Events list END -->
                                <div class="row">
                                    <div class="d-sm-flex align-items-center ">
                                        <!-- Avatar -->
                                        <div class="avatar avatar-md">
                                            <a href="{{ paths('EVENT_BASE') }}/{{ childEvent.identifier }}" class="eventlink">
                                                {% if childEvent.coverImageId is not empty %}
                                                <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ childEvent.coverImageId }}" alt="{{ childEvent.name }}">
                                                {% else %}
                                                <img class="avatar-img border border-white border-3" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="{{ childEvent.name }}">
                                                {% endif %}
                                            </a>
                                        </div>
                                        <div class="ms-sm-4 mt-2 mt-sm-0">
                                            <!-- Info -->
                                            <small class="mb-1"><a href="{{ paths('EVENT_BASE') }}/{{ childEvent.identifier }}">{{ childEvent.name }}</a></small>
                                            <ul class="nav nav-stack small gap-2 row-gap-0">
                                                <li class="nav-item">
                                                    <i class="bi bi-calendar-check"></i> {{ formatDate(childEvent.startDate, "EEE d MMM yyyy", language) | capitalize }} {{ childEvent.startHour }}
                                                </li>
                                                <li class="nav-item">
                                                    <i class="bi bi-geo-alt"></i> {{ childEvent.city }}
                                                </li>
                                                {% if childEvent.followerCount > 0 and (childEvent.showFollowers == true) %}
                                                <li class="nav-item">
                                                    <i class="bi bi-people"></i>
                                                    {% if childEvent.followerCount > 1 %}
                                                    {{ childEvent.followerCount }} {{ label('common.interested') | raw }}
                                                    {% else %}
                                                    {{ childEvent.followerCount }} {{ label('common.interested.sing') | raw }}
                                                    {% endif %}
                                                </li>
                                                {% endif %}
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                                <div class="row text-center mt-3 mb-2 justify-content-center">
                                    <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="small">Vedi tutti gli eventi »</a>
                                </div>
                            </div>
                        {% endif %}
                        {% endif %}
                        <div class="card-footer py-3" id="whopagefollow-{{ entry.event.id }}">
                            <!-- Feed react START -->
                            <ul class="nav nav-fill nav-stack small nav-even">
                                {% if entry.event.type is empty or entry.event.type != 'container' %}
                                <li class="nav-item border-share">
                                    {% if entry.iFollow %}
                                    <a class="nav-link mb-0 active event-add-follow" data-reload-id="whopagefollow-{{ entry.event.id }}" data-value="active" data-user="{{ user is not empty ? (user.profileType == 'customer' or user.profileType == 'system' ? 'logged' : 'notconfirmed') : 'unlogged' }}" data-event-id="{{ entry.event.id }}" href="#!"> <i class="bi bi-heart-fill pe-1"></i>{{ entry.followerCount > 0 ? (label('common.like') | raw) + ' (' + entry.followerCount + ')' : (label('common.no.likes') | raw)  }}</a>
                                    {% else %}
                                    <a class="nav-link mb-0 active event-add-follow" data-reload-id="whopagefollow-{{ entry.event.id }}" data-value="inactive" href="#!" data-user="{{ user is not empty ? (user.profileType == 'customer' or user.profileType == 'system' ? 'logged' : 'notconfirmed') : 'unlogged' }}" data-event-id="{{ entry.event.id }}"> <i class="bi bi-heart pe-1"></i>{{ entry.followerCount > 0 ? (label('common.like') | raw) + ' (' + entry.followerCount + ')' : (label('common.no.likes') | raw) }}</a>
                                    {% endif %}
                                </li>
                                {% endif %}
                                <!-- Card share action dropdown START -->
                                <li class="nav-item dropdown">
                                    <a href="#" class="nav-link mb-0" id="cardShareAction6" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-reply-fill flip-horizontal ps-1"></i>{{ label('common.share') | raw }}
                                    </a>
                                    <!-- Card share action dropdown menu -->
                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="cardShareAction6">
                                        <li>
                                            <a class="dropdown-item" href="https://www.facebook.com/sharer/sharer.php?u={{ publicUrl }}" aria-label="Facebook">
                                                <i class="bi-facebook"></i> Facebook
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="https://api.whatsapp.com/send?text=Guarda%20questo%20evento%20su%20Agorapp!%20{{ publicUrl }}" aria-label="Whatsapp">
                                                <i class="bi-whatsapp"></i> Whatsapp
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="https://t.me/share/url?url={{ publicUrl }}&text=Guarda%20questo%20evento%20su%20Agorapp!" aria-label="Telegram">
                                                <i class="bi-telegram"></i> Telegram
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="https://twitter.com/intent/tweet?url={{ publicUrl }}&text=Guarda%20questo%20evento%20su%20Agorapp!" aria-label="Twitter" target="_blank" rel="noopener">
                                                <i class="bi-twitter"></i> Twitter
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="https://www.linkedin.com/sharing/share-offsite/?url={{ publicUrl }}" aria-label="LinkedIn" target="_blank" rel="noopener">
                                                <i class="bi-linkedin"></i> LinkedIn
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" data-clipboard-text="{{ publicUrl }}" id="copyToClipboardLink" aria-label="{{ label('common.copy.to.clipboard') | raw }}">
                                                <i class="bi-clipboard-check"></i> {{ label('common.copy.to.clipboard') | raw }}
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                            <!-- Feed react END -->
                        </div>
                        <!-- Card Footer END -->
                    </div>
                    <!-- Card feed item END -->

                    <!-- Card feed item START -->
                    {% if skip == 0 %}
                    {% set showwhopage = false %}
                    {% if (wallEventList | length) > 5 %}
                    {% if loop.index == 5 %}
                    {% set showwhopage = true %}
                    {% endif %}
                    {% else %}
                    {% if loop.last %}
                    {% set showwhopage = true %}
                    {% endif %}
                    {% endif  %}

                    {% if showwhopage %}
                    {% set whopageList = follow('whopage', customerEntry.customer.userId, customerEntry.customer.provinceCode, 10, 'random') %}
                    {% if whopageList is not empty %}
                    <div class="card border-lg-b">
                        <!-- Card header START -->
                        <div class="card-header d-flex justify-content-between align-items-center border-0 pb-0">
                            <h6 class="card-title mb-0">{{ label('home.pages.you.may.know') | raw }}</h6>
                        </div>
                        <!-- Card header START -->

                        <!-- Card body START -->
                        <div class="card-body" id="whopagelist">
                            <div class="tiny-slider arrow-hover">
                                <div class="tiny-slider-inner ms-n4" data-arrow="true" data-dots="false" data-items-xl="3" data-items-lg="2" data-items-md="2" data-items-sm="2" data-items-xs="1" data-gutter="12" data-edge="30">
                                    <!-- Slider items -->
                                    {% for whopage in whopageList %}
                                    <div>
                                        <!-- Card add friend item START -->
                                        <div class="card shadow-none text-center border border-primary">
                                            <!-- Card body -->
                                            <div class="card-body p-2 pb-0">
                                                <div class="avatar avatar-xl">
                                                    {% if whopage.page.profileImageId is not empty %}
                                                    <a href="{{ paths('PAGE_BASE') }}/{{ whopage.page.identifier }}"><img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ whopage.page.profileImageId }}" alt=""></a>
                                                    {% else %}
                                                    <a href="{{ paths('PAGE_BASE') }}/{{ whopage.page.identifier }}"><img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt=""></a>
                                                    {% endif %}
                                                </div>
                                                <h6 class="card-title mb-1 mt-3 text-truncate"> <a href="{{ paths('PAGE_BASE') }}/{{ whopage.page.identifier }}"> {{ whopage.page.name }} </a></h6>
                                                {% if whopage.followerCount > 0 and (whopage.showFollowers == true) %}
                                                {% if whopage.followerCount > 1 %}
                                                <p class="mb-0">{{ whopage.followerCount }} followers</p>
                                                {% else %}
                                                <p class="mb-0">{{ whopage.followerCount }} follower</p>
                                                {% endif %}
                                                {% else %}
                                                <p class="mb-0 hidden">{{ label('common.no.follower') | raw }}</p>
                                                {% endif%}
                                            </div>
                                            <!-- Card footer -->
                                            <div class="card-footer p-2 border-bottom">
                                                <button class="btn btn-sm btn-outline-primary rounded-pill w-100 page-add-follow" data-tiny="true" data-value="inactive" data-reload-id="whopagelist" data-user="{{ user is not empty ? (user.profileType == 'customer' or user.profileType == 'system' ? 'logged' : 'notconfirmed') : 'unlogged' }}" data-page-id="{{ whopage.page.id }}"> {{ label('common.follow') | raw }} </button>
                                            </div>
                                        </div>
                                        <!-- Card add friend item END -->
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        <!-- Card body END -->
                    </div>
                    {% endif %}
                    {% endif %}
                    {% endif %}
                    {% else %}
                    <div class="card border-lg-b">
                        <!-- Card header START -->
                        <div class="card-header border-0 pb-0">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <!-- Avatar -->
                                    {% set pageEvent = '' %}
                                    {% if entry.event.validPageIds is not empty %}
                                    {% set pageEvent = get('page', entry.event.validPageIds[0]) %}
                                    {% else %}
                                    {% set pageEvent = get('page', entry.event.pageIds[0]) %}
                                    {% endif %}

                                    <div class="avatar me-2">
                                        {% if pageEvent.profileImageId is not empty %}
                                        <a href="{{ paths('PAGE_BASE') }}/{{ pageEvent.identifier }}" class="walllink" data-wall-back="walllink-{{ entry.event.identifier }}"> <img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageEvent.profileImageId }}" alt="{{ pageEvent.name }}"> </a>
                                        {% else %}
                                        <a href="{{ paths('PAGE_BASE') }}/{{ pageEvent.identifier }}" class="walllink" data-wall-back="walllink-{{ entry.event.identifier }}"><img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ pageEvent.name }}"> </a>
                                        {% endif %}                                                                                                                                                                                                               
                                    </div>

                                    <!-- Title -->
                                    <div>
                                        <h6 class="card-title mb-0"> <a href="{{ paths('PAGE_BASE') }}/{{ pageEvent.identifier }}" class="walllink" data-wall-back="walllink-{{ pageEvent.identifier }}"> {{ pageEvent.name }} {{ (entry.event.pageIds | length) > 1 ? (' ' + (label('common.other.pages') | raw) + ' ') : ''}} </a></h6>
                                        <ul class="avatar-group list-unstyled align-items-sm-center">
                                            {% if entry.walletExtraPages is not empty %}
                                            {% set extraPages = entry.walletExtraPages %}
                                            {% for extraPage in extraPages %}
                                            <li class="avatar avatar-xs">
                                                <a href="{{ paths('PAGE_BASE') }}/{{ extraPage.identifier }}" class="walllink" data-wall-back="walllink-{{ entry.event.identifier }}">
                                                    <img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ extraPage.profileImageId }}" title="{{ extraPage.name }}" onerror="this.src = '{{ contextPath }}/fe/images/avatar/placeholder.jpg'">
                                                </a>
                                            </li>
                                            {% endfor %}
                                            {% set extraPagesAmount = (entry.event.pageIds.size - entry.walletExtraPages.size) %}
                                            {% if extraPagesAmount > 1 %}
                                            <li class="avatar avatar-xs">
                                                <div class="avatar-img rounded-circle bg-primary"><span class="text-white position-absolute top-50 start-50 translate-middle">+{{ extraPagesAmount -1 }}</span></div>
                                            </li>
                                            {% endif %}
                                            {% endif %}
                                            <li class="small ms-3">
                                                {{ formatDate(entry.event.startDate, "EEE dd MMMM yyyy", language) | capitalize }}
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <!-- Card share action menu -->
                                <a href="#" class="text-secondary btn btn-secondary-soft-hover py-1 px-2" id="cardShareAction5" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-three-dots"></i>
                                </a>
                                <!-- Card share action dropdown menu -->
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="cardShareAction5">
                                    {% if user is not empty and user.profileType != 'unconfirmed' %}
                                    <li><a class="dropdown-item" href="#"> <i class="bi bi-person-x fa-fw pe-2"></i>{{ label('common.unfollow') | raw }} {{ pageEvent.name }}</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    {% endif %}
                                    <li><a class="dropdown-item" href="" data-bs-toggle="modal" data-eventId="{{ entry.event.id }}" data-bs-target="#modalReportEvent"> <i class="bi bi-flag fa-fw pe-2"></i>{{ label('common.report.post') | raw }}</a></li>
                                </ul>
                            </div>
                            <!-- Card share action END -->
                            <hr>
                        </div>
                        <!-- Card header START -->

                        <!-- Card body START -->
                        <div class="card-body pb-0 pt-0">
                            <p><b class="text-decoration-underline"><a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" data-wall-back="walllink-{{ entry.event.identifier }}" class="walllink h4 text-link">{{ entry.event.name }}</a></b></p>
                            {% if entry.event.coverImageId is not empty %}
                            <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="walllink" data-wall-back="walllink-{{ entry.event.identifier }}"> <img class="card-img pb-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ entry.event.coverImageId }}" alt="Post"></a>
                            {% else %}
                            {% if pageEvent.coverImageId is not empty %}
                            <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="walllink" data-wall-back="walllink-{{ entry.event.identifier }}"> <img class="card-img pb-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageEvent.coverImageId }}" alt="Post"></a>
                            {% else %}
                            <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="walllink" data-wall-back="walllink-{{ entry.event.identifier }}"> <img class="card-img pb-3" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="Post"></a>
                            {% endif %}
                            {% endif %}
                            <p>
                                {% autoescape false %}{{ newline(entry.event.description) | striphtml | abbreviate(170) }}{% endautoescape %}
                                <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" data-wall-back="walllink-{{ entry.event.identifier }}" class="text-link ms-2">{{ label('common.continue') | raw }}</a>
                            </p>
                        </div>
                        <!-- Card body END -->
                        <!-- Card Footer START -->
                        {% if entry.event.type is not empty and entry.event.type == 'container' %}
                        {% if entry.childEvents is not empty %}
                        <div class="oneEvent px-3">
                            {% for childEvent in entry.childEvents %}
                            <hr>
                            <!-- Events list END -->
                            <div class="row">
                                <div class="d-sm-flex align-items-center ">
                                    <!-- Avatar -->
                                    <div class="avatar avatar-md">
                                        <a href="{{ paths('EVENT_BASE') }}/{{ childEvent.identifier }}" class="eventlink">
                                            {% if childEvent.coverImageId is not empty %}
                                            <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ childEvent.coverImageId }}" alt="{{ childEvent.name }}">
                                            {% else %}
                                            <img class="avatar-img border border-white border-3" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="{{ childEvent.name }}">
                                            {% endif %}
                                        </a>
                                    </div>
                                    <div class="ms-sm-4 mt-2 mt-sm-0">
                                        <!-- Info -->
                                        <small class="mb-1"><a href="{{ paths('EVENT_BASE') }}/{{ childEvent.identifier }}">{{ childEvent.name }}</a></small>
                                        <ul class="nav nav-stack small gap-2 row-gap-0">
                                            <li class="nav-item">
                                                <i class="bi bi-calendar-check"></i> {{ formatDate(childEvent.startDate, "EEE d MMM yyyy", language) | capitalize }} {{ childEvent.startHour }}
                                            </li>
                                            <li class="nav-item">
                                                <i class="bi bi-geo-alt"></i> {{ childEvent.city }}
                                            </li>
                                            {% if childEvent.followerCount > 0 and (childEvent.showFollowers == true) %}
                                            <li class="nav-item">
                                                <i class="bi bi-people"></i>
                                                {% if childEvent.followerCount > 1 %}
                                                {{ childEvent.followerCount }} {{ label('common.interested') | raw }}
                                                {% else %}
                                                {{ childEvent.followerCount }} {{ label('common.interested.sing') | raw }}
                                                {% endif %}
                                            </li>
                                            {% endif %}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                            <div class="row text-center mt-3 mb-2 justify-content-center">
                                <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="small">Vedi tutti gli eventi »</a>
                            </div>
                        </div>
                        {% endif %}
                        {% endif %}
                        <div class="card-footer py-3" id="whopagefollow-{{ entry.event.id }}">
                            <!-- Feed react START -->
                            <ul class="nav nav-fill nav-stack small nav-even">
                                {% if entry.event.type is empty or entry.event.type != 'container' %}
                                <li class="nav-item border-share">
                                    {% if entry.iFollow %}
                                    <a class="nav-link mb-0 active event-add-follow" data-reload-id="whopagefollow-{{ entry.event.id }}" data-value="active" data-user="{{ user is not empty ? (user.profileType == 'customer' or user.profileType == 'system' ? 'logged' : 'notconfirmed') : 'unlogged' }}" data-event-id="{{ entry.event.id }}" href="#!"> <i class="bi bi-heart-fill pe-1"></i>{{ entry.followerCount > 0 ? (label('common.like') | raw) + ' (' + entry.followerCount + ')' : (label('common.no.likes') | raw)  }}</a>
                                    {% else %}
                                    <a class="nav-link mb-0 active event-add-follow" data-reload-id="whopagefollow-{{ entry.event.id }}" data-value="inactive" href="#!" data-user="{{ user is not empty ? (user.profileType == 'customer' or user.profileType == 'system' ? 'logged' : 'notconfirmed') : 'unlogged' }}" data-event-id="{{ entry.event.id }}"> <i class="bi bi-heart pe-1"></i>{{ entry.followerCount > 0 ? (label('common.like') | raw) + ' (' + entry.followerCount + ')' : (label('common.no.likes') | raw) }}</a>
                                    {% endif %}
                                </li>
                                {% endif %}
                                <!-- Card share action dropdown START -->
                                <li class="nav-item dropdown">
                                    <a href="#" class="nav-link mb-0" id="cardShareAction6" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-reply-fill flip-horizontal ps-1"></i>{{ label('common.share') | raw }}
                                    </a>
                                    <!-- Card share action dropdown menu -->
                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="cardShareAction6">
                                        <li>
                                            <a class="dropdown-item" href="https://www.facebook.com/sharer/sharer.php?u={{ publicUrl }}" aria-label="Facebook">
                                                <i class="bi-facebook"></i> Facebook
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="https://api.whatsapp.com/send?text=Guarda%20questo%20evento%20su%20Agorapp!%20{{ publicUrl }}" aria-label="Whatsapp">
                                                <i class="bi-whatsapp"></i> Whatsapp
                                            </a>                            
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="https://t.me/share/url?url={{ publicUrl }}&text=Guarda%20questo%20evento%20su%20Agorapp!" aria-label="Telegram">
                                                <i class="bi-telegram"></i> Telegram
                                            </a>                            
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="https://twitter.com/intent/tweet?url={{ publicUrl }}&text=Guarda%20questo%20evento%20su%20Agorapp!" aria-label="Twitter" target="_blank" rel="noopener">
                                                <i class="bi-twitter"></i> Twitter
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="https://www.linkedin.com/sharing/share-offsite/?url={{ publicUrl }}" aria-label="LinkedIn" target="_blank" rel="noopener">
                                                <i class="bi-linkedin"></i> LinkedIn
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" data-clipboard-text="{{ publicUrl }}" id="copyToClipboardLink" aria-label="{{ label('common.copy.to.clipboard') | raw }}">
                                                <i class="bi-clipboard-check"></i> {{ label('common.copy.to.clipboard') | raw }}
                                            </a>
                                        </li>
                                    </ul>
                                </li>                                                        
                            </ul>
                            <!-- Feed react END -->
                        </div>
                        <!-- Card Footer END -->
                    </div>
                    <!-- Card feed item END -->                

                    <!-- Card feed item START -->
                    {% if skip == 0 %}
                    {% set showwhopage = false %}
                    {% if (wallEventList | length) > 5 %}
                    {% if loop.index == 5 %}
                    {% set showwhopage = true %}
                    {% endif %}
                    {% else %}
                    {% if loop.last %}
                    {% set showwhopage = true %}
                    {% endif %}
                    {% endif  %}

                    {% if showwhopage %}
                    {% set whopageList = follow('whopage', customerEntry.customer.userId, customerEntry.customer.provinceCode, 10, 'random') %}
                    {% if whopageList is not empty %}
                    <div class="card border-lg-b">
                        <!-- Card header START -->
                        <div class="card-header d-flex justify-content-between align-items-center border-0 pb-0">
                            <h6 class="card-title mb-0">{{ label('home.pages.you.may.know') | raw }}</h6>                        
                        </div>      
                        <!-- Card header START -->

                        <!-- Card body START -->
                        <div class="card-body" id="whopagelist">
                            <div class="tiny-slider arrow-hover">
                                <div class="tiny-slider-inner ms-n4" data-arrow="true" data-dots="false" data-items-xl="3" data-items-lg="2" data-items-md="2" data-items-sm="2" data-items-xs="1" data-gutter="12" data-edge="30">
                                    <!-- Slider items -->
                                    {% for whopage in whopageList %}
                                    <div> 
                                        <!-- Card add friend item START -->
                                        <div class="card shadow-none text-center border border-primary">
                                            <!-- Card body -->
                                            <div class="card-body p-2 pb-0">
                                                <div class="avatar avatar-xl">
                                                    {% if whopage.page.profileImageId is not empty %}
                                                    <a href="{{ paths('PAGE_BASE') }}/{{ whopage.page.identifier }}"><img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ whopage.page.profileImageId }}" alt=""></a>
                                                    {% else %}
                                                    <a href="{{ paths('PAGE_BASE') }}/{{ whopage.page.identifier }}"><img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt=""></a>
                                                    {% endif %}
                                                </div>
                                                <h6 class="card-title mb-1 mt-3 text-truncate"> <a href="{{ paths('PAGE_BASE') }}/{{ whopage.page.identifier }}"> {{ whopage.page.name }} </a></h6>
                                                {% if whopage.followerCount > 0 and (whopage.showFollowers == true) %}
                                                    {% if whopage.followerCount > 1 %}
                                                        <p class="mb-0">{{ whopage.followerCount }} followers</p>
                                                    {% else %}
                                                        <p class="mb-0">{{ whopage.followerCount }} follower</p>
                                                    {% endif %}
                                                {% else %}
                                                    <p class="mb-0 hidden">{{ label('common.no.follower') | raw }}</p>
                                                {% endif%}                                                
                                            </div>
                                            <!-- Card footer -->
                                            <div class="card-footer p-2 border-bottom">
                                                <button class="btn btn-sm btn-outline-primary rounded-pill w-100 page-add-follow" data-tiny="true" data-value="inactive" data-reload-id="whopagelist" data-user="{{ user is not empty ? (user.profileType == 'customer' or user.profileType == 'system' ? 'logged' : 'notconfirmed') : 'unlogged' }}" data-page-id="{{ whopage.page.id }}"> {{ label('common.follow') | raw }} </button>
                                            </div>
                                        </div>
                                        <!-- Card add friend item END -->
                                    </div>                                
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        <!-- Card body END -->
                    </div>
                    {% endif %}
                    {% endif %}
                    {% endif %}
                    {% endif %}
                    {% endfor %}
                </div>
                {% if loadmore %}
                <div class="row m-t-sm">
                    <div class="col-xs-12 text-center">
                        <div id="loading" class="spinner">
                            <img src="{{ contextPath }}/fe/images/loader.svg" width="64" alt="loading...">
                        </div>
                    </div>
                </div>                
                {% endif %}
                {% else %}
                {% set whopageList = follow('whopage', customerEntry.customer.userId, customerEntry.customer.provinceCode, 10, 'random') %}
                {% if whopageList is not empty %}
                <div class="card border-lg-lr">
                    <!-- Card header START -->
                    <div class="card-header d-flex justify-content-between align-items-center border-0 pb-0">
                        <h6 class="card-title mb-0">{{ label('home.pages.you.may.know') | raw }}</h6>                        
                    </div>      
                    <!-- Card header START -->

                    <!-- Card body START -->
                    <div class="card-body" id="whopagelist">
                        <div class="tiny-slider arrow-hover">
                            <div class="tiny-slider-inner ms-n4" data-arrow="true" data-dots="false" data-items-xl="3" data-items-lg="2" data-items-md="2" data-items-sm="2" data-items-xs="1" data-gutter="12" data-edge="30">
                                <!-- Slider items -->
                                {% for whopage in whopageList %}
                                <div> 
                                    <!-- Card add friend item START -->
                                    <div class="card shadow-none text-center">
                                        <!-- Card body -->
                                        <div class="card-body p-2 pb-0">
                                            <div class="avatar avatar-xl">
                                                {% if whopage.page.profileImageId is not empty %}
                                                <a href="{{ paths('PAGE_BASE') }}/{{ whopage.page.identifier }}"><img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ whopage.page.profileImageId }}" alt=""></a>
                                                {% else %}
                                                <a href="{{ paths('PAGE_BASE') }}/{{ whopage.page.identifier }}"><img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt=""></a>
                                                {% endif %}
                                            </div>
                                            <h6 class="card-title mb-1 mt-3"> <a href="{{ paths('PAGE_BASE') }}/{{ whopage.page.identifier }}"> {{ whopage.page.name }} </a></h6>
                                            {% if whopage.followerCount > 0 and (whopage.showFollowers == true) %}
                                            {% if whopage.followerCount > 1 %}
                                            <p>{{ whopage.followerCount }} followers</p>
                                            {% else %}
                                            <p>{{ whopage.followerCount }} follower</p>
                                            {% endif %}
                                            {% else %}
                                            <!--<p>{{ label('common.no.follower') | raw }}</p>-->
                                            {% endif%}                                                
                                        </div>
                                        <!-- Card footer -->
                                        <div class="card-footer p-2 border-bottom">
                                            <button class="btn btn-sm btn-primary-soft w-100 page-add-follow" data-tiny="true" data-value="inactive" data-reload-id="whopagelist" data-user="{{ user is not empty ? (user.profileType == 'customer' or user.profileType == 'system' ? 'logged' : 'notconfirmed') : 'unlogged' }}" data-page-id="{{ whopage.page.id }}"> {{ label('common.follow') | raw }} </button>
                                        </div>
                                    </div>
                                    <!-- Card add friend item END -->
                                </div>                                
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    <!-- Card body END -->
                </div>
                {% endif %}
                {% endif %}

                <!-- Card feed item END -->
            </div>
            <div class="pager">
                {% if resultUrl contains '?' %}
                {% if skip > 12 %}
                <a href="{{ resultUrl }}&skip={{ skip - limit }}&limit={{ limit }}" class="pager__prev">&leftarrow; {{ label('common.prev.article') | raw }}</a>
                {% if loadmore %}
                <a href="{{ resultUrl }}&skip={{ skip + limit }}&limit={{ limit }}" class="pager__next">{{ label('common.next.article') | raw }} &rightarrow;</a>
                {% endif %}
                {% else %}
                {% if skip == 12 %}
                <a href="{{ resultUrl }}" class="pager__prev">&leftarrow; {{ label('common.prev.article') | raw }}</a>
                {% endif %}
                {% if loadmore %}
                <a href="{{ resultUrl }}&skip={{ skip + limit }}&limit={{ limit }}" class="pager__next">{{ label('common.next.article') | raw }} &rightarrow;</a>
                {% endif %}
                {% endif %}
                {% else %}
                {% if skip > 12 %}
                <a href="{{ resultUrl }}?skip={{ skip - limit }}&limit={{ limit }}" class="pager__prev">&leftarrow; {{ label('common.prev.article') | raw }}</a>
                {% if loadmore %}
                <a href="{{ resultUrl }}?skip={{ skip + limit }}&limit={{ limit }}" class="pager__next">{{ label('common.next.article') | raw }} &rightarrow;</a>
                {% endif %}
                {% else %}
                {% if skip == 12 %}
                <a href="{{ resultUrl }}" class="pager__prev">&leftarrow; {{ label('common.prev.article') | raw }}</a>
                {% endif %}
                {% if loadmore %}
                <a href="{{ resultUrl }}?skip={{ skip + limit }}&limit={{ limit }}" class="pager__next">{{ label('common.next.article') | raw }} &rightarrow;</a>
                {% endif %}
                {% endif %}                    
                {% endif %}
            </div>
        </div>
        <!-- Main content END -->

        <!-- Right sidebar START -->
        {#
        <div class="col-lg-3">
            {% include "fe/include/snippets/sidenav-right.html" %}                
        </div>
        #}
        <!-- Right sidebar END -->

    </div> <!-- Row END -->
</div>
<!-- Container END -->
{% endblock %}       

{% block pagescripts %}     
<script defer src="{{ contextPath }}/fe/js/clipboard.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function () {
    var clipboard = new ClipboardJS('#copyToClipboardLink');

    clipboard.on('success', function (e) {
        let myAlert = document.querySelector('.toast');
        let bsAlert = new bootstrap.Toast(myAlert);
        bsAlert.show();

        e.clearSelection();
    });
});
</script>
<script src="{{ contextPath }}/fe/js/pages/home.js?{{ buildNumber }}"></script>
{% endblock %}
