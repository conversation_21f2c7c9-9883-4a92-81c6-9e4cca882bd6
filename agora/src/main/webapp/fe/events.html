{% extends "fe/include/base.html" %}

{% block title %}{{ label('events.title.meta') | raw }} | Agorapp{% endblock %}
{% block pagecss %}
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/css/daterangepicker.css" />
{% endblock %}
{% block content %}
<!-- some hidden stuff -->
<div id="pagination" style="display: none;">{{ pagination }}</div>
<div id="paginationFollow" style="display: none;">{{ paginationFollow }}</div>
<a id="eventsUri" style="display: none" href="{{ paths('EVENTS') }}" rel="nofollow"></a>
<a id="eventFollowToggleUri" style="display: none" href="{{ paths('EVENT_FOLLOWER_TOGGLE') }}" rel="nofollow"></a>  
<div id="startDate" style="display: none">{{ fromDate | date("yyyy-MM-dd") }}</div>
<div id="endDate" style="display: none">{{ toDate | date("yyyy-MM-dd") }}</div>
<!-- api call for products autocomplete -->
<a id="dataCitiesUri" style="display: none" href="{{ paths('DATA_CITIES_AC') }}?name="></a>

<!-- Container START -->
<div class="container">
    <div class="row g-4">

        <!-- Sidenav START -->
        <div class="col-lg-3">
            {% include "fe/include/snippets/sidenav-left.html" %}        
        </div>
        <!-- Sidenav END -->

        <!-- Main content START -->
        <div class="col-md-8 col-lg-6 vstack gap-4">            

            <!-- Card START -->
            <div class="card h-100">
                <!-- Card header START -->
                <div class="card-header d-sm-flex align-items-center text-center justify-content-sm-between border-0 pb-0">
                    <h1 class="h4 card-title">{{ label('events') | raw }}</h1>
                    <!-- Button modal -->
                    <a class="btn btn-primary" href="{{ paths('EVENT_ADD') }}"> <i class="fa-solid fa-plus pe-1"></i> {{ label('common.create.event') | raw }}</a>
                </div>
                <!-- Card header START -->
                <!-- Card body START -->
                <div class="card-body">

                    <!-- Tab nav line -->
                    {#
                    <ul class="nav nav-tabs nav-bottom-line justify-content-center justify-content-md-start" id="event-tabs">
                        <li class="nav-item"> <a class="nav-link active" data-bs-toggle="tab" href="#all-events"> {{ label('events.events.follow') | raw }} </a> </li>
                        <li class="nav-item"> <a class="nav-link" data-bs-toggle="tab" href="#follow-events"> {{ label('events.events.all') | raw }} </a> </li>
                    </ul>
                    #}
                    <!-- Tab content START -->
                    <div class="tab-content mb-0 pb-0">
                        <div class="row g-3">
                            <div class="col-12 col-md-6">                                                
                                <select class="select-search form-control" data-search-enabled="true" id="tag" name="tag" value="{{ tag }}">
                                    <option value="" {{ tag == '' ? 'selected' : '' }}>{{ label('common.all') | raw }}</option>
                                    {% for tagTmp in tagList %}
                                        <option value="{{ tagTmp }}" {{ tagTmp == tag ? 'selected' : '' }}>{{ tagTmp | lower }}</option>
                                    {% endfor %}
                                </select>  
                            </div>
                            <div class="col-12 col-md-6">
                                <button type="button" id="daterange-predefined" class="btn btn-white daterange-predefined text-capitalize w-100 border">
                                    <i class="bi bi-calendar-range me-2"></i>
                                    <span></span>
                                    <b class="caret"></b>
                                </button>
                            </div>
                        </div>
                        
                        <div class="tab-pane fade show active" id="all-events">
                            {% if eventList is not empty %}
                            <div class="containerWallEvents">
                                <div class="wallEvent">
                                    <hr>
                                    <div class="row g-0 event-list border-lg-l border-lg-t">
                                        {% for entry in eventList %}
                                        <div class="col-sm-6 col-xl-4" id="eventfollow-{{ entry.event.id }}">
                                            <!-- Event item START -->
                                            <div class="card h-100 border-lg-r border-lg-b">
                                                <div class="position-relative">
                                                    {% if entry.event.coverImageId is not empty %}
                                                    <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="walllink">
                                                        <img class="img-event img-fluid" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ entry.event.coverImageId }}" alt="">                                            
                                                    </a>
                                                    {% else %}
                                                    <img class="img-event img-fluid" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="">                                            
                                                    {% endif %}
                                                </div>
                                                <!-- Card body START -->
                                                <div class="card-body position-relative pt-0">
                                                    <!-- Tag -->
                                                    <!--<a class="btn btn-xs btn-primary mt-n3" href="">{{ entry.event.category | capitalize }} </a>-->
                                                    <h6 class="mt-3"> <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="walllink"> {{ entry.event.name | abbreviate(40) }} </a> </h6>
                                                    <!-- Date time -->
                                                    <p class="mb-0 small"> <i class="bi bi-calendar-check pe-1"></i> {{ formatDate(entry.event.startDate, "EEE d MMM yyyy", language) | capitalize }} {{ entry.event.startHour }}</p>
                                                    <p class="small"> <i class="bi bi-geo-alt pe-1"></i> {{ entry.event.city }} </p>
                                                    <!-- Avatar group START -->
                                                    <ul class="avatar-group list-unstyled align-items-center mb-0">
                                                        {% if entry.followerPages is not empty %}
                                                        {% for followerPage in entry.followerPages %}
                                                        <li class="avatar avatar-xs">                                        
                                                            <a href="{{ paths('PAGE_BASE') }}/{{ followerPage.identifier }}">
                                                                {% if followerPage.profileImageId is not empty %}
                                                                <img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ followerPage.profileImageId }}">
                                                                {% else %}
                                                                <img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg"> 
                                                                {% endif %}
                                                            </a>
                                                        </li>
                                                        {% endfor %}
                                                        {% else %}  
                                                        <li class="avatar avatar-xs">                                                                                                                    
                                                            <img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg">                                                                         
                                                            </a>
                                                        </li>
                                                        {% endif %}
                                                        <li class="ms-4">
                                                            {% if entry.followerCount > 0 and (entry.event.showFollowers == true) %}
                                                            {% if entry.followerCount > 1 %}
                                                            <small>{{ entry.followerCount }} {{ label('common.people.interested') | raw }}</small>
                                                            {% else %}
                                                            <small>{{ entry.followerCount }} {{ label('common.person.interested') | raw }}</small>
                                                            {% endif %}
                                                            {% else %}
                                                            <small>{{ label('common.no.people.interested') | raw }}</small>
                                                            {% endif%}
                                                        </li>
                                                    </ul>
                                                    <!-- Avatar group END -->
                                                    <!-- Button -->
                                                    <div class="d-flex mt-3 justify-content-between">
                                                        <!-- Interested button -->
                                                        <div class="w-100">
                                                            {% if entry.iFollow %}
                                                            <a class="nav-link mb-0 active event-add-follow" data-reload-id="eventfollow-{{ entry.event.id }}" data-value="active" data-user="{{ user is not empty ? (user.profileType == 'customer' or user.profileType == 'system' ? 'logged' : 'notconfirmed') : 'unlogged' }}" data-event-id="{{ entry.event.id }}" href="#!"> <i class="fa-solid bi-heart-fill me-1"></i> {{ label('common.i.interested') | raw }}</a>
                                                            {% else %}
                                                            <a class="nav-link mb-0 active event-add-follow" data-reload-id="eventfollow-{{ entry.event.id }}" data-value="inactive" href="#!" data-user="{{ user is not empty ? (user.profileType == 'customer' or user.profileType == 'system' ? 'logged' : 'notconfirmed') : 'unlogged' }}" data-event-id="{{ entry.event.id }}"> <i class="fa-solid bi-heart me-1"></i> {{ label('common.i.interested') | raw }}</a>
                                                            {% endif %}
                                                        </div>
                                                        <div class="dropdown ms-3">
                                                            <a href="#" class="btn btn-sm btn-primary-soft" id="eventActionShare" data-bs-toggle="dropdown" aria-expanded="false">
                                                                <i class="bi bi-share-fill"></i>
                                                            </a>
                                                            <!-- Dropdown menu -->
                                                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="eventActionShare">
                                                                <li>
                                                                    <a class="dropdown-item" href="https://www.facebook.com/sharer/sharer.php?u={{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" aria-label="Facebook">
                                                                        <i class="bi-facebook"></i> Facebook
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item" href="https://api.whatsapp.com/send?text=Guarda%20questo%20evento%20su%20Agorapp!%20{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" aria-label="Whatsapp">
                                                                        <i class="bi-whatsapp"></i> Whatsapp
                                                                    </a>                            
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item" href="https://t.me/share/url?url={{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}&text=Guarda%20questo%20evento%20su%20Agorapp!" aria-label="Telegram">
                                                                        <i class="bi-telegram"></i> Telegram
                                                                    </a>                            
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item" href="https://twitter.com/intent/tweet?url={{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}&text=Guarda%20questo%20evento%20su%20Agorapp!" aria-label="Twitter" target="_blank" rel="noopener">
                                                                        <i class="bi-twitter"></i> Twitter
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item" href="https://www.linkedin.com/sharing/share-offsite/?url={{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" aria-label="LinkedIn" target="_blank" rel="noopener">
                                                                        <i class="bi-linkedin"></i> LinkedIn
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item" data-clipboard-text="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" id="copyToClipboardLink" aria-label="{{ label('common.copy.to.clipboard') | raw }}">
                                                                        <i class="bi-clipboard-check"></i> {{ label('common.copy.to.clipboard') | raw }}
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- Card body END -->
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>                                
                                <div class="row m-t-sm">
                                    <div class="col-xs-12 text-center">
                                        <div id="loading" class="spinner">
                                            <img src="{{ contextPath }}/fe/images/loader.svg" width="64" alt="loading...">
                                        </div>
                                    </div>
                                </div>  
                                <div class="row m-t-sm">
                                    <div class="pager">
                                        {% if resultUrl contains '?' %}
                                        {% if skip > 12 %}
                                        <a href="{{ resultUrl }}&skip={{ skip - limit }}&limit={{ limit }}&tab=all" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                        {% if loadmore %}
                                        <a href="{{ resultUrl }}&skip={{ skip + limit }}&limit={{ limit }}&tab=all" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                        {% endif %}
                                        {% else %}
                                        {% if skip == 12 %}
                                        <a href="{{ resultUrl }}&tab=all" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                        {% endif %}
                                        {% if loadmore %}
                                        <a href="{{ resultUrl }}&skip={{ skip + limit }}&limit={{ limit }}&tab=all" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                        {% endif %}
                                        {% endif %}
                                        {% else %}
                                        {% if skip > 12 %}
                                        <a href="{{ resultUrl }}?skip={{ skip - limit }}&limit={{ limit }}&tab=all" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                        {% if loadmore %}
                                        <a href="{{ resultUrl }}?skip={{ skip + limit }}&limit={{ limit }}&tab=all" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                        {% endif %}
                                        {% else %}
                                        {% if skip == 12 %}
                                        <a href="{{ resultUrl }}?tab=all" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                        {% endif %}
                                        {% if loadmore %}
                                        <a href="{{ resultUrl }}?skip={{ skip + limit }}&limit={{ limit }}&tab=all" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                        {% endif %}
                                        {% endif %}                    
                                        {% endif %}
                                    </div>                                            
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        <!-- Event top tab END -->

                        <!-- Event local tab START -->
                        <div class="tab-pane fade text-center" id="follow-events">
                            {% if eventListFollow is not empty %}
                            <div class="containerWallEventsFollow">
                                <div class="wallEventFollow">
                                    <div class="row g-4">
                                        {% for entry in eventListFollow %}
                                        <div class="col-sm-6 col-xl-4" id="eventfollow-{{ entry.event.id }}">
                                            <!-- Event item START -->
                                            <div class="card h-100">
                                                <div class="position-relative">
                                                    {% if entry.event.coverImageId is not empty %}
                                                    <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="walllink">
                                                        <img class="img-fluid" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ entry.event.coverImageId }}" alt="">                                            
                                                    </a>
                                                    {% else %}
                                                    <img class="img-fluid" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="">                                            
                                                    {% endif %}
                                                </div>
                                                <!-- Card body START -->
                                                <div class="card-body position-relative pt-0">
                                                    <!-- Tag -->
                                                    <a class="btn btn-xs btn-primary mt-n3" href="">{{ entry.event.category | capitalize }} </a>
                                                    <h6 class="mt-3"> <a href="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" class="walllink"> {{ entry.event.name }} </a> </h6>
                                                    <!-- Date time -->
                                                    <p class="mb-0 small"> <i class="bi bi-calendar-check pe-1"></i> {{ entry.event.startDate | date('EEE dd MMMM yyyy') | capitalize }} ore {{ entry.event.startHour }}</p>
                                                    <p class="small"> <i class="bi bi-geo-alt pe-1"></i> {{ entry.event.city }} </p>
                                                    <!-- Avatar group START -->
                                                    <ul class="avatar-group list-unstyled align-items-center mb-0">
                                                        {% if entry.followerPages is not empty %}
                                                        {% for followerPage in entry.followerPages %}
                                                        <li class="avatar avatar-xs">                                        
                                                            <a href="{{ paths('PAGE_BASE') }}/{{ followerPage.identifier }}">
                                                                {% if followerPage.profileImageId is not empty %}
                                                                <img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ followerPage.profileImageId }}">
                                                                {% else %}
                                                                <img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg"> 
                                                                {% endif %}
                                                            </a>
                                                        </li>
                                                        {% endfor %}
                                                        {% endif %}
                                                        <li class="ms-4">
                                                            {% if entry.followerCount > 0 and (entry.event.showFollowers == true) %}
                                                            {% if entry.followerCount > 1 %}
                                                            <small>{{ entry.followerCount }} {{ label('common.people.interested') | raw }}</small>
                                                            {% else %}
                                                            <small>{{ entry.followerCount }} {{ label('common.person.interested') | raw }}</small>
                                                            {% endif %}
                                                            {% else %}
                                                            <small>{{ label('common.no.people.interested') | raw }}</small>
                                                            {% endif%}
                                                        </li>
                                                    </ul>
                                                    <!-- Avatar group END -->
                                                    <!-- Button -->
                                                    <div class="d-flex mt-3 justify-content-between">
                                                        <!-- Interested button -->
                                                        <div class="w-100">
                                                            {% if entry.iFollow %}
                                                            <a class="nav-link mb-0 active event-add-follow" data-reload-id="eventfollow-{{ entry.event.id }}" data-value="active" data-user="{{ user is not empty ? (user.profileType == 'customer' or user.profileType == 'system' ? 'logged' : 'notconfirmed') : 'unlogged' }}" data-event-id="{{ entry.event.id }}" href="#!"> <i class="fa-solid bi-heart-fill me-1"></i> {{ label('common.i.interested') | raw }}</a>
                                                            {% else %}
                                                            <a class="nav-link mb-0 active event-add-follow" data-reload-id="eventfollow-{{ entry.event.id }}" data-value="inactive" href="#!" data-user="{{ user is not empty ? (user.profileType == 'customer' or user.profileType == 'system' ? 'logged' : 'notconfirmed') : 'unlogged' }}" data-event-id="{{ entry.event.id }}"> <i class="fa-solid bi-heart me-1"></i> {{ label('common.i.interested') | raw }}</a>
                                                            {% endif %}
                                                        </div>
                                                        <div class="dropdown ms-3">
                                                            <a href="#" class="btn btn-sm btn-primary-soft" id="eventActionShare" data-bs-toggle="dropdown" aria-expanded="false">
                                                                <i class="bi bi-share-fill"></i>
                                                            </a>
                                                            <!-- Dropdown menu -->
                                                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="eventActionShare">
                                                                <li>
                                                                    <a class="dropdown-item" href="https://www.facebook.com/sharer/sharer.php?u={{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" aria-label="Facebook">
                                                                        <i class="bi-facebook"></i> Facebook
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item" href="https://api.whatsapp.com/send?text=Guarda%20questo%20evento%20su%20Agorapp!%20{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" aria-label="Whatsapp">
                                                                        <i class="bi-whatsapp"></i> Whatsapp
                                                                    </a>                            
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item" href="https://t.me/share/url?url={{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}&text=Guarda%20questo%20evento%20su%20Agorapp!" aria-label="Telegram">
                                                                        <i class="bi-telegram"></i> Telegram
                                                                    </a>                            
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item" href="https://twitter.com/intent/tweet?url={{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}&text=Guarda%20questo%20evento%20su%20Agorapp!" aria-label="Twitter" target="_blank" rel="noopener">
                                                                        <i class="bi-twitter"></i> Twitter
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item" href="https://www.linkedin.com/sharing/share-offsite/?url={{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" aria-label="LinkedIn" target="_blank" rel="noopener">
                                                                        <i class="bi-linkedin"></i> LinkedIn
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item" data-clipboard-text="{{ paths('EVENT_BASE') }}/{{ entry.event.identifier }}" id="copyToClipboardLink" aria-label="{{ label('common.copy.to.clipboard') | raw }}">
                                                                        <i class="bi-clipboard-check"></i> {{ label('common.copy.to.clipboard') | raw }}
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                                <!-- Card body END -->
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>                                
                                <div class="row m-t-sm">
                                    <div class="col-xs-12 text-center">
                                        <div id="loadingFollow" class="spinnerFollow">
                                            <img src="{{ contextPath }}/fe/images/loader.svg" width="64" alt="loading...">
                                        </div>
                                    </div>
                                </div>  
                                <div class="row m-t-sm">
                                    <div class="pagerFollow">
                                        {% if resultUrlFollow contains '?' %}
                                        {% if skipFollow > 12 %}
                                        <a href="{{ resultUrlFollow }}&skipFollow={{ skipFollow - limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__prevFollow" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                        {% if loadmoreFollow %}
                                        <a href="{{ resultUrlFollow }}&skipFollow={{ skipFollow + limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__nextFollow" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                        {% endif %}
                                        {% else %}
                                        {% if skipFollow == 12 %}
                                        <a href="{{ resultUrlFollow }}&tab=follow" class="pager__prevFollow" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                        {% endif %}
                                        {% if loadmoreFollow %}
                                        <a href="{{ resultUrlFollow }}&skipFollow={{ skipFollow + limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__nextFollow" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                        {% endif %}
                                        {% endif %}
                                        {% else %}
                                        {% if skipFollow > 12 %}
                                        <a href="{{ resultUrlFollow }}?skipFollow={{ skipFollow - limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__prevFollow" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                        {% if loadmoreFollow %}
                                        <a href="{{ resultUrlFollow }}?skipFollow={{ skipFollow + limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__nextFollow" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                        {% endif %}
                                        {% else %}
                                        {% if skipFollow == 12 %}
                                        <a href="{{ resultUrlFollow }}?tab=follow" class="pager__prevFollow" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                        {% endif %}
                                        {% if loadmoreFollow %}
                                        <a href="{{ resultUrlFollow }}?skipFollow={{ skipFollow + limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__nextFollow" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                        {% endif %}
                                        {% endif %}                    
                                        {% endif %}
                                    </div>                                            
                                </div>
                            </div>
                            {% else %}
                            <div class="my-sm-5 py-sm-5">
                                <i class="display-1 text-muted bi bi-calendar2-event"> </i>
                                <h4 class="mt-2 mb-3 text-body">{{ label('events.no.events.found') | raw }}</h4>
                                <a href="{{ paths('EVENT_ADD') }}" class="btn btn-primary"> <i class="fa-solid fa-plus pe-1"></i>{{ label('common.create.event') | raw }} </a>
                            </div>
                            {% endif %}
                        </div>

                    </div>
                    <!-- Tab content END -->
                </div>
                <!-- Card body END -->
            </div>
            <!-- Card END -->
        </div>

    </div> <!-- Row END -->
</div>
<!-- Container END -->
{% endblock %}       

{% block pagescripts %}   
<script src="{{ contextPath }}/fe/js/moment-with-locales.js"></script>
<script src="{{ contextPath }}/fe/js/daterangepicker.js"></script>
<script defer src="{{ contextPath }}/fe/js/clipboard.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function () {
var clipboard = new ClipboardJS('#copyToClipboardLink');

clipboard.on('success', function (e) {
    let myAlert = document.querySelector('.toast');
    let bsAlert = new bootstrap.Toast(myAlert);
    bsAlert.show();

    e.clearSelection();
});



moment.locale('it');
var startDate = moment($('#startDate').text());
var endDate = moment($('#endDate').text());
$('#daterange-predefined').daterangepicker(
        {
            startDate: startDate,
            endDate: endDate,
            dateLimit: {days: 365},
            ranges: {
                'Oggi': [moment(), moment()],
                'Domani': [moment().add(1, 'days'), moment().add(1, 'days')],
                'Questo weekend': [moment().add((5 - moment().day()), 'days'), moment().add(((5 - moment().day()) + 2), 'days', 'days')],
                'Prossimi 90 giorni': [moment(), moment().add(90, 'days')],
            },
            locale: {
                format: 'DD/MM/YYYY',
                applyLabel: 'Applica',
                cancelLabel: 'Annulla',
                startLabel: 'Data inizio',
                endLabel: 'Data fine',
                customRangeLabel: 'Scegli date',
                daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve', 'Sa'],
                monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
                firstDay: 1
            },
            opens: 'left',
            applyClass: 'btn-primary',
            cancelClass: 'btn-white'
        },
        function (start, end) {
            $('#daterange-predefined span').html(start.format('D MMMM YYYY') + ' &nbsp; - &nbsp; ' + end.format('D MMMM YYYY'));
        }
);

$('#daterange-predefined span').html(startDate.format('D MMMM YYYY') + ' &nbsp; - &nbsp; ' + endDate.format('D MMMM YYYY'));

});
</script>
<script src="{{ contextPath }}/fe/js/pages/events.js?{{ buildNumber }}"></script>
{% endblock %}