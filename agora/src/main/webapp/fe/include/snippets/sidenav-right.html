<div class="sticky-top" style="top:4rem">
    <!-- Card follow START -->

    <div class="p-3">
        <!-- Card header START -->
        <div class="card-header pb-0 border-0">
            <h5 class="card-title mb-0">{{ label('common.who.follow') | raw }}</h5>
        </div>
        <!-- Card header END -->
        <!-- Card body START -->
        <div class="card-body" id="whopeoplelist">
            {#
            {% set whoPeopleList = follow('whopeople', customerEntry.customer.userId, customerEntry.customer.provinceCode, 5, 'creationDesc') %}
            #}

            {% set whoPeopleList = follow('whopage', customerEntry.customer.userId, customerEntry.customer.provinceCode, 5, 'random') %}
            {% if whoPeopleList is not empty %}
            {% for whopeople in whoPeopleList %}
            <!-- Connection item START -->
            <div class="hstack gap-2 mb-3">
                <!-- Avatar -->
                <div class="avatar">                                                                                        
                    <a href="{{ paths('PAGE_BASE') }}/{{ whopeople.page.identifier }}">                                    
                        {% if whopeople.page.profileImageId is not empty %}
                        <img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ whopeople.page.profileImageId }}" alt="{{ whopeople.page.name }}"> 
                        {% else %}
                        <img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ whopeople.page.name }}"> 
                        {% endif %}
                    </a>
                </div>
                <!-- Title -->
                <div class="overflow-hidden text-truncate">
                    <a class="h6 mb-0" href="{{ paths('PAGE_BASE') }}/{{ whopeople.page.identifier }}">{{ whopeople.page.name }} </a>
                    <p class="mb-0 small text-truncate">{{ decode('area', whopeople.page.pageType | default('person') )}}</p>
                </div>
                <!-- Button -->
                <a class="btn btn-outline-primary rounded-circle icon-md ms-auto page-add-follow" data-value="inactive" data-reload-id="whopeoplelist" data-user="{{ user is not empty ? (user.profileType == 'customer' or user.profileType == 'system' ? 'logged' : 'notconfirmed') : 'unlogged' }}" data-page-id="{{ whopeople.page.id }}" href="#"><i class="fa-solid fa-plus"> </i></a>
            </div>
            <!-- Connection item END -->
            {% endfor %}
            {% else %}
            {{ label('sidebar.no.one.else.to.follow') | raw }}
            {% endif %}
        </div>
        <!-- Card body END -->
    </div>
</div>
<!-- Card follow START -->


