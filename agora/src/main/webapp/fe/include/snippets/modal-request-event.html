<!-- Modal request events START -->
<div class="modal fade" id="modalRequestEvent" tabindex="-1" aria-labelledby="modalLabelRequestEvent" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <!-- Modal feed header START -->
            <div class="modal-header">
                <h5 class="modal-title" id="modalLabelRequestEvent">{{ label('modal.request.event') | raw }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <!-- Modal feed header END -->
            <!-- Modal feed body START -->
            <div class="modal-body">
                <!-- Form START -->
                <form id="request-event" method="post" action="{{ paths('PAGE_EVENT_REQUEST') }}" class="row g-4">          
                    <!-- Location -->
                    <input type="hidden" id="pageId" name="pageId" class="form-control" value="{{ pageDb.id }}" required>
                    <input type="submit" class="d-none" disabled>
                    <div class="col-12">
                        <label class="form-label">{{ label('modal.location') | raw }}</label>
                        <input type="text" id="location" name="location" class="form-control" placeholder="{{ label('modal.location') | raw }}" required>
                    </div>
                    <!-- Description -->
                    <div class="col-12">
                        <label class="form-label">{{ label('modal.message') | raw }}</label>
                        <textarea id="message" name="message" class="form-control" rows="2" placeholder="{{ label('modal.message.placeholder') | raw }}"></textarea>
                    </div>                    
                </form>
                <!-- Form END -->
            </div>
            <!-- Modal feed body END -->
            <!-- Modal footer -->
            <!-- Button -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger-soft me-2" data-bs-dismiss="modal"> {{ label('modal.cancel') | raw }}</button>
                <button type="button" class="btn btn-success-soft request-event-btn" data-value="inactive" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}">{{ label('modal.send.request') | raw }}</button>
            </div>
        </div>
    </div>
</div>
<!-- Modal create events END -->