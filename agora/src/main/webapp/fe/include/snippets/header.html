<!-- =======================
Header START -->
<header class="navbar-light fixed-top header-static bg-mode">
    <a id="registerUri" style="display: none" rel="nofollow" href="{{ paths('REGISTER') }}/"></a>
    <a id="pageSearchUri" style="display: none" rel="nofollow" href="{{ paths('PAGE_BASE') }}/"></a>
    <a id="imageSearchUri" style="display: none" rel="nofollow" href="{{ paths('IMAGE_SYSTEM') }}?oid="></a>
    <a id="dataSearchUri" style="display: none" rel="nofollow" href="{{ paths('DATA_SEARCH') }}"></a>
    <a id="resultsUri" style="display: none;" rel="nofollow" href="{{ paths('RESULTS') }}?q=all&text=" rel="nofollow"></a>
    <a id="notificationAllReadUri" style="display: none" rel="nofollow" href="{{ paths('NOTIFICATION_ALL_READ') }}" rel="nofollow"></a>
    <a id="notificationReadUri" style="display: none" rel="nofollow" href="{{ paths('NOTIFICATION_READ') }}" rel="nofollow"></a>
    
    <!-- Logo Nav START -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            
            <!-- Logo START -->
            <a class="navbar-brand col-lg-2 me-0" href="{{ paths('HOME') }}">
                <img class="light-mode-item navbar-brand-item" src="{{ contextPath }}/fe/images/logo.svg" alt="logo">
                <img class="dark-mode-item navbar-brand-item" src="{{ contextPath }}/fe/images/logo.svg" alt="logo">
            </a>
            <!-- Logo END -->

            <!-- Responsive navbar toggler -->
            <button class="navbar-toggler ms-auto icon-md btn btn-light p-0 rounded-circle" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
                <i class="bi bi-search fs-6"> </i>
            </button>

            <!-- Main navbar START -->
            {% if user.profileType != 'unconfirmed' %}
            
                <div class="collapse navbar-collapse justify-content-center" id="navbarCollapse">
                            
                    {% if activePage != 'HOME' %}
                        <!-- Nav Search START -->
                        <div class="col-12 col-lg-6 search-header {{ page == 'PAGE' ? 'page-search' : '' }}">
                            <select class="searchall input-group d-none w-100"></select>  
                        </div>
                        <!-- Nav Search END -->
                    {% else %}
                        <div class="col-12 col-lg-6 search-header d-lg-none {{ page == 'PAGE' ? 'page-search' : '' }}">
                            <select class="searchall input-group d-none w-100"></select>  
                        </div>                    
                    {% endif %}
                </div>
            {% endif %}
            <!-- Main navbar END -->

            <!-- Nav right START -->
            <ul class="nav flex-nowrap align-items-center list-unstyled col-lg-2 justify-content-end">

                {% if user is not empty and user.profileType != 'unconfirmed' %}
                
                    <li class="nav-item ms-2">
                        <a class="nav-link bg-light icon-md btn btn-light p-0 rounded-circle" href="{{ paths('WALL') }}">
                            <i class="bi bi-newspaper fs-6"> </i>
                        </a>
                    </li>
                
                    <li class="nav-item dropdown ms-2">
                        {% set entityNotificationEntryWithCount = notification(user.id) %}
                        <a class="nav-link icon-md btn btn-light p-0 rounded-circle" href="#" id="notifDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside">
                            {% if entityNotificationEntryWithCount.newNotificationCount > 0 %}
                                <span class="badge-notif animation-blink"></span>
                            {% endif %}
                            <i class="bi bi-bell-fill fs-6"> </i>
                        </a>
                        <div class="dropdown-menu dropdown-animation dropdown-menu-end dropdown-menu-size-md p-0 shadow-lg border-0" aria-labelledby="notifDropdown">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6 class="m-0">{{ label('common.notifications') | raw }} {% if entityNotificationEntryWithCount.newNotificationCount > 0 %}<span class="badge bg-danger bg-opacity-10 text-danger ms-2">{{ entityNotificationEntryWithCount.newNotificationCount }} {{ label('common.new.plural') | raw }}</span>{% endif %}</h6>
                                    <a class="small notification-all-read" href="#">{{ label('common.mark.all.read') | raw }}</a>
                                </div>
                                <div class="card-body p-0" style="max-height: 400px; overflow-y: auto;">
                                    <ul class="list-group list-group-flush list-unstyled p-2">
                                        <!-- Notif item -->
                                        {% if entityNotificationEntryWithCount.entityNotificationEntryList is not empty %}
                                            {% for notificationEntry in entityNotificationEntryWithCount.entityNotificationEntryList %}
                                                {% set pageEvent = get('page', notificationEntry.entityNotification.validPageIds[0]) %}
                                                <li>
                                                    <div class="list-group-item list-group-item-action rounded {{ notificationEntry.entityNotification.isRead ? '' : 'badge-unread' }} d-flex border-0 mb-1 p-3">
                                                        <div class="avatar text-center d-none d-sm-inline-block">
                                                            <a href="{{ paths('PAGE_BASE') }}/{{ pageEvent.identifier }}" class="{{ notificationEntry.entityNotification.isRead ? '' : 'notification-read' }}" data-notification="{{ notificationEntry.entityNotification.id }}">
                                                            {% if pageEvent.profileImageId is not empty %}
                                                                <img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageEvent.profileImageId }}" alt="">
                                                            {% else %}
                                                                <img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ mypage.name }}"> 
                                                            {% endif %}                                                                                                                                                                         
                                                            </a>
                                                        </div>
                                                        <div class="ms-sm-3">
                                                            <div class=" d-flex">
                                                                {% if notificationEntry.entityNotification.type == 'changed' %}
                                                                    <p class="small mb-2">
                                                                        {{ (label('common.the.event') | raw) }}
                                                                        <a href="{{ paths('EVENT_BASE') }}/{{ notificationEntry.event.identifier }}" class="{{ notificationEntry.entityNotification.isRead ? '' : 'notification-read' }}" data-notification="{{ notificationEntry.entityNotification.id }}">
                                                                            {{ notificationEntry.event.name }}
                                                                        </a><span> in </span>
                                                                        <b>
                                                                            <a href="{{ paths('PAGE_BASE') }}/{{ pageEvent.identifier }}" class="{{ notificationEntry.entityNotification.isRead ? '' : 'notification-read' }}" data-notification="{{ notificationEntry.entityNotification.id }}">
                                                                                {{ pageEvent.name }}
                                                                            </a>
                                                                            {% if (notificationEntry.event.pageIds | length) > 1 %}
                                                                                {{ (label('common.other.pages') | raw) }}
                                                                            {% endif %}
                                                                        </b>
                                                                        {{ (label('common.modified.event') | raw) }}
                                                                        </p>
                                                                {% else %}
                                                                    {% set messageNotification = (label('common.has') | raw) + ' ' %}
                                                                    {% if (notificationEntry.event.pageIds | length) > 1 %}
                                                                        {% set messageNotification = (label('common.other.pages') | raw) + ' ' + (label('common.have') | raw) %}
                                                                    {% endif %}
                                                                    {% if notificationEntry.entityNotification.type == 'published' %}
                                                                        {% set messageNotification = messageNotification + ' ' + (label('common.published.event') | raw) %}
                                                                    {% elseif notificationEntry.entityNotification.type == 'deleted' %}
                                                                        {% set messageNotification = messageNotification + ' ' + (label('common.deleted.event') | raw) + ' ' %}
                                                                    {% endif%}
                                                                    <p class="small mb-2">
                                                                        <b>
                                                                            <a href="{{ paths('PAGE_BASE') }}/{{ pageEvent.identifier }}" class="{{ notificationEntry.entityNotification.isRead ? '' : 'notification-read' }}" data-notification="{{ notificationEntry.entityNotification.id }}">
                                                                                {{ pageEvent.name }}
                                                                            </a>
                                                                        </b>
                                                                        {% if notificationEntry.entityNotification.type == 'deleted' %}
                                                                            {{ messageNotification }}{{ notificationEntry.event.name }}
                                                                        {% else %}
                                                                            {{ messageNotification }}
                                                                            <a href="{{ paths('EVENT_BASE') }}/{{ notificationEntry.event.identifier }}" class="{{ notificationEntry.entityNotification.isRead ? '' : 'notification-read' }}" data-notification="{{ notificationEntry.entityNotification.id }}">
                                                                                {{ notificationEntry.event.name }}
                                                                            </a>
                                                                        {% endif %}
                                                                    </p>
                                                                {% endif %}
                                                                <p class="small ms-3 text-nowrap">{{ notificationEntry.entityNotification.date | date('dd/MM HH:mm') }}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            {% endfor %}
                                        {% else %}
                                            <div class="ms-sm-3">
                                                Nessuna notifica
                                            </div>
                                        {% endif %}
                                    </ul>
                                </div>
                                <div class="card-footer text-center">
                                    <a href="{{ paths('ACCOUNT_NOTIFICATIONS') }}" class="btn btn-sm btn-primary-soft">{{ label('common.view.all') | raw }}</a>
                                </div>
                            </div>
                        </div>
                    </li>
                    <!-- Notification dropdown END -->
                {% endif %}

                <li class="nav-item ms-2 dropdown">
                    <a class="nav-link btn btn-light rounded-circle icon-md p-0" href="#" id="profileDropdown" role="button" data-bs-auto-close="outside" data-bs-display="static" data-bs-toggle="dropdown" aria-expanded="false">
                        {% if customerEntry.customer.imageId is not empty %}
                            <img class="avatar-img rounded-circle" src="{{ paths('IMAGE') }}?oid={{ customerEntry.customer.imageId }}" alt=""/>
                        {% else %}
                            <i class="bi bi-person-fill fs-6"> </i>
                        {% endif %}
                    </a>
                    <ul class="dropdown-menu dropdown-animation dropdown-menu-end pt-3 small me-md-n3" aria-labelledby="profileDropdown">
                        {% if user is not empty %}
                            <!-- Profile info -->
                            <li class="px-3">
                                <div class="d-flex align-items-center position-relative">
                                    <!-- Avatar -->
                                    <div class="avatar me-3">
                                        {% if customerEntry.customer.imageId is not empty %}
                                            <img class="avatar-img rounded-circle" src="{{ paths('IMAGE') }}?oid={{ customerEntry.customer.imageId }}" alt=""/>
                                        {% else %}
                                            <img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="">                        
                                        {% endif %}                                    
                                    </div>
                                    <div>
                                        <a class="h6 stretched-link" href="#">{{ customerEntry.customer.name }} {{ customerEntry.customer.lastname }}</a>
                                        {% if user.profileType == 'unconfirmed' %}
                                            <p class="small text-danger m-0">{{ label('common.not.confirmed') | raw }}</p>
                                        {% else %}
                                            <p class="small m-0">{{ label('common.user') | raw }}</p>
                                        {% endif%}
                                    </div>
                                </div>
                                {% if user.profileType != 'unconfirmed' %}
                                    <a class="dropdown-item btn btn-primary-soft btn-sm my-2 text-center" href="{{ paths('PAGE_BASE') }}/{{ customerEntry.page.identifier }}">{{ label('common.go.to.my.page') | raw }}</a>
                                {% endif %}
                            </li>
                        {% endif %}
                        <li class="dropdown-item"><i class="bi bi-globe fa-fw me-2"></i><a class="{{ language == 'it' ? '' : 'text-reset' }}" href="{{ contextPath }}{{ paths['it'] }}">Italiano</a> / <a class="{{ language == 'en' ? '' : 'text-reset' }}" href="{{ contextPath }}{{ paths['en'] }}">English</a></li>
                        {% if user is not empty %}                        
                            <li><a class="dropdown-item bg-danger-soft-hover" href="{{ paths('LOGOUT_DO') }}"><i class="bi bi-power fa-fw me-2"></i>Logout</a></li>                                                
                        {% else %}
                            <li><a class="dropdown-item" href="{{ paths('ACCESS') }}"><i class="bi bi-box-arrow-in-right fa-fw me-2"></i>{{ label('common.login') | raw }}</a></li>
                            <li><a class="dropdown-item" href="{{ paths('REGISTER') }}"><i class="bi bi-person-add fa-fw me-2"></i>{{ label('common.register') | raw }}</a></li>
                        {% endif %}                        
                    </ul>
                </li>
                {% if user is not empty %}
                    <!-- Advanced filter responsive toggler START -->
                    <div class="d-flex align-items-center d-lg-none">
                        <button class="border-0 bg-transparent p-0" type="button" data-bs-toggle="offcanvas" data-bs-target="#offcanvasSideNavbar" aria-controls="offcanvasSideNavbar">
                            <span class="btn btn-agora"><i class="fa-solid fa-bars align-middle"></i></span>
                        </button>
                    </div>
                    <!-- Advanced filter responsive toggler END -->
                {% endif %}

                <!-- Profile START -->

            </ul>
            <!-- Nav right END -->
        </div>
    </nav>
    <!-- Logo Nav END -->
</header>
<!-- =======================
Header END -->