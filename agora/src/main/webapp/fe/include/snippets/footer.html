<!-- Footer START -->
<footer class="pt-5 bg-mode">
    <div class="container pt-4">
        <div class="row g-4">
            <div class="col-sm-6 col-lg-3">
                <!-- Footer Widget -->
                <img class="light-mode-item navbar-brand-item" src="{{ contextPath }}/fe/images/logo.svg" alt="logo" width="150">
                <img class="dark-mode-item navbar-brand-item" src="{{ contextPath }}/fe/images/logo.svg" alt="logo" width="150">
                <p class="mt-3">{{ label('footer.description') | raw }}</p>
            </div>
            <div class="col-sm-6 col-lg-3">
                <!-- Footer Widget -->
                <h5 class="mb-4">{{ label('footer.about') | raw }}</h5>
                <ul class="nav flex-column">
                    <li class="nav-item"><a class="nav-link pt-0" href="{{ paths('ABOUT') }}">{{ label('footer.about') | raw }}</a></li>
                    <li class="nav-item"><a class="nav-link" href="{{ paths('MEDIAKIT') }}">{{ label('footer.widget') | raw }}</a></li>                    
                </ul>
            </div>
            <div class="col-sm-6 col-lg-3">
                <!-- Footer Widget -->
                <h5 class="mb-4">{{ label('footer.profile') | raw }}</h5>
                <ul class="nav flex-column">
                    {% if user is not empty %}                        
                        {% if user.profileType != 'unconfirmed' %}
                        <li class="nav-item"><a class="nav-link" href="{{ paths('PAGE_BASE') }}/{{ customerEntry.page.identifier }}"><i class="bi bi-person-fill fa-fw me-2"></i>{{ label('common.go.to.my.page') | raw }}</a></li>
                        {% endif %}
                        <li class="nav-item"><a class="nav-link" href="{{ paths('LOGOUT_DO') }}"><i class="bi bi-power fa-fw me-2"></i>Logout</a></li>                                                
                    {% else %}
                        <li class="nav-item"><a class="nav-link" href="{{ paths('ACCESS') }}"><i class="bi bi-box-arrow-in-right fa-fw me-2"></i>{{ label('common.login') | raw }}</a></li>
                        <li class="nav-item"><a class="nav-link" href="{{ paths('REGISTER') }}"><i class="bi bi-person-add fa-fw me-2"></i>{{ label('common.register') | raw }}</a></li>
                    {% endif %}                                                  
                </ul>
            </div>
            <div class="col-sm-6 col-lg-3">
                <!-- Footer Widget -->
                <h5 class="mb-4">{{ label('footer.legal') | raw }}</h5>
                <ul class="nav flex-column">                    
                    {% if (language is empty) or (language == 'it') %}
                    <li class="nav-item">
                        <a class="nav-link" href="https://www.iubenda.com/privacy-policy/33876721">{{ label('footer.privacy.policy') | raw }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="https://www.iubenda.com/privacy-policy/33876721/cookie-policy">{{ label('footer.cookie.policy') | raw }}</a>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="https://www.iubenda.com/privacy-policy/77929162">{{ label('footer.privacy.policy') | raw }}</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="https://www.iubenda.com/privacy-policy/77929162/cookie-policy">{{ label('footer.cookie.policy') | raw }}</a>
                    </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link iubenda-cs-preferences-link" href="#">{{ label('footer.update.preferences') | raw }}</a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <hr class="mb-0 mt-5">
    <div class="bg-light py-3">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <!-- Copyright START -->
                    <p class="text-center mb-0">{{ label('common.copyright') | raw }}</p>
                    <!-- Copyright END -->
                </div>
            </div>
        </div>
    </div>
</footer>
<!-- Footer END -->