<!-- Modal request events START -->
<div class="modal fade" id="modalReportEvent" tabindex="-1" aria-labelledby="modalReportEvent" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <!-- Modal feed header START -->
            <div class="modal-header">
                <h5 class="modal-title">{{ label('modal.report.event') | raw }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <!-- Modal feed header END -->
            <!-- Modal feed body START -->
            <div class="modal-body">
                <!-- Form START -->
                <form id="report-event" method="post" action="{{ paths('EVENT_REPORT_SEND') }}" class="row g-4">          
                    <!-- Location -->
                    <input type="hidden" id="eventId" name="eventId" class="form-control" required>
                    <div class="col-12">
                        <label class="form-label">{{ label('modal.report.why') | raw }}</label>
                        <div class="form-group">
                            <select class="select-search-standard form-control" id="why" name="why" required>
                                <option value="">-</option>                                
                                <option value="errate">{{ label('modal.report.why.1') | raw }}</option>
                                <option value="rimozione">{{ label('modal.report.why.2') | raw }}</option>
                                <option value="altro">{{ label('modal.report.why.3') | raw }}</option>
                            </select>
                        </div>
                    </div>
                    <!-- Description -->
                    <div class="col-12">
                        <label class="form-label">{{ label('modal.message') | raw }}</label>
                        <textarea id="message" name="message" class="form-control" rows="2" placeholder="{{ label('modal.report.why.description') | raw }}"></textarea>
                    </div>                    
                </form>
                <!-- Form END -->
            </div>
            <!-- Modal feed body END -->
            <!-- Modal footer -->
            <!-- Button -->
            <div class="modal-footer">
                <button type="button" class="btn btn-danger-soft me-2" data-bs-dismiss="modal"> {{ label('modal.cancel') | raw }}</button>
                <button type="button" class="btn btn-success-soft report-event-btn" data-value="inactive" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}">{{ label('modal.send.request') | raw }}</button>
            </div>
        </div>
    </div>
</div>
<!-- Modal create events END -->
