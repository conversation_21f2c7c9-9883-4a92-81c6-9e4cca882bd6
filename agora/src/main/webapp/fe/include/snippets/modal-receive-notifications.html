<!-- Modal receive notifications START -->
<div class="modal fade" id="modalReceiveNotifications" tabindex="-1" aria-labelledby="modalLabelReceiveNotifications" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <!-- Modal feed header START -->
      <div class="modal-header">
        <h5 class="modal-title" id="modalLabelReceiveNotifications">{{ label('modal.receive.notification') | raw }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <!-- Modal feed header END -->
      <!-- Modal feed body START -->
      <div class="modal-body">
        <!-- Form START -->
        <form class="row g-4">          
          <!-- Location -->
          <div class="col-12">
            <label class="form-label">{{ label('modal.location') | raw }}</label>
            <input type="email" class="form-control" placeholder="{{ label('modal.location') | raw }}">
          </div>
          <!-- Time -->
          <div class="col-12">
            <label class="form-label">{{ label('modal.period') | raw }}</label>
            <input type="text" class="form-control flatpickr" data-enableTime="true" data-noCalendar="true" placeholder="Select time">
          </div>
          <!-- Description -->
          <div class="col-12">
            <label class="form-label">{{ label('modal.message') | raw }}</label>
            <textarea class="form-control" rows="2" placeholder="{{ label('modal.message.placeholder') | raw }}"></textarea>
          </div>                    
        </form>
        <!-- Form END -->
      </div>
      <!-- Modal feed body END -->
      <!-- Modal footer -->
      <!-- Button -->
      <div class="modal-footer">
        <button type="button" class="btn btn-danger-soft me-2" data-bs-dismiss="modal"> {{ label('modal.cancel') | raw }}</button>
        <button type="button" class="btn btn-success-soft">{{ label('modal.send.request') | raw }}</button>
      </div>
    </div>
  </div>
</div>
<!-- Modal create events END -->