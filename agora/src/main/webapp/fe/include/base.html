<!DOCTYPE html>
<html lang="it" prefix="og: http://ogp.me/ns#">

    <head>        
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

        <!-- FAVICON -->              
        <link rel="apple-touch-icon" sizes="180x180" href="{{ contextPath }}/fe/images/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="{{ contextPath }}/fe/images/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="{{ contextPath }}/fe/images/favicon-16x16.png">
        <link rel="manifest" href="{{ contextPath }}/fe/images/site.webmanifest">
        <link rel="mask-icon" href="{{ contextPath }}/fe/images/safari-pinned-tab.svg" color="#8b734d">
        <meta name="msapplication-TileColor" content="#8b734d">
        <meta name="theme-color" content="#8b734d">

        <!-- TITLE -->
        <title>{% block title %}{% endblock %}</title>

        <!-- PRECONNECT -->
        <link rel="preconnect" href="https://cdn.iubenda.com">
        <link rel="preconnect" href="https://www.iubenda.com">          
        <link rel="preconnect" href="https://hits-i.iubenda.com">  
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>        
        <link href="https://fonts.googleapis.com/css2?family=Kumbh+Sans:wght@100..900&display=swap" rel="stylesheet">
        <link rel="preload" href="{{ contextPath }}/fe/vendor/font-awesome/css/all.min.css" as="style">   
        <link rel="preload" href="{{ contextPath }}/fe/vendor/bootstrap-icons/bootstrap-icons.css" as="style">        
        <link rel="preload" href="{{ contextPath }}/fe/css/style.css" as="style">        
        <link rel="preload" href="{{ contextPath }}/fe/css/custom.css?{{ buildNumber }}" as="style">
        <link href="https://siteria.it/libs/jquery-confirm/3.2.3/dist/jquery-confirm.min.css" rel="stylesheet" type="text/css">

        <!-- IUBENDA-->
        {% if (language is empty) or (language == 'it') %}
            <script type="text/javascript">
                var _iub = _iub || [];
                _iub.csConfiguration = {"countryDetection": true, "enableFadp": true, "enableLgpd": true, "enableUspr": true, "lgpdAppliesGlobally": false, "perPurposeConsent": true, "siteId": 3605346, "cookiePolicyId": 33876721, "lang": "it", "banner": {"acceptButtonDisplay": true, "closeButtonDisplay": false, "customizeButtonDisplay": true, "explicitWithdrawal": true, "listPurposes": true, "logo": null, "position": "float-top-center", "rejectButtonDisplay": true, "showPurposesToggles": true}};
            </script>
            <script type="text/javascript" src="https://cs.iubenda.com/autoblocking/3605346.js"></script>
            <script type="text/javascript" src="//cdn.iubenda.com/cs/gpp/stub.js"></script>
            <script type="text/javascript" src="//cdn.iubenda.com/cs/iubenda_cs.js" charset="UTF-8" async></script>
        {% else %}
            <script type="text/javascript">
            var _iub = _iub || [];
            _iub.csConfiguration = {"countryDetection":true,"enableFadp":true,"enableLgpd":true,"enableUspr":true,"lgpdAppliesGlobally":false,"perPurposeConsent":true,"siteId":3605346,"cookiePolicyId":77929162,"lang":"en","banner":{"acceptButtonDisplay":true,"closeButtonDisplay":false,"customizeButtonDisplay":true,"explicitWithdrawal":true,"listPurposes":true,"logo":null,"position":"float-top-center","rejectButtonDisplay":true,"showPurposesToggles":true}};
            </script>
            <script type="text/javascript" src="https://cs.iubenda.com/autoblocking/3605346.js"></script>
            <script type="text/javascript" src="//cdn.iubenda.com/cs/gpp/stub.js"></script>
            <script type="text/javascript" src="//cdn.iubenda.com/cs/iubenda_cs.js" charset="UTF-8" async></script>
        {% endif %}
        
        <script type="text/javascript">var _iub = _iub || {}; _iub.cons_instructions = _iub.cons_instructions || []; _iub.cons_instructions.push(["init", {api_key: "l5Elnl002wgdlLfJNt2isUqn6KGtevwq"}]);</script><script type="text/javascript" src="https://cdn.iubenda.com/cons/iubenda_cons.js" async></script>
        
        <!-- GTM -->
        {% if not isLocal %}
            <!-- GA4 -->
            <script type="text/plain" class="_iub_cs_activate" data-iub-purposes="4" async src="https://www.googletagmanager.com/gtag/js?id=G-MSHVB15TVF"></script>
            <script>
                window.dataLayer = window.dataLayer || [];
                function gtag() {
                    dataLayer.push(arguments);
                }
                gtag('js', new Date());

                gtag('config', 'G-MSHVB15TVF', {'allow_enhanced_conversions': true});
            </script>
        {% endif %}

        <!-- URL CANONICAL -->
        {% block canonical %}{% endblock %}

        <!-- SOCIAL CARDS -->
        {% block socialcards %}{% endblock %}

        <!-- MAIN CSS -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap">
        <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/OverlayScrollbars-master/css/OverlayScrollbars.min.css">
        <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/font-awesome/css/all.min.css">
        <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/bootstrap-icons/bootstrap-icons.css">	
        <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/select2/select2.min.css">    

        <!-- PAGE CSS -->
        {% block pagecss %}{% endblock %}

        <!-- THEME CSS -->
<!--        <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/css/circular.css">        -->
        <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/css/style.css">        
        <link rel="stylesheet" href="{{ contextPath }}/fe/css/custom.css?{{ buildNumber }}">

        <!-- EXTRA HEAD -->
        {% block pagehead %}{% endblock %}                

        <!-- FB BRAND SAFETY -->        
        <meta name="facebook-domain-verification" content="" />

        <script src="https://siteria.it/libs/infinity-ajax-scroll/3.1.0/infinite-ajax-scroll.min.js"></script>
    </head>

    <body>        

        <!-- HEADER -->        
        {% include "fe/include/snippets/header.html" %}        

        <!-- CONTENT -->
        <main>
            {% block content %}{% endblock %}
        </main>

        {% if activePage == 'HOME' %}
            <!-- FOOTER -->        
            {% include "fe/include/snippets/footer.html" %}         
        {% endif %}

        <!-- THEME SCRIPTS -->     
        <script src="{{ contextPath }}/fe/vendor/tiny-slider/dist/tiny-slider.js"></script>
        <script src="{{ contextPath }}/fe/vendor/OverlayScrollbars-master/js/OverlayScrollbars.min.js"></script>
        <script src="{{ contextPath }}/fe/vendor/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
        <script src="{{ contextPath }}/fe/vendor/glightbox-master/dist/js/glightbox.min.js"></script>
        <script src="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.min.js"></script>
        <script src="{{ contextPath }}/fe/vendor/plyr/plyr.js"></script>
        <script src="{{ contextPath }}/fe/vendor/pswmeter/pswmeter.min.js"></script>        
        <script src="{{ contextPath }}/fe/js/jquery.min.js"></script>
        <script src="https://siteria.it/libs/jquery-blockui/2.70.0/jquery.blockUI.min.js"></script>
        <script defer src="https://siteria.it/libs/uri/1.18.4/URI.js"></script>        
        <script defer src="{{ contextPath }}/fe/js/sweetalert.js"></script>        
        <script type="text/javascript" src="{{ contextPath }}/fe/js/jquery.cookie.min.js"></script>
        <!-- form validation and sending to mail -->
        <script src="{{ contextPath }}/fe/js/jquery.form.js"></script>
        <script src="{{ contextPath }}/fe/js/jquery.validate.min.js"></script>
        <script src="{{ contextPath }}/fe/vendor/select2/select2.min.js"></script>
        {% if (language is empty) or (language == 'it') %}
            <script src="{{ contextPath }}/fe/vendor/select2/it.min.js"></script>
        {% endif %}
        <script defer src="{{ contextPath }}/fe/js/pages/label.js?{{ buildNumber }}"></script>
        <script src="https://siteria.it/libs/jquery-confirm/3.2.3/dist/jquery-confirm.min.js"></script>
        <div id="common.search.page" style="display: none">{{ label('common.search.page') | raw }}</div>
        <div id="common.register" style="display: none">{{ label('common.register') | raw }}</div>
        <div id="common.tryagain" style="display: none">{{ label('common.tryagain') | raw }}</div>
        <div id="common.recover" style="display: none">{{ label('common.recover') | raw }}</div>
        <div id="common.must.registered.event" style="display: none">{{ label('common.must.registered.event') | raw }}</div>
        <div id="common.need.help" style="display: none">{{ label('common.need.help') | raw }}</div>
        <div id="common.contact.assistance" style="display: none">{{ label('common.contact.assistance') | raw }}</div>
        <div id="common.event.added" style="display: none">{{ label('common.event.added') | raw }}</div>
        <div id="common.event.removed" style="display: none">{{ label('common.event.removed') | raw }}</div>
        <div id="common.event.updated" style="display: none">{{ label('common.event.updated') | raw }}</div>
        <div id="common.page.update.success" style="display: none">{{ label('common.page.update.success') | raw }}</div>
        <div id="common.page.add.success" style="display: none">{{ label('common.page.add.success') | raw }}</div>
        <div id="common.notification.add.success" style="display: none">{{ label('common.notification.add.success') | raw }}</div>
        <div id="common.notification.update.success" style="display: none">{{ label('common.notification.update.success') | raw }}</div>
        <div id="common.add.failed" style="display: none">{{ label('common.add.failed') | raw }}</div>
        <div id="common.remove.failed" style="display: none">{{ label('common.remove.failed') | raw }}</div>
        <div id="common.email.sent" style="display: none">{{ label('common.email.sent') | raw }}</div>
        <div id="common.check.inbox" style="display: none">{{ label('common.check.inbox') | raw }}</div>
        <div id="common.mail.not.sent" style="display: none">{{ label('common.mail.not.sent') | raw }}</div>
        <div id="common.ops.error" style="display: none">{{ label('common.ops.error') | raw }}</div>
        <div id="common.delete.failed" style="display: none">{{ label('common.delete.failed') | raw }}</div>
        <div id="common.insertion.failed" style="display: none">{{ label('common.insertion.failed') | raw }}</div>
        <div id="common.continue" style="display: none">{{ label('common.continue') | raw }}</div>
        <div id="common.event.created" style="display: none">{{ label('common.event.created') | raw }}</div>
        <div id="common.page.follow.added" style="display: none">{{ label('common.page.follow.added') | raw }}</div>
        <div id="common.page.follow.removed" style="display: none">{{ label('common.page.follow.removed') | raw }}</div>
        <div id="common.page.notify.added" style="display: none">{{ label('common.page.notify.added') | raw }}</div>
        <div id="common.page.notify.removed" style="display: none">{{ label('common.page.notify.removed') | raw }}</div>
        <div id="common.data.saved.correctly" style="display: none">{{ label('common.data.saved.correctly') | raw }}</div>
        <div id="common.operation.error" style="display: none">{{ label('common.operation.error') | raw }}</div>
        <div id="common.information.saved.success" style="display: none">{{ label('common.information.saved.success') | raw }}</div>
        <div id="common.must.be.registered.page" style="display: none">{{ label('common.must.be.registered.page') | raw }}</div>
        <div id="common.must.be.registered.report.page" style="display: none">{{ label('common.must.be.registered.report.page') | raw }}</div>
        <div id="common.must.be.registered.request.event" style="display: none">{{ label('common.must.be.registered.request.event') | raw }}</div>
        <div id="common.must.be.registered.request.notification" style="display: none">{{ label('common.must.be.registered.request.notification') | raw }}</div>
        <div id="common.must.be.registered.report.event" style="display: none">{{ label('common.must.be.registered.report.event') | raw }}</div>
        <div id="common.search" style="display: none">{{ label('common.search') | raw }}</div>                
        <div id="common.confirm" style="display: none">{{ label('common.confirm') | raw }}</div>
        <div id="common.cancel" style="display: none">{{ label('common.cancel') | raw }}</div>
        <div id="common.operation.completed" style="display: none">{{ label('common.operation.completed') | raw }}</div>
        <div id="common.data.saved" style="display: none">{{ label('common.data.saved') | raw }}</div>        
        <div id="common.data.save.error" style="display: none">{{ label('common.data.save.error') | raw }}</div>
        <div id="email.sent.success" style="display: none">{{ label('email.sent.success') | raw }}</div>
        <div id="email.credentials.sent" style="display: none">{{ label('email.credentials.sent') | raw }}</div>
        <div id="common.go.to.login" style="display: none">{{ label('common.go.to.login') | raw }}</div>        
        <div id="common.data.save.failed" style="display: none">{{ label('common.data.save.failed') | raw }}</div>
        <div id="common.something.went.wrong" style="display: none">{{ label('common.something.went.wrong') | raw }}</div>        

        <script defer src="{{ contextPath }}/fe/js/pages/search.js?{{ buildNumber }}"></script>
        {% if (language is empty) or (language == 'it') %}
        <script src="{{ contextPath }}/fe/js/localization/messages_it.min.js"></script>
        {% endif %}            
        <script>
            (function () {

                // INITIALIZATION OF BLOCKUI
                // =======================================================
                $.blockUI.defaults = {
                    message: '<div class="fancybox-loading">',
                    css: {
                        padding: 0,
                        margin: 0,
                        top: '45%',
                        left: '50%',
                        right: '50%',
                        border: 'none',
                        backgroundColor: 'transparent',
                        cursor: 'wait'
                    },
                    overlayCSS: {
                        backgroundColor: '#000',
                        opacity: 0.4,
                        cursor: 'wait'
                    },
                    baseZ: 1100,
                    showOverlay: true
                };

            })();
        </script>
        <script>
            (function () {
                $('.notification-all-read').off();
                $('.notification-all-read').click(function (event) {
                    event.preventDefault();

                    // prepare call
                    var url = new URI($('#notificationAllReadUri').attr('href'));
                    if (!url) {
                        console.error('missing url');
                        return false;
                    }
                    $.ajax({
                        url: url,
                        type: 'POST',
                        data: null,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success:
                                function (returndata) {
                                    window.location.reload();
                                },
                        error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();

                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Aggiornamento non riuscito.'
                                    });
                                }
                    });

                    return false;
                });

                $('.notification-read').click(function (event) {
                    // prepare call
                    var url = new URI($('#notificationReadUri').attr('href'));
                    if (!url) {
                        console.error('missing url');
                        return false;
                    }

                    url.removeSearch("entityNotificationId");
                    url.addSearch("entityNotificationId", $(this).attr('data-notification'));

                    $.ajax({
                        url: url,
                        type: 'POST',
                        data: null,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success:
                                function (returndata) {

                                },
                        error:
                                function (response, status, errorThrown) {

                                }
                    });
                });
            })();
        </script>
        <!-- PAGE SCRIPTS -->
        {% block pagescripts %}{% endblock %}

        <!-- THEME FUNCTIONS -->
        <script src="{{ contextPath }}/fe/js/functions.js"></script>

        <!-- LINKING DATA -->
        {% block schema %}{% endblock %}

        <script type="application/ld+json">
            {
            "@context": "http://schema.org",
            "@type": "Organization",
            "name": "Agorapp",
            "url": "https://www.agor.app",
            "logo": "{{ contextPath }}/fe/images/logo.svg",
            "sameAs": [
            "",
            "",
            ""
            ]
            }
        </script>
    </body>

</html>
