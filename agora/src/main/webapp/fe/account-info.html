{% extends "fe/include/base.html" %}

{% set activePage = 'INFO' %}

{% block title %}Account | Agorapp{% endblock %}

{% block pagecss %}
<link href="{{ contextPath }}/fe/vendor/slim/slim.min.css" rel="stylesheet" type="text/css" media="all">
<link href="{{ contextPath }}/fe/vendor/choices.js/public/assets/styles/choices.min.css" rel="stylesheet" type="text/css" media="all">
{% endblock %}

{% block content %}
<a id="dataCitiesUri" style="display: none" href="{{ paths('DATA_CITIES') }}?name="></a>

<a id="accountInfoEditSaveUri" style="display: none" href="{{ paths('ACCOUNT_INFO_EDIT_SAVE') }}" rel="nofollow"></a>
<a id="accountRemoveUri" style="display: none" href="{{ paths('ACCOUNT_REMOVE') }}" rel="nofollow"></a>
<a id="accountDeletedUri" style="display: none" href="{{ paths('ACCOUNT_DELETED') }}" rel="nofollow"></a>
<a id="accountSecurityUsernameSaveUri" style="display: none" href="{{ paths('ACCOUNT_SECURITY_USERNAME_SAVE') }}" rel="nofollow"></a>
<a id="accountSecurityChangePasswordSaveUri" style="display: none" href="{{ paths('ACCOUNT_SECURITY_CHANGE_PASSWORD_SAVE') }}" rel="nofollow"></a>
<a id="accountSecurityKeywordSaveUri" style="display: none" href="{{ paths('ACCOUNT_SECURITY_KEYWORD_SAVE') }}" rel="nofollow"></a>
<a id="accountSecuritySmsSendUri" style="display: none" href="{{ paths('ACCOUNT_SECURITY_SMS_DO') }}" rel="nofollow"></a>
<a id="accountSecuritySmsVerifyUri" style="display: none" href="{{ paths('ACCOUNT_SECURITY_SMS_VERIFY') }}" rel="nofollow"></a>
<div id="email.sent" style="display: none">{{ label('email.sent') | raw }}</div>
<div id="email.check.inbox" style="display: none">{{ label('email.check.inbox') | raw }}</div>
<div id="email.send.error" style="display: none">{{ label('email.send.error') | raw }}</div>
<div id="common.continue" style="display: none">{{ label('common.continue') | raw }}</div>
<div id="common.need.help" style="display: none">{{ label('common.need.help') | raw }}</div>
<div id="common.contact.support" style="display: none">{{ label('common.contact.support') | raw }}</div>
<div id="pages.page.removed" style="display: none">{{ label('pages.page.removed') | raw }}</div>
<!-- Container START -->
<div class="container">
    <div class="row g-0">

        <!-- Sidenav START -->
        <div class="col-lg-3">
            {% include "fe/include/snippets/sidenav-left.html" %}
        </div>
        <!-- End Col -->

        <!-- Main content START -->
        <div class="col-lg-6 vstack">                               
            <!-- Account settings START -->
            <div class="card border-lg-lr border-lg-b">

                <!-- Card header START -->
                <div class="card-header border-0 pb-0">
                    {% if user.profileType == 'unconfirmed' %}
                    <!-- Alert -->
                    <div class="alert alert-danger text-center card-alert" role="alert">
                        {{ label('account.not.confirm') | raw }} <a class="alert-link" id="account-confirm-send" href="{{ paths('ACCOUNT_CONFIRM_SEND') }}">{{ label('account.send.link') | raw }}<i class="bi-chevron-right small ms-1"></i></a>
                    </div>
                    <!-- End Alert -->
                    {% endif %}
                    <h1 class="h5 card-title">Account</h1>
                    <p class="mb-0">{{ label('account.info.base') | raw }}</p>
                </div>
                <!-- Card header END -->                    

                <!-- Card body START -->
                <div class="card-body">
                    <!-- Form settings START -->
                    <form class="row g-3" id="form-info-edit" method="post" novalidate="novalidate">                                                        
                        <!-- First name -->
                        <div class="col-sm-6">
                            <label class="form-label">{{ label('common.name') | raw }}</label>
                            <input type="text" class="form-control" name="name" id="name" maxlength="250" placeholder="{{ label('common.name') | raw }}" aria-label="{{ label('common.name') | raw }}" value="{{ customerEntry.customer.name }}" required>
                        </div>
                        <!-- Last name -->
                        <div class="col-sm-6">
                            <label class="form-label">{{ label('common.lastname') | raw }}</label>
                            <input type="text" class="form-control" name="lastname" id="lastname" maxlength="250" placeholder="{{ label('common.lastname') | raw }}" aria-label="{{ label('common.lastname') | raw }}" value="{{ customerEntry.customer.lastname }}" required>
                        </div>                                                        
                        <!-- Page information -->                            
                        <!-- Button  -->
                        <div class="col-12 text-end">
                            <button type="submit" class="btn btn-primary mb-0">{{ label('common.save') | raw }}</button>
                        </div>
                    </form>
                    <!-- Settings END -->
                </div>
                <!-- Card body END -->
            </div>
            <!-- Account settings END -->   

            <!-- Account settings START -->
            <div class="card border-lg-lr border-lg-b">

                <!-- Title START -->
                <div class="card-header border-0 pb-0">
                    {% if user.profileType == 'unconfirmed' %}
                    <!-- Alert -->
                    <div class="alert alert-danger text-center card-alert" role="alert">
                        {{ label('account.not.confirm') | raw }} <a class="alert-link" id="account-confirm-send" href="{{ paths('ACCOUNT_CONFIRM_SEND') }}">{{ label('account.send.link') | raw }}<i class="bi-chevron-right small ms-1"></i></a>
                    </div>
                    <!-- End Alert -->
                    {% endif %}
                    <h1 class="h5 card-title">{{ label('account.info.email.profile') | raw }}</h1>
                    <p class="mb-0">{{ label('account.info.change.email') | raw }}</p>
                </div>
                <!-- Card header START -->                    

                <!-- Card body START -->
                <div class="card-body">
                    <!-- Form settings START -->
                    <form id="form-account-edit" class="row g-3 needs-validation" novalidate>

                        <div class="col-sm-8 col-md-10 d-none d-sm-flex"></div>
                        <!-- Email -->
                        <div class="col-sm-6">
                            <label class="form-label">{{ label('common.email') | raw }}</label>
                            <input type="email" class="form-control" name="email" id="email" placeholder="{{ label('account.info.write.new.email') | raw }}" aria-label="{{ label('account.info.write.new.email') | raw }}" required pattern="[^@\s]+@[^@\s]+\.[a-z]{2,}">
                        </div>
                        <!-- Password -->
                        <div class="col-sm-6">
                            <label class="form-label">{{ label('account.info.current.password') | raw }}</label>
                            <input type="password" class="form-control" name="currentPassword" id="currentChangePassword" placeholder="{{ label('account.info.write.current.password') | raw }}" aria-label="{{ label('account.info.write.current.password') | raw }}" required minlength="3">
                        </div>                                                        
                        <!-- Page information -->                            
                        <!-- Button  -->
                        <div class="col-12 text-end">
                            <button type="submit" class="btn btn-primary mb-0">{{ label('common.save') | raw }}</button>
                        </div>
                    </form>
                    <!-- Settings END -->
                </div>
                <!-- Card body END -->
            </div>
            <!-- Account settings END -->   

            <!-- Account settings START -->
            <div class="card border-lg-lr border-lg-b">

                <!-- Title START -->
                <div class="card-header border-0 pb-0">
                    {% if user.profileType == 'unconfirmed' %}
                    <!-- Alert -->
                    <div class="alert alert-danger text-center card-alert" role="alert">
                        {{ label('account.not.confirm') | raw }} <a class="alert-link" id="account-confirm-send" href="{{ paths('ACCOUNT_CONFIRM_SEND') }}">{{ label('account.send.link') | raw }}<i class="bi-chevron-right small ms-1"></i></a>
                    </div>
                    <!-- End Alert -->
                    {% endif %}
                    <h1 class="h5 card-title">{{ label('common.password') | raw }}</h1>
                    <p class="mb-0">{{ label('account.info.change.password.access') | raw }}</p>
                </div>
                <!-- Card header START -->


                <!-- Card body START -->
                <div class="card-body">
                    <!-- Form settings START -->
                    <form id="form-account-change-password-edit" class="row g-3 needs-validation" novalidate>
                        <!-- Password -->
                        <div class="col-12">                                                                                               
                            <label class="form-label">{{ label('account.info.current.password') | raw }}</label>
                            <input type="password" class="form-control" name="currentPassword" id="currentPassword" placeholder="{{ label('account.info.write.current.password') | raw }}" aria-label="{{ label('account.info.write.current.password') | raw }}" minlength="3" required>
                        </div>
                        <!-- New Password -->
                        <div class="col-12">
                            <label class="form-label">{{ label('account.info.new.password') | raw }}</label>
                            <input type="password" class="form-control" name="newPassword" id="newPassword" placeholder="{{ label('account.info.write.new.password') | raw }}" aria-label="{{ label('account.info.write.new.password') | raw }}" minlength="8" required>
                        </div>                                                        
                        <!-- Confirm new password -->
                        <div class="col-12">
                            <label class="form-label">{{ label('account.info.new.password.confirm') | raw }}</label>
                            <input type="password" class="form-control" name="confirmNewPassword" id="confirmNewPassword" placeholder="{{ label('account.info.confirm.your.password') | raw }}" aria-label="{{ label('account.info.confirm.your.password') | raw }}" minlength="8" required>
                            <p class="card-text small mt-3">{{ label('account.info.password.secure') | raw }}</p>
                            <ul class="small">
                                <li>{{ label('account.info.password.secure1') | raw }}</li>
                                <li>{{ label('account.info.password.secure2') | raw }}</li>
                                <li>{{ label('account.info.password.secure3') | raw }}</li>
                                <li>{{ label('account.info.password.secure4') | raw }}</li>
                            </ul>
                        </div>                            
                        <!-- Button  -->
                        <div class="col-12 text-end">
                            <button type="submit" class="btn btn-primary mb-0">{{ label('common.save') | raw }}</button>
                        </div>
                    </form>
                    <!-- Settings END -->
                </div>
                <!-- Card body END -->
            </div>
            <!-- Account settings END --> 

            <!-- Account settings START -->
            <div class="card mb-4 border-lg-lr border-lg-b">

                <!-- Title START -->
                <div class="card-header border-0 pb-0">
                    {% if user.profileType == 'unconfirmed' %}
                    <!-- Alert -->
                    <div class="alert alert-danger text-center card-alert" role="alert">
                        {{ label('account.not.confirm') | raw }} <a class="alert-link" id="account-confirm-send" href="{{ paths('ACCOUNT_CONFIRM_SEND') }}">{{ label('account.send.link') | raw }}<i class="bi-chevron-right small ms-1"></i></a>
                    </div>
                    <!-- End Alert -->
                    {% endif %}
                    <h1 class="h5 card-title">{{ label('account.info.secret.key') | raw }}</h1>
                    <p class="mb-0">{{ label('account.info.change.secret.key') | raw }}</p>
                </div>
                <!-- Card header START -->


                <!-- Card body START -->
                <div class="card-body">
                    <!-- Form settings START -->
                    <form id="form-account-keyword-edit" class="row g-3 needs-validation" novalidate>
                        <!-- Password -->
                        <div class="col-12">
                            <p class="card-text"><span class="text-dark fw-semibold">{{ label('account.info.secret.what') | raw }}</span> {{ label('account.info.secret.is') | raw }}</p>
                            <p class="card-text"><span class="text-dark fw-semibold">{{ label('account.info.secret.how') | raw }}</span> {{ label('account.info.secret.how.response') | raw }}</p>
                            <label class="form-label">{{ label('common.secret.key') | raw }}</label>
                            <input type="text" class="form-control" name="keyword" id="keyword" placeholder="{{ label('account.info.your.secret.key') | raw }}" aria-label="{{ label('account.info.your.secret.key') | raw }}" maxlength="10" minlength="5" pattern="[a-zA-Z0-9-]+" required value="{{ user.keyword }}">
                            <p class="card-text small mt-3">{{ label('account.info.secret.info') | raw }}</p>
                            <ul class="small">
                                <li>{{ label('account.info.secret.info1') | raw }}</li>
                                <li>{{ label('account.info.secret.info2') | raw }}</li>
                                <li>{{ label('account.info.secret.info3') | raw }}</li>
                                <li>{{ label('account.info.secret.info4') | raw }}</li>
                                <li>{{ label('account.info.secret.info5') | raw }}</li>
                            </ul>
                        </div>                            
                        <!-- Button  -->
                        <div class="col-12">
                            <div class="d-flex">
                                <button class="btn btn-danger mb-0" onclick="deleteProfile();">
                                    <i class="bi bi-trash pe-1"></i>
                                    {{ label('account.delete.profile') | raw }}
                                </button>
                                <button type="submit" class="btn btn-primary mb-0 ms-auto">{{ label('common.save') | raw }}</button>
                            </div>
                        </div>
                    </form>
                    <!-- Settings END -->
                </div>
                <!-- Card body END -->
            </div>
            <!-- Account settings END --> 

        </div>
    </div> <!-- Row END -->
</div>
<!-- Container END -->

{% endblock %}

{% block pagescripts %}
<script src="{{ contextPath }}/fe/vendor/choices.js/public/assets/scripts/choices.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/slim/slim.kickstart.min.js"></script>
<script src="{{ contextPath }}/fe/js/pages/account-info.js?{{ buildNumber }}"></script>
<script src="{{ contextPath }}/fe/js/pages/account-security.js?{{ buildNumber }}"></script>
<script src="https://siteria.it/libs/jquery-confirm/3.2.3/dist/jquery-confirm.min.js"></script>
<link href="https://siteria.it/libs/jquery-confirm/3.2.3/dist/jquery-confirm.min.css" rel="stylesheet" type="text/css">
{% endblock %}
