{% extends "fe/include/base-no-header.html" %}

{% set metaDescription = '' %}

{% block title %}{{ label('access.title.meta') | raw }} | Agorapp{% endblock %}

{% block canonical %}    
    <meta name="description" content="{{ metaDescription }}">
    <link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block content %}
    <a id="accessDoUri" style="display: none" href="{{ paths('ACCESS_DO') }}" rel="nofollow"></a>    
    <a id="accountUri" style="display: none" href="{{ paths('ACCOUNT_INFO') }}" rel="nofollow"></a>    
    <a id="homeUri" style="display: none" href="{{ paths('HOME') }}" rel="nofollow"></a>    
    <a id="forgotUri" style="display: none" href="{{ paths('RECOVER') }}" rel="nofollow"></a>
    <div id="access.error.credential" style="display: none">{{ label('access.error.credential') | raw }}</div>
    <div id="access.error.text" style="display: none">{{ label('access.error.text') | raw }}</div>
    <div id="access.dont.remember" style="display: none">{{ label('access.dont.remember') | raw }}</div>
    
    <!-- Container START -->
    <div class="container">
        <div class="row justify-content-center align-items-center vh-100 py-5">
            <!-- Main content START -->
            <div class="col-sm-10 col-md-8 col-lg-7 col-xl-6 col-xxl-5">
                <div class="text-center">
                    <a href="{{ paths('HOME') }}"><img class="mb-3" src="{{ contextPath }}/fe/images/logo.svg" width="150" alt="Agorapp"></a>
                </div>
                <!-- Sign in START -->
                <div class="card card-body p-4 p-sm-5 border-lg">
                    <!-- Title -->
                    <h1 class="mb-2">{{ label('access.login') | raw }}</h1>
                    <p class="mb-0">{{ label('access.not.account') | raw }}<a href="{{ paths('REGISTER') }}"> {{ label('access.register.here') | raw }}</a></p>
                    <!-- Form START -->                    
                    <form class="mt-sm-4" id="form-login" method="post" novalidate="novalidate">
                        <!-- Email -->
                        <div class="mb-3 input-group-lg">
                            <input type="email" class="form-control" name="email" id="username" placeholder="{{ label('access.enter.email') | raw }}" aria-label="{{ label('access.enter.email') | raw }}" required>
                        </div>
                        <!-- New password -->
                        <div class="mb-3 position-relative">
                            <!-- Password -->
                            <div class="input-group input-group-lg">
                                <input class="form-control fakepassword" type="password" name="password" id="password" placeholder="{{ label('access.enter.password') | raw }}" aria-label="{{ label('access.enter.password') | raw }}" required minlength="{{ isLocal ? '3' : '8' }}">
                                <span class="input-group-text p-0">
                                    <i class="fakepasswordicon fa-solid fa-eye-slash cursor-pointer p-2 w-40px"></i>
                                </span>                                
                            </div>
                            <span class="fakepassword-error"></span>
                        </div>
                        <!-- Remember me -->
                        <div class="mb-3 d-sm-flex justify-content-end">                            
                            <a href="{{ paths('RECOVER') }}">{{ label('access.forgot.password') | raw }}</a>
                        </div>
                        <input type="hidden" name="remember" value="true"/>
                        <!-- Button -->
                        <div class="d-grid"><button type="submit" class="btn btn-lg btn-primary">{{ label('common.login') | raw }}</button></div>
                        <!-- Copyright -->
                        <p class="mb-0 mt-3 text-center">{{ label('common.copyright') | raw }}</p>
                    </form>
                    <!-- Form END -->
                </div>
                <!-- Sign in START -->
            </div>
        </div> <!-- Row END -->
    </div>
    <!-- Container END -->
        
{% endblock %}
{% block pagescripts %}
    <script src="{{ contextPath }}/fe/js/pages/access.js?{{ buildNumber }}"></script>
{% endblock %}
