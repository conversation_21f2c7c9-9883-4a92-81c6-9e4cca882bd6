{% extends "fe/include/base.html" %}

{% set activePage = 'NOTIFICATIONS' %}

{% block title %}{{ label('notification.edit.title.meta') | raw }} | Agorapp{% endblock %}

{% block content %}

<a id="notificationEditUri" style="display: none" href="{{ paths('NOTIFICATION_EDIT') }}?oid={{ customerNotification.id }}" rel="nofollow"></a>
<a id="notificationEditSaveUri" style="display: none" href="{{ paths('NOTIFICATION_EDIT_SAVE') }}?oid={{ customerNotification.id }}" rel="nofollow"></a>
<a id="accountInfoUri" style="display: none" href="{{ paths('ACCOUNT_INFO') }}" rel="nofollow"></a>
<a id="accountNotificationsUri" style="display: none" href="{{ paths('ACCOUNT_NOTIFICATIONS') }}" rel="nofollow"></a>

<script>
    const mapPoint = {
    first: {
        center: {lat: {{ customerNotification.lat }}, lng: {{ customerNotification.lng }}},
        radius: {{ customerNotification.rangeKm }},
        radiusType: '{{ customerNotification.rangeType }}'
    }
    };</script>

<!-- Container START -->
<div class="container">
    <div class="row">

        <!-- Sidenav START -->
        <div class="col-lg-3">
            {% include "fe/include/snippets/sidenav-left.html" %}
        </div>
        <!-- End Col -->

        <!-- Main content START -->
        <div class="col-lg-6 vstack gap-4">                               

            <!-- Notification START -->
            <div class="card border-lg-lr border-lg-b">
                <!-- Card header START -->
                <div class="card-header border-0 pb-0">
                    {% if user.profileType == 'unconfirmed' %}
                    <!-- Alert -->
                    <div class="alert alert-danger text-center card-alert" role="alert">
                        {{ label('account.not.confirm') | raw }} <a class="alert-link" id="account-confirm-send" href="{{ paths('ACCOUNT_CONFIRM_SEND') }}">{{ label('account.send.link') | raw }}<i class="bi-chevron-right small ms-1"></i></a>
                    </div>
                    <!-- End Alert -->
                    {% endif %}
                    <h1 class="h5 card-title">{{ label('common.notifications') | raw }}</h1>
                    <p class="mb-0">{{ label('notification.manage.with.location') | raw }}</p>
                </div>
                <!-- Card header START -->
                <!-- Card body START -->
                <form id="form-notifications-edit" class="row g-3 needs-validation" novalidate>
                    <div class="card-body pb-0">
                        <!-- Notification START -->
                        <ul class="list-group list-group-flush">                            
                            <li class="list-group-item px-0 py-3">
                                {# se è un luogo prevedere gestione visibilità in base alla selezione del tipo #}
                                <div class="row g-3 mb-3" id="address-container">
                                    <!-- Address -->
                                    <div class="col-12">
                                        <label class="form-label">{{ label('notification.insert.location.to.receive') | raw }} <small>{{ label('common.autocomplete.address') | raw }}</small></label>
                                        <input type="text" class="form-control" placeholder="Luogo" id="fulladdress" name="fulladdress" value="{{ customerNotification.fulladdress }}">
                                    </div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label">{{ label('common.country') | raw }}</label>
                                        <select class="form-control select-search" name="countryCode" id="countryCode" required>
                                            <option value="">-</option>
                                            {% for item in lookup("country") %}
                                            <option value="{{ item.code }}" {{ item.code == customerNotification.countryCode ? 'selected' : ''}}>{{ item.description }}</option>
                                            {% endfor %}
                                        </select>                                    
                                    </div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label">{{ label('common.address') | raw }}</label>
                                        <input role="presentation" autocomplete="off" type="text" name="address" id="address" class="form-control maxlength" maxlength="100" placeholder="{{ label('common.address') | raw }}" value="{{ customerNotification.address }}">
                                    </div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label">{{ label('common.city') | raw }}</label>
                                        <input role="presentation" autocomplete="off" type="text" name="city" id="city" class="form-control maxlength" maxlength="100" placeholder="{{ label('common.city') | raw }}" value="{{ customerNotification.city }}">
                                    </div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label">{{ label('common.postalcode') | raw }}</label>
                                        <input role="presentation" autocomplete="off" type="text" name="postalCode" id="postalCode" class="form-control maxlength" maxlength="10" placeholder="{{ label('common.postalcode') | raw }}" value="{{ customerNotification.postalCode }}">
                                    </div>
                                    <div class="col-12 col-sm-6">
                                        <label class="form-label">{{ label('common.province') | raw }}</label>                                    
                                        <select class="form-control select-search" name="provinceCode" id="provinceCode" required value="{{ customerNotification.provinceCode }}">
                                            <option value="">-</option>
                                            {% for item in lookup("province") %}
                                            <option value="{{ item.code }}" {{ item.code == customerNotification.provinceCode ? 'selected' : ''}}>{{ item.description }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </li>
                            <li class="list-group-item px-0 py-3">
                                <div class="row">
                                    <div class="col-8">
                                        <label class="form-label">{{ label('common.radius') | raw }}</label>
                                        <input type="number" min="0" max="250" class="form-control" name="rangeKm" id="rangeKm" onchange="updateCircle();" value="{{ customerNotification.rangeKm }}">
                                    </div>
                                    <div class="col-4 d-flex align-items-end">
                                        <select class="form-select border-agora" data-placeholder="false" name="rangeType" id="rangeType" onchange="updateCircle();">
                                            <option value="Km" {{ customerNotification.rangeType == 'Km' ? 'selected' : '' }}>{{ label('common.kilometers') | raw }}</option>
                                            <option value="Mi" {{ customerNotification.rangeType == 'Mi' ? 'selected' : '' }}>{{ label('common.miles') | raw }}</option>
                                        </select>
                                    </div>
                                </div>
                            </li>
                            <li class="list-group-item px-0 py-3">
                                <div class="row">
                                    <div id="map" style="height: 50vh;"></div>
                                </div>
                            </li>
                            <!-- Notification list item -->
                            
                            <li class="list-group-item d-flex justify-content-between align-items-center px-0 py-3">
                                <div class="me-2">
                                    <h6 class="mb-0">{{ label('notification.for.this.location') | raw }}</h6>
                                    <br>
                                    <p class="small mb-0">{{ label('notification.notification.email') | raw }}</p>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" role="switch" id="emailActive" name="emailActive" {{ customerNotification.emailActive == true ? 'checked' : '' }}>
                                </div>
                            </li>
                            <!-- Notification list item -->
                            <li class="list-group-item px-0 py-3">
                                <!-- Accordion START -->
                                <div class="accordion accordion-flush border-0" id="emailNotifications">
                                    <!-- Accordion item -->
                                    <div class="accordion-item bg-transparent">
                                        <h2 class="accordion-header" id="flush-headingOne">
                                            <a href="#!" class="accordion-button mb-0 p-0 collapsed bg-transparent shadow-none" data-bs-toggle="collapse" data-bs-target="#flush-collapseOne" aria-expanded="false" aria-controls="flush-collapseOne">
                                                <span>
                                                    <span class="mb-0 h6 d-block">{{ label('common.frequency') | raw }}</span>
                                                    <small class="small mb-0">{{ label('common.frequency.how') | raw }}</small>
                                                </span>
                                            </a>
                                        </h2>
                                        <div id="flush-collapseOne" class="accordion-collapse collapse" aria-labelledby="flush-headingOne" data-bs-parent="#emailNotifications">
                                            <div class="accordion-body p-0 pt-3">
                                                <!-- Notification list item -->
                                               
                                                <!-- Notification list item -->
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" value="day" name="frequency" id="NotiSwitchCheckChecked7" {{ (customerNotification.frequency == 'day' or customerNotification.frequency is empty) ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="NotiSwitchCheckChecked7">
                                                        {{ label('common.frequency.day') | raw }}
                                                    </label>
                                                </div>                                                                                                
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" value="week" name="frequency" id="NotiSwitchCheckChecked8" {{ customerNotification.frequency == 'week' ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="NotiSwitchCheckChecked8">
                                                        {{ label('common.frequency.week') | raw }}
                                                    </label>
                                                </div>                                                                                                
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" value="month" name="frequency" id="NotiSwitchCheckChecked9" {{ customerNotification.frequency == 'month' ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="NotiSwitchCheckChecked9">
                                                        {{ label('common.frequency.month') | raw }}
                                                    </label>
                                                </div>                                                                                                
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Accordion END -->
                            </li>  
                        </ul>
                        <!-- Notification END -->
                        <!-- Button save -->
                        <div class="card-footer pt-0 text-end border-0">
                            <button type="submit" class="btn btn-primary mb-0">{{ label('common.save') | raw }}</button>
                        </div>
                    </div>
                    <!-- Card body END -->
                </form>
            </div>
            <!-- Notification END -->

        </div>
    </div> <!-- Row END -->
</div>
<!-- Container END -->


{% endblock %}

{% block pagescripts %}
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBzbbAKQoJEad0bNS1qFMksTDfT60qJUnU&libraries=places&language=it-IT"></script>
<script src="{{ contextPath }}/fe/js/pages/notification-edit.js?{{ buildNumber }}"></script>    
{% endblock %}
