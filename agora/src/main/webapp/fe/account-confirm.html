{% extends "fe/include/base.html" %}

{% block title %}{{ label('account.confirm.title.meta') | raw }} | Agorapp{% endblock %}

{% block content %}
    
    <!-- Container START -->
    <div class="container">
        <!-- Extra space -->
        <div class="h-100px d-none d-lg-block"></div>
        <div class="row align-items-center text-center py-sm-5">            
            <div class="col-lg-8 mx-auto">                
                <img class="mb-5" src="{{ contextPath }}/fe/images/elements/02.svg" alt="logo" width="100">                
                <h2 class="mb-2 h1">{{ label('account.email.confirmed') | raw }}</h2>
                <p>{{ label('account.thanks.registration') | raw }}</p>
                {% if user is not empty %}
                    <div class="col-sm-auto">                    
                        <div class="rounded-circle">
                            <a class="btn btn-primary btn-transition" href="{{ paths('ACCOUNT_PAGES') }}"><i class="bi-plus-square me-1"></i> {{ label('account.go.to.your.page') | raw }}</a>
                        </div>
                    </div>                
                {% else %}
                    <div class="col-sm-auto">
                        <a class="btn btn-primary" href="{{ paths('ACCESS') }}">
                            {{ label('access.login') | raw }}
                        </a>                    
                    </div>
                {% endif %}
            </div>
            <!-- Error 404 START -->
        </div> 
        <!-- Extra space -->
        <div class="h-100px d-none d-lg-block"></div>
        <!-- Row END -->
    </div>
    <!-- Container END -->
{% endblock %}

