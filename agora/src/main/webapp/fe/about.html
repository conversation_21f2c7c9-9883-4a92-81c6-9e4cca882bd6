{% extends "fe/include/base.html" %}

{% set metaDescription = label('about.description.meta') | raw %}

{% block title %}{{ label('about.title.meta') | raw }} | Agorapp{% endblock %}

{% block canonical %}
    <meta name="robots" content="index, follow">
    <meta name="description" content="{{ metaDescription }}">
    <link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block socialcards %}
    <meta property="og:url"                content="{{ publicUrl }}" />
    <meta property="og:type"               content="website" />
    <meta property="og:title"              content="{{ label('about.title.meta') | raw }} | Agorapp" />
    <meta property="og:description"        content="{{ metaDescription }}" />
    <meta property="og:image"              content="{{ contextPath }}/fe/images/about/cover.png" />
    <meta property="og:image:width"        content="1200" />
    <meta property="og:image:height"       content="630" />
    <meta property="og:image:alt"          content="{{ label('about.title.meta') | raw }} | Agorapp" />
{% endblock %}


{% block pagecss %}
    <link href="{{ contextPath }}/fe/css/drop_uploader.min.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.css">    
{% endblock %}


{% block content %}
    
    <!-- Container START -->
    <div class="container">
        <!-- Main content START -->

        <!-- Help search START -->
        <div class="row align-items-center pt-5 pb-5 pb-lg-3">
            <div class="col-md-12">
                <img src="{{ contextPath }}/fe/images/about/cover.png">
            </div>            
        </div>
        <!-- Help search START -->

        <!-- Article Single  -->
        <div class="row">
            <div class="col-12">
                <hr class="text-agora">                                       
                <div class="d-flex flex-column align-items-center flex-md-row">                   
                    <img src="{{ contextPath }}/fe/images/about/what.svg" width="150" class="p-5">
                    <div>
                        <h3 class="mt-4 text-agora">{{ label('about.what.is.it') | raw }}</h3>
                        <p>{{ label('about.what.is.it.descr') | raw }}</p>
                    </div>
                </div>
                <hr class="text-agora">                        
                <div class="d-flex flex-column align-items-center flex-md-row">                   
                    <img src="{{ contextPath }}/fe/images/about/how.svg" width="150" class="p-5">
                    <div>
                        <h3 class="mt-4 text-agora">{{ label('about.how.work') | raw }}</h3>
                        <p>{{ label('about.how.work.descr') | raw }}</p>
                    </div>
                </div>
                <hr class="text-agora">  
                <div class="d-flex flex-column align-items-center flex-md-row">                   
                    <img src="{{ contextPath }}/fe/images/about/about.svg" width="150" class="p-5">
                    <div>
                        <h3 class="mt-4 text-agora">{{ label('about.who') | raw }}</h3>
                        <p>{{ label('about.who.descr') | raw }}</p>                
                    </div>
                </div>    
            </div>
        </div>
        <!-- Article Single  -->
    </div> <!-- Row END -->
    <!-- Container END -->
    <div class="d-lg-none">
        {% include "fe/include/snippets/sidenav-left.html" %}
    </div>
{% endblock %}       

{% block pagescripts %}   
    <script src="{{ contextPath }}/fe/js/pages/page-claim.js?{{ buildNumber }}"></script>        
{% endblock %}