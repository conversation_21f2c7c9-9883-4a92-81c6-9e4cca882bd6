{% extends "fe/include/base-no-header.html" %}

{% set metaDescription = label('mediakit.description.meta') | raw %}

{% block title %}{{ label('mediakit.title.meta') | raw }} | Agorapp{% endblock %}

{% block canonical %}
    <meta name="robots" content="index, follow">
    <meta name="description" content="{{ metaDescription }}">
    <link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block socialcards %}
    <meta property="og:url"                content="{{ publicUrl }}" />
    <meta property="og:type"               content="website" />
    <meta property="og:title"              content="{{ label('mediakit.title.meta') | raw }} | Agorapp" />
    <meta property="og:description"        content="{{ metaDescription }}" />
    <meta property="og:image"              content="{{ contextPath }}/fe/images/mediakit/cover.png" />
    <meta property="og:image:width"        content="1200" />
    <meta property="og:image:height"       content="630" />
    <meta property="og:image:alt"          content="{{ label('mediakit.title.meta') | raw }} | Agorapp" />
{% endblock %}


{% block pagecss %}
    <link href="{{ contextPath }}/fe/css/drop_uploader.min.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.css">    
{% endblock %}


{% block content %}
    
    <!-- Container START -->
    <div class="container-fluid">
        <!-- Main content START -->
        <!-- Article Single  -->
        <div class="row">
            <div class="col-12">
                <!-- Card header START -->
                <div class="card-header border-0 pb-0">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <div>
                                <!-- Avatar -->
                                <div class="avatar avatar-xxl mt-n5 mb-3">
                                    {% if pageDb.profileImageId is not empty %}
                                    <img class="avatar-img rounded-circle border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageDb.profileImageId }}" alt="{{ pageDb.name }}">
                                    {% else %}
                                    <img class="avatar-img rounded-circle border border-white border-3" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ pageDb.name }}">
                                    {% endif %}
                                </div>
                            </div>
                            <div class="ms-sm-4 mt-sm-3">
                                <!-- Info -->
                                <div class="mb-0">
                                    <h1 class="h5 mb-1 d-inline">{{ pageDb.name }}</h1>

                                    <!-- Badge con popover -->
                                    <div class="d-flex flex-wrap align-items-center gap-2 mt-2">
                                        
                                        <!-- LOCK\UNLOCK -->
                                        {% if pageDb.pageTagging == 'everyone' %}
                                        <span class="badge bg-agora text-white"
                                              role="button"
                                              tabindex="0"
                                              data-bs-toggle="popover"
                                              data-bs-trigger="hover focus"
                                              data-bs-placement="bottom"
                                              title="{{ label('popover.page.open.title') | raw }}"
                                              data-bs-content="{{ label('popover.page.open.content') | raw }}">
                                          <i class="bi bi-unlock"></i>
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary text-white"
                                              role="button"
                                              tabindex="0"
                                              data-bs-toggle="popover"
                                              data-bs-trigger="hover focus"
                                              data-bs-placement="bottom"
                                              title="{{ label('popover.page.closed.title') | raw }}"
                                              data-bs-content="{{ label('popover.page.closed.content') | raw }}">
                                          <i class="bi bi-lock"></i>
                                        </span>
                                        {% endif %}

                                        <!-- INFO POPOVER -->
                                        {% set popoverContent = "" %}
                                        {% if pageDb.ownerId != user.id %}
                                            {% set popoverContent = "<b>Pagina Informale</b>: Questa pagina è stata creata da un utente ed è in attesa di essere rivendicata<br>" %}
                                        {% endif %}
                                        {% if pageDb.ownerId != user.id %}
                                            {% set popoverContent = popoverContent + "<b>Pagina Rivendicata</b>: Un utente ha rivendicato la proprietà di questa pagina. Se la ritieni tua puoi chiederne il trasferimento<br>" %}
                                        {% endif %}
                                        {% if pageDb.ownerId != user.id %}
                                            {% set popoverContent = popoverContent + "<b>Pagina Verificata</b>: Questa pagina appartiene al legittimo titolare<br>" %}
                                        {% endif %}
                                        {% if pageDb.ownerId != user.id %}
                                            {% set popoverContent = popoverContent + "<b>Pagina Utente</b>: Questa è la pagina profilo di un utente registrato<br>" %}
                                        {% endif %}                                                                       
                                        <span class="badge bg-primary text-white"
                                              role="button"
                                              tabindex="0"
                                              data-bs-toggle="popover"
                                              data-bs-trigger="hover focus"
                                              data-bs-placement="bottom"
                                              data-bs-html="true"
                                              title="{{ label('badge.page.info.title') | raw }}"
                                              data-bs-content="{{ popoverContent }}">
                                            <i class="bi bi-info-circle me-1"></i> {{ label('badge.page.info.title') | raw }}
                                        </span>

                                        <!-- FOLLOWERS -->
                                        {% if pageDb.showFollowers == true %}
                                            <span class="badge bg-secondary text-white"
                                                  role="button"
                                                  tabindex="0"
                                                  data-bs-toggle="popover"
                                                  data-bs-trigger="hover focus"
                                                  data-bs-placement="bottom"
                                                  title="{{ label('badge.page.info.title') | raw }}"
                                                  data-bs-content="{{ label('badge.page.info.content') }} {{ followerCount }} {{ label('common.follower') | raw }}">
                                                <i class="bi bi-person me-1"></i> {{ followerCount }} {{ label('common.follower') | raw }}
                                            </span>
                                        {% endif %}

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Card header START -->

                <!-- Events START -->
                <div class="card">
                    <!-- Card header START -->
                    <div class="card-header d-sm-flex align-items-center justify-content-between border-0 pb-0">
                        <h5 class="card-title mb-sm-0">Agenda</h5>
                    </div>
                    <!-- Card header END -->
                    {% if eventEntryList is not empty %}
                    <!-- Card body START -->
                    <div class="card-body pt-0">
                        <div class="containerEvents">
                            <div class="oneEvent">
                                {% for eventEntry in eventEntryList %}
                                <div class="row">
                                    {% set pastEvent = false %}
                                    {% if daysbetween(eventEntry.event.startDate, today()) > 0 %}
                                        {% if eventEntry.event.endDate is not empty %}
                                            {% if daysbetween(eventEntry.event.endDate, today()) > 0 %}
                                                {% set pastEvent = true %}
                                            {% endif %}
                                        {% else %}
                                            {% set pastEvent = true %}
                                        {% endif %}
                                    {% endif %}
                                    <div class="d-sm-flex align-items-center {{ pastEvent ? 'opacity-50':'' }}">
                                        <!-- Avatar -->
                                        <div class="avatar avatar-xl">
                                            <a href="{{ paths('EVENT_BASE') }}/{{ eventEntry.event.identifier }}" class="eventlink" target="_blank">
                                                {% if eventEntry.event.coverImageId is not empty %}
                                                <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ eventEntry.event.coverImageId }}" alt="{{ eventEntry.event.name }}">
                                                {% else %}
                                                {% if pageDb.coverImageId is not empty %}
                                                <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageDb.coverImageId }}" alt="{{ eventEntry.event.name }}">
                                                {% else %}
                                                <img class="avatar-img border border-white border-3" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="{{ eventEntry.event.name }}">
                                                {% endif %}
                                                {% endif%}
                                            </a>
                                        </div>
                                        <div class="ms-sm-4 mt-2 mt-sm-0">
                                            <!-- Info -->
                                            <h5 class="mb-1"><a href="{{ paths('EVENT_BASE') }}/{{ eventEntry.event.identifier }}" target="_blank"> {{ eventEntry.event.name }} </a></h5>
                                            <ul class="nav nav-stack small">
                                                <li class="nav-item">
                                                    <i class="bi bi-calendar-check pe-1"></i>{{ formatDate(eventEntry.event.startDate, "EEE d MMM yyyy", language) }} {{ eventEntry.event.startHour }}
                                                </li>
                                                <li class="nav-item">
                                                    <i class="bi bi-geo-alt pe-1"></i> {{ eventEntry.event.city }}
                                                </li>
                                                {% if eventEntry.followerCount > 0 and (eventEntry.event.showFollowers == true) %}
                                                <li class="nav-item">
                                                    <i class="bi bi-people pe-1"></i> {{ eventEntry.followerCount }} {{ label('common.participants') | raw }}
                                                </li>
                                                {% endif %}
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <!-- Events list END -->
                                <hr>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <!-- Card body START -->
                    <div class="card-body">
                        <div class="alert alert-primary w-100 text-center" role="alert">
                            {{ label('page.detail.no.events') | raw }}
                        </div>
                    </div>
                    {% endif %}
                    <!-- Events list END -->
                </div>
                <!-- Events START -->

                <div class="d-flex justify-content-center">
                    <a href="{{ paths('PAGE_BASE') }}/{{ pageDb.identifier }}" class="walllink" target="_blank" data-wall-back="walllink-angela-terzani-staude">{{ label('events.view.all.events') | raw }}</a>
                </div>
            </div>
        </div>
        <!-- Article Single  -->
    </div> <!-- Row END -->
    <!-- Container END -->
    <div class="d-lg-none">
        {% include "fe/include/snippets/sidenav-left.html" %}
    </div>
{% endblock %}       
