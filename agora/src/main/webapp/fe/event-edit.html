{% extends "fe/include/base.html" %}

{% block title %}{{ label('event.edit.title.meta') | raw }} | Agorapp{% endblock %}

{% block pagecss %}   
<link href="{{ contextPath }}/fe/css/slim.min.css" rel="stylesheet" type="text/css" media="all">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/tiny-slider/dist/tiny-slider.css">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/choices.js/public/assets/styles/choices.min.css">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/glightbox-master/dist/css/glightbox.min.css">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.css">
<link href="{{ contextPath }}/fe/vendor/summernote/summernote-lite.min.css" rel="stylesheet">
<link href="https://www.siteria.it/libs/dropuploader/1.8.1/css/drop_uploader.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div id="event.delete.event" style="display: none">{{ label('event.delete.event') | raw }}</div>
<a id="eventEditSaveUri" style="display: none" href="{{ paths('EVENT_EDIT_SAVE') }}?oid={{ event.id }}" rel="nofollow"></a>
<a id="eventEditUri" style="display: none" href="{{ paths('EVENT_EDIT') }}?oid={{ event.id }}" rel="nofollow"></a>
<a id="eventRemoveUri" style="display: none;" href="{{ paths('EVENT_REMOVE') }}?eventId={{ event.id }}" rel="nofollow"></a>
<a id="accountCalendarUri" style="display: none" href="{{ paths('ACCOUNT_CALENDAR') }}" rel="nofollow"></a>
<a id="eventEditFileRemoveUri" style="display: none;" href="{{ paths('EVENT_EDIT_FILE_REMOVE') }}?oid={{ event.id }}" rel="nofollow"></a>
<a id="backUri" style="display: none" href="{{ backUrl }}" rel="nofollow"></a>
<!-- api call for products autocomplete -->
<a id="dataPagesUri" style="display: none" href="{{ paths('DATA_PAGES') }}?ownerId={{ user.id }}"></a>
<a id="dataEventsUri" style="display: none" href="{{ paths('DATA_SEARCH_EVENTS_CONTAINER') }}"></a>
<a id="dataEventTagUri" style="display: none" href="{{ paths('DATA_TAG_EVENT') }}"></a>
<input type="hidden" id="language" value="{{ language }}">
<input type="hidden" id="eventId" value="{{ event.id }}">
<input type="hidden" id="isContainer" value="{{ isContainer }}">

<!-- CLIPBOARD TOAST -->
{% include "fe/include/snippets/clipboard-toast.html" %}

<!-- Container START -->
<div class="container">
    <div class="row">

        <!-- Sidenav START -->
        <div class="col-lg-3">
            {% include "fe/include/snippets/sidenav-left.html" %}
        </div>
        <!-- End Col -->

        <!-- Main content START -->
        <div class="col-lg-6 vstack gap-4">                               
            <!-- Account settings START -->
            <div class="card mb-4">

                <!-- Card header START -->
                {% if isContainer is not empty and isContainer %}
                <div class="card-header border-0 pb-0">
                    <h1 class="h5 card-title">{{ label('event.edit.container') | raw }}</h1>
                    <p class="mb-0">{{ label('event.add.info.container') | raw }}</p>
                </div>
                {% else %}
                <div class="card-header border-0 pb-0">
                    <h1 class="h5 card-title">{{ label('event.edit') | raw }}</h1>
                    <p class="mb-0">{{ label('event.add.info') | raw }}</p>
                </div>
                {% endif %}
                <!-- Card header END -->

                <!-- Card body START -->
                <div class="card-body">
                    <!-- Form START -->
                    <form class="row g-4" id="form-event-edit" method="post">
                        <input type="hidden" name="originalId" value="{{ event.originalId }}">
                        <!-- Cover image -->
                        <div class="col-lg-12">
                            <label class="form-label">{{ label('common.cover.photo') | raw }}</label>
                            {% set originalFilename = '' %}
                            {% if event.coverImageId is not empty %}
                            {% set fileDecoded = get('DocumentDescriptor', event.coverImageId) %}
                            {% set originalFilename = fileDecoded.metadata.originalFilename %}
                            {% endif %}
                            <div class="slim rounded"
                                 data-max-file-size="5"
                                 data-save-initial-image="{{ event.coverImageId is not empty ? 'true' : 'false'}}"
                                 data-push="false"
                                 data-post="output"
                                 data-label="{{ label('slim.upload.photo') | raw }}"
                                 data-label-loading=" "
                                 data-ratio="free"
                                 data-jpeg-compression=100
                                 data-button-edit-label="{{ label('common.edit') | raw }}"
                                 data-button-remove-label="{{ label('common.remove') | raw }}"
                                 data-button-download-label="{{ label('common.download') | raw }}"
                                 data-button-upload-label="{{ label('common.upload') | raw }}"
                                 data-button-rotate-label="{{ label('common.rotate') | raw }}"
                                 data-button-cancel-label="{{ label('common.cancel') | raw }}"
                                 data-button-confirm-label="{{ label('common.confirm') | raw }}"
                                 data-status-file-size="{{ label('slim.file.big') | raw }} $0 MB"
                                 data-status-file-type="{{ label('slim.format.not.valid') | raw }} $0"
                                 data-status-no-support="{{ label('slim.browser.not.support') | raw }}"
                                 data-status-image-too-small="{{ label('slim.file.small') | raw }} $0 pixel"
                                 data-status-content-length="{{ label('slim.server.file.big') | raw }}"
                                 data-status-unknown-response="{{ label('slim.unknown.error') | raw }}"
                                 data-status-upload-success="{{ label('slim.image.saved') | raw }}"
                                 data-meta-originalFilename="{{ originalFilename }}">

                                {% if event.coverImageId is not empty %}
                                <img src="{{ paths('IMAGE_SYSTEM') }}?oid={{ event.coverImageId }}" alt=""/>
                                {% endif %}
                                <input type="file" id="cropper" name="uploaded-cover" data-show-caption="false" data-show-remove="true">
                            </div>
                            <small class="form-text">{{ label('slim.recommended.dimension') | raw }} 1116x280 (ratio 4:1)</small>
                        </div>
                        <!-- End Media -->     
                        <div class="form-group">
                            <label class="col-lg-3 control-label">{{ label('event.poster') | raw }}:</label>
                            <div class="col-lg-12">
                                <div id="uploader-text" style="display: none;">{{ label('upload.drag.file.here') | raw }}</div>
                                <div class="text-center">
                                    <input type="file" name="locandina" data-maxfilessize="4194304" attachment="true" >
                                </div>
                                {% if event.locandina is not empty %}
                                <div class="panel panel-flat">
                                    <label class="col-lg-3 control-label">{{ label('current.poster') | raw }}:</label>

                                    <div class="panel-body">
                                        <div class="content-group-xs" id="bullets"></div>
                                        {% set fileDecoded = get('DocumentDescriptor', event.locandina) %}
                                        <div class="media-body">
                                            <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ event.locandina }}" target="_blank" rel="noopener">
                                                {{ fileDecoded.metadata.originalFilename }}
                                            </a>
                                            <button type="button" class="btn ml-10 btn-primary mb-0 w-25 delete-file" file-idx="{{ loop.index }}" filegroup="locandina">Rimuovi</button>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                            </div>
                        </div>
                        
                        <!-- Status -->
                        <div class="col-sm-6 hide-for-container">
                            <label class="form-label">{{ label('event.status') | raw }}</label>
                            <select class="form-select js-choice" data-search-enabled="true" id="status" name="status" value="{{ event.status }}">
                                <option value="EventScheduled" {{ event.status == 'EventScheduled' ? 'selected' : '' }}>{{ label('event.status.inprogram') | raw }}</option>
                                <option value="EventPostponed" {{ event.status == 'EventPostponed' ? 'selected' : '' }}>{{ label('event.status.postponed') | raw }}</option>
                                <option value="EventCancelled" {{ event.status == 'EventCancelled' ? 'selected' : '' }}>{{ label('event.status.cancelled') | raw }}</option>
                                <option value="EventMovedOnline" {{ event.status == 'EventMovedOnline' ? 'selected' : '' }}>{{ label('event.status.moved') | raw }}</option>
                            </select>
                        </div>                            
                        <!-- Title -->
                        <div class="col-12">
                            <label class="form-label">{{ label('common.title') | raw }}</label>
                            {% if isContainer is not empty and isContainer %}
                            <input type="text" class="form-control" id="name" name="name" placeholder="{{ label('event.title.container') | raw }}" value="{{ event.name }}" required>
                            {% else %}
                            <input type="text" class="form-control" id="name" name="name" placeholder="{{ label('event.title') | raw }}" value="{{ event.name }}" required>
                            {% endif %}
                        </div>
                        <!-- Description -->
                        <div class="col-12">
                            <label class="form-label">{{ label('common.description') | raw }}</label>
                            <textarea class="form-control summernote" rows="3" id="description" name="description" placeholder="{{ label('event.description') | raw }}">{{ event.description }}</textarea>
                        </div>

                        {# se è un luogo prevedere gestione visibilità in base alla selezione del tipo #}
                        <div class="col-12 hide-for-container">
                            <div class="row g-4 pt-0" id="address-container">
                                <!-- Address -->
                                <div class="col-12">
                                    <label class="form-label">{{ label('common.fulladdress') | raw }}</label>
                                    <input type="text" class="form-control" placeholder="{{ label('common.address') | raw }}" id="fulladdress" name="fulladdress" value="{{ event.fulladdress }}">
                                </div>
                                <div class="col-12 col-sm-6">
                                    <label class="form-label">{{ label('common.country') | raw }}</label>
                                    <select class="form-control select-search" name="countryCode" id="countryCode" required>
                                        <option value="">-</option>
                                        {% for item in lookup("country") %}
                                        <option value="{{ item.code }}" {{ item.code == event.countryCode ? 'selected' : ''}}>{{ item.description }}</option>
                                        {% endfor %}
                                    </select>                                    
                                </div>
                                <div class="col-12 col-sm-6">
                                    <label class="form-label">{{ label('common.address') | raw }}</label>
                                    <input role="presentation" autocomplete="off" type="text" name="address" id="address" class="form-control maxlength" maxlength="100" placeholder="{{ label('common.address') | raw }}" value="{{ event.address }}">
                                </div>
                                <div class="col-12 col-sm-6">
                                    <label class="form-label">{{ label('common.city') | raw }}</label>
                                    <input role="presentation" autocomplete="off" type="text" name="city" id="city" class="form-control maxlength" maxlength="100" placeholder="{{ label('common.city') | raw }}" value="{{ event.city }}">
                                </div>
                                <div class="col-12 col-sm-6">
                                    <label class="form-label">{{ label('common.postalcode') | raw }}</label>
                                    <input role="presentation" autocomplete="off" type="text" name="postalCode" id="postalCode" class="form-control maxlength" maxlength="10" placeholder="{{ label('common.postalcode') | raw }}" value="{{ event.postalCode }}">
                                </div>
                                <div class="col-12 col-sm-6" id="provinceDiv">
                                    <label class="form-label">{{ label('common.province') | raw }}</label>                                    
                                    <select class="form-control select-search provinceCode provinceCodeIt" name="provinceCode" required>
                                        <option value="">-</option>
                                        {% for item in lookup("province") %}
                                        <option value="{{ item.code }}" {{ item.code == event.provinceCode ? 'selected' : ''}}>{{ item.description }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-12 col-sm-6" id="provinceExtDiv">
                                    <label class="form-label">{{ label('common.province') | raw }}</label>
                                    <input role="presentation" autocomplete="off" type="text" name="provinceCode" id="provinceCode" class="form-control maxlength provinceCode provinceCodeExt" placeholder="{{ label('common.province') | raw }}" value="{{ event.provinceCode }}">
                                </div>
                                <div class="col-12 col-sm-6">
                                    <label class="form-label">{{ label('common.extra.city') | raw }}</label>
                                    <input role="presentation" autocomplete="off" type="text" name="extraAddress" id="extraAddress" class="form-control maxlength" maxlength="100" placeholder="{{ label('common.extra.city') | raw }}" value="{{ event.extraAddress }}">
                                </div>
                            </div>
                        </div>
                        <!-- Date -->
                        <div class="col-sm-4 hide-for-container">
                            <label class="form-label">{{ label('common.start.date') | raw }}</label>
                            <input type="text" class="form-control flatpickr" id="startDate" name="startDate" placeholder="{{ label('common.start.date') | raw }}" value="{{ event.startDate | date('dd/MM/yyyy') }}" required>
                            <span id="passed-event-warning" class="d-none custom-error">{{ label('event.date.past.warning') | raw }}</span>
                        </div>
                        <!-- Time -->
                        <div class="col-sm-2 hide-for-container">
                            <label class="form-label">{{ label('common.start.hour') | raw }}</label>
                            <input type="text" id="startHour" name="startHour" class="form-control flatpickr" data-enableTime="true" data-noCalendar="true" placeholder="{{ label('common.hours') | raw }}" value="{{ event.startHour }}" required>
                        </div>
                        <!-- Date -->
                        <div class="col-sm-4 hide-for-container">
                            <label class="form-label">{{ label('common.end.date') | raw }}</label>
                            <input type="text" id="endDate" name="endDate" class="form-control flatpickr" placeholder="{{ label('common.end.date') | raw }}" value="{{ event.endDate | date('dd/MM/yyyy') }}">
                        </div>
                        <!-- Time -->
                        <div class="col-sm-2 hide-for-container">
                            <label class="form-label">{{ label('common.end.hour') | raw }}</label>
                            <input type="text" id="endHour" name="endHour" class="form-control flatpickr" data-enableTime="true" data-noCalendar="true" placeholder="{{ label('common.hours') | raw }}" value="{{ event.endHour }}">
                        </div>
                        <!-- Ticket url -->
                        <div class="col-md-4 hide-for-container">
                            <label class="form-label">{{ label('event.ticket.url') | raw }}</label>
                            <input type="text" class="form-control" id="ticketsUrl" name="ticketsUrl" placeholder="{{ label('event.url.ticket.buy') | raw }}" value="{{ event.ticketsUrl }}">
                        </div>
                        <!-- Price -->
                        <div class="col-md-4 hide-for-container">
                            <label class="form-label">{{ label('event.ticket.price') | raw }}</label>
                            <div class="input-group">
                                <span class="input-group-text">€</span>
                                <input type="number" min="0" max="999999" class="form-control apply-changes" name="ticketsMinPrice" id="ticketsMinPrice" placeholder="{{ label('common.price') | raw }}" aria-label="{{ label('common.price') | raw }}" value="{{ event.ticketsMinPrice | numberformat("#0") }}">
                                <span class="input-group-text rounded-1-end">,00</span>
                                <span class="invalid-feedback">{{ label('event.ticket.price.required') | raw }}</span>
                            </div>
                        </div>
                        <!-- Free -->
                        <div class="col-md-4 hide-for-container">
                            <label class="form-label">{{ label('event.ticket.free') | raw }}</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" role="switch"  id="freeEntry" name="freeEntry"  {{ event.freeEntry == true ? 'checked' : '' }}>
                            </div>
                        </div>

                        <div class="col-12 hide-for-container">
                            <label class="form-label">
                                {{ label('event.container') | raw }}
                                <i class="bi bi-question-circle"
                                   data-bs-toggle="tooltip"
                                   title="{{ label('event.container.hint') | raw }}">
                                </i>
                            </label>
                            <select id="parentId" class="form-control" name="parentId">
                                {% if parent is not empty %}
                                <option value="{{ parent.id }}">{{ parent.name }}</option>
                                {% endif %}
                            </select>
                        </div>

<!--                        -->
<!--                        <div class="col-12 hide-for-container">-->
<!--                            <label class="form-label">{{ label('event.parent.event') | raw }}</label>-->
<!--                            <select id="parentId" class="form-control" name="parentId">-->
<!--                                {% if event.parentId is not empty %}-->
<!--                                {% set parentEvent = get('event', event.parentId) %}-->
<!--                                <option value="{{ event.parentId }}">{{ parentEvent.name }}</option>-->
<!--                                {% endif %}-->
<!--                            </select>-->
<!--                        </div>-->

                        <div class="col-12 hide-for-container">
                            <label class="form-label">{{ label('event.pages.tags') | raw }}
                                <i class="bi bi-question-circle" 
                                   data-bs-toggle="tooltip" 
                                   title="{{ label('event.pages.tags.hint') | raw }}">
                                </i>
                            </label>
                            <select class="select-search-multiple form-control" id="tags" name="tags" data-placeholder="{{ label('tags.placeholder.multiple') | raw }}" multiple>
                                {% for tag in event.tags %}
                                <option value="{{ tag }}" selected>{{ tag | lower }}</option>
                                {% endfor %}
                            </select>
                        </div>

                        <div id="pageInitials" style="display: none;">[{% for pageId in event.pageIds %}{% set
                            pageRelated = get('page', pageId) %}{"id": "{{ pageId }}", "text": "{{ pageRelated.name }}",
                            "isFake": "false", "pageTagging": "{{ pageRelated.pageTagging }}", "isClosed": false}{{ (not loop.last or event.pendingPageIds is not empty) ? ',' : '' }}{% endfor %}{% for pageId in event.pendingPageIds %}{% set
                            pageRelated = get('page', pageId) %}{"id": "{{ pageId }}", "text": "{{ pageRelated.name }} 🔒",
                            "isFake": "false", "pageTagging": "{{ pageRelated.pageTagging }}", "isClosed": true}{{ loop.last ? '' : ',' }}{% endfor %}]
                        </div>
                        <div class="col-sm-12">
                            <label class="form-label">{{ label('event.pages.involved') | raw }}
                                <i class="bi bi-question-circle"
                                   data-bs-toggle="tooltip"
                                   title="{{ label('event.pages.involved.hint') | raw }}">
                                </i>
                            </label>
                            <select id="pageIds" class="form-control" name="pageIds" multiple="multiple"
                                    data-tags="true" required {{ isContainer is not empty and isContainer ? 'disabled' :
                            '' }}>
                            <!-- ...altri tag... -->
                            </select>
                        </div>
                        <div class="col-sm-12 d-none">
                            <!-- Container per i tag selezionati -->
                            <div id="selected-pages-container" class="d-none"></div>  
                        </div>
                        <!-- Show Followers -->
                        <div class="col-md-4 hide-for-container">
                            <label class="form-label">{{ label('event.interested.show') | raw }}</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" role="switch" id="showFollowers" name="showFollowers" {{ event.showFollowers == true ? 'checked' : '' }}>
                            </div>
                        </div>
                        <!-- Divider -->
                        <hr>                            
                        <!-- Button  -->
                        <div class="col-12 text-end">
                            <div class="row">
                                <div class="col-md-10 col-sm-12 px-1 mt-1">
                                    {% if isContainer is not empty and isContainer %}
                                    <button type="submit" class="btn btn-primary mb-0 w-100">{{ label('common.update.event.container') | raw }}</button>
                                    {% else %}
                                    <button type="submit" class="btn btn-primary mb-0 w-100">{{ label('common.update.event') | raw }}</button>
                                    {% endif %}
                                </div>
                                <div class="col-md-2 col-sm-12 px-1 mt-1">
                                    {% if event.id is not empty %}
                                    <button id="btn-delete" type="button" class="btn heading-btn btn-danger w-100"><i class="bi bi-trash pe-1"></i>{{ label('common.remove') | raw }}</button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </form>
                    <!-- Form END -->
                </div>
                <!-- Card body END -->
            </div>
            <!-- Account settings END -->                   

        </div>
    </div> <!-- Row END -->
</div>
<!-- Container END -->

{% endblock %}       

{% block pagescripts %}   
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBzbbAKQoJEad0bNS1qFMksTDfT60qJUnU&libraries=places&language=it-IT&callback=initAutocomplete"></script>
<script src="https://siteria.it/libs/dropuploader/1.8.1/js/drop_uploader.min.js"></script>
<script src="{{ contextPath }}/fe/js/slim.kickstart.min.js"></script>    
<script src="{{ contextPath }}/fe/vendor/choices.js/public/assets/scripts/choices.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/glightbox-master/dist/js/glightbox.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.min.js"></script>        
<script src="{{ contextPath }}/fe/vendor/flatpickr/dist/l10n/it.js"></script>        
<script src="{{ contextPath }}/fe/vendor/summernote/summernote-lite.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/summernote/lang/summernote-it-IT.js"></script>
<script src="{{ contextPath }}/fe/js/pages/event-edit.js?{{ buildNumber }}"></script>
{% endblock %}