{% extends "fe/include/base.html" %}

{% set metaTitle = entry.event.name + ' | Agorapp' %}
{% set metaDescription = entry.event.description | abbreviate(170) %}

{% block title %}{{ metaTitle }}{% endblock %}

{% block canonical %}

<meta name="description" content="{{ metaDescription }}">
<meta name="event-id" content="{{ entry.event.id }}">
<link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block socialcards %}
<meta property="og:url"                content="{{ publicUrl }}" />
<meta property="og:type"               content="website" />
<meta property="og:title"              content="{{ metaTitle }}" />
<meta property="og:description"        content="{{ metaDescription }}" />
<meta property="og:image"              content="" />
<meta property="og:image:width"        content="1200" />
<meta property="og:image:height"       content="630" />
<meta property="og:image:alt"          content="{{ metaTitle }}" />
{% endblock %}

{% block content %}
<a id="eventFollowToggleUri" style="display: none" href="{{ paths('EVENT_FOLLOWER_TOGGLE') }}" rel="nofollow"></a>
<a id="eventPathBaseUri" style="display: none" href="{{ paths('EVENT_BASE') }}" rel="nofollow"></a>
<a id="eventPagesUri" style="display: none" href="{{ paths('EVENT_PAGES_AJAX') }}" rel="nofollow"></a>
<a id="eventListUri" style="display: none" href="{{ paths('EVENT_LIST_AJAX') }}" rel="nofollow"></a>
<a id="eventNavigationUri" style="display: none" href="{{ paths('EVENT_NAVIGATION_AJAX') }}" rel="nofollow"></a>
<a id="imageUri" style="display: none" href="{{ paths('IMAGE_SYSTEM') }}" rel="nofollow"></a>
<div id="eventId" style="display: none">{{ entry.event.id }}</div>
{% if entry.event.parentId is not empty %}
<div id="eventParentId" style="display: none">{{ entry.event.parentId }}</div>
{% endif %}

<div id="common.all.pages" style="display: none">{{ label('common.all.pages') | raw }}</div>
<div id="common.no.events.found" style="display: none">{{ label('common.no.events.found') | raw }}</div>
<div id="common.participants" style="display: none">{{ label('common.participants') | raw }}</div>
<div id="common.loading" style="display: none">{{ label('common.loading') | raw }}</div>
<div id="common.error.loading" style="display: none">{{ label('common.error.loading') | raw }}</div>
<div id="common.retry" style="display: none">{{ label('common.retry') | raw }}</div>
<div id="common.filtered.by.page" style="display: none">{{ label('common.filtered.by.page') | raw }}</div>
<div id="common.sorted.by" style="display: none">{{ label('common.sorted.by') | raw }}</div>
<div id="common.oldest.first" style="display: none">{{ label('common.oldest.first') | raw }}</div>
<div id="common.newest.first" style="display: none">{{ label('common.newest.first') | raw }}</div>
<div id="common.events" style="display: none">{{ label('common.events') | raw }}</div>

<!-- Container START -->
<div class="container">
    {% set isVertical = false %}
    {% if entry.event.coverImageId is not empty %}
    {% set coverImage = get('documentdescriptor', entry.event.coverImageId) %}
    {% if coverImage is not empty and coverImage.isVertical() %}
    {% set isVertical = true %}
    {% endif %}
    {% endif %}

    {% if isVertical == true %}
    <div class="row g-4">
        <!-- Colonna di sinistra larga 4 con immagine sticky -->
        <div class="col-md-4 border-lg-lr border-lg-b pe-0 ps-0 pt-0 bg-light">
            <div class="" style="top: 2rem;">
                {% if isContainer is empty or isContainer == false %}
                <div class="position-absolute mt-3 ms-3 bg-mode text-center overflow-hidden p-1 d-inline-block">
                    <div class="bg-primary p-2 text-white small lh-1">{{ formatDate(entry.event.startDate, "EEEE", language) }}</div>
                    <h5 class="mb-0 py-2 lh-1">{{ formatDate(entry.event.startDate, "dd/MM", language) }}</h5>
                </div>
                {% endif %}

                {% if entry.event.coverImageId is not empty %}
                <img src="{{ paths('IMAGE_SYSTEM') }}?oid={{ entry.event.coverImageId }}" class="w-100">
                {% else %}
                {% if entry.event.pageIds is not empty %}
                {% set firstPage = get('page', entry.event.pageIds[0]) %}
                {% endif %}
                {% if firstPage is not empty and firstPage.coverImageId is not empty %}
                <img src="{{ paths('IMAGE_SYSTEM') }}?oid={{ firstPage.coverImageId }}" class="w-100">
                {% else %}
                <img src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" class="w-100">
                {% endif %}
                {% endif %}

                <div class="mt-0 p-3">

                    {% if isContainer is empty or isContainer == false %}
                    <div class="mt-3">
                        <!-- Timings -->
                        <h5>{{ label('common.timetables') | raw }}</h5>
                        <p class="small mb-0">{{ entry.event.startHour }}{% if entry.event.endHour %}- {{ entry.event.endHour }}{% endif %}</p>
                    </div>
                    <div class="mt-3">
                        <h5>{{ label('common.entry') | raw }}</h5>
                        {% if entry.event.freeEntry %}
                        <p class="small mb-0"> {{ label('common.free') | raw }}</p>
                        {% else %}
                        <p class="small mb-0"> {{ label('common.starting.from') | raw }} {{entry.event.ticketsMinPrice | numberformat("#,##0") }}€</p>
                        {% endif %}
                    </div>
                    {% endif %}

                    <div class="mt-3">
                        <h5>{{ label('common.interact') | raw }}</h5>
                        <div class="d-flex flex-column justify-content-start ms-sm-auto">
                            {% if isContainer is empty or isContainer == false %}
                            {% if entry.iFollow %}
                            <button class="btn btn-danger-soft flex-fill event-add-follow mb-2 text-nowrap" data-value="active" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-event-id="{{ entry.event.id }}"><i class="bi bi-dash-circle-fill pe-1"></i> {{ label('common.i.not.interested') | raw }}</button>
                            {% else %}
                            <button class="btn btn-success-soft flex-fill event-add-follow mb-2 text-nowrap" data-value="inactive" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-event-id="{{ entry.event.id }}"><i class="bi bi-plus-circle-fill pe-1"></i> {{ label('common.i.interested') | raw }}</button>
                            {% endif %}
                            <button id="add-to-calendar" class="btn btn-warning-soft mb-2 text-nowrap"><i class="bi-calendar-date-fill me-1"></i> {{ label('common.add.to.calendar') | raw }}</button>
                            <div class="dropdown flex-fill">
                                <!-- Card share action menu -->
                                <button class="btn btn-primary-soft w-100 mb-2" type="button" id="profileAction2" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-share-fill pe-1"></i> {{ label('common.share') | raw }}
                                </button>
                                <!-- Card share action dropdown menu -->
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="profileAction2">
                                    <li>
                                        <a class="dropdown-item" href="https://www.facebook.com/sharer/sharer.php?u={{ publicUrl }}" aria-label="Facebook">
                                            <i class="bi-facebook"></i> Facebook
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="https://api.whatsapp.com/send?text=Guarda%20questo%20evento%20su%20Agorapp!%20{{ publicUrl }}" aria-label="Whatsapp">
                                            <i class="bi-whatsapp"></i> Whatsapp
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="https://t.me/share/url?url={{ publicUrl }}&text=Guarda%20questo%20evento%20su%20Agorapp!" aria-label="Telegram">
                                            <i class="bi-telegram"></i> Telegram
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="https://twitter.com/intent/tweet?url={{ publicUrl }}&text=Guarda%20questo%20evento%20su%20Agorapp!" aria-label="Twitter" target="_blank" rel="noopener">
                                            <i class="bi-twitter"></i> Twitter
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="https://www.linkedin.com/sharing/share-offsite/?url={{ publicUrl }}" aria-label="LinkedIn" target="_blank" rel="noopener">
                                            <i class="bi-linkedin"></i> LinkedIn
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" data-clipboard-text="{{ publicUrl }}" id="copyToClipboardLink" aria-label="{{ label('common.copy.to.clipboard') | raw }}">
                                            <i class="bi-clipboard-check"></i> {{ label('common.copy.to.clipboard') | raw }}
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            {% else %}
                            <a href="{{ paths('EVENT_ADD') }}?parentId={{ entry.event.id }}"
                               class="btn btn-primary-soft mb-2 mb-lg-0">
                                <i class="bi bi-plus-circle-fill pe-1"></i> {{ label('common.create.event') | raw
                                }}
                            </a>
                            {% endif %}
                        </div>

                        <div id="panelFollower">
                            {% if entry.followerCount > 0 %}
                            <div class="mt-1">
                                <h5 class="text-capitalize">{{ label('common.interested') | raw }}</h5>
                                <!-- Avatar group START -->
                                <ul class="avatar-group list-unstyled align-items-center">
                                    {% if entry.followerPages is not empty %}
                                    {% for followerPage in entry.followerPages %}
                                    <li class="avatar avatar-xs">
                                        <a href="{{ paths('PAGE_BASE') }}/{{ followerPage.identifier }}">
                                            {% if followerPage.profileImageId is not empty %}
                                            <img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ followerPage.profileImageId }}">
                                            {% else %}
                                            <img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg">
                                            {% endif %}
                                        </a>
                                    </li>
                                    {% endfor %}
                                    {% endif %}
                                    <li class="ms-4">
                                        {% if entry.followerCount > 1 %}
                                        <small>{{ entry.followerCount }} {{ label('common.people.interested') | raw }}</small>
                                        {% else %}
                                        <small>{{ entry.followerCount }} {{ label('common.person.interested') | raw }}</small>
                                        {% endif %}
                                    </li>
                                </ul>
                            </div>
                            {% endif%}
                        </div>

                        {% if entry.event.ticketsUrl is not empty %}
                        <h5 class="mt-3">{{ label('common.ticket') | raw }}</h5>
                        <!-- Button -->
                        <a class="btn btn-primary d-block" target="_blank" href="https://{{ entry.event.ticketsUrl | replace({'https://': '', 'http://': ''}) }}"> {{ label('common.buy.ticket') | raw }} </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Colonna di destra con testo -->
        <div class="col-md-8 py-4 border-lg-r border-lg-b">

            <h1 class="mb-4">{{ entry.event.name }}</h1>

            <!-- Event Navigation START -->
            {% if entry.event.parentId is not empty %}
            <div class="d-flex justify-content-between align-items-center mb-3">
                <button id="prevEventBtn" class="btn btn-outline-primary" style="display: none;">
                    <i class="bi bi-chevron-left"></i> {{ label('common.previous') | raw }}
                </button>
                <button id="nextEventBtn" class="btn btn-outline-primary" style="display: none;">
                    {{ label('common.next') | raw }} <i class="bi bi-chevron-right"></i>
                </button>
            </div>
            {% endif %}
            <!-- Event Navigation END -->

            <div class="mb-4">
                <div class="row g-4">
                    <div class="col-lg-6">
                        <h5>{{ label('common.description') | raw }}</h5>
                    </div>
                    <div class="col-lg-6 text-end">
                        {% if (entry.event.ownerId == user.id and daysbetween(entry.event.startDate, today()) <= 0 or hoursbetween(entry.event.creation, now()) <= 2) or user.profileType == 'system' or user.profileType == 'admin' %}
                        <a class="btn btn-primary-soft text-nowrap" href="{{ paths('EVENT_EDIT') }}?oid={{ entry.event.id }}&backUrl={{ paths('EVENT_BASE') }}/"><i class="bi bi-pencil pe-1"></i> {{ label('event.edit') | raw }}</a>
                        {% endif %}
                    </div>
                </div>
                <p class="mb-0">{% autoescape false %}{{ newline(entry.event.description) }}{% endautoescape %}</p>
                {% if entry.event.tags is not empty %}
                <p>
                    <ul class="nav nav-stack gap-0 gap-sm-2">
                        <li class="nav-item">
                            <i class="bi bi-tags-fill"></i>
                            {% for tag in entry.event.tags %}
                            <a href="{{ paths('RESULTS') }}?q=events&text={{ tag }}">#{{ tag | lower }}</a>
                            {% endfor %}
                        </li>
                    </ul>
                </p>
                {% endif %}
            </div>

        </div>

        {% if isContainer is empty or isContainer == false %}
        <!-- Related events START -->
        <div class="row g-0">
            <div class="col-lg-6 border-lg-l">
                <!-- Card START -->
                <div class="card">
                    <div class="card-header border-0">
                        <h5 class="card-title">{{ label('event.pages.involved') | raw }}</h5>
                        <!-- Button modal -->
                    </div>
                    {% if entry.event.pageIds is not empty or entry.event.fakePageIds %}
                    <!-- Card body START -->
                    <div class="card-body pt-0">
                        {% for pageId in entry.event.pageIds %}
                        {% set pageRelated = get('page', pageId) %}
                        <!-- Related events item -->
                        <div class="d-sm-flex flex-wrap align-items-center mb-3">
                            <!-- Avatar -->
                            <div class="avatar avatar-md">
                                <a href="{{ paths('PAGE_BASE') }}/{{ pageRelated.identifier }}">
                                    {% if pageRelated.profileImageId is not empty %}
                                    <img class="avatar-img rounded-circle border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageRelated.profileImageId }}" alt="{{ pageRelated.name }}">
                                    {% else %}
                                    <img class="avatar-img rounded-circle border border-white border-3" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ pageRelated.name }}">
                                    {% endif %}
                                </a>
                            </div>
                            <!-- info -->
                            <div class="ms-sm-2 my-2 my-sm-0">
                                <h6 class="mb-0"><a href="{{ paths('PAGE_BASE') }}/{{ pageRelated.identifier }}">{{ pageRelated.name }}</a></h6>
                            </div>
                            <!-- Button -->
                        </div>

                        {% endfor %}

                        {% for fakePageId in entry.event.fakePageIds %}
                        <!-- Related events item -->
                        <div class="d-sm-flex flex-wrap align-items-center mb-3">
                            <!-- Avatar -->
                            <div class="avatar avatar-md">
                                <img class="avatar-img rounded-circle border border-white border-3" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ fakePageId }}">
                            </div>
                            <!-- info -->
                            <div class="ms-sm-2 my-2 my-sm-0">
                                <h6 class="mb-0">{{ fakePageId }}</h6>
                            </div>
                            <!-- Button -->
                        </div>

                        {% endfor %}
                    </div>
                    {% else %}
                        <p>{{ label('pages.no.pages.involved') | raw }}</p>
                    {% endif %}
                    <!-- Card body END -->
                </div>

                <!-- Pending Pages Card START (only for event owners) -->
                {% if user is not empty and entry.event.ownerId == user.id and entry.event.pendingPageIds is not empty %}
                <div class="card mt-3">
                    <div class="card-header border-0">
                        <h5 class="card-title">{{ label('event.pages.pending') | raw }}</h5>
                        <p class="small mb-0">{{ label('event.pages.pending.description') | raw }}</p>
                    </div>
                    <!-- Card body START -->
                    <div class="card-body pt-0">
                        {% for pageId in entry.event.pendingPageIds %}
                        {% set pageRelated = get('page', pageId) %}
                        {% if pageRelated is not empty %}
                        <!-- Pending page item -->
                        <div class="d-sm-flex flex-wrap align-items-center mb-3 p-2 bg-warning bg-opacity-10 rounded">
                            <!-- Avatar -->
                            <div class="avatar avatar-md">
                                {% if pageRelated.profileImageId is not empty %}
                                <img class="avatar-img rounded-circle border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageRelated.profileImageId }}" alt="{{ pageRelated.name }}">
                                {% else %}
                                <img class="avatar-img rounded-circle border border-white border-3" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ pageRelated.name }}">
                                {% endif %}
                            </div>
                            <div class="ms-sm-2 my-2 my-sm-0 flex-grow-1">
                                <h6 class="mb-0">
                                    <i class="fas fa-clock text-warning me-1"></i>
                                    {{ pageRelated.name }}
                                </h6>
                                <p class="small mb-0">{{ label('event.pages.pending.status') | raw }}</p>
                            </div>
                            <!-- Status badge -->
                            <span class="badge bg-warning">{{ label('common.pending') | raw }}</span>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                    <!-- Card body END -->
                </div>
                <!-- Pending Pages Card END -->
                {% endif %}
                <!-- Card END -->
            </div>
            <div class="col-lg-6 border-lg-r">
                <!-- Card START -->
                <div class="card">
                    <div class="card-header border-0 pb-0">
                        <h5 class="card-title mb-0">{{ label('event.location') | raw }}</h5>
                        <p class="small"> <i class="bi bi-geo-alt pe-1"></i>{{ entry.event.fulladdress }}
                            {% if entry.event.extraAddress is not empty %}
                            <br>
                            {{ entry.event.extraAddress }}
                            {% endif %}
                        </p>
                        <!-- Button modal -->
                    </div>
                    <!-- Card body START -->
                    <div class="card-body pt-0">
                        <!-- Google map -->
                        <iframe class="w-100 d-block rounded-bottom" height="230" src="https://www.google.com/maps/embed/v1/place?key=AIzaSyBzbbAKQoJEad0bNS1qFMksTDfT60qJUnU&q={{ entry.event.fulladdress }}&zoom=12"  style="border:0;" aria-hidden="false" tabindex="0"></iframe>
                    </div>
                    <!-- Card body END -->
                </div>
                {% if entry.event.locandina is not empty %}
                <div class="card">
                    <div class="card-header border-0 pb-0 mb-3">
                        <a class="btn btn-primary w-50 float-end mb-2 mb-lg-0" type="button" href="{{ paths('FILE') }}?oid={{ entry.event.locandina }}" target="_blank">
                            <i class="bi bi-download pe-1"></i> {{ label('event.poster') | raw }}
                        </a>
                    </div>
                </div>
                {% endif %}
                <!-- Card END -->
            </div>
        </div>
        <!-- Related events END -->
        {% endif %}

        {% if entry.event.childIds is not empty %}
        <div class="col-lg-12 border-lg-lr mt-0">
            <hr class="mt-3"/>
        </div>
        <!-- Events START -->
        <div class="card border-lg-lr border-lg-b mt-0">
            <!-- Card header START -->
            <div class="card-header d-sm-flex align-items-center justify-content-between border-0 pb-0">
                <h5 class="card-title mb-sm-0">{{ label('common.agenda') | raw }}</h5>
            </div>
            <!-- Card header END -->

            <!-- Filter controls START -->
            <div class="card-header border-0 pt-0">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="pageFilter" class="form-label small">{{ label('common.filter.by.page') | raw }}</label>
                        <select class="form-select" id="pageFilter" name="pageFilter">
                            <option value="">{{ label('common.all.pages') | raw }}</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="sortOrder" class="form-label small">{{ label('common.sort.by.date') | raw }}</label>
                        <select class="form-select" id="sortOrder" name="sortOrder">
                            <option value="desc">{{ label('common.newest.first') | raw }}</option>
                            <option value="asc">{{ label('common.oldest.first') | raw }}</option>
                        </select>
                    </div>
                </div>
            </div>
            <!-- Filter controls END -->
            <!-- Card body START -->
            <div class="card-body pt-0">
                <div class="containerEvents">
                    <div class="oneEvent">
                        {% for eventEntry in entry.event.childIds %}
                        {% set tmpEvent = get('event', eventEntry) %}
                        <div class="row">
                            {% set pastEvent = false %}
                            {% if daysbetween(tmpEvent.event.startDate, today()) > 0 %}
                                {% if tmpEvent.event.endDate is not empty %}
                                    {% if daysbetween(tmpEvent.event.endDate, today()) > 0 %}
                                        {% set pastEvent = true %}
                                    {% endif %}
                                {% else %}
                                    {% set pastEvent = true %}
                                {% endif %}
                            {% endif %}
                            <div class="d-sm-flex align-items-center {{ pastEvent ? 'opacity-50':'' }}">
                                <!-- Avatar -->
                                <div class="avatar avatar-xl">
                                    <a href="{{ paths('EVENT_BASE') }}/{{ tmpEvent.identifier }}" class="eventlink">
                                        {% if tmpEvent.coverImageId is not empty %}
                                        <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ tmpEvent.coverImageId }}" alt="{{ tmpEvent.name }}">
                                        {% else %}
                                        {% if pageDb.coverImageId is not empty %}
                                        <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageDb.coverImageId }}" alt="{{ tmpEvent.name }}">
                                        {% else %}
                                        <img class="avatar-img border border-white border-3" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="{{ tmpEvent.name }}">
                                        {% endif %}
                                        {% endif%}
                                    </a>
                                </div>
                                <div class="ms-sm-4 mt-2 mt-sm-0">
                                    <!-- Info -->
                                    <h5 class="mb-1"><a href="{{ paths('EVENT_BASE') }}/{{ tmpEvent.identifier }}"> {{ tmpEvent.name }} </a></h5>
                                    <ul class="nav nav-stack small">
                                        <li class="nav-item">
                                            <i class="bi bi-calendar-check pe-1"></i>{{ formatDate(tmpEvent.startDate, "EEE d MMM yyyy", language) }} {{ tmpEvent.startHour }}
                                        </li>
                                        <li class="nav-item">
                                            <i class="bi bi-geo-alt pe-1"></i> {{ tmpEvent.city }}
                                        </li>
                                        {% if eventEntry.followerCount > 0 %}
                                        <li class="nav-item">
                                            <i class="bi bi-people pe-1"></i> {{ eventEntry.followerCount }} {{ label('common.participants') | raw }}
                                        </li>
                                        {% endif %}
                                    </ul>
                                </div>                                
                            </div>
                        </div>
                        <!-- Events list END -->
                        <hr>
                        {% endfor %}
                    </div>
                </div>
            </div>
            <!-- Events list END -->
        </div>
        <!-- Events START -->
        {% endif %}
    </div>
    {% else %}
    <div class="row g-0 border-lg-lr border-lg-b">
        <!-- Main content START -->
        <div class="col-md-8 col-lg-9 vstack gap-4">
            <!-- Card START -->
            {% if entry.event.coverImageId is not empty %}
            <div class="card card-body card-overlay-bottom border-lg-b" style="background-image:url({{ paths('IMAGE_SYSTEM') }}?oid={{ entry.event.coverImageId }}); background-position: center; background-size: cover; background-repeat: no-repeat;">
                {% else %}
                {% if entry.event.pageIds is not empty %}
                {% set firstPage = get('page', entry.event.pageIds[0]) %}
                {% endif %}
                {% if firstPage is not empty and firstPage.coverImageId is not empty %}
                <div class="card card-body card-overlay-bottom border-lg-b" style="background-image:url({{ paths('IMAGE_SYSTEM') }}?oid={{ firstPage.coverImageId }}); background-position: center; background-size: cover; background-repeat: no-repeat;">
                    {% else %}
                    <div class="card card-body card-overlay-bottom border-lg-b" style="background-image:url({{ contextPath }}/fe/images/bg/placeholder-event.jpg); background-position: top; background-size: cover; background-repeat: no-repeat;">
                        {% endif %}
                        {% endif %}
                        {% if isContainer is empty or isContainer == false %}
                        <!-- Card body START -->
                        <div class="row g-3 justify-content-between">
                            <!-- Date START -->
                            <div class="col-lg-2">
                                <div class="bg-mode text-center overflow-hidden p-1 d-inline-block">
                                    <div class="bg-primary p-2 text-white small lh-1">{{ formatDate(entry.event.startDate, "EEEE", language) }}</div>
                                    <h5 class="mb-0 py-2 lh-1">{{ formatDate(entry.event.startDate, "dd/MM", language) }}</h5>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        <!-- Event name START -->
                        <div class="row g-3 justify-content-between align-items-center mt-5 pt-5 position-relative z-index-9">
                            <div class="col-lg-9">
                                <h1 class="h3 mb-1 text-white">{{ entry.event.name }}</h1>
                            </div>
                            {% if entry.event.ticketsUrl is not empty %}
                            <!-- Button -->
                            <div class="col-lg-3 text-lg-end">
                                <a class="btn btn-primary" target="_blank" href="https://{{ entry.event.ticketsUrl | replace({'https://': '', 'http://': ''}) }}"> {{ label('common.buy.ticket') | raw }} </a>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Event Navigation START -->
                        {% if entry.event.parentId is not empty %}
                        <div class="row g-3 justify-content-between align-items-center mt-3 position-relative z-index-9">
                            <div class="col-6">
                                <button id="prevEventBtnHorizontal" class="btn btn-outline-light" style="display: none;">
                                    <i class="bi bi-chevron-left"></i> {{ label('common.previous') | raw }}
                                </button>
                            </div>
                            <div class="col-6 text-end">
                                <button id="nextEventBtnHorizontal" class="btn btn-outline-light" style="display: none;">
                                    {{ label('common.next') | raw }} <i class="bi bi-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                        {% endif %}
                        <!-- Event Navigation END -->
                        <!-- Event name END -->
                    </div>
                    <!-- Card END -->

                    <!-- Card About START -->
                    <div class="card card-body border-lg-b">
                        <!-- Card body START -->
                        <div class="row g-4">
                            <!-- info -->
                            <div class="col-lg-6">
                                <h5>{{ label('common.description') | raw }}</h5>
                            </div>
                            <div class="col-lg-6 text-end">
                                {% if (entry.event.ownerId == user.id and daysbetween(entry.event.startDate, today()) <= 0 or hoursbetween(entry.event.creation, now()) <= 2) or user.profileType == 'system' or user.profileType == 'admin' %}
                                <a class="btn btn-primary-soft text-nowrap" href="{{ paths('EVENT_EDIT') }}?oid={{ entry.event.id }}&backUrl={{ paths('EVENT_BASE') }}/"><i class="bi bi-pencil pe-1"></i> {{ label('event.edit') | raw }}</a>
                                {% endif %}
                            </div>
                            <div class="col-12 mt-0">
                                <p class="mb-0">{% autoescape false %}{{ newline(entry.event.description) }}{% endautoescape %}</p>
                                {% if entry.event.tags is not empty %}
                                <p>
                                <ul class="nav nav-stack gap-0 gap-sm-2">
                                    <li class="nav-item">
                                        <i class="bi bi-tags-fill"></i>
                                        {% for tag in entry.event.tags %}
                                        <a href="{{ paths('RESULTS') }}?q=events&text={{ tag }}">#{{ tag | lower }}</a>
                                        {% endfor %}
                                    </li>
                                </ul>
                                </p>
                                {% endif %}
                            </div>
                            {% if isContainer is empty or isContainer == false %}
                            <div class="col-sm-6 col-lg-4">
                                <!-- Timings -->
                                <h5>{{ label('common.timetables') | raw }}</h5>
                                <p class="small mb-0">{{ entry.event.startHour }}{% if entry.event.endHour %}- {{ entry.event.endHour }}{% endif %}</p>
                            </div>
                            <!-- Entry Fees -->
                            <div class="col-sm-6 col-lg-4">
                                <h5>{{ label('common.entry') | raw }}</h5>
                                {% if entry.event.freeEntry %}
                                <p class="small mb-0"> {{ label('common.free') | raw }}</p>
                                {% else %}
                                <p class="small mb-0"> {{ label('common.starting.from') | raw }} {{entry.event.ticketsMinPrice | numberformat("#,##0") }}€</p>
                                {% endif %}
                            </div>
                            {% endif %}
                            
                        </div>
                        {% if daysbetween(entry.event.startDate, today()) <= 0 or entry.event.type == "container" %}
                        <hr class="mt-4">
                        <div class="row align-items-center" id="panelFollower">
                            {% if entry.followerCount > 0 and (entry.event.showFollowers == true) %}
                            <div class="col-lg-4">
                                <h5 class="text-capitalize">{{ label('common.interested') | raw }}</h5>
                                <!-- Avatar group START -->
                                <ul class="avatar-group list-unstyled align-items-center">
                                    {% if entry.followerPages is not empty %}
                                    {% for followerPage in entry.followerPages %}
                                    <li class="avatar avatar-xs">                                        
                                        <a href="{{ paths('PAGE_BASE') }}/{{ followerPage.identifier }}">
                                            {% if followerPage.profileImageId is not empty %}
                                            <img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ followerPage.profileImageId }}">
                                            {% else %}
                                            <img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg"> 
                                            {% endif %}
                                        </a>
                                    </li>
                                    {% endfor %}
                                    {% endif %}
                                    <li class="ms-4">
                                        {% if entry.followerCount > 1 %}
                                        <small>{{ entry.followerCount }} {{ label('common.people.interested') | raw }}</small>
                                        {% else %}
                                        <small>{{ entry.followerCount }} {{ label('common.person.interested') | raw }}</small>
                                        {% endif %}
                                    </li>
                                </ul>
                            </div>
                            {% endif%}

                            <div class="col-lg-8">
                                <!--<h5>
                                    {{ label('common.interact') | raw }}
                                    {% if entry.event.ownerId == user.id and (entry.event.showFollowers == null or entry.event.showFollowers == false) %}
                                        {% if entry.followerCount > 1 %}
                                            &lt;!&ndash; Icona info con popover &ndash;&gt;
                                            <i class="bi bi-info-circle text-primary"
                                               role="button"
                                               tabindex="0"
                                               data-bs-toggle="popover"
                                               data-bs-trigger="hover focus"
                                               data-bs-placement="right"
                                               title="Informazioni"
                                               data-bs-content="{{ entry.followerCount }} {{ label('common.people.interested') | raw }}">
                                            </i>
                                        {% else %}
                                            &lt;!&ndash; Icona info con popover &ndash;&gt;
                                            <i class="bi bi-info-circle text-primary"
                                               role="button"
                                               tabindex="0"
                                               data-bs-toggle="popover"
                                               data-bs-trigger="hover focus"
                                               data-bs-placement="right"
                                               title="Informazioni"
                                               data-bs-content="{{ entry.followerCount }} {{ label('common.person.interested') | raw }}">
                                            </i>
                                        {% endif %}
                                    {% endif%}
                                </h5>-->
                                <div class="d-flex flex-column flex-lg-row justify-content-start ms-sm-auto">
                                    {% if isContainer is empty or isContainer == false %}
                                    {% if entry.iFollow %}
                                    <button class="btn btn-danger-soft flex-fill event-add-follow me-lg-2 mb-2 mb-lg-0 text-nowrap" data-value="active" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-event-id="{{ entry.event.id }}"><i class="bi bi-dash-circle-fill pe-1"></i> {{ label('common.i.not.interested') | raw }}</button>
                                    {% else %}
                                    <button class="btn btn-success-soft flex-fill event-add-follow me-lg-2 mb-2 mb-lg-0 text-nowrap" data-value="inactive" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-event-id="{{ entry.event.id }}"><i class="bi bi-plus-circle-fill pe-1"></i> {{ label('common.i.interested') | raw }}</button>
                                    {% endif %}
                                    <button id="add-to-calendar" class="btn btn-warning-soft me-lg-2 mb-2 mb-lg-0 text-nowrap"><i class="bi-calendar-date-fill me-1"></i> {{ label('common.add.to.calendar') | raw }}</button>
                                    <div class="dropdown flex-fill">
                                        <!-- Card share action menu -->
                                        <button class="btn btn-primary-soft w-100 mb-2 mb-lg-0" type="button" id="profileAction2" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="bi bi-share-fill pe-1"></i> {{ label('common.share') | raw }}
                                        </button>
                                        <!-- Card share action dropdown menu -->
                                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="profileAction2">
                                            <li>
                                                <a class="dropdown-item" href="https://www.facebook.com/sharer/sharer.php?u={{ publicUrl }}" aria-label="Facebook">
                                                    <i class="bi-facebook"></i> Facebook
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="https://api.whatsapp.com/send?text=Guarda%20questo%20evento%20su%20Agorapp!%20{{ publicUrl }}" aria-label="Whatsapp">
                                                    <i class="bi-whatsapp"></i> Whatsapp
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="https://t.me/share/url?url={{ publicUrl }}&text=Guarda%20questo%20evento%20su%20Agorapp!" aria-label="Telegram">
                                                    <i class="bi-telegram"></i> Telegram
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="https://twitter.com/intent/tweet?url={{ publicUrl }}&text=Guarda%20questo%20evento%20su%20Agorapp!" aria-label="Twitter" target="_blank" rel="noopener">
                                                    <i class="bi-twitter"></i> Twitter
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="https://www.linkedin.com/sharing/share-offsite/?url={{ publicUrl }}" aria-label="LinkedIn" target="_blank" rel="noopener">
                                                    <i class="bi-linkedin"></i> LinkedIn
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" data-clipboard-text="{{ publicUrl }}" id="copyToClipboardLink" aria-label="{{ label('common.copy.to.clipboard') | raw }}">
                                                    <i class="bi-clipboard-check"></i> {{ label('common.copy.to.clipboard') | raw }}
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                    {% else %}
                                    <a href="{{ paths('EVENT_ADD') }}?parentId={{ entry.event.id }}"
                                       class="btn btn-primary-soft mb-2 mb-lg-0">
                                        <i class="bi bi-plus-circle-fill pe-1"></i> {{ label('common.create.event') | raw
                                        }}
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
                <!-- Card About END -->

                {% if isContainer is empty or isContainer == false %}
                <!-- Related events START -->
                <div class="row g-0">
                    <div class="col-lg-6">
                        <!-- Card START -->
                        <div class="card">
                            <div class="card-header border-0">
                                <h5 class="card-title">{{ label('event.pages.involved') | raw }}</h5>
                                <!-- Button modal -->
                            </div>
                            {% if entry.event.pageIds is not empty or entry.event.fakePageIds %}
                            <!-- Card body START -->
                            <div class="card-body pt-0">
                                {% for pageId in entry.event.pageIds %}
                                {% set pageRelated = get('page', pageId) %}
                                <!-- Related events item -->
                                <div class="d-sm-flex flex-wrap align-items-center mb-3">
                                    <!-- Avatar -->
                                    <div class="avatar avatar-md">
                                        <a href="{{ paths('PAGE_BASE') }}/{{ pageRelated.identifier }}">
                                            {% if pageRelated.profileImageId is not empty %}
                                            <img class="avatar-img rounded-circle border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageRelated.profileImageId }}" alt="{{ pageRelated.name }}">
                                            {% else %}
                                            <img class="avatar-img rounded-circle border border-white border-3" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ pageRelated.name }}">
                                            {% endif %}     
                                        </a>
                                    </div>
                                    <!-- info -->
                                    <div class="ms-sm-2 my-2 my-sm-0">
                                        <h6 class="mb-0"><a href="{{ paths('PAGE_BASE') }}/{{ pageRelated.identifier }}">{{ pageRelated.name }}</a></h6>
                                    </div>
                                    <!-- Button -->
                                </div>

                                {% endfor %}

                                {% for fakePageId in entry.event.fakePageIds %}
                                <!-- Related events item -->
                                <div class="d-sm-flex flex-wrap align-items-center mb-3">
                                    <!-- Avatar -->
                                    <div class="avatar avatar-md">
                                        <img class="avatar-img rounded-circle border border-white border-3" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ fakePageId }}">
                                    </div>
                                    <!-- info -->
                                    <div class="ms-sm-2 my-2 my-sm-0">
                                        <h6 class="mb-0">{{ fakePageId }}</h6>
                                    </div>
                                    <!-- Button -->
                                </div>

                                {% endfor %}
                            </div>
                            {% else %}
                            Nessuna pagina coinvolta
                            {% endif %}
                            <!-- Card body END -->
                        </div>
                        <!-- Card END -->

                        <!-- Pending Pages Card START (only for event owners) -->
                        {% if user is not empty and entry.event.ownerId == user.id and entry.event.pendingPageIds is not empty %}
                        <div class="card mt-3">
                            <div class="card-header border-0">
                                <h5 class="card-title">{{ label('event.pages.pending') | raw }}</h5>
                                <p class="small mb-0">{{ label('event.pages.pending.description') | raw }}</p>
                            </div>
                            <!-- Card body START -->
                            <div class="card-body pt-0">
                                {% for pageId in entry.event.pendingPageIds %}
                                {% set pageRelated = get('page', pageId) %}
                                {% if pageRelated is not empty %}
                                <!-- Pending page item -->
                                <div class="d-sm-flex flex-wrap align-items-center mb-3 p-2 bg-warning bg-opacity-10 rounded">
                                    <!-- Avatar -->
                                    <div class="avatar avatar-md">
                                        {% if pageRelated.profileImageId is not empty %}
                                        <img class="avatar-img rounded-circle border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageRelated.profileImageId }}" alt="{{ pageRelated.name }}">
                                        {% else %}
                                        <img class="avatar-img rounded-circle border border-white border-3" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ pageRelated.name }}">
                                        {% endif %}
                                    </div>
                                    <div class="ms-sm-2 my-2 my-sm-0 flex-grow-1">
                                        <h6 class="mb-0">
                                            <i class="fas fa-clock text-warning me-1"></i>
                                            {{ pageRelated.name }}
                                        </h6>
                                        <p class="sma mb-0">{{ label('event.pages.pending.status') | raw }}</p>
                                    </div>
                                    <!-- Status badge -->
                                    <span class="badge bg-warning">{{ label('common.pending') | raw }}</span>
                                </div>
                                {% endif %}
                                {% endfor %}
                            </div>
                            <!-- Card body END -->
                        </div>
                        <!-- Pending Pages Card END -->
                        {% endif %}
                    </div>
                    <div class="col-lg-6">
                        <!-- Card START -->
                        <div class="card">
                            <div class="card-header border-0 pb-0">
                                <h5 class="card-title mb-0">{{ label('event.location') | raw }}</h5>
                                <p class="small"> <i class="bi bi-geo-alt pe-1"></i>{{ entry.event.fulladdress }}
                                    {% if entry.event.extraAddress is not empty %}
                                    <br>
                                    {{ entry.event.extraAddress }}
                                    {% endif %}
                                </p>
                                <!-- Button modal -->
                            </div>
                            <!-- Card body START -->
                            <div class="card-body pt-0">
                                <!-- Google map -->
                                <iframe class="w-100 d-block rounded-bottom" height="230" src="https://www.google.com/maps/embed/v1/place?key=AIzaSyBzbbAKQoJEad0bNS1qFMksTDfT60qJUnU&q={{ entry.event.fulladdress }}&zoom=12"  style="border:0;" aria-hidden="false" tabindex="0"></iframe>
                            </div>
                            <!-- Card body END -->
                        </div>
                        {% if entry.event.locandina is not empty %}
                        <div class="card">
                            <div class="card-header border-0 pb-0 mb-3">
                                <a class="btn btn-primary w-50 float-end mb-2 mb-lg-0" type="button" href="{{ paths('FILE') }}?oid={{ entry.event.locandina }}" target="_blank">
                                    <i class="bi bi-download pe-1"></i> {{ label('event.poster') | raw }}
                                </a>
                            </div>
                        </div>
                        {% endif %}
                        <!-- Card END -->
                    </div>
                </div>
                <!-- Related events END -->
                {% endif %}

                {% if entry.event.childIds is not empty %}
                <hr class="mt-3"/>
                <!-- Events START -->
                <div class="card">
                    <!-- Card header START -->
                    <div class="card-header d-sm-flex align-items-center justify-content-between border-0 pb-0">
                        <h5 class="card-title mb-sm-0">{{ label('common.event.related') | raw }}</h5>
                    </div>
                    <!-- Card header END -->

                    <!-- Filter controls START -->
                    <div class="card-header border-0 pt-0">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="pageFilterHorizontal" class="form-label small">{{ label('common.filter.by.page') | raw }}</label>
                                <select class="form-select" id="pageFilterHorizontal" name="pageFilter">
                                    <option value="">{{ label('common.all.pages') | raw }}</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="sortOrderHorizontal" class="form-label small">{{ label('common.sort.by.date') | raw }}</label>
                                <select class="form-select" id="sortOrderHorizontal" name="sortOrder">
                                    <option value="desc">{{ label('common.newest.first') | raw }}</option>
                                    <option value="asc">{{ label('common.oldest.first') | raw }}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <!-- Filter controls END -->
                    <!-- Card body START -->
                    <div class="card-body pt-0">
                        <div class="containerEvents">
                            <div class="oneEvent">                            
                                {% for eventEntry in entry.event.childIds %}
                                {% set tmpEvent = get('event', eventEntry) %}
                                <div class="row">
                                    {% set pastEvent = false %}
                                    {% if daysbetween(tmpEvent.startDate, today()) > 0 %}
                                        {% if tmpEvent.endDate is not empty %}
                                            {% if daysbetween(tmpEvent.endDate, today()) > 0 %}
                                                {% set pastEvent = true %}
                                            {% endif %}
                                        {% else %}
                                            {% set pastEvent = true %}
                                        {% endif %}
                                    {% endif %}
                                    <div class="d-sm-flex align-items-center {{ pastEvent ? 'opacity-50':'' }}">
                                        <!-- Avatar -->
                                        <div class="avatar avatar-xl">                                            
                                            <a href="{{ paths('EVENT_BASE') }}/{{ tmpEvent.identifier }}" class="eventlink">
                                                {% if tmpEvent.coverImageId is not empty %}
                                                <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ tmpEvent.coverImageId }}" alt="{{ tmpEvent.name }}">
                                                {% else %}
                                                {% if pageDb.coverImageId is not empty %}
                                                <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageDb.coverImageId }}" alt="{{ tmpEvent.name }}">
                                                {% else %}
                                                <img class="avatar-img border border-white border-3" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="{{ tmpEvent.name }}">
                                                {% endif %}
                                                {% endif%}
                                            </a>
                                        </div>
                                        <div class="ms-sm-4 mt-2 mt-sm-0">
                                            <!-- Info -->
                                            <h5 class="mb-1"><a href="{{ paths('EVENT_BASE') }}/{{ tmpEvent.identifier }}"> {{ tmpEvent.name }} </a></h5>
                                            <ul class="nav nav-stack small">
                                                <li class="nav-item">
                                                    <i class="bi bi-calendar-check pe-1"></i>{{ formatDate(tmpEvent.startDate, "EEE d MMM yyyy", language) }} {{ tmpEvent.startHour }}
                                                </li>
                                                <li class="nav-item">
                                                    <i class="bi bi-geo-alt pe-1"></i> {{ tmpEvent.city }}
                                                </li>
                                                {% if eventEntry.followerCount > 0 and (tmpEvent.showFollowers == true) %}
                                                <li class="nav-item">
                                                    <i class="bi bi-people pe-1"></i> {{ eventEntry.followerCount }} {{ label('common.participants') | raw }}
                                                </li>
                                                {% endif %}
                                            </ul>
                                        </div>
                                       
                                    </div>
                                </div>
                                <!-- Events list END -->
                                <hr>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    <!-- Events list END -->
                </div>
                <!-- Events START -->
                {% endif %}
            </div>
            <!-- Main content END -->
        </div> <!-- Row END -->
        {% endif %}
        <!-- Container END -->
        <div class="d-lg-none">
            {% include "fe/include/snippets/sidenav-left.html" %}
        </div>
        {% endblock %}

        {% block pagescripts %}
        <script src="https://cdn.jsdelivr.net/npm/add-to-calendar-button@2" async defer></script>
        <script type="application/javascript">
            const config = {
            hideCheckmark: true,
            language: "{{ language }}",
            name: "{{ entry.event.name }}",
            options: ["Apple","Google","iCal","Outlook.com","Yahoo","MicrosoftTeams","Microsoft365"],
            startDate: "{{ entry.event.startDate | date('yyyy-MM-dd') }}",
            {% if entry.event.startHour is not empty and entry.event.endHour is not empty %}
            startTime:"{{ entry.event.startHour }}",
            endTime:"{{ entry.event.endHour }}",
            {% endif %}
            {% if entry.event.endDate is not empty %}
            endDate:"{{ entry.event.endDate | date('yyyy-MM-dd') }}",
            {% else %}
            endDate:"{{ entry.event.startDate | date('yyyy-MM-dd') }}",
            {% endif %}
            timeZone:"Europe/Rome",
            location:"{{ entry.event.fulladdress }}"
            };
            const button = document.getElementById('add-to-calendar');
            if (button) {
            button.addEventListener('click', () => atcb_action(config, button));
            }
        </script>
        <script defer src="{{ contextPath }}/fe/js/clipboard.min.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                var clipboard = new ClipboardJS('#copyToClipboardLink');

                clipboard.on('success', function (e) {
                    let myAlert = document.querySelector('.toast');
                    let bsAlert = new bootstrap.Toast(myAlert);
                    bsAlert.show();

                    e.clearSelection();
                });
            });
        </script>
        <script src="{{ contextPath }}/fe/js/pages/event-detail.js?{{ buildNumber }}"></script>
        {% endblock %}