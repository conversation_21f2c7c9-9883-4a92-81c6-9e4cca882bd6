{% extends "fe/include/base.html" %}

{% set activePage = 'CALENDAR' %}

{% block title %}{{ label('account.calendar.title.meta') | raw }} | Agorapp{% endblock %}

{% block pagecss %}
<link href="{{ contextPath }}/fe/vendor/slim/slim.min.css" rel="stylesheet" type="text/css" media="all">
<link href="{{ contextPath }}/fe/vendor/choices.js/public/assets/styles/choices.min.css" rel="stylesheet" type="text/css" media="all">
{% endblock %}

{% block content %}
<div id="pagination" style="display: none;">{{ pagination }}</div>
<div id="paginationEventsFollow" style="display: none;">{{ paginationEventsFollow }}</div>
<div id="event.delete.event" style="display: none">{{ label('event.delete.event') | raw }}</div>
<a id="dataCitiesUri" style="display: none" href="{{ paths('DATA_CITIES') }}?name="></a>
<a id="accountInfoEditSaveUri" style="display: none" href="{{ paths('ACCOUNT_INFO_EDIT_SAVE') }}" rel="nofollow"></a>
<a id="accountRemoveUri" style="display: none" href="{{ paths('ACCOUNT_REMOVE') }}" rel="nofollow"></a>
<a id="accountDeletedUri" style="display: none" href="{{ paths('ACCOUNT_DELETED') }}" rel="nofollow"></a>
<a id="accountCalendarUri" style="display: none" href="{{ paths('ACCOUNT_CALENDAR') }}" rel="nofollow"></a>
<a id="eventFollowToggleUri" style="display: none" href="{{ paths('EVENT_FOLLOWER_TOGGLE') }}" rel="nofollow"></a>
<a id="eventRemoveUri" style="display: none" href="{{ paths('EVENT_REMOVE') }}" rel="nofollow"></a>

<!-- Container START -->
<div class="container">
    <div class="row g-0">

        <!-- Sidenav START -->
        <div class="col-lg-3">
            {% include "fe/include/snippets/sidenav-left.html" %}
        </div>
        <!-- End Col -->

        <!-- Main content START -->
        <div class="col-lg-6 vstack gap-4">
            <!-- Events START -->
            <div class="card border-lg-lr border-lg-b">
                
                
                <!-- Card header START -->
                    <div class="card-header border-0 pb-0">
                        {% if user.profileType == 'unconfirmed' %}
                        <!-- Alert -->
                        <div class="alert alert-danger text-center card-alert" role="alert">
                            {{ label('account.not.confirm') | raw }} 
                            <a class="alert-link" id="account-confirm-send" href="{{ paths('ACCOUNT_CONFIRM_SEND') }}">
                                {{ label('account.send.link') | raw }}<i class="bi-chevron-right small ms-1"></i>
                            </a>
                        </div>
                        <!-- End Alert -->
                        {% endif %}

                        <div class="row align-items-center">
                            <div class="col-sm-8">
                                <h1 class="h5 card-title mb-1">{{ label('common.events') | raw }}</h1>
                                <p class="mb-2">{{ label('account.events.followed.and.created') | raw }}</p>
                            </div>
                            {% if user.profileType != 'unconfirmed' %}
                            <div class="col-sm-4 text-sm-end text-center">
                                <div class="dropdown">
                                    <button class="btn btn-primary mt-2 mt-sm-0 w-100 w-md-auto dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fa-solid fa-plus pe-1"></i> {{ label('common.create.event') | raw }}
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li>
                                            <a class="dropdown-item d-flex align-items-center" href="{{ paths('EVENT_ADD') }}">
                                                <svg id='Calendar_Date_One_32' width='32' height='32' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='32' height='32' stroke='none' fill='#000000' opacity='0'/>


                                                <g transform="matrix(2 0 0 2 16 16)" >
                                                <g style="" >
                                                <g transform="matrix(1 0 0 1 -2.37 -5.11)" >
                                                <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.75, -2.02)" d="M 4.75342 0.75 L 4.75342 3.28918" stroke-linecap="round" />
                                                </g>
                                                <g transform="matrix(1 0 0 1 2.11 -5.11)" >
                                                <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-9.24, -2.02)" d="M 9.23975 0.75 L 9.23975 3.28918" stroke-linecap="round" />
                                                </g>
                                                <g transform="matrix(1 0 0 1 -0.13 0.42)" >
                                                <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7, -7.55)" d="M 1.09183 10.5249 C 1.25317 11.9642 2.42311 13.1071 3.86983 13.1768 C 4.86978 13.2249 5.89127 13.2499 6.99991 13.2499 C 8.10855 13.2499 9.13003 13.2249 10.13 13.1768 C 11.5767 13.1071 12.7466 11.9642 12.908 10.5249 C 13.0166 9.55554 13.1058 8.56176 13.1058 7.54941 C 13.1058 6.53707 13.0166 5.54329 12.908 4.57397 C 12.7466 3.13459 11.5767 1.99174 10.13 1.92207 C 9.13003 1.87391 8.10855 1.84888 6.99991 1.84888 C 5.89127 1.84888 4.86978 1.87391 3.86983 1.92207 C 2.42311 1.99174 1.25317 3.13459 1.09183 4.57397 C 0.983182 5.54329 0.894043 6.53707 0.894043 7.54941 C 0.894043 8.56176 0.983182 9.55554 1.09183 10.5249 Z" stroke-linecap="round" />
                                                </g>
                                                <g transform="matrix(1 0 0 1 -0.13 0.42)" >
                                                <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7, -7.55)" d="M 7 5.29803 L 7 9.80076" stroke-linecap="round" />
                                                </g>
                                                <g transform="matrix(1 0 0 1 -0.88 -1.26)" >
                                                <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-6.25, -5.86)" d="M 5.49909 6.42372 L 5.87432 6.42372 C 6.49601 6.42372 7 5.91973 7 5.29803 L 7 5.29803" stroke-linecap="round" />
                                                </g>
                                                <g transform="matrix(1 0 0 1 -0.12 2.68)" >
                                                <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7, -9.8)" d="M 8.50098 9.80078 L 5.49915 9.80078" stroke-linecap="round" />
                                                </g>
                                                </g>
                                                </g>
                                                </svg>
                                                <div class="ms-2">
                                                    <div class="fw-bold">{{ label('common.create.event') | raw }}</div>
                                                    <small class="text-gray-600">{{ label('event.single.description') | raw }}</small>
                                                </div>
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item d-flex align-items-center" href="{{ paths('EVENT_ADD') }}?isContainer=true">
                                                <svg id='Calendar_Mark_32' width='32' height='32' viewBox='0 0 32 32' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><rect width='32' height='32' stroke='none' fill='#000000' opacity='0'/>


                                                <g transform="matrix(2 0 0 2 16 16)" >
                                                <g style="" >
                                                <g transform="matrix(1 0 0 1 -2.37 -5.11)" >
                                                <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.75, -2.02)" d="M 4.75342 0.75 L 4.75342 3.28918" stroke-linecap="round" />
                                                </g>
                                                <g transform="matrix(1 0 0 1 2.11 -5.11)" >
                                                <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-9.24, -2.02)" d="M 9.23975 0.75 L 9.23975 3.28918" stroke-linecap="round" />
                                                </g>
                                                <g transform="matrix(1 0 0 1 -0.13 0.42)" >
                                                <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7, -7.55)" d="M 1.09183 10.5249 C 1.25317 11.9642 2.42311 13.1071 3.86983 13.1768 C 4.86978 13.2249 5.89127 13.2499 6.99991 13.2499 C 8.10855 13.2499 9.13003 13.2249 10.13 13.1768 C 11.5767 13.1071 12.7466 11.9642 12.908 10.5249 C 13.0166 9.55554 13.1058 8.56176 13.1058 7.54941 C 13.1058 6.53707 13.0166 5.54329 12.908 4.57397 C 12.7466 3.13459 11.5767 1.99174 10.13 1.92207 C 9.13003 1.87391 8.10855 1.84888 6.99991 1.84888 C 5.89127 1.84888 4.86978 1.87391 3.86983 1.92207 C 2.42311 1.99174 1.25317 3.13459 1.09183 4.57397 C 0.983182 5.54329 0.894043 6.53707 0.894043 7.54941 C 0.894043 8.56176 0.983182 9.55554 1.09183 10.5249 Z" stroke-linecap="round" />
                                                </g>
                                                <g transform="matrix(1 0 0 1 -2.88 -1.08)" >
                                                <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.25, -6.05)" d="M 4 6.04944 L 4.5 6.04944" stroke-linecap="round" />
                                                </g>
                                                <g transform="matrix(1 0 0 1 -2.88 1.92)" >
                                                <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-4.25, -9.05)" d="M 4 9.04944 L 4.5 9.04944" stroke-linecap="round" />
                                                </g>
                                                <g transform="matrix(1 0 0 1 -0.13 -1.08)" >
                                                <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7, -6.05)" d="M 6.75 6.04944 L 7.25 6.04944" stroke-linecap="round" />
                                                </g>
                                                <g transform="matrix(1 0 0 1 2.63 -1.08)" >
                                                <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-9.75, -6.05)" d="M 9.5 6.04944 L 10 6.04944" stroke-linecap="round" />
                                                </g>
                                                <g transform="matrix(1 0 0 1 -0.13 1.92)" >
                                                <path style="stroke: rgb(51,61,76); stroke-width: 0.75; stroke-dasharray: none; stroke-linecap: round; stroke-dashoffset: 0; stroke-linejoin: round; stroke-miterlimit: 4; fill: none; fill-rule: nonzero; opacity: 1;" transform=" translate(-7, -9.05)" d="M 6.75 9.04944 L 7.25 9.04944" stroke-linecap="round" />
                                                </g>
                                                </g>
                                                </g>
                                                </svg>
                                                <div class="ms-2">
                                                    <div class="fw-bold">{{ label('event.create.serial') | raw }}</div>
                                                    <small class="text-gray-600">{{ label('event.serial.description') | raw }}</small>
                                                </div>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <!-- Card header END -->
                
                <!-- Card body START -->
                <div class="card-body" id="myTabContent">
                    <ul class="nav nav-tabs border-0 justify-content-center" id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active btn" id="followedevents-tab" data-bs-toggle="tab" data-bs-target="#followedevents-tab-pane" type="button" role="tab" aria-controls="followedevents-tab-pane" aria-selected="true">{{ label('account.events.future') | raw }}</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link btn" id="followedeventspast-tab" data-bs-toggle="tab" data-bs-target="#followedeventspast-tab-pane" type="button" role="tab" aria-controls="followedeventspast-tab-pane" aria-selected="true">{{ label('account.events.past') | raw }}</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link btn me-2" id="myevents-tab" data-bs-toggle="tab" data-bs-target="#myevents-tab-pane" type="button" role="tab" aria-controls="myevents-tab-pane" aria-selected="false">{{ label('account.your.events') | raw }}</button>
                        </li>
                    </ul>

                    <!-- Events list START -->
                    <div class="tab-content">
                        <div class="tab-pane fade" id="myevents-tab-pane" role="tabpanel" aria-labelledby="myevents-tab" tabindex="0">
                            {% if myEventEntries is not empty %}
                            <div class="containerMyEvents">
                                <div class="myEvents">
                                    {% for myevent in myEventEntries %}
                                        <div class="card border-bottom">
                                            <div class="card-header border-0 px-0">
                                                <!-- Connections Item -->                                        
                                                <div class="d-flex flex-column flex-sm-row align-items-start">
                                                    <div class="d-flex flex-nowrap flex-fill">
                                                        {% set pastEvent = false %}
                                                        {% if daysbetween(myevent.event.startDate, today()) > 0 %}
                                                            {% if myevent.event.endDate is not empty %}
                                                                {% if daysbetween(myevent.event.endDate, today()) > 0 %}
                                                                    {% set pastEvent = true %}
                                                                {% endif %}
                                                            {% else %}
                                                                {% set pastEvent = true %}
                                                            {% endif %}
                                                        {% endif %}
                                                        
                                                        <div class="avatar me-3 mb-3 mb-md-0 {{ pastEvent ? 'opacity-50' : '' }}">                                                
                                                            <a href="{{ paths('EVENT_BASE') }}/{{ myevent.event.identifier }}" class="eventlink">
                                                                {% if myevent.event.coverImageId is not empty %}
                                                                <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ myevent.event.coverImageId }}" alt="{{ myevent.event.name }}">
                                                                {% else %}
                                                                <img class="avatar-img border border-white border-3" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="{{ myevent.event.name }}"> 
                                                                {% endif %}                                                                                                                                                                                                               
                                                            </a>
                                                        </div>
                                                        <!-- Info -->
                                                        <div class="w-100 {{ pastEvent ? 'opacity-50' : '' }}">
                                                            <div class="d-sm-flex align-items-start">
                                                                <h6 class="mb-0"><a href="{{ paths('EVENT_BASE') }}/{{ myevent.event.identifier }}">{{ myevent.event.name }} </a></h6>
                                                            </div>
                                                            {% if myevent.event.type is not empty and myevent.event.type == 'container' %}
                                                            <span class="badge bg-primary">Container</span>
                                                            <ul class="nav nav-stack small gap-2 row-gap-0">
                                                                <li class="nav-item">
                                                                    <i class="bi bi-people pe-1"></i> {{ myevent.event.pageIds | length }} {{ label('common.pages') | raw }}
                                                                </li>
                                                                <li class="nav-item">
                                                                    <i class="bi bi-calendar-check pe-1"></i> {{ myevent.event.childIds | length }} {{ label('common.events') | raw }}
                                                                </li>
                                                            </ul>
                                                            {% else %}
                                                            <ul class="nav nav-stack small gap-2 row-gap-0">
                                                                <li class="nav-item">
                                                                    <i class="bi bi-calendar-check"></i> {{ formatDate(myevent.event.startDate, "EEE d MMM yyyy", language) | capitalize }} {{ myevent.event.startHour }}
                                                                </li>
                                                                <li class="nav-item">
                                                                    <i class="bi bi-geo-alt"></i> {{ myevent.event.city }}
                                                                </li>
                                                                {% if myevent.followerCount > 0 and (myevent.showFollowers == true) %}
                                                                <li class="nav-item">
                                                                    <i class="bi bi-people"></i>
                                                                    {% if myevent.followerCount > 1 %}
                                                                    {{ myevent.followerCount }} {{ label('common.interested') | raw }}
                                                                    {% else %}
                                                                    {{ myevent.followerCount }} {{ label('common.interested.sing') | raw }}
                                                                    {% endif %}
                                                                </li>
                                                                {% endif %}
                                                            </ul>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                    <div class="d-flex justify-content-start justify-content-md-end order-3 mt-2 mt-md-0">
                                                        {% if myevent.event.type is empty or myevent.event.type != 'container' %}
                                                        <a class="btn btn-primary-soft btn-sm mb-0 me-2 text-nowrap" href="{{ paths('EVENT_CLONE') }}?oid={{ myevent.event.id }}"><i class="bi bi-copy pe-1"></i>{{ label('common.clone') | raw }}</a>
                                                        {% endif %}
                                                        {% if (daysbetween(myevent.event.startDate, today()) <= 0 or hoursbetween(myevent.event.creation, now()) <= 2) %}
                                                        <a class="btn btn-primary-soft btn-sm mb-0 me-2 text-nowrap" href="{{ paths('EVENT_EDIT') }}?oid={{ myevent.event.id }}" target="_blank"><i class="bi bi-pencil pe-1"></i>{{ label('common.edit') | raw }}</a>
                                                        {% endif %}
                                                        <button class="btn btn-danger-soft btn-sm mb-0 me-2 event-remove text-nowrap" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-event-id="{{ myevent.event.id }}"><i class="bi bi-trash pe-1"></i>{{ label('common.remove') | raw }}</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="row m-t-sm">
                                <div class="col-xs-12 text-center">
                                    <div id="loading" class="spinner">
                                        <img src="{{ contextPath }}/fe/images/loader.svg" width="64" alt="loading...">
                                    </div>
                                </div>
                            </div>
                            <div class="row m-t-sm">
                                <div class="pager">
                                    {% if resultUrl contains '?' %}
                                    {% if skip > 12 %}
                                    <a href="{{ resultUrl }}&skip={{ skip - limit }}&limit={{ limit }}&tab=myevents" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                    {% if loadmore %}
                                    <a href="{{ resultUrl }}&skip={{ skip + limit }}&limit={{ limit }}&tab=myevents" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                    {% endif %}
                                    {% else %}
                                    {% if skip == 12 %}
                                    <a href="{{ resultUrl }}&tab=myevents" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                    {% endif %}
                                    {% if loadmore %}
                                    <a href="{{ resultUrl }}&skip={{ skip + limit }}&limit={{ limit }}&tab=myevents" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                    {% endif %}
                                    {% endif %}
                                    {% else %}
                                    {% if skip > 12 %}
                                    <a href="{{ resultUrl }}?skip={{ skip - limit }}&limit={{ limit }}&tab=myevents" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                    {% if loadmore %}
                                    <a href="{{ resultUrl }}?skip={{ skip + limit }}&limit={{ limit }}&tab=myevents" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                    {% endif %}
                                    {% else %}
                                    {% if skip == 12 %}
                                    <a href="{{ resultUrl }}?tab=myevents" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                    {% endif %}
                                    {% if loadmore %}
                                    <a href="{{ resultUrl }}?skip={{ skip + limit }}&limit={{ limit }}&tab=myevents" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                    {% endif %}
                                    {% endif %}                    
                                    {% endif %}
                                </div>                                            
                            </div>
                            {% else %}
                            {% if user.profileType != 'unconfirmed' %}
                            <div class="alert alert-primary w-100 text-center" role="alert">
                                {{ label('account.no.events') | raw }} <a class="fw-bold" href="{{ paths('EVENT_ADD') }}">{{ label('account.first.event') | raw }}</a>
                            </div>
                            {% else %}
                            <div class="alert alert-primary w-100 text-center" role="alert">
                                {{ label('account.first.confirm') | raw }}
                            </div>
                            {% endif %}
                            {% endif %}
                        </div>
                        <div class="tab-pane fade show active" id="followedevents-tab-pane" role="tabpanel" aria-labelledby="followedevents-tab" tabindex="0">
                            <!-- Connections Item -->
                            {% if followEvents is not empty %}
                            <div class="containerEventsFollow">
                                <div class="eventsFollow">
                                    {% for followEvent in followEvents %}
                                        <div class="card border-bottom">
                                            <div class="card-header border-0 px-0">
                                                <div class="d-flex flex-column flex-sm-row align-items-start">
                                                    <div class="d-flex flex-nowrap flex-fill">
                                                        <!-- Avatar -->
                                                        {% set pastEvent = false %}
                                                        {% if daysbetween(followEvent.event.startDate, today()) > 0 %}
                                                            {% if followEvent.event.endDate is not empty %}
                                                                {% if daysbetween(followEvent.event.endDate, today()) > 0 %}
                                                                    {% set pastEvent = true %}
                                                                {% endif %}
                                                            {% else %}
                                                                {% set pastEvent = true %}
                                                            {% endif %}
                                                        {% endif %}
                                                        
                                                        <div class="avatar me-3 mb-3 mb-md-0 {{ pastEvent ? 'opacity-50':'' }}">                                                    
                                                            <a href="{{ paths('EVENT_BASE') }}/{{ followEvent.event.identifier }}" class="eventlink">
                                                                {% if followEvent.event.coverImageId is not empty %}
                                                                <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ followEvent.event.coverImageId }}" alt="{{ followEvent.event.name }}">
                                                                {% else %}
                                                                <img class="avatar-img border border-white border-3" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="{{ followEvent.event.name }}">
                                                                {% endif %}                                                                                                                                                                                                                                                                                                                      
                                                            </a>
                                                        </div>
                                                        <div class="w-100 {{ pastEvent ? 'opacity-50':'' }}">
                                                            <div class="d-sm-flex align-items-start">
                                                                <h6 class="mb-0"><a href="{{ paths('EVENT_BASE') }}/{{ followEvent.event.identifier }}">{{ followEvent.event.name }}</a></h6>
                                                            </div>                                                    
                                                            <ul class="nav nav-stack small gap-2 row-gap-0">
                                                                <li class="nav-item">
                                                                    <i class="bi bi-calendar-check pe-1"></i> {{ formatDate(followEvent.event.startDate, "EEE d MMM yyyy", language) | capitalize }} {{ label('common.hours') | raw }} {{ followEvent.event.startHour }}
                                                                </li>
                                                                <li class="nav-item">
                                                                    <i class="bi bi-geo-alt pe-1"></i> {{ followEvent.event.city }}
                                                                </li>
                                                                {% if followEvent.followerCount > 0 and (followEvent.event.showFollowers == true) %}
                                                                    <li class="nav-item">
                                                                        <i class="bi bi-people pe-1"></i>
                                                                        {% if followEvent.followerCount > 1 %}
                                                                            {{ followEvent.followerCount }} {{ label('common.interested') | raw }}
                                                                        {% else %}
                                                                            {{ followEvent.followerCount }} {{ label('common.interested.sing') | raw }}
                                                                        {% endif %}
                                                                    </li>
                                                                {% endif %}
                                                            </ul>
                                                        </div>
                                                    </div>                                                
                                                    <div class="d-flex justify-content-start justify-content-md-end order-3 mt-2 mt-md-0">                                                    
                                                        <button class="btn btn-danger-soft btn-sm mb-0 me-2 event-add-follow text-nowrap" data-value="active" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-event-id="{{ followEvent.event.id }}"><i class="bi bi-dash-circle-fill pe-1"></i>{{ label('common.do.not.care') | raw }}</button>
                                                    </div>                                                
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="row m-t-sm">
                                <div class="col-xs-12 text-center">
                                    <div id="loadingFollow" class="spinnerFollow">
                                        <img src="{{ contextPath }}/fe/images/loader.svg" width="64" alt="loading...">
                                    </div>
                                </div>
                            </div>
                            <div class="row m-t-sm">
                                <div class="pagerFollow">
                                    {% if resultUrlFollow contains '?' %}
                                    {% if skipFollow > 12 %}
                                    <a href="{{ resultUrlFollow }}&skipFollow={{ skipFollow - limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__prevFollow" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                    {% if loadmoreFollow %}
                                    <a href="{{ resultUrlFollow }}&skipFollow={{ skipFollow + limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__nextFollow" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                    {% endif %}
                                    {% else %}
                                    {% if skipFollow == 12 %}
                                    <a href="{{ resultUrlFollow }}&tab=follow" class="pager__prevFollow" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                    {% endif %}
                                    {% if loadmoreFollow %}
                                    <a href="{{ resultUrlFollow }}&skipFollow={{ skipFollow + limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__nextFollow" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                    {% endif %}
                                    {% endif %}
                                    {% else %}
                                    {% if skipFollow > 12 %}
                                    <a href="{{ resultUrlFollow }}?skipFollow={{ skipFollow - limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__prevFollow" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                    {% if loadmoreFollow %}
                                    <a href="{{ resultUrlFollow }}?skipFollow={{ skipFollow + limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__nextFollow" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                    {% endif %}
                                    {% else %}
                                    {% if skipFollow == 12 %}
                                    <a href="{{ resultUrlFollow }}?tab=follow" class="pager__prevFollow" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                    {% endif %}
                                    {% if loadmoreFollow %}
                                    <a href="{{ resultUrlFollow }}?skipFollow={{ skipFollow + limitFollow }}&limitFollow={{ limitFollow }}&tab=follow" class="pager__nextFollow" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                    {% endif %}
                                    {% endif %}                    
                                    {% endif %}
                                </div>                                            
                            </div>
                            {% else %}
                            <div class="alert alert-primary w-100 text-center" role="alert">
                                {{ label('account.no.page.follow') | raw }}
                            </div>
                            {% endif %}
                        </div>
                        <div class="tab-pane fade" id="followedeventspast-tab-pane" role="tabpanel" aria-labelledby="followedeventspast-tab" tabindex="0">
                            <!-- Connections Item -->
                            {% if followEventsPast is not empty %}
                            <div class="containerEventsFollowPast">
                                <div class="eventsFollowPast">
                                    {% for followEventPast in followEventsPast %}
                                    <div class="card border-bottom">
                                        <div class="card-header border-0 px-0">
                                            <div class="d-flex flex-column flex-sm-row align-items-start">
                                                <div class="d-flex flex-nowrap flex-fill">
                                                    <!-- Avatar -->
                                                    {% if daysbetween(followEventPast.event.startDate, today()) > 0 %}
                                                        {% if followEventPast.event.endDate is not empty %}
                                                            {% if daysbetween(followEventPast.event.endDate, today()) > 0 %}
                                                                {% set pastEvent = true %}
                                                            {% endif %}
                                                        {% else %}
                                                            {% set pastEvent = true %}
                                                        {% endif %}
                                                    {% endif %}

                                                    <div class="avatar me-3 mb-3 mb-md-0 {{ pastEvent ? 'opacity-50':'' }}">                                                    
                                                        <a href="{{ paths('EVENT_BASE') }}/{{ followEvent.event.identifier }}" class="eventlink">
                                                            {% if followEventPast.event.coverImageId is not empty %}
                                                            <img class="avatar-img border border-white border-3" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ followEventPast.event.coverImageId }}" alt="{{ followEventPast.event.name }}">
                                                            {% else %}
                                                            <img class="avatar-img border border-white border-3" src="{{ contextPath }}/fe/images/bg/placeholder-event.jpg" alt="{{ followEventPast.event.name }}">
                                                            {% endif %}                                                                                                                                                                                                                                                                                                                      
                                                        </a>
                                                    </div>
                                                    <div class="w-100 {{ pastEvent ? 'opacity-50':'' }}">
                                                        <div class="d-sm-flex align-items-start">
                                                            <h6 class="mb-0"><a href="{{ paths('EVENT_BASE') }}/{{ followEventPast.event.identifier }}">{{ followEventPast.event.name }}</a></h6>
                                                        </div>                                                    
                                                        <ul class="nav nav-stack small gap-2 row-gap-0">
                                                            <li class="nav-item">
                                                                <i class="bi bi-calendar-check"></i> {{ formatDate(followEventPast.event.startDate, "EEE d MMM yyyy", language) | capitalize }} {{ followEventPast.event.startHour }}
                                                            </li>
                                                            <li class="nav-item">
                                                                <i class="bi bi-geo-alt"></i> {{ followEventPast.event.city }}
                                                            </li>
                                                            {% if followEventPast.followerCount > 0 and (followEventPast.event.showFollowers == true) %}
                                                                <li class="nav-item">
                                                                    <i class="bi bi-people"></i>
                                                                    {% if followEventPast.followerCount > 1 %}
                                                                        {{ followEventPast.followerCount }} {{ label('common.interested') | raw }}
                                                                    {% else %}
                                                                        {{ followEventPast.followerCount }} {{ label('common.interested.sing') | raw }}
                                                                    {% endif %}
                                                                </li>
                                                            {% endif %}
                                                        </ul>
                                                    </div>
                                                </div>                                            
                                                <div class="d-flex justify-content-start justify-content-md-end order-3 mt-2 mt-md-0">                                                    
                                                    <button class="btn btn-danger-soft btn-sm mb-0 me-2 event-add-follow text-nowrap" data-value="active" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-event-id="{{ followEventPast.event.id }}"><i class="bi bi-dash-circle-fill pe-1"></i>{{ label('common.do.not.care') | raw }}</button>
                                                </div>                                                                                                
                                            </div>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="row m-t-sm">
                                <div class="col-xs-12 text-center">
                                    <div id="loadingFollowPast" class="spinnerFollowPast">
                                        <img src="{{ contextPath }}/fe/images/loader.svg" width="64" alt="loading...">
                                    </div>
                                </div>
                            </div>
                            <div class="row m-t-sm">
                                <div class="pagerFollowPast">
                                    {% if resultUrlFollowPast contains '?' %}
                                    {% if skipFollowPast > 12 %}
                                    <a href="{{ resultUrlFollowPast }}&skipFollowPast={{ skipFollowPast - limitFollowPast }}&limitFollowPast={{ limitFollowPast }}&tab=followPast" class="pager__prevFollowPast" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                    {% if loadmoreFollowPast %}
                                    <a href="{{ resultUrlFollowPast }}&skipFollowPast={{ skipFollowPast + limitFollowPast }}&limitFollowPast={{ limitFollowPast }}&tab=followPast" class="pager__nextFollowPast" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                    {% endif %}
                                    {% else %}
                                    {% if skipFollowPast == 12 %}
                                    <a href="{{ resultUrlFollowPast }}&tab=followPast" class="pager__prevFollowPast" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                    {% endif %}
                                    {% if loadmoreFollowPast %}
                                    <a href="{{ resultUrlFollowPast }}&skipFollowPast={{ skipFollowPast + limitFollowPast }}&limitFollowPast={{ limitFollowPast }}&tab=followPast" class="pager__nextFollowPast" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                    {% endif %}
                                    {% endif %}
                                    {% else %}
                                    {% if skipFollowPast > 12 %}
                                    <a href="{{ resultUrlFollowPast }}?skipFollowPast={{ skipFollowPast - limitFollowPast }}&limitFollowPast={{ limitFollowPast }}&tab=followPast" class="pager__prevFollowPast" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                    {% if loadmoreFollowPast %}
                                    <a href="{{ resultUrlFollowPast }}?skipFollowPast={{ skipFollowPast + limitFollowPast }}&limitFollowPast={{ limitFollow }}&tab=followPast" class="pager__nextFollowPast" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                    {% endif %}
                                    {% else %}
                                    {% if skipFollowPast == 12 %}
                                    <a href="{{ resultUrlFollowPast }}?tab=followPast" class="pager__prevFollowPast" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                    {% endif %}
                                    {% if loadmoreFollowPast %}
                                    <a href="{{ resultUrlFollowPast }}?skipFollowPast={{ skipFollowPast + limitFollowPast }}&limitFollowPast={{ limitFollowPast }}&tab=followPast" class="pager__nextFollowPast" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                    {% endif %}
                                    {% endif %}                    
                                    {% endif %}
                                </div>                                            
                            </div>
                            {% else %}
                            <div class="alert alert-primary w-100 text-center" role="alert">
                                {{ label('account.no.page.follow') | raw }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <!-- Events list END -->
                </div>
                <!-- Card body END -->
            </div>
            <!-- Events END -->

        </div>
    </div> <!-- Row END -->
</div>
<!-- Container END -->

{% endblock %}

{% block pagescripts %}
<script src="{{ contextPath }}/fe/vendor/choices.js/public/assets/scripts/choices.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/slim/slim.kickstart.min.js"></script>
<script src="{{ contextPath }}/fe/js/pages/account-calendar.js?{{ buildNumber }}"></script>
{% endblock %}
