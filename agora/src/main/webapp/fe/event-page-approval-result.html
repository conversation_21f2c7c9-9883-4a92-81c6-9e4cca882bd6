{% extends "fe/include/base.html" %}

{% block title %}{{ label('page.approval.result.title') | raw }} | Agorapp{% endblock %}

{% block content %}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-6 col-md-8">
            <div class="card shadow-sm mt-5">
                <div class="card-body text-center p-5">
                    {% if action == 'accept' %}
                        <!-- Success - Approved -->
                        <div class="mb-4">
                            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                        </div>
                        <h2 class="h3 text-success mb-3">{{ label('page.approval.result.approved.title') | raw }}</h2>
                        <p class="mb-4">{{ label('page.approval.result.approved.message') | raw }}</p>
                    {% else %}
                        <!-- Declined -->
                        <div class="mb-4">
                            <i class="fas fa-times-circle text-danger" style="font-size: 4rem;"></i>
                        </div>
                        <h2 class="h3 text-danger mb-3">{{ label('page.approval.result.declined.title') | raw }}</h2>
                        <p class="mb-4">{{ label('page.approval.result.declined.message') | raw }}</p>
                    {% endif %}

                    <!-- Event and Page Info -->
                    {% if event is not empty and page is not empty %}
                    <div class="bg-light rounded p-3 mb-4">
                        <div class="text-start">
                            <div class="mb-3">
                                <small>{{ label('common.event') | raw }}:</small>
                                <div class="fw-bold">{{ event.name }}</div>
                            </div>
                            <div class="mb-3">
                                <small>{{ label('common.page') | raw }}:</small>
                                <div class="fw-bold">{{ page.name }}</div>
                            </div>
                            <div>
                                <small>{{ label('common.status') | raw }}:</small>
                                {% if action == 'accept' %}
                                <div class="text-success fw-bold">{{ label('common.approved') | raw }}</div>
                                {% else %}
                                <div class="text-danger fw-bold">{{ label('common.declined') | raw }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Action Button -->
                    <div class="d-grid">
                        <a href="{{ paths('HOME') }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-home me-2"></i>
                            {{ label('page.approval.result.back.home') | raw }}
                        </a>
                    </div>
                    
                    <!-- Additional Info -->
                    <div class="mt-4">
                        <small>{{ label('page.approval.result.footer') | raw }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
