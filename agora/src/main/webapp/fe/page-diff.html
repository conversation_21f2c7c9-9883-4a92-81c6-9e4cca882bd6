{% extends "fe/include/base.html" %}

{% block title %}{{ label('page.diff.title.meta') | raw }} | Agorapp{% endblock %}

{% block pagecss %}
<link href="{{ contextPath }}/fe/css/page-diff.css" rel="stylesheet" type="text/css" media="all">
{% endblock %}

{% block content %}
<a id="pageMergeUri" style="display: none" href="{{ paths('PAGE_DIFF_MERGE') }}" rel="nofollow"></a>
<a id="firstPageId" style="display: none" href="{{ firstPage.id }}" rel="nofollow"></a>
<a id="backUri" style="display: none" href="{{ paths('PAGE_BASE') }}/" rel="nofollow"></a>
<a id="firstPageIdentifier" style="display: none" href="{{ firstPage.identifier }}" rel="nofollow"></a>
<a id="secondPageId" style="display: none" href="{{ secondPage.id }}" rel="nofollow"></a>

<div class="container my-4">
    <div class="row p-0 diff-container">
        <div class="col-12 p-0">
            <table class="table">
                <tr>
                    <td colspan="4" class="text-center h4">{{ label('common.profile.photo') | raw }}</td>
                </tr>
                <tr field="profileImageId">
                    <td class="col-5 text-center">
                        {% if firstPage.profileImageId is not empty %}
                        <img width="250" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ firstPage.profileImageId }}"
                             alt=""/>
                        {% endif %}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-1" index="1">
                    </td>
                    <td class="col-5 text-center">
                        {% if secondPage.profileImageId is not empty %}
                        <img width="250" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ secondPage.profileImageId }}"
                             alt=""/>
                        {% endif %}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-1" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">{{ label('common.cover.photo') | raw }}</td>
                </tr>
                <tr field="coverImageId">
                    <td class="col-5 text-center">
                        {% if firstPage.coverImageId is not empty %}
                        <img width="250" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ firstPage.coverImageId }}" alt=""/>
                        {% endif %}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-2" index="1">
                    </td>
                    <td class="col-5 text-center">
                        {% if secondPage.coverImageId is not empty %}
                        <img width="250" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ secondPage.coverImageId }}" alt=""/>
                        {% endif %}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-2" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">{{ label('page.type') | raw }}</td>
                </tr>
                <tr field="pageType">
                    <td class="col-5">
                        {{ decode('area', firstPage.pageType | default('person') )}}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-3" index="1">
                    </td>
                    <td class="col-5">
                        {{ decode('area', secondPage.pageType | default('person') )}}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-3" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">Tags</td>
                </tr>
                <tr field="tags">
                    <td class="col-5">
                        {% for tag in firstPage.tags %}
                        {{ tag | lower }}{% if not loop.last %}, {% endif %}
                        {% endfor %}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-4" index="1">
                    </td>
                    <td class="col-5">
                        {% for tag in secondPage.tags %}
                        {{ tag | lower }}{% if not loop.last %}, {% endif %}
                        {% endfor %}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-4" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">{{ label('common.description') | raw }}</td>
                </tr>
                <tr field="description">
                    <td class="col-5">
                        {{ firstPage.description | striphtml }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-5" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.description | striphtml }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-5" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">{{ label('common.short.description') | raw }}</td>
                </tr>
                <tr field="shortDescription">
                    <td class="col-5">
                        {{ firstPage.shortDescription | striphtml }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-6" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.shortDescription | striphtml }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-6" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">{{ label('common.fulladdress') | raw }}</td>
                </tr>
                <tr field="fulladdress">
                    <td class="col-5">
                        {{ firstPage.fulladdress }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-7" index="1" onclick="manageFullAddressClick(this);">
                    </td>
                    <td class="col-5">
                        {{ secondPage.fulladdress }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-7" index="2" onclick="manageFullAddressClick(this);">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">{{ label('common.country') | raw }}</td>
                </tr>
                <tr field="countryCode">
                    <td class="col-5">
                        {{ decode('country', firstPage.countryCode )}}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-8" index="1">
                    </td>
                    <td class="col-5">
                        {{ decode('country', secondPage.countryCode )}}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-8" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">{{ label('common.address') | raw }}</td>
                </tr>
                <tr field="address">
                    <td class="col-5">
                        {{ firstPage.address }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-9" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.address }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-9" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">{{ label('common.city') | raw }}</td>
                </tr>
                <tr field="city">
                    <td class="col-5">
                        {{ firstPage.city }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-10" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.city }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-10" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">{{ label('common.postalcode') | raw }}</td>
                </tr>
                <tr field="postalCode">
                    <td class="col-5">
                        {{ firstPage.postalCode }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-11" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.postalCode }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-11" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">{{ label('common.province') | raw }}</td>
                </tr>
                <tr field="provinceCode">
                    <td class="col-5">
                        {{ decode('province', firstPage.provinceCode )}}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-12" index="1">
                    </td>
                    <td class="col-5">
                        {{ decode('province', secondPage.provinceCode )}}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-12" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">{{ label('common.extra.city') | raw }}</td>
                </tr>
                <tr field="extraAddress">
                    <td class="col-5">
                        {{ firstPage.extraAddress }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-13" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.extraAddress }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-13" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">{{ label('page.show.follower') | raw }}</td>
                </tr>
                <tr field="showFollowers">
                    <td class="col-5">
                        {{ firstPage.showFollowers == true ? 'si' : 'no' }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-14" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.showFollowers == true ? 'si' : 'no' }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-14" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">{{ label('common.website') | raw }}</td>
                </tr>
                <tr field="websiteUrl">
                    <td class="col-5">
                        {{ firstPage.websiteUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-15" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.websiteUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-15" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">Facebook</td>
                </tr>
                <tr field="facebookUrl">
                    <td class="col-5">
                        {{ firstPage.facebookUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-16" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.facebookUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-16" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">Twitter</td>
                </tr>
                <tr field="twitterUrl">
                    <td class="col-5">
                        {{ firstPage.twitterUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-17" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.twitterUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-17" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">Instagram</td>
                </tr>
                <tr field="instagramUrl">
                    <td class="col-5">
                        {{ firstPage.instagramUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-18" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.instagramUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-18" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">Linkedin</td>
                </tr>
                <tr field="linkedinUrl">
                    <td class="col-5">
                        {{ firstPage.linkedinUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-19" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.linkedinUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-19" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">YouTube</td>
                </tr>
                <tr field="youtubeUrl">
                    <td class="col-5">
                        {{ firstPage.youtubeUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-20" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.youtubeUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-20" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">Reddit</td>
                </tr>
                <tr field="redditUrl">
                    <td class="col-5">
                        {{ firstPage.redditUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-21" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.redditUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-21" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">Medium</td>
                </tr>
                <tr field="mediumUrl">
                    <td class="col-5">
                        {{ firstPage.mediumUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-22" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.mediumUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-22" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">TikTok</td>
                </tr>
                <tr field="tiktok">
                    <td class="col-5">
                        {{ firstPage.tiktok }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-23" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.tiktok }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-23" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">Spotify</td>
                </tr>
                <tr field="spotifiyUrl">
                    <td class="col-5">
                        {{ firstPage.spotifiyUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-24" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.spotifiyUrl }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-24" index="2">
                    </td>
                </tr>

                <tr>
                    <td colspan="4" class="text-center h4">{{ label('common.permissions') | raw }}</td>
                </tr>
                <tr field="pageTagging">
                    <td class="col-5">
                        {{ firstPage.pageTagging == 'everyone' ? label('common.everyone') | raw :
                        label('common.only.owner') | raw }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-25" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.pageTagging == 'everyone' ? label('common.everyone') | raw :
                        label('common.only.owner') | raw }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-25" index="2">
                    </td>
                </tr>

                {% if user.profileType == 'system' or user.profileType == 'admin' %}
                <tr>
                    <td colspan="4" class="text-center h4">Identificativo pagina</td>
                </tr>
                <tr field="identifier">
                    <td class="col-5">
                        {{ firstPage.identifier }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-26" index="1">
                    </td>
                    <td class="col-5">
                        {{ secondPage.identifier }}
                    </td>
                    <td class="col-1 text-center align-middle">
                        <input type="radio" class="form-check-input fake-check" name="diff-row-26" index="2">
                    </td>
                </tr>
                {% endif %}
            </table>

            <div class="d-flex justify-content-center mt-4">
                <button class="mb-4 btn-lg btn btn-primary" onclick="submitMerge();">UNISCI PAGINE</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block pagescripts %}
<script src="{{ contextPath }}/fe/js/pages/page-diff.js?{{ buildNumber }}"></script>
<script src="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/flatpickr/dist/l10n/it.js"></script>
{% endblock %}