{% extends "fe/include/base.html" %}

{% set metaDescription = label('mediakit.description.meta') | raw %}

{% block title %}{{ label('mediakit.title.meta') | raw }} | Agorapp{% endblock %}

{% block canonical %}
    <meta name="robots" content="index, follow">
    <meta name="description" content="{{ metaDescription }}">
    <link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block socialcards %}
    <meta property="og:url"                content="{{ publicUrl }}" />
    <meta property="og:type"               content="website" />
    <meta property="og:title"              content="{{ label('mediakit.title.meta') | raw }} | Agorapp" />
    <meta property="og:description"        content="{{ metaDescription }}" />
    <meta property="og:image"              content="{{ contextPath }}/fe/images/mediakit/cover.png" />
    <meta property="og:image:width"        content="1200" />
    <meta property="og:image:height"       content="630" />
    <meta property="og:image:alt"          content="{{ label('mediakit.title.meta') | raw }} | Agorapp" />
{% endblock %}


{% block pagecss %}
    <link href="{{ contextPath }}/fe/css/drop_uploader.min.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.css">    
{% endblock %}


{% block content %}
    
<a id="dataPagesUri" style="display:none" href="{{ paths('DATA_PAGES') }}?ownerId={{ user.id }}"></a>
<a id="iframeBaseUri" style="display:none" href="{{ baseUrl }}{{ paths('PAGE_IFRAME') }}"></a>

<!-- Container START -->
<div class="container">
    <!-- Main content START -->
    <!-- Help search START -->
    <div class="row align-items-center pt-5 pb-5 pb-lg-3">
        <div class="col-md-8">
            <h3 class="mt-4 text-agora">{{ label('mediakit.title') | raw }}</h3>
        </div>
        <div class="col-md-4 text-end">
            <button class="btn btn-primary btn-lg" onclick="downloadAll()">
                <i class="fas fa-download me-2"></i>{{ label('mediakit.download.all') | raw }}
            </button>
        </div>            
    </div>
    <!-- Help search END -->
    <!-- Article Single START -->
    <div class="row g-4">
        <!-- Card Logo PNG -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 shadow-sm">
                <div class="card-img-top d-flex align-items-center justify-content-center p-4" style="height: 200px; background-color: #f8f9fa;">
                    <img src="{{ contextPath }}/fe/images/mediakit/agorapp-logo.png" alt="Agorapp Logo PNG" class="img-fluid" style="max-height: 150px;">
                </div>
                <div class="card-body text-center">
                    <h5 class="card-title">{{ label('mediakit.logo.png') | raw }}</h5>
                    <p class="card-text text-gray-400">{{ label('mediakit.logo.png.description') | raw }}</p>
                    <a href="{{ contextPath }}/fe/images/mediakit/agorapp-logo.png" download="agorapp-logo.png" class="btn btn-outline-primary">
                        <i class="fas fa-download me-2"></i>{{ label('common.download') | raw }}
                    </a>
                </div>
            </div>
        </div>
        <!-- Card Logo PNG Bianco -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 shadow-sm">
                <div class="card-img-top d-flex align-items-center justify-content-center p-4" style="height: 200px; background-color: #f8f9fa;">
                    <img src="{{ contextPath }}/fe/images/mediakit/agorapp-logo-white.png" alt="Agorapp Logo PNG" class="img-fluid" style="max-height: 150px;">
                </div>
                <div class="card-body text-center">
                    <h5 class="card-title">{{ label('mediakit.logo.png.white') | raw }}</h5>
                    <p class="card-text text-gray-400">{{ label('mediakit.logo.png.white.description') | raw }}</p>
                    <a href="{{ contextPath }}/fe/images/mediakit/agorapp-logo-white.png" download="agorapp-logo-white.png" class="btn btn-outline-primary">
                        <i class="fas fa-download me-2"></i>{{ label('common.download') | raw }}
                    </a>
                </div>
            </div>
        </div>
        <!-- Card Logo SVG -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 shadow-sm">
                <div class="card-img-top d-flex align-items-center justify-content-center p-4" style="height: 200px; background-color: #f8f9fa;">
                    <img src="{{ contextPath }}/fe/images/mediakit/agorapp-logo.svg" alt="Agorapp Logo SVG" class="img-fluid" style="max-height: 150px;">
                </div>
                <div class="card-body text-center">
                    <h5 class="card-title">{{ label('mediakit.logo.svg') | raw }}</h5>
                    <p class="card-text text-gray-400">{{ label('mediakit.logo.svg.description') | raw }}</p>
                    <a href="{{ contextPath }}/fe/images/mediakit/agorapp-logo.svg" download="agorapp-logo.svg" class="btn btn-outline-primary">
                        <i class="fas fa-download me-2"></i>{{ label('common.download') | raw }}
                    </a>
                </div>
            </div>
        </div>
        <!-- Card Profilo B -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 shadow-sm">
                <div class="card-img-top d-flex align-items-center justify-content-center p-4" style="height: 200px; background-color: #f8f9fa;">
                    <img src="{{ contextPath }}/fe/images/mediakit/agorapp-profilo-B.jpg" alt="Agorapp Profilo B" class="img-fluid" style="max-height: 150px;">
                </div>
                <div class="card-body text-center">
                    <h5 class="card-title">{{ label('mediakit.profile.b') | raw }}</h5>
                    <p class="card-text text-gray-400">{{ label('mediakit.profile.b.description') | raw }}</p>
                    <a href="{{ contextPath }}/fe/images/mediakit/agorapp-profilo-B.jpg" download="agorapp-profilo-B.jpg" class="btn btn-outline-primary">
                        <i class="fas fa-download me-2"></i>{{ label('common.download') | raw }}
                    </a>
                </div>
            </div>
        </div>
        <!-- Card Profilo C -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 shadow-sm">
                <div class="card-img-top d-flex align-items-center justify-content-center p-4" style="height: 200px; background-color: #f8f9fa;">
                    <img src="{{ contextPath }}/fe/images/mediakit/agorapp-profilo-C.jpg" alt="Agorapp Profilo C" class="img-fluid" style="max-height: 150px;">
                </div>
                <div class="card-body text-center">
                    <h5 class="card-title">{{ label('mediakit.profile.c') | raw }}</h5>
                    <p class="card-text text-gray-400">{{ label('mediakit.profile.c.description') | raw }}</p>
                    <a href="{{ contextPath }}/fe/images/mediakit/agorapp-profilo-C.jpg" download="agorapp-profilo-C.jpg" class="btn btn-outline-primary">
                        <i class="fas fa-download me-2"></i>{{ label('common.download') | raw }}
                    </a>
                </div>
            </div>
        </div>
        <!-- Card Profilo D -->
        <div class="col-lg-4 col-md-6">
            <div class="card h-100 shadow-sm">
                <div class="card-img-top d-flex align-items-center justify-content-center p-4" style="height: 200px; background-color: #f8f9fa;">
                    <img src="{{ contextPath }}/fe/images/mediakit/agorapp-profilo-D.jpg" alt="Agorapp Profilo D" class="img-fluid" style="max-height: 150px;">
                </div>
                <div class="card-body text-center">
                    <h5 class="card-title">{{ label('mediakit.profile.d') | raw }}</h5>
                    <p class="card-text text-gray-400">{{ label('mediakit.profile.d.description') | raw }}</p>
                    <a href="{{ contextPath }}/fe/images/mediakit/agorapp-profilo-D.jpg" download="agorapp-profilo-D.jpg" class="btn btn-outline-primary">
                        <i class="fas fa-download me-2"></i>{{ label('common.download') | raw }}
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- Article Single END -->
    <!-- Help search START -->
    <div class="row align-items-center pt-5 pb-5 pb-lg-3">
        <div class="col-md-12">
            <h3 class="mt-4 text-agora">{{ label('mediakit.create.iframe') | raw }}</h3>
        </div>        
    </div>
    <!-- Help search END -->
    
    <!-- Article Single START -->
    <div class="row g-4">
        <!-- Card Logo PNG -->
        <div class="col-md-8 col-lg-6 mx-auto">
            <select id="pageId" class="form-control" multiple="multiple" data-tags="true">
            </select>

            <div id="iframe-code-card" class="card bg-light mt-3" style="display: none;">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Iframe Code</h6>
                    <button id="copy-btn" class="btn btn-sm btn-outline-primary" title="Copy to clipboard">
                        <i class="bi bi-clipboard"></i> Copy
                    </button>
                </div>
                <div class="card-body">
                    <code id="iframe-container"
                          class="d-block p-3 bg-white rounded border"
                          style="white-space: pre-wrap; word-break: break-all;">
                    </code>
                </div>
            </div>
        </div>
    </div>    
    </div> 
    <!-- Container END -->

    <div class="d-lg-none">
        {% include "fe/include/snippets/sidenav-left.html" %}
    </div>
{% endblock %}

{% block pagescripts %}
    <script src="{{ contextPath }}/fe/js/pages/page-search-iframe.js?{{ buildNumber }}"></script>
{% endblock %}
