{% extends "fe/include/base.html" %}

{% set metaDescription = label('about.description.meta') | raw %}

{% block title %}{{ label('about.title.meta') | raw }} | Agorapp{% endblock %}

{% block canonical %}
    <meta name="robots" content="index, follow">
    <meta name="description" content="{{ metaDescription }}">
    <link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block socialcards %}
    <meta property="og:url"                content="{{ publicUrl }}" />
    <meta property="og:type"               content="website" />
    <meta property="og:title"              content="{{ label('about.title.meta') | raw }} | Agorapp" />
    <meta property="og:description"        content="{{ metaDescription }}" />
    <meta property="og:image"              content="{{ contextPath }}/fe/images/about/cover.png" />
    <meta property="og:image:width"        content="1200" />
    <meta property="og:image:height"       content="630" />
    <meta property="og:image:alt"          content="{{ label('about.title.meta') | raw }} | Agorapp" />
{% endblock %}


{% block pagecss %}
    <link href="{{ contextPath }}/fe/css/drop_uploader.min.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.css">    
{% endblock %}


{% block content %}

<a id="dataPagesUri" style="display: none" href="{{ paths('DATA_PAGES') }}?ownerId={{ user.id }}"></a>
<a id="iframeBaseUri" style="display: none" href="{{ paths('PAGE_IFRAME') }}"></a>

    <!-- Container START -->
    <div class="container-fluid h-100">
        <div class="mt-4 row h-100 justify-content-center align-items-center">
            <div class="col-md-8 col-lg-6">
                <!-- Search Section -->
                <div class="text-center mb-4">
                    <h3 class="mb-3">Generate Page Iframe</h3>
                    <select id="pageId" class="form-control" multiple="multiple" data-tags="true">
                    </select>
                </div>
                
                <!-- Iframe Code Card -->
                <div id="iframe-code-card" class="card bg-light" style="display: none;">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Iframe Code</h6>
                        <button id="copy-btn" class="btn btn-sm btn-outline-primary" title="Copy to clipboard">
                            <i class="bi bi-clipboard"></i> Copy
                        </button>
                    </div>
                    <div class="card-body">
                        <code id="iframe-container" class="d-block p-3 bg-white rounded border" style="white-space: pre-wrap; word-break: break-all;">
                        </code>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Container END -->
    <div class="d-lg-none">
        {% include "fe/include/snippets/sidenav-left.html" %}
    </div>
{% endblock %}       

{% block pagescripts %}   
    <script src="{{ contextPath }}/fe/js/pages/page-search-iframe.js?{{ buildNumber }}"></script>
{% endblock %}