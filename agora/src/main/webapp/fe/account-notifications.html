{% extends "fe/include/base.html" %}

{% set activePage = 'NOTIFICATIONS' %}

{% block title %}{{ label('account.notifications.title.meta') | raw }} | Agorapp{% endblock %}

{% block pagecss %}
<link href="{{ contextPath }}/fe/vendor/slim/slim.min.css" rel="stylesheet" type="text/css" media="all">
<link href="{{ contextPath }}/fe/vendor/choices.js/public/assets/styles/choices.min.css" rel="stylesheet" type="text/css" media="all">
{% endblock %}

{% block content %}
<a id="dataCitiesUri" style="display: none" href="{{ paths('DATA_CITIES') }}?name="></a>

<a id="notificationActiveUri" style="display: none" href="{{ paths('NOTIFICATION_ACTIVE') }}" rel="nofollow"></a>
<a id="notificationRemoveUri" style="display: none" href="{{ paths('NOTIFICATION_REMOVE') }}" rel="nofollow"></a>
<div id="notification.edit.activation" style="display: none">{{ label('notification.edit.activation') | raw }}</div>
<div id="notification.delete.notifications" style="display: none">{{ label('notification.delete.notifications') | raw }}</div>

<!-- Container START -->
<div class="container">
    <div class="row g-0">

        <!-- Sidenav START -->
        <div class="col-lg-3">
            {% include "fe/include/snippets/sidenav-left.html" %}
        </div>
        <!-- End Col -->

        <!-- Main content START -->
        <div class="col-lg-6 vstack gap-4">
            <!-- Account settings START -->
            <div class="card mb-4 border-lg-lr border-lg-b">

                <!-- Card header START -->
                <div class="card-header border-0 pb-0">
                    {% if user.profileType == 'unconfirmed' %}
                    <!-- Alert -->
                    <div class="alert alert-danger text-center card-alert" role="alert">
                        {{ label('account.not.confirm') | raw }} <a class="alert-link" id="account-confirm-send" href="{{ paths('ACCOUNT_CONFIRM_SEND') }}">{{ label('account.send.link') | raw }}<i class="bi-chevron-right small ms-1"></i></a>
                    </div>
                    <!-- End Alert -->
                    {% endif %}                    
                
                
                    <div class="row align-items-center">
                        <div class="col-sm-8">
                            <h1 class="h5 card-title mb-1">{{ label('common.notifications') | raw }}</h1>
                            <p class="mb-2">{{ label('account.notifications.payoff') | raw }}</p>
                        </div>
                        {% if user.profileType != 'unconfirmed' %}
                        <div class="col-sm-4 text-sm-end text-center">
                            <a class="btn btn-primary mt-2 mt-sm-0 w-100 w-md-auto" href="{{ paths('NOTIFICATION_ADD') }}">
                                <i class="fa-solid fa-plus pe-1"></i> {{ label('common.add.notifications') | raw }} ({{ label('common.email') | raw }})
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Card header END -->
                <div class="card-body" id="myTabContent">

                    <ul class="nav nav-tabs border-0 justify-content-center" id="myTab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link btn active me-2" id="notifications-tab" data-bs-toggle="tab" data-bs-target="#notifications-tab-pane" type="button" role="tab" aria-controls="notifications-tab-pane" aria-selected="false">{{ label('account.notifications.your') | raw }} ({{ newNotificationCount }})</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link btn" id="customernotification-tab" data-bs-toggle="tab" data-bs-target="#customernotification-tab-pane" type="button" role="tab" aria-controls="customernotification-tab-pane" aria-selected="true">{{ label('commons.maintenance') | raw }} (email)</button>
                        </li>
                    </ul>
                    <div class="tab-content" >
                        <div class="tab-pane fade show active" id="notifications-tab-pane" role="tabpanel" aria-labelledby="notifications-tab" tabindex="0">
                            <!-- Connections Item -->
                            <div class="containerNotifications">
                                {% if entityNotificationEntryList is not empty %}
                                    <div class="notification-element">
                                        {% for entityNotificationEntry in entityNotificationEntryList %}
                                            <div class="card border-bottom">
                                                <div class="card-header border-0 px-0">
                                                    <div class="d-flex flex-column flex-sm-row align-items-start">
                                                        <div class="d-flex flex-nowrap flex-fill">
                                                            <div class="avatar me-3 mb-3 mb-md-0">
                                                                {% set pageEvent = get('page', entityNotificationEntry.entityNotification.validPageIds[0]) %}
                                                                {% if pageEvent.profileImageId is not empty %}
                                                                <a href="{{ paths('PAGE_BASE') }}/{{ pageEvent.identifier }}" class="{{ entityNotificationEntry.entityNotification.isRead ? '' : 'notification-read' }}" data-notification="{{ entityNotificationEntry.entityNotification.id }}">
                                                                    <img class="avatar-img rounded-circle" src="{{ paths('IMAGE_SYSTEM') }}?oid={{ pageEvent.profileImageId }}" alt="">
                                                                </a>
                                                                {% else %}
                                                                <img class="avatar-img rounded-circle" src="{{ contextPath }}/fe/images/avatar/placeholder.jpg" alt="{{ mypage.name }}"> 
                                                                {% endif %}                                                                                                         
                                                            </div>
                                                            <!-- Info -->
                                                            <div class="w-100">
                                                                <div class="d-flex flex-column">
                                                                    {% set messageNotification = (label('common.has') | raw) + ' ' %}
                                                                    {% if (entityNotificationEntry.event.pageIds | length) > 1 %}
                                                                    {% set messageNotification = (label('common.other.pages') | raw) %}
                                                                    {% endif %}
                                                                    {% if entityNotificationEntry.entityNotification.type == 'published' %}
                                                                    {% set messageNotification = messageNotification + ' ' + (label('common.published.event') | raw) %}
                                                                    {% elseif entityNotificationEntry.entityNotification.type == 'changed' %}
                                                                    {% set messageNotification = messageNotification + ' ' + (label('common.modified.event') | raw) %}
                                                                    {% elseif entityNotificationEntry.entityNotification.type == 'deleted' %}
                                                                    {% set messageNotification = messageNotification + ' ' + (label('common.deleted.event') | raw) %}
                                                                    {% endif%}
                                                                    <p class="small mb-1">
                                                                        <b>
                                                                            <a href="{{ paths('PAGE_BASE') }}/{{ pageEvent.identifier }}" class="{{ entityNotificationEntry.entityNotification.isRead ? '' : 'notification-read' }}" data-notification="{{ entityNotificationEntry.entityNotification.id }}">
                                                                                {{ pageEvent.name }}
                                                                            </a>
                                                                        </b>
                                                                        {% if entityNotificationEntry.entityNotification.type == 'deleted' %}
                                                                        {{ messageNotification }}{{ entityNotificationEntry.event.name }}
                                                                        {% else %}
                                                                        {{ messageNotification }}
                                                                        <a href="{{ paths('EVENT_BASE') }}/{{ entityNotificationEntry.event.identifier }}" class="{{ entityNotificationEntry.entityNotification.isRead ? '' : 'notification-read' }}" data-notification="{{ entityNotificationEntry.entityNotification.id }}">
                                                                            {{ entityNotificationEntry.event.name }}
                                                                        </a>
                                                                        {% endif %}
                                                                    </p>

                                                                    <p class="small text-nowrap mb-0">{{ entityNotificationEntry.entityNotification.date | date('d MMMM')| capitalize }} {{ label('common.at.time') | raw }} {{ entityNotificationEntry.entityNotification.date | date('HH:mm') }}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <div class="alert alert-primary w-100 text-center" role="alert">
                                        {{ label('account.notifications.dont.have') | raw }}
                                    </div>
                                {% endif %}
                                <!-- Card body END -->
                            </div>
                            {% if entityNotificationEntryList is not empty %}
                            <div class="row m-t-sm">
                                <div class="col-xs-12 text-center">
                                    <div id="loading" class="spinner">
                                        <img src="{{ contextPath }}/fe/images/loader.svg" width="64" alt="loading...">
                                    </div>
                                </div>
                            </div> 
                        </div> 
                        {% endif %}
                        <div class="row m-t-sm">
                            <div class="pager">
                                {% if resultUrl contains '?' %}
                                {% if skip > 12 %}
                                <a href="{{ resultUrl }}&skip={{ skip - limit }}&limit={{ limit }}" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                {% if loadmore %}
                                <a href="{{ resultUrl }}&skip={{ skip + limit }}&limit={{ limit }}" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                {% endif %}
                                {% else %}
                                {% if skip == 12 %}
                                <a href="{{ resultUrl }}" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                {% endif %}
                                {% if loadmore %}
                                <a href="{{ resultUrl }}&skip={{ skip + limit }}&limit={{ limit }}" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                {% endif %}
                                {% endif %}
                                {% else %}
                                {% if skip > 12 %}
                                <a href="{{ resultUrl }}?skip={{ skip - limit }}&limit={{ limit }}" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                {% if loadmore %}
                                <a href="{{ resultUrl }}?skip={{ skip + limit }}&limit={{ limit }}" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                {% endif %}
                                {% else %}
                                {% if skip == 12 %}
                                <a href="{{ resultUrl }}" class="pager__prev" hidden>&leftarrow; {{ label('common.prev.article') | raw }}</a>
                                {% endif %}
                                {% if loadmore %}
                                <a href="{{ resultUrl }}?skip={{ skip + limit }}&limit={{ limit }}" class="pager__next" hidden>{{ label('common.next.article') | raw }} &rightarrow;</a>
                                {% endif %}
                                {% endif %}                    
                                {% endif %}
                            </div>                                            
                        </div>                    
                        <div class="tab-pane fade" id="customernotification-tab-pane" role="tabpanel" aria-labelledby="customernotification-tab" tabindex="0">
                            <!-- Connections Item -->
                            {% if customerNotificationList is not empty %}
                            {% for customerNotification in customerNotificationList %}
                            <div class="card border-bottom">
                                <div class="card-header border-0 px-0">
                                    <div class="d-md-flex align-items-center">
                                <!-- Info -->
                                <div class="w-100">
                                    <div class="d-sm-flex align-items-start">
                                        <h6 class="mb-0"><a href="{{ paths('NOTIFICATION_EDIT') }}?oid={{ customerNotification.id }}">{{ customerNotification.city }} </a></h6>
                                    </div>
                                    <ul class="nav nav-stack small gap-0 gap-sm-2">
                                        {% if customerNotification.city is not empty %}
                                        <li class="nav-item">
                                            <i class="bi bi-geo-alt pe-1"></i> {{ customerNotification.city }}
                                        </li>
                                        {% endif %}                                                          
                                        {% if customerNotification.rangeKm > 0 %}                                                            
                                        <li class="nav-item">
                                            {{ label('account.notification.range') | raw }} {{ customerNotification.rangeKm }} {{ customerNotification.rangeType }}
                                        </li>                                                                                                                            
                                        {% endif %}
                                        <li class="nav-item">
                                            {{ label('account.notification.status') | raw }}: {{ customerNotification.emailActive ? label('common.active') | raw : label('common.inactive') | raw }}
                                        </li>
                                    </ul>
                                </div>
                                <div class="ms-md-auto d-flex">
                                    <a class="btn btn-primary-soft btn-sm mb-0 me-2 text-nowrap" href="{{ paths('NOTIFICATION_EDIT') }}?oid={{ customerNotification.id }}"><i class="bi bi-box-arrow-up-right pe-1"></i> {{ label('common.edit') | raw }}</a>
                                    {% if customerNotification.emailActive %}
                                    <button class="btn btn-danger-soft btn-sm mb-0 me-2 notification-active text-nowrap" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-notification-id="{{ customerNotification.id }}" data-active="false"><i class="bi bi-dash-circle-fill pe-1"></i>{{ label('common.deactivate') | raw }}</button>
                                    {% else %}
                                    <button class="btn btn-success-soft btn-sm mb-0 me-2 notification-active text-nowrap" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-notification-id="{{ customerNotification.id }}" data-active="true"><i class="bi bi-plus-circle-fill pe-1"></i>{{ label('common.activate') | raw }}</button>
                                    {% endif %}
                                    <button class="btn btn-danger-soft btn-sm mb-0 me-2 notification-remove text-nowrap" data-user="{{ user is not empty ? 'logged' : 'unlogged' }}" data-notification-id="{{ customerNotification.id }}"><i class="bi bi-trash pe-1"></i>{{ label('common.remove') | raw }}</button>
                                </div>
                            </div>
                                </div>
                            </div>
                            {% endfor %}
                            {% else %}
                            {% if user.profileType != 'unconfirmed' %}
                            <div class="alert alert-primary w-100 text-center" role="alert">
                                {{ label('account.notifications.dont.create') | raw }} <a class="fw-bold" href="{{ paths('NOTIFICATION_ADD') }}">{{ label('account.notifications.add.now') | raw }}</a>
                            </div>
                            {% else %}
                            <div class="alert alert-primary w-100 text-center" role="alert">
                                {{ label('account.notifications.first.confirm') | raw }}
                            </div>
                            {% endif %}
                            {% endif %}
                            <!-- Card body END -->
                        </div>
                    </div>
                </div>
            </div>                    

        </div>
        <!-- Account settings END -->

    </div>
</div> <!-- Row END -->
<!-- Container END -->

{% endblock %}

{% block pagescripts %}
<script src="{{ contextPath }}/fe/vendor/choices.js/public/assets/scripts/choices.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/slim/slim.kickstart.min.js"></script>
<script src="{{ contextPath }}/fe/js/pages/account-notifications.js?{{ buildNumber }}"></script>
{% endblock %}
