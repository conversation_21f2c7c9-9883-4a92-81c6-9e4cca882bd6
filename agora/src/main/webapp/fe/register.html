{% extends "fe/include/base-no-header.html" %}

{% block title %}{{ label('register.title.meta') | raw }} | Agorapp{% endblock %}

{% block canonical %}    
    <meta name="description" content="{{ label('register.description.meta') | raw }}">
    <link rel="canonical" href="{{ publicUrl }}">
{% endblock %}

{% block content %}
 <a id="registerSendUri" style="display: none" href="{{ paths('REGISTER_SEND') }}" rel="nofollow"></a>
 <a id="accountUri" style="display: none" href="{{ paths('ACCOUNT_INFO') }}" rel="nofollow"></a>
 
<!-- Container START -->
<div class="container">
    <div class="row justify-content-center align-items-center vh-100 py-5">
        <!-- Main content START -->
        <div class="col-sm-10 col-md-8 col-lg-7 col-xl-6 col-xxl-5">
            <div class="text-center">
                <a href="{{ paths('HOME') }}"><img class="mb-3" src="{{ contextPath }}/fe/images/logo.svg" width="150" alt="Agorapp"></a>
            </div>
            <!-- Sign up START -->
            <div class="card card-body p-4 p-sm-5 border-lg">
                <div class="text-center">
                    <!-- Title -->
                    <h1 class="mb-2">{{ label('common.register') | raw }}</h1>
                    <span class="d-block">{{ label('register.already.account') | raw }} <a href="{{ paths('ACCESS') }}">{{ label('register.login.here') | raw }}</a></span>
                </div>
                <!-- Form START -->
                <form class="mt-4" id="form-register" method="post" novalidate="novalidate">
                    <div class="row">
                        <div class="col-md-6 mb-3 input-group-lg">
                            <input type="text" class="form-control" name="name" id="name" placeholder="{{ label('common.name') | raw }}" aria-label="{{ label('common.name') | raw }}" required data-cons-subject="first_name">                        
                        </div>
                        <div class="col-md-6 mb-3 input-group-lg">
                            <input type="text" class="form-control" name="lastname" id="lastname" placeholder="{{ label('common.lastname') | raw }}" aria-label="{{ label('common.lastname') | raw }}" required data-cons-subject="last_name">
                        </div>
                    </div>
                    <!-- Email -->
                    <div class="mb-3 input-group-lg">
                        <input type="email" class="form-control" name="email" id="username" placeholder="Email" aria-label="Email" required pattern="[^@\s]+@[^@\s]+\.[a-z]{2,}" data-cons-subject="email">                                                
                    </div>
                    <!-- New password -->
                    <div class="mb-3 position-relative">
                        <!-- Input group -->
                        <div class="input-group input-group-lg">
                            <input class="form-control fakepassword" type="password" name="password" id="psw-input" placeholder="Password" aria-label="Password" required minlength="{{ isLocal ? '3' : '8' }}">                                                        
                            <span class="input-group-text p-0">
                                <i class="fakepasswordicon fa-solid fa-eye-slash cursor-pointer p-2 w-40px"></i>
                            </span>
                        </div>
                        <span class="fakepassword-error"></span>
                        <!-- Pswmeter -->
                        <div id="pswmeter" class="mt-2"></div>
                        <div class="d-flex mt-1">
                            <div id="pswmeter-message" class="rounded"></div>
                            <!-- Password message notification -->
                            <div class="ms-auto">
                                <i class="bi bi-info-circle ps-1" data-bs-container="body" data-bs-toggle="popover" data-bs-placement="top" data-bs-content="{{ label('register.secure.password') | raw }}" data-bs-original-title="" title=""></i>
                            </div>
                        </div>
                    </div>
                    <!-- Confirm password -->
                    <div class="mb-3 input-group-lg">
                        <input class="form-control" type="password" name="password-confirm" id="psw-confirm-input" placeholder="{{ label('common.password.confirm') | raw }}" aria-label="{{ label('common.password.confirm') | raw }}" required minlength="{{ isLocal ? '3' : '8' }}">
                    </div>   
                    <div class="col-12">
                        <div class="form-check">                                    
                            <input class="form-check-input" name="privacy" id="privacy" type="checkbox" required data-cons-preference="general">
                            <label class="form-check-label small text-secondary" for="privacy">
                                {% if (language is empty) or (language == 'it') %}
                                {{ label('common.accept.privacy') | raw }} <a href="https://www.iubenda.com/privacy-policy/33876721" class="pt-link pt-base-dark-color">Privacy Policy</a>
                                {% else %}
                                {{ label('common.accept.privacy') | raw }} <a href="https://www.iubenda.com/privacy-policy/77929162" class="pt-link pt-base-dark-color">Privacy Policy</a>
                                {% endif %}
                            </label>
                        </div>
                    </div>
                    <!-- Button -->
                    <div class="d-grid"><button type="submit" class="btn btn-lg btn-primary">{{ label('common.register') | raw }}</button></div>
                    <!-- Copyright -->
                    <p class="mb-0 mt-3 text-center">{{ label('common.copyright') | raw }}</p>
                </form>
                <!-- Form END -->
            </div>
            <!-- Sign up END -->
        </div>
    </div> <!-- Row END -->
</div>
<!-- Container END -->
 {% endblock %}
{% block pagescripts %}    
    <script src="{{ contextPath }}/fe/js/pages/register.js?{{ buildNumber }}"></script>
{% endblock %}
