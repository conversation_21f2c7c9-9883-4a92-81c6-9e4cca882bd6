{% extends "fe/include/base.html" %}

{% block title %}{{ label('page.add.title.meta') | raw }} | Agorapp{% endblock %}

{% block pagecss %}
<link href="{{ contextPath }}/fe/css/slim.min.css" rel="stylesheet" type="text/css" media="all">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/tiny-slider/dist/tiny-slider.css">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/choices.js/public/assets/styles/choices.min.css">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/glightbox-master/dist/css/glightbox.min.css">    
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.css">
<link href="{{ contextPath }}/fe/vendor/summernote/summernote-lite.min.css" rel="stylesheet">
<link rel="stylesheet" type="text/css" href="{{ contextPath }}/fe/css/description-truncation.css?{{ buildNumber }}">

<style>
    .select2-results__option.select2-results__option--highlighted {
        background-color: white!important;
        color: var(--bs-body-color)!important;
    }
</style>
{% endblock %}

{% block content %}
<a id="pageAddUri" style="display: none" href="{{ paths('PAGE_ADD') }}" rel="nofollow"></a>
<a id="pageAddSaveUri" style="display: none" href="{{ paths('PAGE_ADD_SAVE') }}" rel="nofollow"></a>
<a id="accountInfoUri" style="display: none" href="{{ paths('ACCOUNT_INFO') }}" rel="nofollow"></a>
<a id="accountPagesUri" style="display: none" href="{{ paths('ACCOUNT_PAGES') }}" rel="nofollow"></a>
<a id="dataPagesUri" style="display: none" href="{{ paths('DATA_PAGES') }}?ownerId={{ user.id }}"></a>
<a id="dataPageTagUri" style="display: none" href="{{ paths('DATA_TAG_PAGE') }}"></a>
<a id="dataPageDetailUri" style="display: none" href="{{ paths('PAGE_DETAIL') }}"></a>
<input type="hidden" id="language" value="{{ language }}">
<!-- Container START -->
<div class="container">
    <div class="row">

        <!-- Sidenav START -->
        <div class="col-lg-3">
            {% include "fe/include/snippets/sidenav-left.html" %}
        </div>
        <!-- End Col -->

        <!-- Main content START -->
        <div class="col-lg-6 vstack gap-4">
            <!-- Account settings START -->
            <div class="card border-lg-lr border-lg-b">

                <!-- Card header START -->
                <div class="card-header border-0 pb-0">
                    <h1 class="h5 card-title">{{ label('page.add') | raw }}</h1>
                    <p class="mb-0">{{ label('page.add.public.and.visible') | raw }}</p>
                </div>
                <!-- Card header END -->

                <!-- Card body START -->
                <div class="card-body">
                    <form id="form-page-add" method="post" novalidate="novalidate">
                        <div class="row g-3 mb-3">
                            <!-- Page information -->
                            <div class="d-flex flex-column col-12 page-name-container">
                                <label class="form-label">{{ label('page.add.name') | raw }}</label>
                                <input type="text" class="form-control" placeholder="{{ label('page.add.name') | raw }}" id="name" name="name" value="{{ name }}" required autocomplete="off" style="z-index: 100;">

                                <select id="pageIds" class="form-control" placeholder="{{ label('page.add.name') | raw }}" name="pageIds" multiple="multiple" autocomplete="off" data-tags="true">
                                    <!-- ...altri tag... -->
                                </select>
                            </div>
                        </div>
                        <div class="row g-3 mb-3">
                            <!-- Profile phone -->
                            <div class="col-lg-3">
                                <label class="form-label">{{ label('common.profile.photo') | raw }}</label>
                                <div class="slim rounded"
                                     data-max-file-size="5"
                                     data-push="false"
                                     data-post="output"
                                     data-label="{{ label('slim.upload.photo') | raw }}"
                                     data-label-loading=" "
                                     data-ratio="1:1"
                                     data-jpeg-compression=100
                                     data-button-edit-label="{{ label('common.edit') | raw }}"
                                     data-button-remove-label="{{ label('common.remove') | raw }}"
                                     data-button-download-label="{{ label('common.download') | raw }}"
                                     data-button-upload-label="{{ label('common.upload') | raw }}"
                                     data-button-rotate-label="{{ label('common.rotate') | raw }}"
                                     data-button-cancel-label="{{ label('common.cancel') | raw }}"
                                     data-button-confirm-label="{{ label('common.confirm') | raw }}"
                                     data-status-file-size="{{ label('slim.file.big') | raw }} $0 MB"
                                     data-status-file-type="{{ label('slim.format.not.valid') | raw }} $0"
                                     data-status-no-support="{{ label('slim.browser.not.support') | raw }}"
                                     data-status-image-too-small="{{ label('slim.file.small') | raw }} $0 pixel"
                                     data-status-content-length="{{ label('slim.server.file.big') | raw }}"
                                     data-status-unknown-response="{{ label('slim.unknown.error') | raw }}"
                                     data-status-upload-success="{{ label('slim.image.saved') | raw }}">

                                    <input type="file" id="cropper" name="uploaded-profile" data-show-caption="false" data-show-remove="true">
                                </div>
                                <small class="form-text">{{ label('slim.recommended.dimension') | raw }} 600x600 (ratio 1:1)</small>
                            </div>
                            <!-- Cover image -->
                            <div class="col-lg-12">
                                <label class="form-label">{{ label('common.cover.photo') | raw }}</label>
                                <div class="slim rounded"
                                     data-max-file-size="5"
                                     data-push="false"
                                     data-post="output"
                                     data-label="{{ label('slim.upload.photo') | raw }}"
                                     data-label-loading=" "
                                     data-ratio="free"
                                     data-button-edit-label="{{ label('common.edit') | raw }}"
                                     data-button-remove-label="{{ label('common.remove') | raw }}"
                                     data-button-download-label="{{ label('common.download') | raw }}"
                                     data-button-upload-label="{{ label('common.upload') | raw }}"
                                     data-button-rotate-label="{{ label('common.rotate') | raw }}"
                                     data-button-cancel-label="{{ label('common.cancel') | raw }}"
                                     data-button-confirm-label="{{ label('common.confirm') | raw }}"
                                     data-status-file-size="{{ label('slim.file.big') | raw }} $0 MB"
                                     data-status-file-type="{{ label('slim.format.not.valid') | raw }} $0"
                                     data-status-no-support="{{ label('slim.browser.not.support') | raw }}"
                                     data-status-image-too-small="{{ label('slim.file.small') | raw }} $0 pixel"
                                     data-status-content-length="{{ label('slim.server.file.big') | raw }}"
                                     data-status-unknown-response="{{ label('slim.unknown.error') | raw }}"
                                     data-status-upload-success="{{ label('slim.image.saved') | raw }}">

                                    <input type="file" id="cropper" name="uploaded-cover" data-show-caption="false" data-show-remove="true">
                                </div>
                                <small class="form-text">{{ label('slim.recommended.dimension') | raw }} 1116x280 (ratio 4:1)</small>
                            </div>
                            <!-- End Media -->
                        </div>
                        <div class="row g-3 mb-3">
                            <!-- Type -->
                            <div class="col-sm-6">
                                <label class="form-label">{{ label('page.type') | raw }}</label>
                                <select class="form-select js-choice pageType" id="pageType" name="pageType" required>
                                    <option value="">-</option>
                                    {% for pageType in lookup('area') %}
                                    <option value="{{ pageType.code }}">{{ language == 'it' ? pageType.description : pageType.descriptionEnglish }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                          
                        </div>
                        <div class="row g-3 mb-3">
                            <div class="col-12">
                                <label class="form-label">{{ label('common.tags') | raw }}</label>
                                <select class="select-search-multiple form-control" id="tags" name="tags" data-placeholder="{{ label('tags.placeholder.multiple') | raw }}" multiple>
                                </select>
                            </div>
                        </div> 

                        <div class="row g-3 mb-3">
                            <!-- Page information -->
                            <div class="col-12">
                                <label class="form-label">{{ label('common.description') | raw }}</label>
                                <textarea class="form-control summernote" rows="3" placeholder="{{ label('common.description') | raw }}" id="description" name="description" required></textarea>

                                <!-- Truncation Preview -->
                                <div id="description-preview" class="mt-2" style="display: none;">
                                    <div class="alert alert-info">
                                        <h6><i class="bi bi-eye me-1"></i> {{ label('common.truncation.preview') | raw }} ({{ firm.descriptionTruncateLength | default(300) }} {{ label('common.characters') | raw }} ):</h6>
                                        <div id="preview-content" class="border rounded p-2 bg-light"></div>
                                        <small class="mt-1 d-block">
                                            <span id="preview-char-count">0</span> {{ label('common.characters.visible') | raw }}.
                                            <span id="preview-truncated-info" style="display: none;">
                                                {{ label('common.truncation.info') | raw }}
                                            </span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row g-3 mb-3">
                            <!-- Page information -->
                            <div class="col-12">
                                <label class="form-label">{{ label('common.short.description') | raw }}</label>
                                <textarea class="form-control" rows="2" maxlength="50" placeholder="{{ label('common.short.description') | raw }}" id="shortDescription" name="shortDescription"></textarea>
                            </div>
                        </div>
                        {# se è un luogo prevedere gestione visibilità in base alla selezione del tipo #}
                        <div class="row g-3 mb-3" id="address-container">
                            <!-- Address -->
                            <div class="col-12">
                                <label class="form-label">{{ label('common.fulladdress') | raw }} <small>{{ label('common.autocomplete.address') | raw }}</small></label>
                                <input type="text" class="form-control" placeholder="{{ label('common.address') | raw }}" id="fulladdress" name="fulladdress">
                            </div>
                            <div class="col-12 col-sm-6">
                                <label class="form-label">{{ label('common.country') | raw }}</label>
                                <select class="form-control select-search" name="countryCode" id="countryCode">
                                    <option value="">-</option>
                                    {% for item in lookup("country") %}
                                    <option value="{{ item.code }}">{{ item.description }}</option>
                                    {% endfor %}
                                </select>                                    
                            </div>
                            <div class="col-12 col-sm-6">
                                <label class="form-label">{{ label('common.address') | raw }}</label>
                                <input role="presentation" autocomplete="off" type="text" name="address" id="address" class="form-control maxlength" maxlength="100" placeholder="{{ label('common.address') | raw }}">
                            </div>
                            <div class="col-12 col-sm-6">
                                <label class="form-label">{{ label('common.city') | raw }}</label>
                                <input role="presentation" autocomplete="off" type="text" name="city" id="city" class="form-control maxlength" maxlength="100" placeholder="{{ label('common.city') | raw }}">
                            </div>
                            <div class="col-12 col-sm-6">
                                <label class="form-label">{{ label('common.postalcode') | raw }}</label>
                                <input role="presentation" autocomplete="off" type="text" name="postalCode" id="postalCode" class="form-control maxlength" maxlength="10" placeholder="{{ label('common.postalcode') | raw }}">
                            </div>
                            <div class="col-12 col-sm-6" id="provinceDiv">
                                <label class="form-label">{{ label('common.province') | raw }}</label>
                                <select class="form-control select-search provinceCode provinceCodeIt" name="provinceCode" id="provinceCode">
                                    <option value="">-</option>
                                    {% for item in lookup("province") %}
                                    <option value="{{ item.code }}" {{ item.code == provinceCode ? 'selected' : ''}}>{{ item.description }}</option>
                                    {% endfor %}
                                </select>                                    
                            </div>
                            <div class="col-12 col-sm-6" id="provinceExtDiv">
                                <label class="form-label">{{ label('common.province') | raw }}</label>
                                <input role="presentation" autocomplete="off" type="text" name="provinceCode" id="provinceCode" class="form-control maxlength provinceCode provinceCodeExt" placeholder="{{ label('common.province') | raw }}">
                            </div>
                            <div class="col-12 col-sm-6">
                                <label class="form-label">{{ label('common.extra.city') | raw }}</label>
                                <input role="presentation" autocomplete="off" type="text" name="extraAddress" id="extraAddress" class="form-control maxlength" maxlength="100" placeholder="{{ label('common.extra.city') | raw }}">
                            </div>
                        </div>
                        <div class="row g-3 mb-3">
                            <!-- Show Followers -->
                            <div class="col-md-4">
                                <label class="form-label">{{ label('page.show.follower') | raw }}</label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" role="switch" id="showFollowers" name="showFollowers">
                                </div>
                            </div>
                        </div>
                        <!-- Divider -->
                        <hr>
                        <div class="row g-3 mb-3">
                            <!-- Social Links START -->
                            <div class="col-12">
                                <h5 class="card-title mb-0">Links</h5>
                            </div>
                            <!-- Website -->
                            <div class="col-sm-6">
                                <label  class="form-label">{{ label('common.website') | raw }}</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-browser-chrome text-facebook"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link {{ label('common.website') | raw }}" id="websiteUrl" name="websiteUrl">
                                </div>
                            </div>
                            <!-- Facebook -->
                            <div class="col-sm-6">
                                <label  class="form-label">Facebook</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-facebook text-facebook"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link Facebook" id="facebookUrl" name="facebookUrl">
                                </div>
                            </div>
                            <!-- Twitter -->
                            <div class="col-sm-6">
                                <label class="form-label">Twitter</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-twitter text-twitter"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link Twitter" id="twitterUrl" name="twitterUrl">
                                </div>
                            </div>
                            <!-- Instagram -->
                            <div class="col-sm-6">
                                <label class="form-label">Instagram</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-instagram text-instagram"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link Instagram" id="instagramUrl" name="instagramUrl">
                                </div>
                            </div>
                            <!-- Linkedin -->
                            <div class="col-sm-6">
                                <label class="form-label">Linkedin</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-linkedin"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link Linkedin" id="linkedinUrl" name="linkedinUrl">
                                </div>
                            </div>
                            <!-- YouTube -->
                            <div class="col-sm-6">
                                <label class="form-label">YouTube</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-youtube"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link YouTube" id="youtubeUrl" name="youtubeUrl">
                                </div>
                            </div>
                            <!-- Reddit -->
                            <div class="col-sm-6">
                                <label class="form-label">Reddit</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-reddit"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link Reddit" id="redditUrl" name="redditUrl">
                                </div>
                            </div>
                            <!-- Medium -->
                            <div class="col-sm-6">
                                <label class="form-label">Medium</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-medium"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link Medium" id="mediumUrl" name="mediumUrl">
                                </div>
                            </div>
                            <!-- tiktok -->
                            <div class="col-sm-6">
                                <label class="form-label">TikTok</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-tiktok"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link TikTok" id="tiktok" name="tiktok">
                                </div>
                            </div>
                            <!-- Spotify -->
                            <div class="col-sm-6">
                                <label class="form-label">Spotify</label>
                                <div class="input-group">
                                    <span class="input-group-text border-0"> <i class="bi bi-spotify"></i> </span>
                                    <input type="text" class="form-control" placeholder="Link Spotify" id="spotifiyUrl" name="spotifiyUrl">
                                </div>
                            </div>                                
                        </div>
                        <hr>
                        <div class="row g-3 mb-3">
                            <!-- Social Links START -->
                            <div class="col-12">
                                <h5 class="card-title mb-0">{{ label('common.permissions') | raw }}</h5>
                            </div>                                
                            <div class="col-sm-6">
                                <label class="form-label">{{ label('page.who.can.post') | raw }}</label>
                                <select class="form-select js-choice pageType" id="pageTagging" name="pageTagging" required>
                                    <option value="everyone">{{ label('common.everyone') | raw }}</option>                                        
                                    <option value="owner">{{ label('common.only.owner') | raw }}</option>                                        
                                </select>
                            </div>
                            <!-- Divider -->
                            <hr>
                            <!-- Button  -->
                            <div class="col-12 text-end">
                                <button type="submit" class="btn btn-primary mb-0 w-100">{{ label('page.add.create') | raw }}</button>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- Card body END -->
            </div>
            <!-- Account settings END -->

        </div>
    </div> <!-- Row END -->
</div>
<!-- Container END -->

{% endblock %}

{% block pagescripts %}
<script>
    // async registering autocomplete address
    function initAutocomplete() {
        (function () {
            var input = $('#fulladdress')[0];
            var options = {
                offset: 2
            };
            autocomplete = new google.maps.places.Autocomplete(input, options);
            autocomplete.addListener('place_changed', selectAddress);

            // hacking google logo
            setTimeout(function () {
                $('.pac-container').removeClass('pac-logo');
            }, 1000);
        })();
    }
    function selectAddress() {
        (function () {
            // get city
            var city = '';
            var address = '';
            var number = '';
            var postalCode = '';
            var countryCode = '';
            var provinceCode = '';
            var venue = '';
            var place = autocomplete.getPlace();
            if (place !== null) {
                if (typeof place.address_components !== 'undefined') {
                    for (var i = 0; i < place.address_components.length; i++) {
                        var type = place.address_components[i].types[0];
                        if (type === 'locality') {
                            city = place.address_components[i]['long_name'];
                        }
                        if (type === 'route') {
                            address = place.address_components[i]['long_name'];
                        }
                        if (type === 'street_number') {
                            number = place.address_components[i]['long_name'];
                        }
                        if (type === 'postal_code') {
                            postalCode = place.address_components[i]['long_name'];
                        }
                        if (type === 'administrative_area_level_2') {
                            provinceCode = place.address_components[i]['short_name'];
                        }
                        if (type === 'country') {
                            countryCode = place.address_components[i]['short_name'];
                        }
                        //                  type for venue
                        //                "point_of_interest"
                        //                "establishment"
                        if (place.types.includes("point_of_interest") || place.types.includes("establishment")) {
                            venue = place.name;
                        }
                    }
                    if (address !== '') {
                        if (number !== '') {
                            address += ' ' + number;
                        }
                    }
                }
            }
            $('#countryCode').val(countryCode).change();
            checkCountryCode();
            $('#city').val(city);
            $('#address').val(address);
            $('#postalCode').val(postalCode);
            $('#provinceCode').val(provinceCode).change();
        })();
    }
</script>
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBzbbAKQoJEad0bNS1qFMksTDfT60qJUnU&libraries=places&language=it-IT&callback=initAutocomplete"></script>
<script src="{{ contextPath }}/fe/js/slim.kickstart.min.js"></script>    
<script src="{{ contextPath }}/fe/vendor/choices.js/public/assets/scripts/choices.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/glightbox-master/dist/js/glightbox.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/flatpickr/dist/flatpickr.min.js"></script>    
<script src="{{ contextPath }}/fe/vendor/flatpickr/dist/l10n/it.js"></script>        
<script src="{{ contextPath }}/fe/vendor/summernote/summernote-lite.min.js"></script>
<script src="{{ contextPath }}/fe/vendor/summernote/lang/summernote-it-IT.js"></script>
<script src="{{ contextPath }}/fe/js/content-truncation.js?{{ buildNumber }}"></script>
<script src="{{ contextPath }}/fe/js/pages/page-add.js?{{ buildNumber }}"></script>

<script>
$(document).ready(function() {
    var truncateLength = {{ firm.descriptionTruncateLength | default(300) }};
    var $preview = $('#description-preview');
    var $previewContent = $('#preview-content');
    var $charCount = $('#preview-char-count');
    var $truncatedInfo = $('#preview-truncated-info');

    function updatePreview() {
        if (!window.ContentTruncation) {
            return;
        }

        var content = $('#description').summernote('code');
        if (!content || content.trim() === '' || content === '<p><br></p>' || content.trim().length <= truncateLength) {
            $preview.hide();
            return;
        }

        var result = window.ContentTruncation.truncateHtml(content, truncateLength);

        if (result.isTruncated) {
            // Show truncated version with "..." and note about full content
            $previewContent.html(result.truncated + "... <em>[{{ label('common.show.more') | raw }}]</em>");
            $truncatedInfo.show();
        } else {
            // Show complete content as-is
            $previewContent.html(content);
            $truncatedInfo.hide();
        }

        $charCount.text(result.visibleLength);
        $preview.show();
    }

    // Update preview when Summernote content changes
    $('#description').on('summernote.change', function() {
        setTimeout(updatePreview, 100); // Small delay to ensure content is updated
    });

    // Initial preview update
    setTimeout(function() {
        if ($('#description').length && typeof $('#description').summernote === 'function') {
            updatePreview();
        }
    }, 1000); // Wait for Summernote to initialize
});
</script>
{% endblock %}