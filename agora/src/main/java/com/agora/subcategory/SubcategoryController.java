package com.agora.subcategory;

import com.github.slugify.Slugify;
import com.agora.core.Defaults;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.AreaDao;
import com.agora.dao.CategoryDao;
import com.agora.dao.SubcategoryDao;
import com.agora.pojo.Area;
import com.agora.pojo.Category;
import com.agora.pojo.Subcategory;
import com.agora.pojo.User;
import com.agora.pojo.types.ProfileType;
import com.agora.support.image.slim.SlimImage;
import com.agora.util.ParamUtils;
import com.agora.util.PojoUtils;
import com.agora.util.RouteUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class SubcategoryController {
    private static final Logger LOGGER = LoggerFactory.getLogger(SubcategoryController.class.getName());
    
    public static TemplateViewRoute subcategories = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        attributes.put("user", user);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        List<Subcategory> subcategoryList = SubcategoryDao.loadSubcategoryList();
        attributes.put("subcategoryList", subcategoryList);        
        
        return Manager.render(Templates.SUBCATEGORIES, attributes, RouteUtils.pathType(request));
    };
    
    public static TemplateViewRoute subcategory_edit = (Request request, Response response) -> {
        
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        Subcategory subcategory;
        if (oid != null) {
            subcategory = SubcategoryDao.loadSubcategory(oid);
        } else {
            subcategory = new Subcategory();
        }
        attributes.put("subcategory", subcategory);
        
        List<Area> areaList = AreaDao.loadAreaList();
        attributes.put("areaList", areaList);
        
        List<Category> categoryList = CategoryDao.loadCategoryList();
        attributes.put("categoryList", categoryList);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.SUBCATEGORY_EDIT, attributes, RouteUtils.pathType(request));
    };
    
    public static Route subcategory_edit_save = (Request request, Response response) -> {
        // params
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        if (oid != null) {
            Subcategory subcategoryOld = SubcategoryDao.loadSubcategory(oid);
            Subcategory subcategory = PojoUtils.mergeFromRequest(request, subcategoryOld);

            Slugify slg = new Slugify();
            String identifier = subcategory.getTitle();
            subcategory.setIdentifier(slg.slugify(identifier));                

            String identifierEnglish = subcategory.getTitleEnglish();
            subcategory.setIdentifierEnglish(slg.slugify(identifierEnglish));

            if (isValidSubcategory(subcategory)) {
                SubcategoryDao.updateSubcategory(subcategory);

                // image
                String slim = request.queryParams("uploaded-files");
                if (StringUtils.isNotBlank(slim)) {
                    SlimImage uploaded = null;
                    try {
                        uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    if (uploaded != null) {
                        SubcategoryDao.updateSubcategoryImage(user.getUsername(), oid, uploaded);
                    } else {
                        SubcategoryDao.removeSubcategoryImage(oid);
                    }
                }
                    
                // navigation
                    response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.SUBCATEGORIES));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.SUBCATEGORY_EDIT) + "?oid=" + subcategory.getId());
            }
        } else {
            // params
            Subcategory subcategory = PojoUtils.createFromRequest(request, Subcategory.class);
            
            Slugify slg = new Slugify();
            String identifier = subcategory.getTitle();
            subcategory.setIdentifier(slg.slugify(identifier));                

            String identifierEnglish = subcategory.getTitleEnglish();
            subcategory.setIdentifierEnglish(slg.slugify(identifierEnglish));

            if (isValidSubcategory(subcategory)) {
                
                ObjectId subcategoryId = SubcategoryDao.insertSubcategory(subcategory);

                // image
                String slim = request.queryParams("uploaded-files");
                if (StringUtils.isNotBlank(slim)) {
                    SlimImage uploaded = null;
                    try {
                        uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    if (uploaded != null) {
                        SubcategoryDao.updateSubcategoryImage(user.getUsername(), subcategoryId, uploaded);
                    }
                }
                
                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.SUBCATEGORIES));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.SUBCATEGORY_EDIT));
            }
        }        

        return null;
    };  
    
    public static Route subcategory_remove = (Request request, Response response) -> {
        
        ObjectId subcategoryId = ParamUtils.toObjectId(request.queryParams("subcategoryId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        if (subcategoryId != null) {
            // params
            Subcategory subcategory = SubcategoryDao.loadSubcategory(subcategoryId);
            if (subcategory != null) {
                SubcategoryDao.updateSubcategoryCancelled(subcategoryId, true);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };      
    
    private static boolean isValidSubcategory(Subcategory entity) {
        boolean valid = (entity != null) &&
                StringUtils.isNotBlank(entity.getTitle()) &&
                StringUtils.isNotBlank(entity.getCode()) &&
                StringUtils.isNotBlank(entity.getArea()) &&
                StringUtils.isNotBlank(entity.getCategory()) &&
                !exists(entity)
                ;
        
        if (!valid) {
            if (entity != null) {
                LOGGER.warn(
                        "subcategory validation problem:\n" +
                        "- title " + entity.getTitle() + "\n" +
                        "- code " + entity.getCode()+ "\n" +
                        "- area " + entity.getArea()+ "\n" +
                        "- category " + entity.getCategory()+ "\n"

                );
            } else {
                LOGGER.warn(
                        "subcategory validation problem:\n" +
                        "- empty"
                );
            }
        }
        
        return valid;
    }   

    private static boolean exists(Subcategory entity) {
        boolean exists = false;
        if (StringUtils.isNotBlank(entity.getCode()) && StringUtils.isNotBlank(entity.getArea()) && StringUtils.isNotBlank(entity.getCategory()) && StringUtils.isNotBlank(entity.getIdentifier())) {
            Subcategory subcategory = null;
            try {
                subcategory = SubcategoryDao.loadSubcategoryByCode(entity.getCode());
                if (subcategory == null) {
                    subcategory = SubcategoryDao.loadSubcategoryByIdentifier(entity.getArea(), entity.getCategory(), entity.getIdentifier());
                } else {
                    if (StringUtils.equals(subcategory.getId().toString(), entity.getId().toString())) {
                        subcategory = null;
                        subcategory = SubcategoryDao.loadSubcategoryByIdentifier(entity.getArea(), entity.getCategory(), entity.getIdentifier());
                    }
                }
                if (subcategory != null) {
                    if (StringUtils.equals(subcategory.getId().toString(), entity.getId().toString())) {
                        subcategory = null;
                    }
                }
                
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            exists = (subcategory != null);
        }
        return exists;
    }    
    
}
