package com.agora.area;

import com.github.slugify.Slugify;
import com.agora.core.Defaults;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.AreaDao;
import com.agora.pojo.Area;
import com.agora.pojo.User;
import com.agora.pojo.types.ProfileType;
import com.agora.support.image.slim.SlimImage;
import com.agora.util.ParamUtils;
import com.agora.util.PojoUtils;
import com.agora.util.RouteUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class AreaController {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(AreaController.class.getName());
    
    public static TemplateViewRoute areas = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        attributes.put("user", user);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        List<Area> areaList = AreaDao.loadAreaList();
        attributes.put("areaList", areaList);        
        
        return Manager.render(Templates.AREAS, attributes, RouteUtils.pathType(request));
    };
    
    public static TemplateViewRoute area_edit = (Request request, Response response) -> {
        
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        Area area;
        if (oid != null) {
            area = AreaDao.loadArea(oid);
        } else {
            area = new Area();
        }
        attributes.put("area", area);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.AREA_EDIT, attributes, RouteUtils.pathType(request));
    };
    
    public static Route area_edit_save = (Request request, Response response) -> {
        // params
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        if (oid != null) {
            Area areaOld = AreaDao.loadArea(oid);
            Area area = PojoUtils.mergeFromRequest(request, areaOld);
            
            Slugify slg = new Slugify();
            String identifier = area.getTitle();
            area.setIdentifier(slg.slugify(identifier));                

            String identifierEnglish = area.getTitleEnglish();
            area.setIdentifierEnglish(slg.slugify(identifierEnglish));
            
            if (isValidArea(area)) {
                AreaDao.updateArea(area);

                // image
                String slim = request.queryParams("uploaded-files");
                if (StringUtils.isNotBlank(slim)) {
                    SlimImage uploaded = null;
                    try {
                        uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    if (uploaded != null) {
                        AreaDao.updateAreaImage(user.getUsername(), oid, uploaded);
                    } else {
                        AreaDao.removeAreaImage(oid);
                    }
                }
                    
                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.AREAS));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.AREA_EDIT) + "?oid=" + area.getId());
            }
        } else {
            // params
            Area area = PojoUtils.createFromRequest(request, Area.class);
            
            Slugify slg = new Slugify();
            String identifier = area.getTitle();
            area.setIdentifier(slg.slugify(identifier));                

            String identifierEnglish = area.getTitleEnglish();
            area.setIdentifierEnglish(slg.slugify(identifierEnglish));     

            if (isValidArea(area)) {
                ObjectId areaId = AreaDao.insertArea(area);

                // image
                String slim = request.queryParams("uploaded-files");
                if (StringUtils.isNotBlank(slim)) {
                    SlimImage uploaded = null;
                    try {
                        uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    if (uploaded != null) {
                        AreaDao.updateAreaImage(user.getUsername(), areaId, uploaded);
                    }
                }
                
                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.AREAS));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.AREA_EDIT));
            }
        }        

        return null;
    };  
    
    public static Route area_remove = (Request request, Response response) -> {
        
        ObjectId areaId = ParamUtils.toObjectId(request.queryParams("areaId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        if (areaId != null) {
            // params
            Area area = AreaDao.loadArea(areaId);
            if (area != null) {
                AreaDao.updateAreaCancelled(areaId, true);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };      
    
    private static boolean isValidArea(Area entity) {
        boolean valid = (entity != null) &&
                StringUtils.isNotBlank(entity.getTitle()) &&
                StringUtils.isNotBlank(entity.getCode()) &&
                !exists(entity)
                ;
        
        if (!valid) {
            if (entity != null) {
                LOGGER.warn(
                        "area validation problem:\n" +
                        "- title " + entity.getTitle() + "\n" +
                        "- code " + entity.getCode()+ "\n"
                );
            } else {
                LOGGER.warn(
                        "area validation problem:\n" +
                        "- empty"
                );
            }
        }
        
        return valid;
    }   

    public static boolean exists(Area entity) {
        boolean exists = false;
        if (StringUtils.isNotBlank(entity.getCode()) && StringUtils.isNotBlank(entity.getIdentifier())) {
            Area area = null;
            try {
                area = AreaDao.loadAreaByCode(entity.getCode());
                if (area == null) {
                    area = AreaDao.loadAreaByIdentifier(entity.getIdentifier());
                } else {
                    if (StringUtils.equals(area.getId().toString(), entity.getId().toString())) {
                        area = null;
                        area = AreaDao.loadAreaByIdentifier(entity.getIdentifier());
                    }
                }
                if (area != null) {
                    if (StringUtils.equals(area.getId().toString(), entity.getId().toString())) {
                        area = null;
                    }
                }

            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            exists = (area != null);
        }
        return exists;
    }
    
}
