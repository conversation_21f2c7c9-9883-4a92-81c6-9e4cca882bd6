package com.agora.post;

import com.github.slugify.Slugify;
import com.agora.core.Defaults;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.PostDao;
import com.agora.dao.StableImageDao;
import com.agora.pojo.Post;
import com.agora.pojo.User;
import com.agora.pojo.types.ProfileType;
import com.agora.pojo.types.StableImageType;
import com.agora.support.file.posted.PostedFile;
import com.agora.support.image.slim.SlimImage;
import com.agora.util.ParamUtils;
import com.agora.util.PojoUtils;
import com.agora.util.RouteUtils;
import com.agora.util.TimeUtils;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class PostController {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(PostController.class.getName());
    
    public static TemplateViewRoute posts = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.blogger.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        attributes.put("user", user);
        
        String language = StringUtils.defaultIfBlank(request.queryParams("language"), Defaults.LANGUAGE);
        attributes.put("language", language);
        
        // date filter
        Date startDate = DateUtils.addMonths(new Date(), -1);
        Date endDate = TimeUtils.today();

        if (StringUtils.isNotBlank(request.queryParams("startDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("startDate"));
            if (tmp != null) {
                startDate = tmp;
            }
        }
        if (StringUtils.isNotBlank(request.queryParams("endDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("endDate"));
            if (tmp != null) {
                endDate = tmp;
            }
        }

        attributes.put("startDate", startDate);
        attributes.put("endDate", endDate);
        if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.blogger.toString())) {
            List<Post> postList = PostDao.loadPostListByDateRangeAndUserId(startDate, endDate, user.getId());
            attributes.put("postList", postList);        
        } else {
            List<Post> postList = PostDao.loadPostListByDateRange(startDate, endDate);
            attributes.put("postList", postList);        
        }
        
        return Manager.render(Templates.POSTS, attributes, RouteUtils.pathType(request));
    };
    
    public static TemplateViewRoute post_edit = (Request request, Response response) -> {
        
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.blogger.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        // tag list
        List<String> tagList = PostDao.loadPostTagList();
        attributes.put("tagList", tagList);        
        
        Post post;
        if (oid != null) {
            post = PostDao.loadPost(oid);
        } else {
            post = new Post();
        }
        attributes.put("post", post);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.POST_EDIT, attributes, RouteUtils.pathType(request));
    };
    
    public static Route post_edit_save = (Request request, Response response) -> {
        // params
        List<SlimImage> slims = new ArrayList<>();
        List<PostedFile> files = new ArrayList<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, slims, files);

        ObjectId oid = ParamUtils.toObjectId(params.get("oid"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.blogger.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        if (oid != null) {
            // params
            Post previous = PostDao.loadPost(oid);
            Post post = (Post) BeanUtils.cloneBean(previous);
            
            post = PojoUtils.mergeFromParams(params, post);
            String tags[] = request.queryParamsValues("taglist");
            if ((tags != null) && (tags.length > 0)) {
                post.setTags(tags);
            }
                        
            if (isValidPost(post)) {
                
//                Slugify slg = new Slugify();
//                String identifier = post.getTitle() + "-" + RouteUtils.generateIdentifier();
//                post.setIdentifier(slg.slugify(identifier));   
                PostDao.updatePost(post);

                // images
                if (!slims.isEmpty()) {
                    PostDao.updatePostImages(user.getUsername(), oid, slims);
                } else {
                    PostDao.removePostImages(oid);
                }
                    
                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.POSTS));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.POST_EDIT) + "?oid=" + post.getId());
            }
        } else {
            // params
            Post post = PojoUtils.createFromParams(params, Post.class);
            
            if (isValidPost(post)) {
                post.setUserId(user.getId());
                String tags[] = request.queryParamsValues("taglist");
                if ((tags != null) && (tags.length > 0)) {
                    post.setTags(tags);
                }

                Slugify slg = new Slugify();
                String identifier = post.getTitle() + "-" + RouteUtils.generateIdentifier();
                post.setIdentifier(slg.slugify(identifier));                
                
                ObjectId postId = PostDao.insertPost(post);

                // images
                if (!slims.isEmpty()) {
                    PostDao.updatePostImages(user.getUsername(), postId, slims);
                } else {
                    PostDao.removePostImages(postId);
                }
                
                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.POSTS));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.POST_EDIT));
            }
        }        

        return null;
    };  
    
    public static Route post_remove = (Request request, Response response) -> {
        
        ObjectId postId = ParamUtils.toObjectId(request.queryParams("postId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.blogger.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        if (postId != null) {
            // params
            Post post = PostDao.loadPost(postId);
            if (post != null) {
                PostDao.updatePostCancelled(postId, true);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };      

    public static Route post_image_save = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
		if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) &&
				!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.blogger.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // uploaded files
        List<PostedFile> files = new ArrayList<>();
        PojoUtils.paramsFromRequest(request, null, files);
        if (files.isEmpty()) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "missing file");
        }
        if (files.size() > 1) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "too many uploaded files " + files.size());
        }
        
        // uploaded file
        PostedFile file = files.get(0);
        if (file.getBytes() == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "empty file");
        }
        if (file.getBytes().length == 0) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "empty file");
        }
        if (StringUtils.isBlank(file.getName())) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "missing filename");
        }
        
        // vaild extensions
        String[] allowed = new String[] {"png", "jpg", "jpeg", "gif"};
        
        String ext = FilenameUtils.getExtension(file.getName());
        if (StringUtils.isBlank(ext)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "missing filename extension " + file.getName());
        }
        
        boolean found = false;
        for (String allow : allowed) {
            if (StringUtils.equalsIgnoreCase(allow, ext)) {
                found = true;
            }
        }
        if (!found) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "unmanaged filename extension " + file.getName());
        }
        
        // store stable image
        ObjectId stableImageId = null;
        try {
            stableImageId = StableImageDao.insertStableImage(StableImageType.post, user.getUsername(), file.getName(), file.getBytes());
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        if (stableImageId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "unable to insert stable image " + file.getName());
        }
        
        // editor specification on https://github.com/summernote/summernote/issues/1173#issuecomment-115730874
        String url = RouteUtils.baseUrl(request) + Paths.IMAGE + "?oid=" + stableImageId;
        return url;
    };  


    
    ////////////
    // internals

    private static boolean isValidPost(Post entity) {
        boolean valid = (entity != null) &&
                StringUtils.isNotBlank(entity.getTitle()) &&
                StringUtils.isNotBlank(entity.getCategory())
                ;
        
        if (!valid) {
            if (entity != null) {
                LOGGER.warn(
                        "post validation problem:\n" +
                        "- title " + entity.getTitle()+ "\n" +
                        "- category " + entity.getCategory()+ "\n"
                );
            } else {
                LOGGER.warn(
                        "post validation problem:\n" +
                        "- empty"
                );
            }
        }
        
        return valid;
    }
    
}
