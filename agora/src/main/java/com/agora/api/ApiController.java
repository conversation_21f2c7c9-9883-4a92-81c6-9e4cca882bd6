package com.agora.api;

import com.agora.commons.CacheCommons;
import com.agora.commons.EventCommons;
import com.agora.commons.PageCommons;
import com.agora.core.Manager;
import com.agora.dao.ApilogDao;
import com.agora.dao.EventDao;
import com.agora.dao.FirmDao;
import com.agora.dao.PageDao;
import com.agora.event.EventEntry;
import com.agora.page.PageEntry;
import com.agora.pojo.Apilog;
import com.agora.pojo.Event;
import com.agora.pojo.Firm;
import com.agora.pojo.Page;
import com.agora.util.ParamUtils;
import com.agora.util.PojoUtils;
import com.agora.util.RouteUtils;
import com.agora.util.TimeUtils;
import com.github.slugify.Slugify;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;

/**
 *
 * <AUTHOR>
 */
public class ApiController {

    private static final long QUOTA_MILLIES = 1 * 1000;

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiController.class.getName());

    private static final Map<String, Date> lastAccesses = new HashedMap<>();

    public static Route ping = (Request request, Response response) -> {

        // apikey
        String apikey = request.queryParams("apikey");
        if (StringUtils.isBlank(apikey)) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // firm
        Firm firm = FirmDao.loadFirm();
        if (firm == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // wrong apikey
        if (!StringUtils.equals(firm.getApikey(), apikey) && !StringUtils.equals(firm.getSecondApikey(), apikey)) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // log
        ObjectId apilogId = apilog(null, "ping", request, null, 0L, null);
        long millies = TimeUtils.now().getTime();

        // quota
        if (exeed("ping")) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401, "quota exedeed");
        }

        ApiResult result = new ApiResult();
        result.setDone(true);

        // log
        millies = TimeUtils.now().getTime() - millies;
        apilog(apilogId, "ping", request, result, millies, null);

        return result;
    };

    public static Route pages = (Request request, Response response) -> {

        // apikey
        String apikey = request.queryParams("apikey");
        if (StringUtils.isBlank(apikey)) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // firm
        Firm firm = FirmDao.loadFirm();
        if (firm == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // wrong apikey
        if (!StringUtils.equals(firm.getApikey(), apikey) && !StringUtils.equals(firm.getSecondApikey(), apikey)) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // log
        ObjectId apilogId = apilog(null, "pages", request, null, 0L, null);
        long millies = TimeUtils.now().getTime();

        // quota
        if (exeed("pages")) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401, "quota exedeed");
        }

        // params
        Date fromDate = null;
        String from = request.queryParams("from");
        if (StringUtils.isNotBlank(from)) {
            fromDate = TimeUtils.toDate(from, "yyyyMMdd");
        }
        Date toDate = null;
        String to = request.queryParams("to");
        if (StringUtils.isNotBlank(to)) {
            toDate = TimeUtils.toDate(to, "yyyyMMdd");
        }

        int skip = 0;
        int limit = 0;
        String skipStr = request.queryParams("skip");
        String limitStr = request.queryParams("limit");

        if (StringUtils.isNotBlank(skipStr)) {
            try {
                skip = Integer.parseInt(skipStr);
            } catch (NumberFormatException e) {
                skip = 0;
            }
        }

        if (StringUtils.isNotBlank(limitStr)) {
            try {
                limit = Integer.parseInt(limitStr);
            } catch (NumberFormatException e) {
                limit = 0;
            }
        }

        // pages
        List<Page> pages = null;
        try {
            pages = PageDao.loadPageListByDateRange(fromDate, toDate, skip, limit);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "database error");
        }

        // entries
        List<PageEntry> entries = PageCommons.toEntries(pages);

        ApiPagesResult result = new ApiPagesResult();
        result.setEntries(entries);
        result.setDone(true);

        // log
        millies = TimeUtils.now().getTime() - millies;
        apilog(apilogId, "pages", request, result, millies, null);

        return result;
    };

    public static Route page = (Request request, Response response) -> {

        // apikey
        String apikey = request.queryParams("apikey");
        if (StringUtils.isBlank(apikey)) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // firm
        Firm firm = FirmDao.loadFirm();
        if (firm == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // wrong apikey
        if (!StringUtils.equals(firm.getApikey(), apikey) && !StringUtils.equals(firm.getSecondApikey(), apikey)) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // log
        ObjectId apilogId = apilog(null, "page_detail", request, null, 0L, null);
        long millies = TimeUtils.now().getTime();

        // quota
        if (exeed("page_detail")) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401, "quota exedeed");
        }

        // params
        ObjectId oid = ParamUtils.toObjectId(request.params("oid"));
        if (oid == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "empty oid");
        }

        // pages
        Page page = null;
        try {
            page = PageDao.loadPage(oid);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "database error");
        }

        // entry
        PageEntry entry = PageCommons.toEntry(page);

        ApiPageResult result = new ApiPageResult();
        result.setEntry(entry);
        result.setDone(true);

        // log
        millies = TimeUtils.now().getTime() - millies;
        apilog(apilogId, "page_detail", request, result, millies, null);

        return result;
    };

    public static Route events = (Request request, Response response) -> {

        // apikey
        String apikey = request.queryParams("apikey");
        if (StringUtils.isBlank(apikey)) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // firm
        Firm firm = FirmDao.loadFirm();
        if (firm == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // wrong apikey
        if (!StringUtils.equals(firm.getApikey(), apikey) && !StringUtils.equals(firm.getSecondApikey(), apikey)) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // log
        ObjectId apilogId = apilog(null, "events", request, null, 0L, null);
        long millies = TimeUtils.now().getTime();

        // quota
        if (exeed("events")) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401, "quota exedeed");
        }

        // params
        Date fromDate = null;
        String from = request.queryParams("from");
        if (StringUtils.isNotBlank(from)) {
            fromDate = TimeUtils.toDate(from, "yyyyMMdd");
        }
        Date toDate = null;
        String to = request.queryParams("to");
        if (StringUtils.isNotBlank(to)) {
            toDate = TimeUtils.toDate(to, "yyyyMMdd");
        }

        int skip = 0;
        int limit = 0;
        String skipStr = request.queryParams("skip");
        String limitStr = request.queryParams("limit");

        if (StringUtils.isNotBlank(skipStr)) {
            try {
                skip = Integer.parseInt(skipStr);
            } catch (NumberFormatException e) {
                skip = 0;
            }
        }

        if (StringUtils.isNotBlank(limitStr)) {
            try {
                limit = Integer.parseInt(limitStr);
            } catch (NumberFormatException e) {
                limit = 0;
            }
        }

        // pages
        List<Event> events = null;
        try {
            events = EventDao.loadEventListByDateRange(fromDate, toDate, skip, limit);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "database error");
        }

        // entries
        List<EventEntry> entries = EventCommons.toEntries(events);

        ApiEventsResult result = new ApiEventsResult();
        result.setEntries(entries);
        result.setDone(true);

        // log
        millies = TimeUtils.now().getTime() - millies;
        apilog(apilogId, "events", request, result, millies, null);

        return result;
    };

    public static Route event = (Request request, Response response) -> {

        // apikey
        String apikey = request.queryParams("apikey");
        if (StringUtils.isBlank(apikey)) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // firm
        Firm firm = FirmDao.loadFirm();
        if (firm == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // wrong apikey
        if (!StringUtils.equals(firm.getApikey(), apikey) && !StringUtils.equals(firm.getSecondApikey(), apikey)) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // log
        ObjectId apilogId = apilog(null, "event_detail", request, null, 0L, null);
        long millies = TimeUtils.now().getTime();

        // quota
        if (exeed("event_detail")) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401, "quota exedeed");
        }

        // params
        ObjectId oid = ParamUtils.toObjectId(request.params("oid"));
        if (oid == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "empty oid");
        }

        // events
        Event event = null;
        try {
            event = EventDao.loadEvent(oid);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "database error");
        }

        // entry
        EventEntry entry = EventCommons.toEntry(event);

        ApiEventResult result = new ApiEventResult();
        result.setEntry(entry);
        result.setDone(true);

        // log
        millies = TimeUtils.now().getTime() - millies;
        apilog(apilogId, "event_detail", request, result, millies, null);

        return result;
    };

    public static Route pages_operate = (Request request, Response response) -> {

        // apikey
        String apikey = request.queryParams("apikey");
        if (StringUtils.isBlank(apikey)) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // firm
        Firm firm = FirmDao.loadFirm();
        if (firm == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // wrong apikey
        if (!StringUtils.equals(firm.getApikey(), apikey) && !StringUtils.equals(firm.getSecondApikey(), apikey)) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // log
        ObjectId apilogId = apilog(null, "pages operate", request, null, 0L, null);
        long millies = TimeUtils.now().getTime();

        // quota
        if (exeed("pages")) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401, "quota exedeed");
        }

        ApiResult result = new ApiResult();
        result.setIndexIdentifiers(new LinkedHashMap<>());

        // params
        String json = request.queryParams("json");
        if (StringUtils.isNotBlank(json)) {
            Gson gson = new Gson();
            JsonObject element = gson.fromJson(json, JsonObject.class);

            if (element.isJsonObject()) {
                JsonObject jsonObject = element.getAsJsonObject();

                if (jsonObject.has("items") && jsonObject.get("items").isJsonArray()) {
                    JsonArray items = jsonObject.getAsJsonArray("items");

                    int index = 0;
                    for (JsonElement item : items) {
                        try {
                            if (item instanceof JsonObject) {
                                JsonObject object = item.getAsJsonObject();
                                if (object != null) {
                                    if (object.has("operation")) {
                                        boolean valid = false;
                                        String operation = object.get("operation").toString();
                                        if (StringUtils.equalsIgnoreCase(operation, "update") || StringUtils.equalsIgnoreCase(operation, "delete")) {
                                            if (object.has("identifier")) {
                                                if (StringUtils.isNotBlank(object.get("identifier").toString())) {
                                                    valid = true;
                                                }
                                            }
                                        } else if (StringUtils.equalsIgnoreCase(operation, "insert")) {
                                            valid = true;
                                        }

                                        if (valid) {
                                            Page parsedPage = gson.fromJson(item, Page.class);
                                            if (PageCommons.isValidPage(parsedPage)) {
                                                if (StringUtils.equalsIgnoreCase(operation, "insert")) {
                                                    Slugify slg = new Slugify();
                                                    String identifier = slg.slugify(parsedPage.getName());
                                                    if (StringUtils.isNotBlank(identifier)) {
                                                        Page exist = PageDao.loadPageByIdentifier(identifier);
                                                        if (exist != null) {
                                                            identifier = parsedPage.getName() + "-" + RouteUtils.generateIdentifier();
                                                        }

                                                        parsedPage.setIdentifier(slg.slugify(identifier));
                                                        PageDao.insertPage(parsedPage);
                                                        result.getIndexIdentifiers().put(index, "OK - " + parsedPage.getIdentifier());
                                                    } else {
                                                        result.getIndexIdentifiers().put(index, "KO - Unable to create page identifier (missing page name?)");
                                                    }
                                                } else if (StringUtils.equalsIgnoreCase(operation, "update")) {
                                                    if (StringUtils.isNotBlank(parsedPage.getIdentifier())) {
                                                        Page pageDb = PageDao.loadPageByIdentifier(parsedPage.getIdentifier());
                                                        if (pageDb != null) {
                                                            Map<String, String> params = new HashMap<>();
                                                            for (Map.Entry<String, JsonElement> entry : object.entrySet()) {
                                                                params.put(entry.getKey(), entry.getValue().getAsString());
                                                            }

                                                            pageDb = PojoUtils.mergeFromParams(params, pageDb);
                                                            PageDao.updatePage(pageDb);
                                                        } else {
                                                            result.getIndexIdentifiers().put(index, "KO - Page not found on DB for identifier '" + parsedPage.getIdentifier() + "'");
                                                        }
                                                    } else {
                                                        result.getIndexIdentifiers().put(index, "KO - Missing identifier");
                                                    }
                                                } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                                                    if (StringUtils.isNotBlank(parsedPage.getIdentifier())) {
                                                        Page pageDb = PageDao.loadPageByIdentifier(parsedPage.getIdentifier());
                                                        if (pageDb != null) {
                                                            PageDao.deletePage(pageDb.getId());
                                                            result.getIndexIdentifiers().put(index, "OK - " + parsedPage.getIdentifier());
                                                        } else {
                                                            result.getIndexIdentifiers().put(index, "KO - Page not found on DB for identifier '" + parsedPage.getIdentifier() + "'");
                                                        }
                                                    } else {
                                                        result.getIndexIdentifiers().put(index, "KO - Missing identifier");
                                                    }
                                                } else {
                                                    result.getIndexIdentifiers().put(index, "KO - Invalid operation '" + operation + "'");
                                                }
                                            } else {
                                                result.getIndexIdentifiers().put(index, "KO - Page not valid (name of the page missing?)");
                                            }
                                        } else {
                                            result.getIndexIdentifiers().put(index, "KO - Invalid request, update and delete need the 'identifier' key");
                                        }
                                    } else {
                                        result.getIndexIdentifiers().put(index, "KO - 'operation' key missing");
                                    }
                                } else {
                                    result.getIndexIdentifiers().put(index, "KO - JsonObject is null");
                                }
                            } else {
                                result.getIndexIdentifiers().put(index, "KO - Not a JsonObject");
                            }
                        } catch (Exception ex) {
                            LOGGER.error("Unable to process page row " + index);
                            result.getIndexIdentifiers().put(index, "KO - Unexpected Error");
                        }

                        index++;
                    }
                } else {
                    System.out.println("No array found under 'items'");
                }
            } else {
                System.out.println("The root element is not a JSON object.");
            }
        }

        // log
        millies = TimeUtils.now().getTime() - millies;
        apilog(apilogId, "pages operate", request, result, millies, null);

        return result;
    };

    public static Route events_operate = (Request request, Response response) -> {

        // apikey
        String apikey = request.queryParams("apikey");
        if (StringUtils.isBlank(apikey)) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // firm
        Firm firm = FirmDao.loadFirm();
        if (firm == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // wrong apikey
        if (!StringUtils.equals(firm.getApikey(), apikey) && !StringUtils.equals(firm.getSecondApikey(), apikey)) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // log
        ObjectId apilogId = apilog(null, "events operate", request, null, 0L, null);
        long millies = TimeUtils.now().getTime();

        // quota
        if (exeed("events")) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401, "quota exedeed");
        }

        ApiResult result = new ApiResult();
        result.setIndexIdentifiers(new LinkedHashMap<>());

        // params
        String json = request.queryParams("json");
        if (StringUtils.isNotBlank(json)) {
            Gson gson = new Gson();
            JsonObject element = gson.fromJson(json, JsonObject.class);

            if (element.isJsonObject()) {
                JsonObject jsonObject = element.getAsJsonObject();

                if (jsonObject.has("items") && jsonObject.get("items").isJsonArray()) {
                    JsonArray items = jsonObject.getAsJsonArray("items");

                    int index = 0;
                    for (JsonElement item : items) {
                        try {
                            if (item instanceof JsonObject) {
                                JsonObject object = item.getAsJsonObject();
                                if (object != null) {
                                    if (object.has("operation")) {
                                        boolean valid = false;
                                        String operation = object.get("operation").toString();
                                        if (StringUtils.equalsIgnoreCase(operation, "update") || StringUtils.equalsIgnoreCase(operation, "delete")) {
                                            if (object.has("identifier")) {
                                                if (StringUtils.isNotBlank(object.get("identifier").toString())) {
                                                    valid = true;
                                                }
                                            }
                                        } else if (StringUtils.equalsIgnoreCase(operation, "insert")) {
                                            valid = true;
                                        }

                                        if (valid) {
                                            Event parsedEvent = gson.fromJson(item, Event.class);
                                            if (EventCommons.isValidEvent(parsedEvent)) {
                                                if (StringUtils.equalsIgnoreCase(operation, "insert")) {
                                                    Slugify slg = new Slugify();
                                                    String identifier = slg.slugify(parsedEvent.getName());
                                                    if (StringUtils.isNotBlank(identifier)) {
                                                        Event exist = EventDao.loadEventByIdentifier(identifier);
                                                        if (exist != null) {
                                                            identifier = parsedEvent.getName() + "-" + RouteUtils.generateIdentifier();
                                                        }

                                                        parsedEvent.setIdentifier(slg.slugify(identifier));
                                                        EventDao.insertEvent(parsedEvent);
                                                        result.getIndexIdentifiers().put(index, "OK - " + parsedEvent.getIdentifier());
                                                    } else {
                                                        result.getIndexIdentifiers().put(index, "KO - Unable to create event identifier (missing event name?)");
                                                    }
                                                } else if (StringUtils.equalsIgnoreCase(operation, "update")) {
                                                    if (StringUtils.isNotBlank(parsedEvent.getIdentifier())) {
                                                        Event eventDb = EventDao.loadEventByIdentifier(parsedEvent.getIdentifier());
                                                        if (eventDb != null) {
                                                            Map<String, String> params = new HashMap<>();
                                                            for (Map.Entry<String, JsonElement> entry : object.entrySet()) {
                                                                params.put(entry.getKey(), entry.getValue().getAsString());
                                                            }

                                                            eventDb = PojoUtils.mergeFromParams(params, eventDb);
                                                            EventDao.updateEvent(eventDb);
                                                        } else {
                                                            result.getIndexIdentifiers().put(index, "KO - Event not found on DB for identifier '" + parsedEvent.getIdentifier() + "'");
                                                        }
                                                    } else {
                                                        result.getIndexIdentifiers().put(index, "KO - Missing identifier");
                                                    }
                                                } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                                                    if (StringUtils.isNotBlank(parsedEvent.getIdentifier())) {
                                                        Event eventDb = EventDao.loadEventByIdentifier(parsedEvent.getIdentifier());
                                                        if (eventDb != null) {
                                                            EventDao.deleteEvent(eventDb.getId());
                                                            result.getIndexIdentifiers().put(index, "OK - " + parsedEvent.getIdentifier());
                                                        } else {
                                                            result.getIndexIdentifiers().put(index, "KO - Event not found on DB for identifier '" + parsedEvent.getIdentifier() + "'");
                                                        }
                                                    } else {
                                                        result.getIndexIdentifiers().put(index, "KO - Missing identifier");
                                                    }
                                                } else {
                                                    result.getIndexIdentifiers().put(index, "KO - Invalid operation '" + operation + "'");
                                                }
                                            } else {
                                                result.getIndexIdentifiers().put(index, "KO - Event not valid (name of the event missing?)");
                                            }
                                        } else {
                                            result.getIndexIdentifiers().put(index, "KO - Invalid request, update and delete need the 'identifier' key");
                                        }
                                    } else {
                                        result.getIndexIdentifiers().put(index, "KO - 'operation' key missing");
                                    }
                                } else {
                                    result.getIndexIdentifiers().put(index, "KO - JsonObject is null");
                                }
                            } else {
                                result.getIndexIdentifiers().put(index, "KO - Not a JsonObject");
                            }
                        } catch (Exception ex) {
                            LOGGER.error("Unable to process event row " + index);
                            result.getIndexIdentifiers().put(index, "KO - Unexpected Error");
                        }

                        index++;
                    }
                } else {
                    System.out.println("No array found under 'items'");
                }
            } else {
                System.out.println("The root element is not a JSON object.");
            }
        }

        // log
        millies = TimeUtils.now().getTime() - millies;
        apilog(apilogId, "events operate", request, result, millies, null);

        return result;
    };

    public static Route reload_cache = (Request request, Response response) -> {

        // apikey
        String apikey = request.queryParams("apikey");
        if (StringUtils.isBlank(apikey)) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // what
        String what = request.queryParams("what");
        if (StringUtils.isBlank(what)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "empty what");
        }

        // firm
        Firm firm = FirmDao.loadFirm();
        if (firm == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // wrong apikey
        if (!StringUtils.equals(firm.getApikey(), apikey) && !StringUtils.equals(firm.getSecondApikey(), apikey)) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // log
        ObjectId apilogId = apilog(null, "reload cache (" + what + ")", request, null, 0L, null);
        long millies = TimeUtils.now().getTime();

        ApiResult result = new ApiResult();

        if (StringUtils.equalsIgnoreCase(what, "page")) {
            CacheCommons.reloadHomePages();
            result.setDone(true);
        } else if (StringUtils.equalsIgnoreCase(what, "event")) {
            CacheCommons.reloadHomeEvents();
            result.setDone(true);
        } else if (StringUtils.equalsIgnoreCase(what, "tags")) {
            CacheCommons.reloadMostUsedTags();
            result.setDone(true);
        }

        // log
        millies = TimeUtils.now().getTime() - millies;
        apilog(apilogId, "reload cache (" + what + ")", request, result, millies, null);

        return result;
    };

    private static boolean exeed(String name) {
        boolean exeed = false;

        // ?????? @mike: un'api alla volta
        // ...
        // ?????? @mike: sistemare quote (60s)
        // ...
        // ?????? @mike: rimosso vincolo
//        Date last = lastAccesses.get(name);
//        Date now = TimeUtils.now();
//        if (last != null) {
//            long delta = now.getTime() - last.getTime();
//            if (delta < (QUOTA_MILLIES)) {
//                exeed = true;
//            }
//        }
//        lastAccesses.put(name, now);
        return exeed;
    }

    private static ObjectId apilog(ObjectId apilogId, String name, Request request, ApiResult result, long millies, String message) {
        if (StringUtils.isBlank(name)) {
            LOGGER.error("missing name logging api");
            return null;
        }
        if (request == null) {
            LOGGER.error("missing request logging api " + name);
            return null;
        }
        if ((apilogId == null) && (result != null)) {
            LOGGER.error("wrong apilog update logging api " + name);
            return null;
        }

        Apilog apilog = null;
        if (apilogId == null) {
            apilog = new Apilog();
        } else {
            try {
                apilog = ApilogDao.loadApilog(apilogId);
            } catch (Exception ex) {
                LOGGER.error("suppressed");
            }
            if (apilog == null) {
                LOGGER.error("missing apilogId logging api " + name + " " + apilogId);
                return null;
            }
        }

        apilog.setName(name);
        apilog.setMethod(request.requestMethod());
        apilog.setParams(request.queryMap().toMap());
        apilog.setBody(request.body());

        if (result != null) {

            apilog.setOut(Manager.serializeToJson(result));
            apilog.setMillies(millies);
            apilog.setDone(result.getDone());
            apilog.setMessage(null);

            int count = 0;
            int kos = 0;
            if (result instanceof ApiPageResult) {
                ApiPageResult apiPageResult = (ApiPageResult) result;
                if (apiPageResult.getEntry() != null) {
                    count = 1;
                }

            } else if (result instanceof ApiPagesResult) {
                ApiPagesResult apiPagesResult = (ApiPagesResult) result;
                if (apiPagesResult.getEntries() != null) {
                    count = apiPagesResult.getEntries().size();
                }

            }

            apilog.setCount(count);
            apilog.setKos(kos);
        }

        if (apilogId == null) {
            try {
                apilogId = ApilogDao.insertApilog(apilog);
            } catch (Exception ex) {
                LOGGER.error("suppressed");
            }
        } else {
            try {
                ApilogDao.updateApilog(apilog);
            } catch (Exception ex) {
                LOGGER.error("suppressed");
            }
        }

        return apilogId;
    }

}
