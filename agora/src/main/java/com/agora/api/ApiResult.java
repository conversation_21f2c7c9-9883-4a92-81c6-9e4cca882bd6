package com.agora.api;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ApiResult {

    private Boolean done;
    private Map<Integer, String> indexIdentifiers;

    public Boolean getDone() {
        return done;
    }

    public void setDone(Boolean done) {
        this.done = done;
    }

    public Map<Integer, String> getIndexIdentifiers() {
        return indexIdentifiers;
    }

    public void setIndexIdentifiers(Map<Integer, String> indexIdentifiers) {
        this.indexIdentifiers = indexIdentifiers;
    }

}
