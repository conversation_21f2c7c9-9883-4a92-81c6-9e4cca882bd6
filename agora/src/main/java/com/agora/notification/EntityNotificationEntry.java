package com.agora.notification;

import com.agora.pojo.EntityNotification;
import com.agora.pojo.Event;
import com.agora.pojo.Page;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class EntityNotificationEntry {
    private Event event;
    private EntityNotification entityNotification;
    private List<Page> validPageList;

    public Event getEvent() {
        return event;
    }

    public void setEvent(Event event) {
        this.event = event;
    }

    public EntityNotification getEntityNotification() {
        return entityNotification;
    }

    public void setEntityNotification(EntityNotification entityNotification) {
        this.entityNotification = entityNotification;
    }

    public List<Page> getValidPageList() {
        return validPageList;
    }

    public void setValidPageList(List<Page> validPageList) {
        this.validPageList = validPageList;
    }
}
