package com.agora.notification;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class EntityNotificationEntryWithCount {
    private Long newNotificationCount;
    private Long totalNotificationCount;
    private List<EntityNotificationEntry> entityNotificationEntryList;

    public Long getNewNotificationCount() {
        return newNotificationCount;
    }

    public void setNewNotificationCount(Long newNotificationCount) {
        this.newNotificationCount = newNotificationCount;
    }

    public Long getTotalNotificationCount() {
        return totalNotificationCount;
    }

    public void setTotalNotificationCount(Long totalNotificationCount) {
        this.totalNotificationCount = totalNotificationCount;
    }

    public List<EntityNotificationEntry> getEntityNotificationEntryList() {
        return entityNotificationEntryList;
    }

    public void setEntityNotificationEntryList(List<EntityNotificationEntry> entityNotificationEntryList) {
        this.entityNotificationEntryList = entityNotificationEntryList;
    }
    
}
