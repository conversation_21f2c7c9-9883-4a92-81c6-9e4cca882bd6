package com.agora.notification;

import com.agora.commons.CustomerCommons;
import com.agora.core.Defaults;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.CustomerDao;
import com.agora.dao.CustomerNotificationDao;
import com.agora.pojo.Customer;
import com.agora.pojo.CustomerNotification;
import com.agora.pojo.Location;
import com.agora.pojo.User;
import com.agora.pojo.types.ProfileType;
import com.agora.support.Geocoder;
import com.agora.util.ParamUtils;
import com.agora.util.PojoUtils;
import com.agora.util.RouteUtils;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class NotificationController {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationController.class.getName());

    public static TemplateViewRoute notification_add = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);      
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        Customer customer;
        if (user != null) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
            }
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        } else {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        attributes.put("customerEntry", CustomerCommons.toEntry(customer));
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        
        return Manager.render(Templates.NOTIFICATION_ADD, attributes, RouteUtils.pathType(request));
    }; 
    
    public static TemplateViewRoute notification_edit = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);      
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        Customer customer;
        if (user != null) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
            }
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        } else {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        attributes.put("customerEntry", CustomerCommons.toEntry(customer));
        
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        if (oid == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, language));
            return Manager.renderEmpty();
        }
        
        CustomerNotification customerNotification = CustomerNotificationDao.loadCustomerNotification(oid);
        attributes.put("customerNotification", customerNotification);
        
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        return Manager.render(Templates.NOTIFICATION_EDIT, attributes, RouteUtils.pathType(request));
    }; 

    public static Route notification_add_save = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow logged only users
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        // logged customer
        Customer customer = CustomerDao.loadCustomerByUserId(user.getId());

        
        // params
        Map<String, String> params = PojoUtils.paramsFromRequest(request);

        // merge
        CustomerNotification customerNotification = PojoUtils.createFromParams(params, CustomerNotification.class);
        if (StringUtils.isBlank(params.get("active"))) {
            customerNotification.setActive(false);
        }
        if (StringUtils.isBlank(params.get("emailActive"))) {
            customerNotification.setEmailActive(false);
        }
        if (isValidNotification(customerNotification)) {
            customerNotification.setUserId(user.getId());
            if (customer != null) {
                customerNotification.setCustomerId(customer.getId());
            }

            Location location = geocode(customerNotification);
            if (location != null) {
                customerNotification.setLat(location.getCoordinates()[0].toString());
                customerNotification.setLng(location.getCoordinates()[1].toString());
            }
            
            ObjectId customerNotificationId = CustomerNotificationDao.insertCustomerNotification(customerNotification);
                    
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Dati notifica non validi");
        }
        return "ok";
    };
    
    public static Route notification_edit_save = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow logged only users
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // params
        Map<String, String> params = PojoUtils.paramsFromRequest(request);
        
        ObjectId customerNotificationId = ParamUtils.toObjectId(request.queryParams("oid"));
        
        if (customerNotificationId != null) {
        // params
            CustomerNotification customerNotificationOld = CustomerNotificationDao.loadCustomerNotification(customerNotificationId);
            if (customerNotificationOld != null) {
                CustomerNotification customerNotification = (CustomerNotification) BeanUtils.cloneBean(customerNotificationOld);

                customerNotification = PojoUtils.mergeFromParams(params, customerNotification);
                if (StringUtils.isBlank(params.get("active"))) {
                    customerNotification.setActive(false);
                }
                if (StringUtils.isBlank(params.get("emailActive"))) {
                    customerNotification.setEmailActive(false);
                }
                if (isValidNotification(customerNotification)) {
                    if (StringUtils.isEmpty(customerNotification.getLat())
                            || !StringUtils.equals(customerNotificationOld.getCity(), customerNotification.getCity())
                            || !StringUtils.equals(customerNotificationOld.getAddress(), customerNotification.getAddress())) {
                        Location location = geocode(customerNotification);
                        if (location != null) {
                            customerNotification.setLat(location.getCoordinates()[0].toString());
                            customerNotification.setLng(location.getCoordinates()[1].toString());
                        }
                    }

                    CustomerNotificationDao.updateCustomerNotification(customerNotification);

                } else {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Dati notifica non validi");
                }
                return "ok";
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Dati notifica non validi");
            }
        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };
    
    public static Route notification_active = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow customer only users
        if ((user != null) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.customer.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }


        // customerNotificationId
        ObjectId customerNotificationId = ParamUtils.toObjectId(request.queryParams("customerNotificationId"));
        if (customerNotificationId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        Boolean active = BooleanUtils.toBoolean(request.queryParams("active"));
        CustomerNotificationDao.updateCustomerNotificationActive(customerNotificationId, active);
        
        return "ok";
    };
    
    public static Route notification_remove = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow customer only users
        if ((user != null) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.customer.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }


        // customerNotificationId
        ObjectId customerNotificationId = ParamUtils.toObjectId(request.queryParams("customerNotificationId"));
        if (customerNotificationId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        CustomerNotificationDao.updateCustomerNotificationCancelled(customerNotificationId, true);
        
        return "ok";
    };
        

//    public static TemplateViewRoute notification_detail = (Request request, Response response) -> {
//        // attributes
//        Map<String, Object> attributes = new HashMap<>();
//
//        // logged user
//        String token = Manager.getToken(request);
//        User user = Manager.getUser(token, response);
//        attributes.put("user", user);
//
//        // logged customer
//        Customer customer = null;
//        if (user != null) {
//            customer = CustomerDao.loadCustomerByUserId(user.getId());
//        }
//        attributes.put("customer", customer);
//        
//        // path
//        attributes.put("path", request.servletPath());
//
//        // public url
//        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
//        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));
//
//        // identifier
//        String identifier = request.params("identifier");
//        attributes.put("identifier", identifier);
//
//        // notification
//        Notification notification = null;
//        if (StringUtils.isNotBlank(identifier)) {
//            notification = NotificationDao.loadNotificationByIdentifier(identifier);
//        }
//        attributes.put("entry", NotificationCommons.toEntry(notification));
//
//        // pageFollow
//        NotificationFollower notificationFollow = null;
//        long followerCount = 0;
//        if (notification != null) {
//            try {
//                followerCount = NotificationFollowerDao.loadNotificationFollowerCount(notification.getId());
//            } catch (Exception ex) {
//                LOGGER.error("suppressed", ex);
//            }
//            attributes.put("followerCount", followerCount);
//        }
//        
//        if ((user != null) && (notification != null)) {
//            if ((user.getId() != null) && (notification.getId() != null)) {
//                try {
//                    notificationFollow = NotificationFollowerDao.loadNotificationFollowerByUserAndNotification(user.getId(), notification.getId());
//                } catch (Exception ex) {
//                    LOGGER.error("suppressed", ex);
//                }
//                attributes.put("notificationFollow", notificationFollow);
//            }
//        }
//        return Manager.render(Templates.NOTIFICATION_DETAIL, attributes, RouteUtils.pathType(request));
//    };
//    
    ////////////
    // internals
    
    private static boolean isValidNotification(CustomerNotification entity) {
        boolean valid = (entity != null) 
                ;

        if (!valid) {
            if (entity != null) {
                LOGGER.warn(
                        "notification validation problem:\n" 
                );
            } else {
                LOGGER.warn(
                        "notification validation problem:\n" +
                        "- empty"
                );
            }
        }

        return valid;
    }

        
    public static Location geocode(CustomerNotification customerNotification) {
        Location location = null;

        if (customerNotification != null) {
            if (StringUtils.isNotBlank(customerNotification.getCity())
                    && StringUtils.isNotBlank(customerNotification.getAddress())
                    && StringUtils.isNotBlank(customerNotification.getProvinceCode())
                    && StringUtils.isNotBlank(customerNotification.getPostalCode())) {

                String address = customerNotification.getAddress() + ", " + customerNotification.getPostalCode() + " " + customerNotification.getCity() + ", " + customerNotification.getProvinceCode();
                location = Geocoder.geocode(address);
            }
        }

        return location;
    }
}