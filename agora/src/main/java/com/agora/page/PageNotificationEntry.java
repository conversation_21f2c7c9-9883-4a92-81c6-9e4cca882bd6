package com.agora.page;

import com.agora.pojo.Page;
import com.agora.pojo.PageNotification;
import com.agora.pojo.User;

/**
 *
 * <AUTHOR>
 */
public class PageNotificationEntry {

    private User user;
    private Page page;
    private PageNotification pageNotification;
    private Long followerCount;

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public PageNotification getPageNotification() {
        return pageNotification;
    }

    public void setPageNotification(PageNotification pageNotification) {
        this.pageNotification = pageNotification;
    }

    public Long getFollowerCount() {
        return followerCount;
    }

    public void setFollowerCount(Long followerCount) {
        this.followerCount = followerCount;
    }

}
