package com.agora.page;

import com.agora.pojo.Page;
import com.agora.pojo.PageFollower;
import com.agora.pojo.User;

/**
 *
 * <AUTHOR>
 */
public class PageFollowerEntry {

    private User user;
    private Page page;
    private PageFollower pageFollower;
    private Long followerCount;

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public PageFollower getPageFollower() {
        return pageFollower;
    }

    public void setPageFollower(PageFollower pageFollower) {
        this.pageFollower = pageFollower;
    }

    public Long getFollowerCount() {
        return followerCount;
    }

    public void setFollowerCount(Long followerCount) {
        this.followerCount = followerCount;
    }

}
