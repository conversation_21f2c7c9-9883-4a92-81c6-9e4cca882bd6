package com.agora.page;

import com.agora.commons.CustomerCommons;
import com.agora.commons.EventCommons;
import com.agora.commons.NotificationCommons;
import com.agora.commons.PageCommons;
import com.agora.commons.QrcodeCommons;
import com.agora.core.Defaults;
import com.agora.core.MailTemplates;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.CustomerDao;
import com.agora.dao.EventDao;
import com.agora.dao.EventRequestDao;
import com.agora.pojo.Event;
import com.agora.dao.FirmDao;
import com.agora.dao.PageClaimDao;
import com.agora.dao.PageDao;
import com.agora.dao.PageFollowerDao;
import com.agora.dao.PageNotificationDao;
import com.agora.dao.PageReportDao;
import com.agora.dao.SmtpDao;
import com.agora.dao.UserDao;
import com.agora.message.Attachment;
import com.agora.message.MessageSender;
import com.agora.message.SmtpService;
import com.agora.pojo.Customer;
import com.agora.pojo.Event;
import com.agora.pojo.EventRequest;
import com.agora.pojo.Firm;
import com.agora.pojo.Location;
import com.agora.pojo.Page;
import com.agora.pojo.PageClaim;
import com.agora.pojo.PageFollower;
import com.agora.pojo.PageNotification;
import com.agora.pojo.PageReport;
import com.agora.pojo.Smtp;
import com.agora.pojo.User;
import com.agora.pojo.types.PageStatusType;
import com.agora.pojo.types.ProfileType;
import com.agora.support.Geocoder;
import com.agora.support.file.posted.PostedFile;
import com.agora.support.image.slim.SlimImage;
import com.agora.util.EnvironmentUtils;
import com.agora.util.ParamUtils;
import com.agora.util.PojoUtils;
import com.agora.util.RouteUtils;
import com.agora.util.TemporaryUtils;
import com.agora.util.TimeUtils;
import com.github.slugify.Slugify;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URIBuilder;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class PageController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PageController.class.getName());

    public static TemplateViewRoute page = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        if (user != null) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
            }
            Customer customer = CustomerDao.loadCustomerByUserId(user.getId());
            attributes.put("customerEntry", CustomerCommons.toEntry(customer));
        }

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        // identifier
        String qrcode = request.queryParams("qrcode");
        attributes.put("qrcode", qrcode);

        // post
        Page page = null;
        if (StringUtils.isNotBlank(qrcode)) {
            page = PageDao.loadPageByQrCode(qrcode);
        }

        response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.PAGE_BASE + "/" + page.getIdentifier() + "?qrcode=" + qrcode, Defaults.LANGUAGE));

        return null;

    };

    public static TemplateViewRoute page_detail = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer;
        if (user != null) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
            }
            customer = CustomerDao.loadCustomerByUserId(user.getId());
            attributes.put("customerEntry", CustomerCommons.toEntry(customer));
        }

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        // identifier
        String identifier = request.params("identifier");
        attributes.put("identifier", identifier);

        // post
        Page pageDb = null;
        if (StringUtils.isNotBlank(identifier)) {
            pageDb = PageDao.loadPageByIdentifier(identifier);
        }

        if (pageDb == null) {
            response.status(HttpStatus.NOT_FOUND_404);
        }

        // localized versions of current path
        // rewrite paths
        Map<String, String> paths = Paths.localizedPathsMap(Paths.PAGE_BASE);
        for (Map.Entry<String, String> entry : paths.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            String updatedValue = value + "/" + pageDb.getIdentifier();
            paths.put(key, updatedValue);
        }

        attributes.put("paths", paths);
        if (user != null) {
            EventRequest eventRequest = EventRequestDao.loadEventRequestByPageIdAndUserId(pageDb.getId(), user.getId());
            attributes.put("eventRequest", eventRequest);

            PageReport pageReport = PageReportDao.loadPageReportByPageIdAndUserId(pageDb.getId(), user.getId());
            attributes.put("pageReport", pageReport);
        }

        // pageFollow
        PageFollower pageFollow = null;
        PageNotification pageNotification = null;
        long followerCount = 0;
        if (pageDb != null) {
            try {
                followerCount = PageFollowerDao.loadPageFollowerCount(pageDb.getId());
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            attributes.put("followerCount", followerCount);
        }

        if ((user != null) && (pageDb != null)) {
            if ((user.getId() != null) && (pageDb.getId() != null)) {
                try {
                    pageFollow = PageFollowerDao.loadPageFollowerByUserAndPage(user.getId(), pageDb.getId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                attributes.put("pageFollow", pageFollow);
            }
        }

        if ((user != null) && (pageDb != null)) {
            if ((user.getId() != null) && (pageDb.getId() != null)) {
                try {
                    pageNotification = PageNotificationDao.loadPageNotificationByUserAndPage(user.getId(), pageDb.getId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                attributes.put("pageNotification", pageNotification);
            }
        }

        List<Event> futureEventList = null;
        if (pageDb.getId() != null) {
            try {
                futureEventList = EventDao.loadFutureEventListByPageId(pageDb.getId());
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }
        if (futureEventList != null) {
            Gson gson = new Gson();
            String futureEventListJson = gson.toJson(futureEventList);

            attributes.put("futureEventListJson", futureEventListJson);

        }

        int pagination = 12;
        int skip = NumberUtils.toInt(request.queryParams("skip"));
        int limit = NumberUtils.toInt(request.queryParams("limit"));
        if (limit <= 0) {
            limit = pagination;
        }
        attributes.put("skip", skip);
        attributes.put("limit", limit);
        attributes.put("pagination", pagination);

        List<Event> eventList = null;
        if (pageDb.getId() != null) {
            try {
                eventList = EventDao.loadEventListByPageId(pageDb.getId(), skip, limit + 1);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }

        // loadmore
        boolean loadmore = false;
        if ((eventList != null) && (!eventList.isEmpty())) {
            loadmore = eventList.size() > limit;
        }
        if ((eventList != null) && (!eventList.isEmpty())) {
            if (eventList.size() > limit) {
                eventList = eventList.subList(0, limit);
            }
        }

        attributes.put("eventEntryList", EventCommons.toEntries(eventList));

        attributes.put("loadmore", loadmore);

        URIBuilder uriBuilder;
        try {
            if (RouteUtils.publicQueryString(request) != null) {
                uriBuilder = new URIBuilder(RouteUtils.publicUrl(request) + "?" + RouteUtils.publicQueryString(request));
                List<NameValuePair> queryParameters = uriBuilder.getQueryParams();
                for (Iterator<NameValuePair> queryParameterItr = queryParameters.iterator(); queryParameterItr.hasNext();) {
                    NameValuePair queryParameter = queryParameterItr.next();
                    if (queryParameter.getName().equals("skip")) {
                        queryParameterItr.remove();
                    }
                    if (queryParameter.getName().equals("tab")) {
                        queryParameterItr.remove();
                    }
                    if (queryParameter.getName().equals("limit")) {
                        queryParameterItr.remove();
                    }
                }
                uriBuilder.setParameters(queryParameters);

                String resultUrl = uriBuilder.build().toString();
                attributes.put("resultUrl", resultUrl);
            } else {
                attributes.put("resultUrl", RouteUtils.publicUrl(request));
            }
        } catch (URISyntaxException ex) {
            //
        }
        attributes.put("eventEntryList", EventCommons.toEntries(eventList));

        attributes.put("pageDb", pageDb);

        // firm configuration for truncation settings
        try {
            Firm firm = FirmDao.loadFirm();
            attributes.put("firm", firm);
        } catch (Exception ex) {
            LOGGER.error("Error loading firm configuration", ex);
        }

        return Manager.render(Templates.PAGE_DETAIL, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute page_diff = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        if (user != null) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
            }

            List<Page> duplicatePages = new ArrayList<>();
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) ||
                StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
                // verifico se ho passato 2 pagine a mano
                String firstPageId = request.queryParams("firstPageId");
                String secondPageId = request.queryParams("secondPageId");
                if (firstPageId != null && secondPageId != null) {
                    if (firstPageId.equals(secondPageId)) {
                        throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Non posso unire due pagine uguali");
                    }
                    Page firstPage = PageDao.loadPage(ParamUtils.toObjectId(firstPageId));
                    Page secondPage = PageDao.loadPage(ParamUtils.toObjectId(secondPageId));
                    duplicatePages.add(firstPage);
                    duplicatePages.add(secondPage);
                }
            }

            // verifico se ci sono pagine duplicate
            if (duplicatePages.isEmpty()) {
                duplicatePages = PageDao.loadDuplicatePages(user.getId());
                if (duplicatePages.isEmpty()) {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Non ci sono pagine duplicate");
                }
            }

            if (duplicatePages.size() >= 2 && duplicatePages.size() % 2 == 0) {
                // ci sono almeno 2 pagine duplicate
                attributes.put("firstPage", duplicatePages.get(0));
                attributes.put("secondPage", duplicatePages.get(1));
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Non ci sono abbastanza pagine duplicate");
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Errore inaspettato");
        }

        return Manager.render(Templates.PAGE_DIFF, attributes, RouteUtils.pathType(request));
    };

    public static Route page_diff_merge = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow logged only users
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // params
        List<SlimImage> slims = new ArrayList<>();
        List<PostedFile> files = new ArrayList<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, slims, files);

        // logged customer
        if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // verifico se ci sono pagine duplicate
        List<Page> duplicatePages = PageDao.loadDuplicatePages(user.getId());
        if (duplicatePages.isEmpty() && !params.containsKey("firstPageId") && !params.containsKey("secondPageId")) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Non ci sono pagine duplicate");
        }

        if ((duplicatePages.size() >= 2 && duplicatePages.size() % 2 == 0) || (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()))) {
            if (params.containsKey("firstPageId") && params.containsKey("secondPageId")) {
                Page firstPage = PageDao.loadPage(ParamUtils.toObjectId(params.get("firstPageId")));
                Page secondPage = PageDao.loadPage(ParamUtils.toObjectId(params.get("secondPageId")));

                if (firstPage != null) {
                    if (secondPage != null) {
                        if (firstPage.getId().equals(secondPage.getId())) {
                            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Non posso unire due pagine uguali");
                        }

                        // merge campi
                        if (params.containsKey("profileImageId")) {
                            int index = Integer.parseInt(params.get("profileImageId"));
                            if (index == 2) {
                                firstPage.setProfileImageId(secondPage.getProfileImageId());
                            }
                        }
                        if (params.containsKey("coverImageId")) {
                            int index = Integer.parseInt(params.get("coverImageId"));
                            if (index == 2) {
                                firstPage.setCoverImageId(secondPage.getCoverImageId());
                            }
                        }
                        if (params.containsKey("pageType")) {
                            int index = Integer.parseInt(params.get("pageType"));
                            if (index == 2) {
                                firstPage.setPageType(secondPage.getPageType());
                            }
                        }
                        if (params.containsKey("tags")) {
                            int index = Integer.parseInt(params.get("tags"));
                            if (index == 2) {
                                firstPage.setTags(secondPage.getTags());
                            }
                        }
                        if (params.containsKey("description")) {
                            int index = Integer.parseInt(params.get("description"));
                            if (index == 2) {
                                firstPage.setDescription(secondPage.getDescription());
                            }
                        }
                        if (params.containsKey("shortDescription")) {
                            int index = Integer.parseInt(params.get("shortDescription"));
                            if (index == 2) {
                                firstPage.setShortDescription(secondPage.getShortDescription());
                            }
                        }
                        if (params.containsKey("fulladdress")) {
                            int index = Integer.parseInt(params.get("fulladdress"));
                            if (index == 2) {
                                firstPage.setFulladdress(secondPage.getFulladdress());
                            }
                        }
                        if (params.containsKey("countryCode")) {
                            int index = Integer.parseInt(params.get("countryCode"));
                            if (index == 2) {
                                firstPage.setCountryCode(secondPage.getCountryCode());
                            }
                        }
                        if (params.containsKey("address")) {
                            int index = Integer.parseInt(params.get("address"));
                            if (index == 2) {
                                firstPage.setAddress(secondPage.getAddress());
                            }
                        }
                        if (params.containsKey("city")) {
                            int index = Integer.parseInt(params.get("city"));
                            if (index == 2) {
                                firstPage.setCity(secondPage.getCity());
                            }
                        }
                        if (params.containsKey("postalCode")) {
                            int index = Integer.parseInt(params.get("postalCode"));
                            if (index == 2) {
                                firstPage.setPostalCode(secondPage.getPostalCode());
                            }
                        }
                        if (params.containsKey("provinceCode")) {
                            int index = Integer.parseInt(params.get("provinceCode"));
                            if (index == 2) {
                                firstPage.setProvinceCode(secondPage.getProvinceCode());
                            }
                        }
                        if (params.containsKey("extraAddress")) {
                            int index = Integer.parseInt(params.get("extraAddress"));
                            if (index == 2) {
                                firstPage.setExtraAddress(secondPage.getExtraAddress());
                            }
                        }
                        if (params.containsKey("showFollowers")) {
                            int index = Integer.parseInt(params.get("showFollowers"));
                            if (index == 2) {
                                firstPage.setShowFollowers(secondPage.getShowFollowers());
                            }
                        }
                        if (params.containsKey("websiteUrl")) {
                            int index = Integer.parseInt(params.get("websiteUrl"));
                            if (index == 2) {
                                firstPage.setWebsiteUrl(secondPage.getWebsiteUrl());
                            }
                        }
                        if (params.containsKey("facebookUrl")) {
                            int index = Integer.parseInt(params.get("facebookUrl"));
                            if (index == 2) {
                                firstPage.setFacebookUrl(secondPage.getFacebookUrl());
                            }
                        }
                        if (params.containsKey("twitterUrl")) {
                            int index = Integer.parseInt(params.get("twitterUrl"));
                            if (index == 2) {
                                firstPage.setTwitterUrl(secondPage.getTwitterUrl());
                            }
                        }
                        if (params.containsKey("instagramUrl")) {
                            int index = Integer.parseInt(params.get("instagramUrl"));
                            if (index == 2) {
                                firstPage.setInstagramUrl(secondPage.getInstagramUrl());
                            }
                        }
                        if (params.containsKey("linkedinUrl")) {
                            int index = Integer.parseInt(params.get("linkedinUrl"));
                            if (index == 2) {
                                firstPage.setLinkedinUrl(secondPage.getLinkedinUrl());
                            }
                        }
                        if (params.containsKey("youtubeUrl")) {
                            int index = Integer.parseInt(params.get("youtubeUrl"));
                            if (index == 2) {
                                firstPage.setYoutubeUrl(secondPage.getYoutubeUrl());
                            }
                        }
                        if (params.containsKey("redditUrl")) {
                            int index = Integer.parseInt(params.get("redditUrl"));
                            if (index == 2) {
                                firstPage.setRedditUrl(secondPage.getRedditUrl());
                            }
                        }
                        if (params.containsKey("mediumUrl")) {
                            int index = Integer.parseInt(params.get("mediumUrl"));
                            if (index == 2) {
                                firstPage.setMediumUrl(secondPage.getMediumUrl());
                            }
                        }
                        if (params.containsKey("tiktok")) {
                            int index = Integer.parseInt(params.get("tiktok"));
                            if (index == 2) {
                                firstPage.setTiktok(secondPage.getTiktok());
                            }
                        }
                        if (params.containsKey("spotifiyUrl")) {
                            int index = Integer.parseInt(params.get("spotifiyUrl"));
                            if (index == 2) {
                                firstPage.setSpotifiyUrl(secondPage.getSpotifiyUrl());
                            }
                        }
                        if (params.containsKey("pageTagging")) {
                            int index = Integer.parseInt(params.get("pageTagging"));
                            if (index == 2) {
                                firstPage.setPageTagging(secondPage.getPageTagging());
                            }
                        }
                        if (params.containsKey("identifier")) {
                            int index = Integer.parseInt(params.get("identifier"));
                            if (index == 2) {
                                firstPage.setIdentifier(secondPage.getIdentifier());
                            }
                        }

                        PageDao.updatePage(firstPage);

                        // SPOSTO FOLLOWER, EVENTI E NOTIFICHE
                        List<PageFollower> pageFollowersToUpdate = PageFollowerDao.loadPageFollowerListByPage(secondPage.getId());
                        if (pageFollowersToUpdate != null && !pageFollowersToUpdate.isEmpty()) {
                            for (PageFollower pageFollower : pageFollowersToUpdate) {
                                PageFollower pageFollowerDb = PageFollowerDao.loadPageFollowerByUserAndPage(user.getId(), firstPage.getId());
                                if (pageFollowerDb != null) {
                                    PageFollowerDao.updatePageFollowerCancelled(pageFollower.getId(), true);
                                } else {
                                    pageFollower.setPageId(firstPage.getId());
                                    PageFollowerDao.updatePageFollower(pageFollower);
                                }
                            }
                        }

                        List<Event> pageEventsToUpdate = EventDao.loadEventListByPageId(secondPage.getId());
                        if (pageEventsToUpdate != null && !pageEventsToUpdate.isEmpty()) {
                            for (Event event : pageEventsToUpdate) {
                                if (event.getPageIds() == null) {
                                    event.setPageIds(new ArrayList<>());
                                }
                                event.getPageIds().remove(secondPage.getId());
                                if (!event.getPageIds().contains(firstPage.getId())) {
                                    event.getPageIds().add(firstPage.getId());
                                }
                                EventDao.updateEvent(event);
                            }
                        }

                        List<PageNotification> pageNotificationsToUpdate = PageNotificationDao.loadPageNotificationListByPage(secondPage.getId());
                        if (pageNotificationsToUpdate != null && !pageNotificationsToUpdate.isEmpty()) {
                            for (PageNotification pageNotification : pageNotificationsToUpdate) {
                                PageNotification pageNotificationDb = PageNotificationDao.loadPageNotificationByUserAndPage(user.getId(), firstPage.getId());
                                if (pageNotificationDb != null) {
                                    PageNotificationDao.updatePageNotificationCancelled(pageNotification.getId(), true);
                                } else {
                                    pageNotification.setPageId(firstPage.getId());
                                    PageNotificationDao.updatePageNotification(pageNotification);
                                }
                            }
                        }

                        // aggiorno qua per evitare che ci siano query che controllano se la pagina non sia cancellata
                        PageDao.updatePageCancelled(secondPage.getId(), true);
                        return firstPage.getIdentifier();
                    } else {
                        throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Errore inaspettato (0x00C)");
                    }
                } else {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Errore inaspettato (0x00B)");
                }
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Errore inaspettato (0x00A)");
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Non ci sono abbastanza pagine duplicate");
        }

    };
    
    public static TemplateViewRoute page_claim = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer;
        if (user != null) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
            }
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        } else {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        // identifier
        String identifier = request.params("identifier");
        attributes.put("identifier", identifier);

        // post
        Page page = null;
        if (StringUtils.isNotBlank(identifier)) {
            page = PageDao.loadPageByIdentifier(identifier);
        }

        if (page == null) {
            response.status(HttpStatus.NOT_FOUND_404);
        }
        attributes.put("page", page);

        return Manager.render(Templates.PAGE_CLAIM, attributes, RouteUtils.pathType(request));
    };

    public static Route page_claim_send = (Request request, Response response) -> {
        // params
        List<FileItem> fields = new ServletFileUpload(new DiskFileItemFactory()).parseRequest(request.raw());

        String name = null;
        String lastname = null;
        String phone = null;
        String email = null;
        String page = null;
        String message = null;
        String recaptchaToken = null;

        List<Attachment> attachments = new ArrayList<>();
        Map<String, Object> messageFields = new HashMap<>();

        PageClaim pageClaim = new PageClaim();

        for (FileItem field : fields) {
            if (field.isFormField()) {
                switch (field.getFieldName()) {
                    case "name":
                        name = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        messageFields.put("name", name);
                        pageClaim.setName(name);
                        break;
                    case "lastname":
                        lastname = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        messageFields.put("lastname", lastname);
                        pageClaim.setLastname(lastname);
                        break;
                    case "page":
                        page = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        messageFields.put("page", page);
                        pageClaim.setPage(page);
                        break;
                    case "message":
                        message = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        messageFields.put("message", message);
                        pageClaim.setMessage(message);
                        break;
                    case "g-recaptcha-response":
                        recaptchaToken = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "email":
                        email = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        messageFields.put("email", email);
                        pageClaim.setEmail(email);
                        break;
                    case "phone":
                        phone = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        messageFields.put("phone", phone);
                        pageClaim.setPhoneNumber(phone);
                        break;
                    default:
                        LOGGER.warn("received unknown field " + field.getFieldName());
                        break;
                }
            } else {
                // posted files (skip empty)
                if (field.getSize() > 0L) {
                    String nameFld = FilenameUtils.getName(field.getName());
                    String extension = FilenameUtils.getExtension(field.getName());
                    try ( InputStream content = field.getInputStream()) {
                        String filename = TemporaryUtils.saveTemporaryFile(IOUtils.toByteArray(content), extension);
                        attachments.add(new Attachment(nameFld, filename, field.getContentType()));
                    }
                }
            }
        }
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // language
        String language = RouteUtils.language(request);

        if (!MessageSender.validEmailAddress(email)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        PageClaimDao.insertPageClaim(pageClaim);

        // smtp
        SmtpService smtp = null;
        Smtp tmp = SmtpDao.loadSmtp();
        if (tmp != null) {
            smtp = new SmtpService(
                    tmp.getHostname(),
                    tmp.getPort(),
                    tmp.getAuthentication(),
                    tmp.getUsername(),
                    tmp.getPassword(),
                    tmp.getEncryption(),
                    tmp.getStartTls(),
                    tmp.getApikey(),
                    tmp.getSender()
            );
        }
        if (smtp != null) {

            String recipient = "<EMAIL>";

            // local safety!
            if (EnvironmentUtils.isLocal()) {
                LOGGER.info("hiding original email address of " + email + " due to local environment");
                recipient = EnvironmentUtils.localEmail();
            }

            boolean done = Manager.sendMail(smtp,
                    Manager.smtpSender,
                    smtp.getSender(),
                    recipient,
                    MailTemplates.CONTACT_PAGE_CLAIM,
                    messageFields,
                    RouteUtils.pathType(request),
                    attachments,
                    null);
            // ignoring send results...
            if (!done) {
                Spark.halt(HttpStatus.BAD_REQUEST_400, "Errore durante l'invio email");
            }
        } else {
            Spark.halt(HttpStatus.BAD_REQUEST_400, "Errore durante l'invio email");
        }

        return "1";
    };

    public static TemplateViewRoute page_add = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        Customer customer;
        if (user != null) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
            }
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        } else {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        attributes.put("user", user);

        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        String pageType = ParamUtils.emptyToNull(request.queryParams("pageType"));
        attributes.put("pageType", pageType);
        String name = ParamUtils.emptyToNull(request.queryParams("name"));
        attributes.put("name", name);

        return Manager.render(Templates.PAGE_ADD, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute page_edit = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer;
        if (user != null) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
            }
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        } else {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        if (oid == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        Page page = PageDao.loadPage(oid);
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            if (!page.getOwnerId().equals(user.getId())) {
                response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            }
        }
        attributes.put("page", page);

        String pageType = page.getPageType();

        if (ParamUtils.emptyToNull(request.queryParams("pageType")) != null) {
            pageType = request.queryParams("pageType");
        }
        attributes.put("pageType", pageType);

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        attributes.put("backUrl", request.queryParams("backUrl"));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        // firm configuration for truncation settings
        try {
            Firm firm = FirmDao.loadFirm();
            attributes.put("firm", firm);
        } catch (Exception ex) {
            LOGGER.error("Error loading firm configuration", ex);
        }

        return Manager.render(Templates.PAGE_EDIT, attributes, RouteUtils.pathType(request));
    };

    public static Route page_add_save = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow logged only users
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // params
        List<SlimImage> slims = new ArrayList<>();
        List<PostedFile> files = new ArrayList<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, slims, files);

        // merge
        Page page = PojoUtils.createFromParams(params, Page.class);
        if (StringUtils.isBlank(params.get("showFollowers"))) {
            page.setShowFollowers(false);
        }
        if (page.getIsUserPage() == null) {
            page.setIsUserPage(false);
        }
        if (StringUtils.isNotBlank(page.getDescription())) {
            page.setDescription(StringUtils.trim(page.getDescription()));
        }

        if (PageCommons.isValidPage(page)) {
            page.setStatus(PageStatusType.published.toString());
            page.setUserId(user.getId());
            page.setOwnerId(user.getId());
            Slugify slg = new Slugify();
            String identifier = page.getName();
            Page exist = PageDao.loadPageByIdentifier(slg.slugify(identifier));
            if (exist != null) {
                identifier = page.getName() + "-" + RouteUtils.generateIdentifier();
            }
            page.setIdentifier(slg.slugify(identifier));

            Location location = geocode(page);
            if (location != null) {
                page.setLat(location.getCoordinates()[0].toString());
                page.setLng(location.getCoordinates()[1].toString());
            }

            ObjectId pageId = PageDao.insertPage(page);

            // image
            String slim = params.get("uploaded-profile");
            if (StringUtils.isNotBlank(slim)) {
                SlimImage uploaded = null;
                try {
                    uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if ((uploaded != null)) {
                    if (uploaded.getBytes() != null) {
                        PageDao.updateProfileImage(user.getUsername(), pageId, uploaded);
                    }
                }
            }

            // logo
            String slimlogo = params.get("uploaded-cover");
            if (StringUtils.isNotBlank(slimlogo)) {
                SlimImage uploaded = null;
                try {
                    uploaded = Manager.deserializeFromJson(slimlogo, SlimImage.class);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if ((uploaded != null)) {
                    if (uploaded.getBytes() != null) {
                        PageDao.updateCoverImage(user.getUsername(), pageId, uploaded);
                    }
                }
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Dati pagina non validi");
        }
        return "ok";
    };

    public static Route page_edit_save = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow logged only users
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // params
        List<SlimImage> slims = new ArrayList<>();
        List<PostedFile> files = new ArrayList<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, slims, files);

        ObjectId pageId = ParamUtils.toObjectId(request.queryParams("oid"));

        if (pageId != null) {
            // params
            Page pageOld = PageDao.loadPage(pageId);
            if (pageOld != null) {
                Page tmpPage = (Page) BeanUtils.cloneBean(pageOld);

                tmpPage = PojoUtils.mergeFromParams(params, tmpPage);
                if (StringUtils.isBlank(params.get("showFollowers"))) {
                    tmpPage.setShowFollowers(false);
                }

                if (!StringUtils.equalsIgnoreCase(pageOld.getIdentifier(), tmpPage.getIdentifier())) {
                    Page exist = PageDao.loadPageByIdentifier(tmpPage.getIdentifier());
                    if (exist != null) {
                        throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Identificativo già presente");
                    }
                }
                if (StringUtils.isNotBlank(tmpPage.getDescription())) {
                    tmpPage.setDescription(StringUtils.trim(tmpPage.getDescription()));
                }

                if (PageCommons.isValidPage(tmpPage)) {
                    if (StringUtils.isEmpty(tmpPage.getLat())
                            || !StringUtils.equals(pageOld.getCity(), tmpPage.getCity())
                            || !StringUtils.equals(pageOld.getAddress(), tmpPage.getAddress())) {
                        Location location = geocode(tmpPage);
                        if (location != null) {
                            tmpPage.setLat(location.getCoordinates()[0].toString());
                            tmpPage.setLng(location.getCoordinates()[1].toString());
                        }
                    }

                    PageDao.updatePage(tmpPage);

                    // image
                    String slim = params.get("uploaded-profile");
                    if (StringUtils.isNotBlank(slim)) {
                        SlimImage uploaded = null;
                        try {
                            uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                        if (uploaded != null) {
                            if (uploaded.getBytes() != null) {
                                PageDao.updateProfileImage(user.getUsername(), pageId, uploaded);
                            }
                        } else {
                            PageDao.removeProfileImage(pageId);
                        }
                    } else {
                        PageDao.removeProfileImage(pageId);
                    }

                    // logo
                    String slimlogo = params.get("uploaded-cover");
                    if (StringUtils.isNotBlank(slimlogo)) {
                        SlimImage uploaded = null;
                        try {
                            uploaded = Manager.deserializeFromJson(slimlogo, SlimImage.class);
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                        if (uploaded != null) {
                            if (uploaded.getBytes() != null) {
                                PageDao.updateCoverImage(user.getUsername(), pageId, uploaded);
                            }
                        } else {
                            PageDao.removeCoverImage(pageId);
                        }
                    } else {
                        PageDao.removeCoverImage(pageId);
                    }

                    //se ho modificato owner sposto anche tutti gli eventi
                    if (!pageOld.getOwnerId().equals(tmpPage.getOwnerId())) {
                        EventDao.changeEventOwnerByPageId(tmpPage.getId(), pageOld.getOwnerId(), tmpPage.getOwnerId());
                    }

                    // Auto-approve pending events when page changes from "owner" to "everyone"
                    if (StringUtils.equalsIgnoreCase(pageOld.getPageTagging(), "owner")
                        && StringUtils.equalsIgnoreCase(tmpPage.getPageTagging(), "everyone")) {
                        autoApprovePendingEvents(tmpPage.getId());
                    }
                } else {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Dati pagina non validi");
                }
                return tmpPage.getIdentifier();
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Dati pagina non validi");
            }
        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static Route page_follower_toggle = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow customer only users
        if ((user != null)
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.customer.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // productId
        ObjectId pageId = ParamUtils.toObjectId(request.queryParams("pageId"));
        if (pageId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // product
        Page page = null;
        try {
            page = PageDao.loadPage(pageId);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        if (page == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // product wishlist
        PageFollower pageFollower = null;
        try {
            pageFollower = PageFollowerDao.loadPageFollowerByUserAndPage(user.getId(), pageId);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }

        if (pageFollower == null) {

            // add to wishlist
            pageFollower = new PageFollower();
            pageFollower.setUserId(user.getId());
            pageFollower.setPageId(page.getId());

            PageFollowerDao.insertPageFollower(pageFollower);

        } else {

            // remove from wishlist
            PageFollowerDao.updatePageFollowerCancelled(pageFollower.getId(), true);

        }

        return "ok";
    };

    public static Route page_notification_toggle = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow customer only users
        if ((user != null)
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.customer.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // productId
        ObjectId pageId = ParamUtils.toObjectId(request.queryParams("pageId"));
        if (pageId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // product
        Page page = null;
        try {
            page = PageDao.loadPage(pageId);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        if (page == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // product wishlist
        PageNotification pageNotification = null;
        try {
            pageNotification = PageNotificationDao.loadPageNotificationByUserAndPage(user.getId(), pageId);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }

        if (pageNotification == null) {

            // add to wishlist
            pageNotification = new PageNotification();
            pageNotification.setUserId(user.getId());
            pageNotification.setPageId(page.getId());

            PageNotificationDao.insertPageNotification(pageNotification);

        } else {

            // remove from wishlist
            PageNotificationDao.updatePageNotificationCancelled(pageNotification.getId(), true);

        }

        return "ok";
    };

    public static Route page_remove = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow customer only users
        if ((user != null)
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.customer.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // productId
        ObjectId pageId = ParamUtils.toObjectId(request.queryParams("pageId"));
        if (pageId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // product
        Page page = null;
        try {
            page = PageDao.loadPage(pageId);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        if (page == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        if (!page.getOwnerId().equals(user.getId())) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        PageDao.updatePageCancelled(pageId, true);

        PageFollowerDao.updatePageFollowerListCancelledByPageId(pageId, true);

        return "ok";
    };

    public static Route page_request_event = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow logged only users
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        Map<String, String> params = PojoUtils.paramsFromRequest(request);

        ObjectId pageId = ParamUtils.toObjectId(params.get("pageId"));

        if (pageId != null) {
            EventRequest eventRequest = PojoUtils.createFromParams(params, EventRequest.class);
            eventRequest.setUserId(user.getId());
            EventRequestDao.insertEventRequest(eventRequest);
            return "ok";

        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static Route page_request_event_remove = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow logged only users
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // productId
        ObjectId pageId = ParamUtils.toObjectId(request.queryParams("pageId"));
        if (pageId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // product
        Page page = null;
        try {
            page = PageDao.loadPage(pageId);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        if (page == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        EventRequest eventRequest = EventRequestDao.loadEventRequestByPageIdAndUserId(pageId, user.getId());
        if (eventRequest != null) {
            eventRequest.setCancelled(true);
            EventRequestDao.updateEventRequest(eventRequest);
            return "ok";
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

    };

    public static Route page_report_send = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        // allow logged only users
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        Map<String, String> params = PojoUtils.paramsFromRequest(request);

        ObjectId pageId = ParamUtils.toObjectId(params.get("pageId"));

        if (pageId != null) {
            PageReport pageReport = PojoUtils.createFromParams(params, PageReport.class);
            pageReport.setUserId(user.getId());
            PageReportDao.insertPageReport(pageReport);
            return "ok";

        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static TemplateViewRoute page_iframe = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer;
        if (user != null) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
            }
            customer = CustomerDao.loadCustomerByUserId(user.getId());
            attributes.put("customerEntry", CustomerCommons.toEntry(customer));
        }

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        // identifier
        String identifier = request.params("identifier");
        attributes.put("identifier", identifier);

        // post
        Page pageDb = null;
        if (StringUtils.isNotBlank(identifier)) {
            pageDb = PageDao.loadPageByIdentifier(identifier);
        }

        if (pageDb == null) {
            response.status(HttpStatus.NOT_FOUND_404);
        }

        // localized versions of current path
        // rewrite paths
        Map<String, String> paths = Paths.localizedPathsMap(Paths.PAGE_BASE);
        for (Map.Entry<String, String> entry : paths.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            String updatedValue = value + "/" + pageDb.getIdentifier();
            paths.put(key, updatedValue);
        }

        attributes.put("paths", paths);
        if (user != null) {
            EventRequest eventRequest = EventRequestDao.loadEventRequestByPageIdAndUserId(pageDb.getId(), user.getId());
            attributes.put("eventRequest", eventRequest);

            PageReport pageReport = PageReportDao.loadPageReportByPageIdAndUserId(pageDb.getId(), user.getId());
            attributes.put("pageReport", pageReport);
        }

        // pageFollow
        PageFollower pageFollow = null;
        PageNotification pageNotification = null;
        long followerCount = 0;
        if (pageDb != null) {
            try {
                followerCount = PageFollowerDao.loadPageFollowerCount(pageDb.getId());
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            attributes.put("followerCount", followerCount);
        }

        if ((user != null) && (pageDb != null)) {
            if ((user.getId() != null) && (pageDb.getId() != null)) {
                try {
                    pageFollow = PageFollowerDao.loadPageFollowerByUserAndPage(user.getId(), pageDb.getId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                attributes.put("pageFollow", pageFollow);
            }
        }

        if ((user != null) && (pageDb != null)) {
            if ((user.getId() != null) && (pageDb.getId() != null)) {
                try {
                    pageNotification = PageNotificationDao.loadPageNotificationByUserAndPage(user.getId(), pageDb.getId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                attributes.put("pageNotification", pageNotification);
            }
        }

        List<Event> futureEventList = null;
        if (pageDb.getId() != null) {
            try {
                futureEventList = EventDao.loadFutureEventListByPageId(pageDb.getId());
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }
        if (futureEventList != null) {
            Gson gson = new Gson();
            String futureEventListJson = gson.toJson(futureEventList);

            attributes.put("futureEventListJson", futureEventListJson);

        }

        int pagination = 12;
        int skip = NumberUtils.toInt(request.queryParams("skip"));
        int limit = NumberUtils.toInt(request.queryParams("limit"));
        if (limit <= 0) {
            limit = pagination;
        }
        attributes.put("skip", skip);
        attributes.put("limit", limit);
        attributes.put("pagination", pagination);

        List<Event> eventList = null;
        if (pageDb.getId() != null) {
            try {
                eventList = EventDao.loadEventListByPageId(pageDb.getId(), skip, limit + 1);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }

        // loadmore
        boolean loadmore = false;
        if ((eventList != null) && (!eventList.isEmpty())) {
            loadmore = eventList.size() > limit;
        }
        if ((eventList != null) && (!eventList.isEmpty())) {
            if (eventList.size() > limit) {
                eventList = eventList.subList(0, limit);
            }
        }

        attributes.put("eventEntryList", EventCommons.toEntries(eventList));

        attributes.put("loadmore", loadmore);

        URIBuilder uriBuilder;
        try {
            if (RouteUtils.publicQueryString(request) != null) {
                uriBuilder = new URIBuilder(RouteUtils.publicUrl(request) + "?" + RouteUtils.publicQueryString(request));
                List<NameValuePair> queryParameters = uriBuilder.getQueryParams();
                for (Iterator<NameValuePair> queryParameterItr = queryParameters.iterator(); queryParameterItr.hasNext();) {
                    NameValuePair queryParameter = queryParameterItr.next();
                    if (queryParameter.getName().equals("skip")) {
                        queryParameterItr.remove();
                    }
                    if (queryParameter.getName().equals("tab")) {
                        queryParameterItr.remove();
                    }
                    if (queryParameter.getName().equals("limit")) {
                        queryParameterItr.remove();
                    }
                }
                uriBuilder.setParameters(queryParameters);

                String resultUrl = uriBuilder.build().toString();
                attributes.put("resultUrl", resultUrl);
            } else {
                attributes.put("resultUrl", RouteUtils.publicUrl(request));
            }
        } catch (URISyntaxException ex) {
            //
        }
        attributes.put("eventEntryList", EventCommons.toEntries(eventList));

        attributes.put("pageDb", pageDb);

        return Manager.render(Templates.PAGE_IFRAME, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute page_search_iframe = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer;
        if (user != null) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
                return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
            }
            customer = CustomerDao.loadCustomerByUserId(user.getId());
            attributes.put("customerEntry", CustomerCommons.toEntry(customer));
        }

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        return Manager.render(Templates.PAGE_SEARCH_IFRAME, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute be_pages = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        attributes.put("user", user);

        // date filter
        Date endDate = TimeUtils.today();
        Date startDate = DateUtils.addDays(new Date(), -60);

        if (StringUtils.isNotBlank(request.queryParams("startDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("startDate"));
            if (tmp != null) {
                startDate = tmp;
            }
        }
        if (StringUtils.isNotBlank(request.queryParams("endDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("endDate"));
            if (tmp != null) {
                endDate = tmp;
            }
        }

        attributes.put("startDate", startDate);
        attributes.put("endDate", endDate);

        String[] selectedProvinces = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedProvinces"))) {
            selectedProvinces = StringUtils.split(request.queryParams("selectedProvinces"), "|");
        }
        attributes.put("selectedProvinces", selectedProvinces);

        String[] selectedPrint = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedPrint"))) {
            selectedPrint = StringUtils.split(request.queryParams("selectedPrint"), "|");
        }
        attributes.put("selectedPrint", selectedPrint);

        String[] selectedGhosts = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedGhosts"))) {
            selectedGhosts = StringUtils.split(request.queryParams("selectedGhosts"), "|");
        }
        attributes.put("selectedGhosts", selectedGhosts);
//
//        List<Page> pageListComplete = PageDao.loadPageListByDateRange(startDate, endDate);
//
//        attributes.put("pageList", PageCommons.toEntries(pageListComplete));

        return Manager.render(Templates.BE_PAGES, attributes, RouteUtils.pathType(request));
    };

    public static Route be_pages_data = (Request request, Response response) -> {

        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        attributes.put("user", user);

        // date filter
        Date endDate = TimeUtils.today();
        Date startDate = DateUtils.addDays(new Date(), -60);

        if (StringUtils.isNotBlank(request.queryParams("startDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("startDate"));
            if (tmp != null) {
                startDate = tmp;
            }
        }
        if (StringUtils.isNotBlank(request.queryParams("endDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("endDate"));
            if (tmp != null) {
                endDate = tmp;
            }
        }

        attributes.put("startDate", startDate);
        attributes.put("endDate", endDate);

        String[] selectedProvinces = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedProvinces"))) {
            selectedProvinces = StringUtils.split(request.queryParams("selectedProvinces"), "|");
        }
        attributes.put("selectedProvinces", selectedProvinces);

        String[] selectedPrint = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedPrint"))) {
            selectedPrint = StringUtils.split(request.queryParams("selectedPrint"), "|");
        }
        attributes.put("selectedPrint", selectedPrint);

        String[] selectedGhosts = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedGhosts"))) {
            selectedGhosts = StringUtils.split(request.queryParams("selectedGhosts"), "|");
        }
        attributes.put("selectedGhosts", selectedGhosts);

        List<Page> pageListComplete = PageDao.loadPageListByDateRange(startDate, endDate);
        List<PageEntry> pageList = PageCommons.toEntries(pageListComplete, Arrays.asList("customer"));

        String context = RouteUtils.contextPath(request);
        String language = Defaults.LANGUAGE;

        JsonObject json = new JsonObject();
        if (pageList != null && !pageList.isEmpty()) {
            JsonArray array = new JsonArray();
            for (PageEntry entry : pageList) {
                boolean add = true;
                if ((selectedGhosts != null)
                        && (selectedGhosts.length > 0)) {
                    if (selectedGhosts.length == 1) {
                        if (BooleanUtils.toBoolean(selectedGhosts[0]) != BooleanUtils.isTrue(entry.getPage().getIsFake())) {
                            add = false;
                        }
                    }
                }
                if (add) {
                    JsonArray element = new JsonArray();

                    // 1
                    String img = "n.d.";
                    if (entry.getPage().getProfileImageId() != null) {
                        img = "<img src='" + context + Paths.IMAGE_SYSTEM + "?oid=" + entry.getPage().getProfileImageId() + "' width='120' loading='lazy'/>";
                    }
                    element.add(new JsonPrimitive(img));

                    // 2
                    String customer = "Agorapp";
                    if (entry.getCustomer() != null) {
                        customer = entry.getCustomer().getName() + " " + entry.getCustomer().getLastname();
                    }
                    element.add(new JsonPrimitive(customer));

                    // 3
                    if (BooleanUtils.isTrue(entry.getPage().getIsFake())) {
                        String pageName = "<div id='pageId' style='display: none' sortable-value='" + entry.getPage().getName() + "'>" + entry.getPage().getId() + "</div>";
                        pageName += "<a href='" + context + Paths.BE_PAGE_EDIT + "?oid=" + entry.getPage().getId() + "' class='text-bold text-danger' target='_blank' sortable-value='" + entry.getPage().getName() + "'>" + entry.getPage().getName() + " [GHOST]</a>";
                        element.add(new JsonPrimitive(pageName));
                    } else {
                        String pageName = "<div id='pageId' style='display: none' sortable-value='" + entry.getPage().getName() + "'>" + entry.getPage().getId() + "</div>";
                        pageName += "<a href='" + context + Paths.BE_PAGE_EDIT + "?oid=" + entry.getPage().getId() + "' class='text-bold' target='_blank' sortable-value='" + entry.getPage().getName() + "'>" + entry.getPage().getName() + "</a>";
                        element.add(new JsonPrimitive(pageName));
                    }

                    // 4
                    String link = "<a href='" + context + Paths.PAGE_BASE.replace(":language", language) + "/" + entry.getPage().getIdentifier() + "' target='_blank'>Link ↗</a>";
                    element.add(new JsonPrimitive(link));

                    // 5
                    String category = "";
                    if (StringUtils.equals(entry.getPage().getPageType(), "person")) {
                        category = "<div class='label bg-brown'>Persona</div>";
                    } else if (StringUtils.equals(entry.getPage().getPageType(), "business")) {
                        category = "<div class='label bg-success'>Attività</div>";
                    } else if (StringUtils.equals(entry.getPage().getPageType(), "place")) {
                        category = "<div class='label bg-pink'>Luogo</div>";
                    }
                    element.add(new JsonPrimitive(category));

                    // 6
                    if (StringUtils.isNotBlank(entry.getPage().getFulladdress())) {
                        element.add(new JsonPrimitive(entry.getPage().getFulladdress()));
                    } else {
                        element.add(new JsonPrimitive(""));
                    }

                    // 7
                    SimpleDateFormat dateFormat = new SimpleDateFormat("dd MMM yyyy HH:mm", new Locale("it", "IT"));
                    SimpleDateFormat dateFormatSort = new SimpleDateFormat("yyyyMMddHHmm");
                    String creation = "<span sortable-value='" + dateFormatSort.format(entry.getPage().getCreation()) + "'>" + dateFormat.format(entry.getPage().getCreation()) + "</span>";
                    element.add(new JsonPrimitive(creation));

                    // 8
                    if (StringUtils.isNotBlank(entry.getPage().getIdentifier())) {
                        element.add(new JsonPrimitive(entry.getPage().getIdentifier()));
                    } else {
                        element.add(new JsonPrimitive(""));
                    }

                    // 9
                    String actions = "<ul class='icons-list'>\n"
                            + "<li class='dropdown'>\n"
                            + "    <a href='#' class='dropdown-toggle' data-toggle='dropdown'>\n"
                            + "        <i class='icon-menu9'></i>\n"
                            + "    </a>\n"
                            + "    <ul class='dropdown-menu dropdown-menu-right'>\n"
                            + "        <li>\n"
                            + "            <a href='" + context + Paths.BE_PAGE_REMOVE_PROFILE_IMAGE + "?oid=" + entry.getPage().getId() + "'><i class='icon-trash-alt'></i> Elimina immagine profilo</a>\n"
                            + "        </li>\n"
                            + "        <li>\n"
                            + "            <a href='" + context + Paths.BE_PAGE_REMOVE_POSTER + "?oid=" + entry.getPage().getId() + "'><i class='icon-trash-alt'></i> Elimina copertina</a>\n"
                            + "        </li>\n"
                            + "        <li>\n"
                            + "            <a href='" + context + Paths.BE_PAGE_REMOVE + "?pageId=" + entry.getPage().getId() + "' class='remove-page'><i class='icon-trash-alt'></i> Elimina pagina</a>\n"
                            + "        </li>\n"
                            + "    </ul>\n"
                            + "</li>\n"
                            + "</ul>";
                    element.add(new JsonPrimitive(actions));

                    // 10
                    element.add(new JsonPrimitive(""));

                    array.add(element);
                }
            }

            json.add("data", array);
        }

        return json.toString();
    };

    public static TemplateViewRoute be_page_edit = (Request request, Response response) -> {

        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));

        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        Page page;
        if (oid != null) {
            page = PageDao.loadPage(oid);
        } else {
            page = new Page();
        }
        attributes.put("page", page);

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.BE_PAGE_EDIT, attributes, RouteUtils.pathType(request));
    };

    public static Route be_page_edit_save = (Request request, Response response) -> {
        // params

        List<SlimImage> slims = new ArrayList<>();
        List<PostedFile> files = new ArrayList<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, slims, files);

        ObjectId oid = ParamUtils.toObjectId(params.get("oid"));

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.REGISTER, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        if (oid != null) {
            // params
            Page previous = PageDao.loadPage(oid);
            ObjectId oldOwner = previous.getOwnerId();
            String titleOld = previous.getName();
            Page tmpPage = (Page) BeanUtils.cloneBean(previous);

            tmpPage = PojoUtils.mergeFromParams(params, tmpPage);
            if (StringUtils.isBlank(params.get("showFollowers"))) {
                tmpPage.setShowFollowers(false);
            }
            ObjectId newOwner = tmpPage.getOwnerId();

            if (PageCommons.isValidPage(tmpPage)) {
//                if (!StringUtils.equalsIgnoreCase(titleOld, page.getName())) {
//                    Slugify slg = new Slugify();
//                    String identifier = page.getName() + "-" + RouteUtils.generateIdentifier();
//                    page.setIdentifier(slg.slugify(identifier));
//                }
                PageDao.updatePage(tmpPage);

                // image
                String slim = params.get("uploaded-profile");
                if (StringUtils.isNotBlank(slim)) {
                    SlimImage uploaded = null;
                    try {
                        uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    if (uploaded != null) {
                        if (uploaded.getBytes() != null) {
                            PageDao.updateProfileImage(user.getUsername(), oid, uploaded);
                        }
                    } else {
                        PageDao.removeProfileImage(oid);
                    }
                } else {
                    PageDao.removeProfileImage(oid);
                }

                // logo
                String slimlogo = params.get("uploaded-cover");
                if (StringUtils.isNotBlank(slimlogo)) {
                    SlimImage uploaded = null;
                    try {
                        uploaded = Manager.deserializeFromJson(slimlogo, SlimImage.class);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    if (uploaded != null) {
                        if (uploaded.getBytes() != null) {
                            PageDao.updateCoverImage(user.getUsername(), oid, uploaded);
                        }
                    } else {
                        PageDao.removeCoverImage(oid);
                    }
                } else {
                    PageDao.removeCoverImage(oid);
                }
                if (newOwner != null && (oldOwner == null || !oldOwner.equals(newOwner))) {
                    User userOwner = UserDao.loadUser(newOwner);
                    Customer customerOwner = CustomerDao.loadCustomerByUserId(newOwner);
                    String lang = Defaults.LANGUAGE;
                    if (customerOwner != null) {
                        if (StringUtils.isNotBlank(customerOwner.getCountryCode()) && !StringUtils.equalsIgnoreCase(customerOwner.getCountryCode(), "it")) {
                            lang = "en";
                        }
                        if (!NotificationCommons.notifyPageOwnerChange(request, userOwner, customerOwner, lang)) {
                            // throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Registrazione errata");
                        }
                    }
                }

                // Auto-approve pending events when page changes from "owner" to "everyone"
                if (StringUtils.equalsIgnoreCase(previous.getPageTagging(), "owner")
                        && StringUtils.equalsIgnoreCase(tmpPage.getPageTagging(), "everyone")) {
                    autoApprovePendingEvents(tmpPage.getId());
                }

                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.BE_PAGES));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.BE_PAGE_EDIT) + "?oid=" + tmpPage.getId());
            }
        } else {
            // params
            Page page = PojoUtils.createFromParams(params, Page.class);

            if (PageCommons.isValidPage(page)) {
                page.setUserId(user.getId());
                Slugify slg = new Slugify();
                String identifier = page.getName() + "-" + RouteUtils.generateIdentifier();
                page.setIdentifier(slg.slugify(identifier));

                ObjectId pageId = PageDao.insertPage(page);

                // image
                String slim = params.get("uploaded-profile");
                if (StringUtils.isNotBlank(slim)) {
                    SlimImage uploaded = null;
                    try {
                        uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    if (uploaded != null) {
                        if (uploaded.getBytes() != null) {
                            PageDao.updateProfileImage(user.getUsername(), oid, uploaded);
                        }
                    } else {
                        PageDao.removeProfileImage(oid);
                    }
                }

                // logo
                String slimlogo = params.get("uploaded-cover");
                if (StringUtils.isNotBlank(slimlogo)) {
                    SlimImage uploaded = null;
                    try {
                        uploaded = Manager.deserializeFromJson(slimlogo, SlimImage.class);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    if (uploaded != null) {
                        if (uploaded.getBytes() != null) {
                            PageDao.updateCoverImage(user.getUsername(), oid, uploaded);
                        }
                    } else {
                        PageDao.removeCoverImage(oid);
                    }
                }

                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.BE_PAGES));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.BE_PAGE_EDIT));
            }
        }

        return null;
    };

    public static Route be_page_remove = (Request request, Response response) -> {

        ObjectId pageId = ParamUtils.toObjectId(request.queryParams("pageId"));

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            Spark.halt(HttpStatus.UNAUTHORIZED_401);
            return null;
        }

        if (pageId != null) {
            // params
            Page page = PageDao.loadPage(pageId);
            if (page != null) {
                PageDao.updatePageCancelled(pageId, true);
//                if (StringUtils.isNotBlank(page.getProtocol())) {
//                    if (!NotificationCommons.notifyPage(request, NotificationCommons.PageNotificationType.deleted, page)) {
//                        // ...
//                    }
//                }
            } else {
                Spark.halt(HttpStatus.BAD_REQUEST_400);
            }

        } else {
            Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";
    };

    public static Route be_page_qrcode_generate = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        ObjectId printId = null;
        if (oid != null) {
            Page page = null;
            try {
                page = PageDao.loadPage(oid);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (page != null) {
                String qrcode = page.getQrcode();
                if (page.getQrcode() == null) {
                    qrcode = RouteUtils.generateIdentifier();
                    page.setQrcode(qrcode);
                }
                // generate qrcode
                String textQr = RouteUtils.language(RouteUtils.baseUrl(request) + Paths.PAGE_BASE + "?qrcode=" + qrcode, Defaults.LANGUAGE);
                String qrCodeSvg = QrcodeCommons.generateQRCodeImage(textQr);
                if (qrCodeSvg != null) {
                    printId = PageDao.updateQrcodeImage(qrcode, oid, qrCodeSvg);
                } else {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400);
                }

            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return printId;
    };

    public static Route be_page_poster_remove = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        if (oid != null) {
            Page page = null;
            try {
                page = PageDao.loadPage(oid);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (page != null) {
                page.setCoverImageId(null);
                PageDao.updatePage(page);

                response.redirect(RouteUtils.contextPath(request) + Paths.BE_PAGE_EDIT + "?oid=" + page.getId().toString());
                return null;
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
    };

    public static Route be_page_profile_image_remove = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        if (oid != null) {
            Page page = null;
            try {
                page = PageDao.loadPage(oid);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (page != null) {
                page.setProfileImageId(null);
                PageDao.updatePage(page);

                response.redirect(RouteUtils.contextPath(request) + Paths.BE_PAGE_EDIT + "?oid=" + page.getId().toString());
                return null;
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
    };

    public static Location geocode(Page page) {
        Location location = null;

        if (page != null) {
            if (StringUtils.isNotBlank(page.getCity())
                    && StringUtils.isNotBlank(page.getAddress())
                    && StringUtils.isNotBlank(page.getProvinceCode())
                    && StringUtils.isNotBlank(page.getPostalCode())) {

                String address = page.getAddress() + ", " + page.getPostalCode() + " " + page.getCity() + ", " + page.getProvinceCode();
                location = Geocoder.geocode(address);
            }
        }

        return location;
    }

    /**
     * Auto-approve all events that have the specified page in their pendingPageIds
     * by moving the page from pendingPageIds to pageIds
     */
    private static void autoApprovePendingEvents(ObjectId pageId) {
        try {
            // Find all events that have this page in their pendingPageIds
            List<Event> eventsWithPendingPage = EventDao.loadEventsByPendingPageId(pageId);

            for (Event event : eventsWithPendingPage) {
                boolean updated = false;

                // Remove from pendingPageIds
                if (event.getPendingPageIds() != null && event.getPendingPageIds().contains(pageId)) {
                    event.getPendingPageIds().remove(pageId);
                    updated = true;
                }

                // Add to pageIds if not already present
                if (event.getPageIds() == null) {
                    event.setPageIds(new ArrayList<>());
                }
                if (!event.getPageIds().contains(pageId)) {
                    event.getPageIds().add(pageId);
                    updated = true;
                }

                // Update the event if changes were made
                if (updated) {
                    // Clean up empty lists
                    if (event.getPendingPageIds() != null && event.getPendingPageIds().isEmpty()) {
                        event.setPendingPageIds(null);
                    }

                    EventDao.updateEvent(event);
                    LOGGER.info("Auto-approved page {} for event {}", pageId, event.getId());
                }
            }

            if (!eventsWithPendingPage.isEmpty()) {
                LOGGER.info("Auto-approved page {} for {} events", pageId, eventsWithPendingPage.size());
            }

        } catch (Exception ex) {
            LOGGER.error("Error auto-approving pending events for page: " + pageId, ex);
        }
    }
}
