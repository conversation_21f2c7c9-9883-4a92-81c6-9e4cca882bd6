package com.agora.page;

import com.agora.pojo.Customer;
import com.agora.pojo.Page;
import com.agora.pojo.User;

/**
 *
 * <AUTHOR>
 */
public class PageEntry {
    private User user;
    private Customer customer;
    private Page page;
    private Long followerCount;
    private Long followedCount;

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Customer getCustomer() {
        return customer;
    }

    public void setCustomer(Customer customer) {
        this.customer = customer;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public Long getFollowerCount() {
        /*// se dal backend ho impostato un numero di followers allora mostro quello
        if (this.page.getFollowers() != null) {
            return followerCount + this.page.getFollowers();
        }*/
        return followerCount;
    }

    public void setFollowerCount(Long followerCount) {
        this.followerCount = followerCount;
    }

    public Long getFollowedCount() {
        return followedCount;
    }

    public void setFollowedCount(Long followedCount) {
        this.followedCount = followedCount;
    }

}
