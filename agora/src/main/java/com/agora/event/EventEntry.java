package com.agora.event;

import com.agora.pojo.Customer;
import com.agora.pojo.Event;
import com.agora.pojo.Page;
import com.agora.pojo.User;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
public class EventEntry {

    private Event event;
    private User user;
    private Customer customer;
    private Long followerCount;
    private Long followedCount;
    private Boolean iFollow;
    private List<Page> followerPages;       //5 follower random di cui mostrare avatar

    // campi per visualizzazione home
    private List<Page> extraPages;
    private List<Page> walletExtraPages;
    private List<Event> childEvents;

    public Event getEvent() {
        return event;
    }

    public void setEvent(Event event) {
        this.event = event;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Customer getCustomer() {
        return customer;
    }

    public void setCustomer(Customer customer) {
        this.customer = customer;
    }

    public Long getFollowerCount() {
        /*// se dal backend ho impostato un numero di followers allora mostro quello
        if (this.event.getFollowers() != null) {
            return followerCount + this.event.getFollowers();
        }*/
        return followerCount;
    }

    public void setFollowerCount(Long followerCount) {
        this.followerCount = followerCount;
    }

    public Long getFollowedCount() {
        return followedCount;
    }

    public void setFollowedCount(Long followedCount) {
        this.followedCount = followedCount;
    }

    public Boolean getiFollow() {
        return iFollow;
    }

    public void setiFollow(Boolean iFollow) {
        this.iFollow = iFollow;
    }

    public List<Page> getFollowerPages() {
        return followerPages;
    }

    public void setFollowerPages(List<Page> followerPages) {
        this.followerPages = followerPages;
    }

    public List<Page> getExtraPages() {
        return extraPages;
    }

    public List<Page> getExtraPages(int limit) {
        if (extraPages == null || extraPages.isEmpty()) {
            return extraPages;
        }
        if (extraPages.size() > limit) {
            return extraPages.subList(0, limit);
        } else {
            return extraPages;
        }
    }

    public void setExtraPages(List<Page> extraPages) {
        this.extraPages = extraPages;

        setWalletExtraPages(getExtraPages(5));
    }

    public List<Page> getWalletExtraPages() {
        return walletExtraPages;
    }

    public void setWalletExtraPages(List<Page> walletExtraPages) {
        this.walletExtraPages = walletExtraPages;
    }

    public List<Event> getChildEvents() {
        return childEvents;
    }

    public void setChildEvents(List<Event> childEvents) {
        this.childEvents = childEvents;
    }
}
