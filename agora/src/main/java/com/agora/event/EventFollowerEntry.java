package com.agora.event;

import com.agora.pojo.Event;
import com.agora.pojo.EventFollower;
import com.agora.pojo.User;

/**
 *
 * <AUTHOR>
 */
public class EventFollowerEntry {

    private User user;
    private Event event;
    private EventFollower eventFollower;
    private Long followerCount;

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Event getEvent() {
        return event;
    }

    public void setEvent(Event event) {
        this.event = event;
    }

    public EventFollower getEventFollower() {
        return eventFollower;
    }

    public void setEventFollower(EventFollower eventFollower) {
        this.eventFollower = eventFollower;
    }

    public Long getFollowerCount() {
        return followerCount;
    }

    public void setFollowerCount(Long followerCount) {
        this.followerCount = followerCount;
    }

}
