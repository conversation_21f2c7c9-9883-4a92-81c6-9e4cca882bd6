package com.agora.vendor;

import com.agora.commons.EntityCommons;
import com.agora.commons.VendorCommons;
import com.agora.core.Defaults;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.CounterDao;
import com.agora.dao.CustomerDao;
import com.agora.dao.VendorDao;
import com.agora.dao.UserDao;
import com.agora.dashboard.login.PasswordHash;
import com.agora.pojo.Customer;
import com.agora.pojo.User;
import com.agora.pojo.Vendor;
import com.agora.pojo.types.ProfileType;
import com.agora.support.image.slim.SlimImage;
import com.agora.util.ParamUtils;
import com.agora.util.PojoUtils;
import com.agora.util.RouteUtils;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class VendorController {

    private static final Logger LOGGER = LoggerFactory.getLogger(VendorController.class.getName());

    public static TemplateViewRoute vendors = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        // avatar
        ObjectId entityImageId = EntityCommons.imageIdFromUser(user);
        attributes.put("entityImageId", entityImageId);

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        List<Vendor> vendorList = VendorDao.loadVendorList();
        attributes.put("vendorList", VendorCommons.toEntries(vendorList));

        return Manager.render(Templates.VENDORS, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute vendors_add = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        // avatar
        ObjectId entityImageId = EntityCommons.imageIdFromUser(user);
        attributes.put("entityImageId", entityImageId);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.VENDORS_ADD, attributes, RouteUtils.pathType(request));
    };

    public static Route vendors_add_save = (Request request, Response response) -> {

        // params
        Vendor vendor = PojoUtils.createFromRequest(request, Vendor.class);
        String username = request.queryParams("username");
        String password = request.queryParams("password");
        String passwordConfirm = request.queryParams("password-confirm");
        String profileType = request.queryParams("profileType");

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        if (StringUtils.isNotBlank(username)) {
            User userVendor = UserDao.loadUserByUsername(username);
            ObjectId userId = null;
            if (userVendor == null) {
                if (StringUtils.isNotBlank(password) && StringUtils.equals(password, passwordConfirm)) {
                    
                    User usr = new User();
                    usr.setName(vendor.getName());
                    usr.setEmail(username);
                    usr.setUsername(username);
                    usr.setPassword(PasswordHash.createHash(password));
                    usr.setProfileType(profileType);
                    userId = UserDao.insertUser(usr);
                    
                } else {
                    response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.VENDORS_ADD));
                    return null;
                }
            } else {
                if (StringUtils.equalsIgnoreCase(userVendor.getProfileType(), ProfileType.customer.toString())) {

                    // upgrade user type from customer to vendors
                    userVendor.setProfileType(ProfileType.vendor.toString());
                    UserDao.updateUser(userVendor);

                    Customer customer = CustomerDao.loadCustomerByUserId(userVendor.getId());
                    if (customer != null) {
                        customer.setUserId(null);
                        CustomerDao.updateCustomer(customer);
                    }

                    userId = userVendor.getId();
                } else if (StringUtils.equalsIgnoreCase(userVendor.getProfileType(), ProfileType.vendor.toString())) {
                    userId = userVendor.getId();
                } else {
                    response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.VENDORS_ADD));
                }
            }
            if (userId != null) {
                if (isValidVendor(vendor)) {

                    String birthDay = request.queryParams("birthDay");
                    String birthMonth = request.queryParams("birthMonth");
                    String birthYear = request.queryParams("birthYear");

                    Date birthDate = null;
                    if (StringUtils.isNotBlank(birthDay) &&
                            StringUtils.isNotBlank(birthMonth) &&
                            StringUtils.isNotBlank(birthYear)) {
                        birthDate = new Date();
                        birthDate = DateUtils.setYears(birthDate, NumberUtils.toInt(birthYear));
                        birthDate = DateUtils.setMonths(birthDate, NumberUtils.toInt(birthMonth, 1) - 1);
                        birthDate = DateUtils.setDays(birthDate, NumberUtils.toInt(birthDay));
                        birthDate = DateUtils.setHours(birthDate, 0);
                        birthDate = DateUtils.setMinutes(birthDate, 0);
                        birthDate = DateUtils.setSeconds(birthDate, 0);
                        birthDate = DateUtils.setMilliseconds(birthDate, 0);
                    }
                    vendor.setBirthDate(birthDate);


                    String sinceDay = request.queryParams("sinceDay");
                    String sinceMonth = request.queryParams("sinceMonth");
                    String sinceYear = request.queryParams("sinceYear");

                    Date sinceDate = new Date();
                    if (StringUtils.isNotBlank(sinceDay) &&
                            StringUtils.isNotBlank(sinceMonth) &&
                            StringUtils.isNotBlank(sinceYear)) {
                        sinceDate = DateUtils.setYears(sinceDate, NumberUtils.toInt(sinceYear));
                        sinceDate = DateUtils.setMonths(sinceDate, NumberUtils.toInt(sinceMonth, 1) - 1);
                        sinceDate = DateUtils.setDays(sinceDate, NumberUtils.toInt(sinceDay));
                        sinceDate = DateUtils.setHours(sinceDate, 0);
                        sinceDate = DateUtils.setMinutes(sinceDate, 0);
                        sinceDate = DateUtils.setSeconds(sinceDate, 0);
                        sinceDate = DateUtils.setMilliseconds(sinceDate, 0);
                    }
                    vendor.setSinceDate(sinceDate);
                    vendor.setUserId(userId);
                    vendor.setCity(StringUtils.capitalize(StringUtils.lowerCase(StringUtils.trim(vendor.getCity()))));
                    
                    int next = CounterDao.next("vendor-protocol");
                    vendor.setCode("" + next);
                    
                    ObjectId vendorId = VendorDao.insertVendor(vendor);

                    // image
                    String slim = request.queryParams("uploaded-files");
                    if (StringUtils.isNotBlank(slim)) {
                        SlimImage uploaded = null;
                        try {
                            uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                        if ((uploaded != null)) {
                            VendorDao.updateVendorImage(user.getUsername(), vendorId, uploaded);
                        }
                    }

                    // navigation
                    response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.VENDORS));

                } else {
                    response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.VENDORS_ADD));
                }
            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.VENDORS_ADD));
            }
        } else {
            response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.VENDORS_ADD));
        }

        return null;
    };

    public static Route vendors_edit_save = (Request request, Response response) -> {

        // params
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        Boolean isNote = BooleanUtils.toBoolean(request.queryParams("isNote"));
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        if (oid != null) {
            Vendor vendorOld = VendorDao.loadVendor(oid);
            Vendor vendor = PojoUtils.mergeFromRequest(request, vendorOld);
            if (isValidVendor(vendor)) {

                String birthDay = request.queryParams("birthDay");
                String birthMonth = request.queryParams("birthMonth");
                String birthYear = request.queryParams("birthYear");

                Date birthDate = null;
                if (StringUtils.isNotBlank(birthDay) &&
                        StringUtils.isNotBlank(birthMonth) &&
                        StringUtils.isNotBlank(birthYear)) {
                    birthDate = new Date();
                    birthDate = DateUtils.setYears(birthDate, NumberUtils.toInt(birthYear));
                    birthDate = DateUtils.setMonths(birthDate, NumberUtils.toInt(birthMonth, 1) - 1);
                    birthDate = DateUtils.setDays(birthDate, NumberUtils.toInt(birthDay));
                    birthDate = DateUtils.setHours(birthDate, 0);
                    birthDate = DateUtils.setMinutes(birthDate, 0);
                    birthDate = DateUtils.setSeconds(birthDate, 0);
                    birthDate = DateUtils.setMilliseconds(birthDate, 0);
                }
                vendor.setBirthDate(birthDate);

                String sinceDay = request.queryParams("sinceDay");
                String sinceMonth = request.queryParams("sinceMonth");
                String sinceYear = request.queryParams("sinceYear");

                Date sinceDate = new Date();
                if (StringUtils.isNotBlank(sinceDay) &&
                        StringUtils.isNotBlank(sinceMonth) &&
                        StringUtils.isNotBlank(sinceYear)) {
                    sinceDate = DateUtils.setYears(sinceDate, NumberUtils.toInt(sinceYear));
                    sinceDate = DateUtils.setMonths(sinceDate, NumberUtils.toInt(sinceMonth, 1) - 1);
                    sinceDate = DateUtils.setDays(sinceDate, NumberUtils.toInt(sinceDay));
                    sinceDate = DateUtils.setHours(sinceDate, 0);
                    sinceDate = DateUtils.setMinutes(sinceDate, 0);
                    sinceDate = DateUtils.setSeconds(sinceDate, 0);
                    sinceDate = DateUtils.setMilliseconds(sinceDate, 0);
                }
                vendor.setSinceDate(sinceDate);
                vendor.setCity(StringUtils.capitalize(StringUtils.lowerCase(StringUtils.trim(vendor.getCity()))));
                VendorDao.updateVendor(vendor);

                // image
                if (!isNote) {
                    String slim = request.queryParams("uploaded-files");
                    if (StringUtils.isNotBlank(slim)) {
                        SlimImage uploaded = null;
                        try {
                            uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                        if ((uploaded != null)) {
                            VendorDao.updateVendorImage(user.getUsername(), oid, uploaded);
                        }
                    } else {
                        VendorDao.removeVendorImage(oid);
                    }
                }
                
                String profileType = request.queryParams("profileType");
                if (StringUtils.isNotBlank(profileType)) {
                    User userVendor = UserDao.loadUser(vendor.getUserId());
                    if (!StringUtils.equalsIgnoreCase(profileType, userVendor.getProfileType())) {
                        userVendor.setProfileType(profileType);
                        UserDao.updateUser(userVendor);
                    }
                } 
                
                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.VENDOR_VIEW) + "?oid=" + vendor.getId());

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.VENDOR_VIEW) + "?oid=" + vendor.getId());
            }
        }

        return null;
    };

    public static TemplateViewRoute vendor_view = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        // avatar
        ObjectId entityImageId = EntityCommons.imageIdFromUser(user);
        attributes.put("entityImageId", entityImageId);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // vendor
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        if (oid == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.BE_LOGIN);
            return Manager.renderEmpty();
        }

        Vendor vendor = VendorDao.loadVendor(oid);
        attributes.put("vendor", vendor);

        User userVendor = UserDao.loadUser(vendor.getUserId());
        attributes.put("userVendor", userVendor);

        return Manager.render(Templates.VENDOR_VIEW, attributes, RouteUtils.pathType(request));
    };

    public static Route vendor_remove = (Request request, Response response) -> {

        ObjectId vendorId = ParamUtils.toObjectId(request.queryParams("vendorId"));

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            Spark.halt(HttpStatus.UNAUTHORIZED_401);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            Spark.halt(HttpStatus.UNAUTHORIZED_401);
            return null;
        }


        if (vendorId != null) {
            // params
            Vendor vendor = VendorDao.loadVendor(vendorId);
            if (vendor != null) {
                VendorDao.updateVendorCancelled(vendorId, true);
            } else {
                Spark.halt(HttpStatus.BAD_REQUEST_400);
            }

        } else {
            Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";
    };

    ////////////
    // internals
    
    private static boolean isValidVendor(Vendor entity) {
        boolean valid = (entity != null) &&
                StringUtils.isNotBlank(entity.getLastname()) &&
                StringUtils.isNotBlank(entity.getPhoneNumber()) &&
                StringUtils.isNotBlank(entity.getEmail()) &&
                StringUtils.isNotBlank(entity.getAddress()) &&
                StringUtils.isNotBlank(entity.getCity()) &&
                StringUtils.isNotBlank(entity.getPostalCode()) &&
                StringUtils.isNotBlank(entity.getProvinceCode())
                ;

        if (!valid) {
            if (entity != null) {
                LOGGER.warn(
                        "vendor validation problem:\n" +
                        "- lastname " + entity.getLastname() + "\n" +
                        "- phoneNumber " + entity.getPhoneNumber() + "\n" +
                        "- email " + entity.getEmail() + "\n" +
                        "- address " + entity.getAddress() + "\n" +
                        "- city " + entity.getCity() + "\n" +
                        "- postalCode " + entity.getPostalCode() + "\n" +
                        "- provinceCode " + entity.getProvinceCode() + "\n"
                );
            } else {
                LOGGER.warn(
                        "vendor validation problem:\n" +
                        "- empty"
                );
            }
        }

        return valid;
    }

}
