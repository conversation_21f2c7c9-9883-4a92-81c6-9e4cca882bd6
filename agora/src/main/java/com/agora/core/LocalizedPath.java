package com.agora.core;

import java.lang.annotation.ElementType;
import java.lang.annotation.Repeatable;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 *
 * <AUTHOR>
 */

@Repeatable(LocalizedPaths.class)
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface LocalizedPath {
    
    public String language() default Defaults.LANGUAGE;
    public String path() default "";
    
}
