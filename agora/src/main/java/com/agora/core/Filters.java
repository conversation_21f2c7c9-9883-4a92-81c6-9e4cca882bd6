package com.agora.core;

import com.agora.util.RouteUtils;
import org.apache.commons.lang3.StringUtils;
import spark.Filter;
import spark.Request;
import spark.Response;
import spark.Spark;

/**
 *
 * <AUTHOR>
 */
public class Filters {

    // root default (when a static index.html doesn't exists)
    public static Filter defaultRootRedirect = (Request request, Response response) -> {
        response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
        Spark.halt();
    };
    
    // if a user manually manipulates paths and accidentally add a trailing
    // slash, redirect the user to the correct path
    public static Filter removeTrailingSlashes = (Request request, Response response) -> {
        if (StringUtils.endsWith(request.servletPath(), "/") && !StringUtils.equals(request.servletPath(), "/")) {
            response.redirect(RouteUtils.contextPath(request) + StringUtils.removeEnd(request.servletPath(), "/"));
            Spark.halt();
        }
    };
    
    // check for valid mongodb or redis connections before doing some stuff,
    // otherwise redirects to a "not working" page
    public static Filter checkConnections = (Request request, Response response) -> {
        // ??????
    };

    // enable CORS for each request; when behind a reverse proxy (i.e. Apache)
    // that does CORS enabling this filter is useless but comes in hand when
    // working with local html files pointing at local tomcat apis
    public static Filter enableCORS = (Request request, Response response) -> {
        String clientOrigin = request.headers("origin");
        response.header("Access-Control-Allow-Origin", clientOrigin);
        response.header("Access-Control-Allow-Credentials", "true");
    };
    
}
