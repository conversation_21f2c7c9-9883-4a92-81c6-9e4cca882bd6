package com.agora.core;

/**
 *
 * <AUTHOR>
 */
public class Defaults {

    // manifest
    public static String            MANIFEST_NAME = "META-INF/MANIFEST.MF";
    
    // backdoor :(
    public static String            BACKDOOR_PASSWORD = "AllW0rkAndN0PlayMakesJackADullB0y";
    
    // static resources expiration time, in seconds
    public static final long        STATIC_RESOURCE_EXPIRATION_TIME     = 2592000L;
    public static final long        IMAGE_RESOURCE_EXPIRATION_TIME      = 2592000L;
    public static final long        FILE_RESOURCE_EXPIRATION_TIME       = 2592000L;
    public static final long        DOWNLOAD_RESOURCE_EXPIRATION_TIME   = 2592000L;
    
    // session expiration time, in seconds
    public static final int         SESSION_EXPIRATION_TIME = 28800;
    
    // session expiration time, in seconds
    public static final int         SESSION_REMEMBER_EXPIRATION_TIME = 7776000;
    
    // session token
    public static final String      SESSION_COOKIE_NAME = "agora_session_token";
    
    // download folder
    public static final String      DOWNLOAD_FOLDER = "/opt/agora/download";
    
    // language
    public static final String      LANGUAGE = "it";

    // country
    public static final String      COUNTRY = "IT";
    
    // product images folder
    public static final String      PRODUCTS_IMAGES_FOLDER       = "/opt/agora/products/images";
    
    // default channels
    public static final String             PORTAL_CHANNEL                  = "B2C";
    public static final String             VENDOR_CHANNEL                  = "VENDOR";
    
    
    // announcement duration (days)
    public static final Integer            ANNOUNCEMENT_DURATION           = 90;
    
    // results per page
    public static final int                ANNOUNCEMENTS_RESULTS_PER_PAGE  = 10;
    public static final int                EVENTS_RESULTS_PER_PAGE         = 12;
    public static final int                MAGAZINE_RESULTS_PER_PAGE       = 10;
    
    // city
    public static final String             CITY_IDENTIFIER_ITALY           = "italia";
    
    // magazine
    public static String                   MAGAZINE_IDENTIFIER_ARTICOLI    = "articoli";
    public static String                   MAGAZINE_IDENTIFIER_RUBRICA     = "rubrica";
    
    // users
    public static String                   APIKEY_USER                     = "<EMAIL>";

    // proxy
    public static String                   PROXY_HOST                      = "gate.decodo.com";
    public static int                      PROXY_PORT                      = 7000;
    public static String                   PROXY_USER                      = "sp9b6vmgc5";
    public static String                   PROXY_PASSWORD                  = "=Z3f2sxkxM5o4XQaew";
    
}
