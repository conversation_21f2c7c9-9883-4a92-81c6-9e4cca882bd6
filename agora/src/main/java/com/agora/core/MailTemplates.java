package com.agora.core;

/**
 *
 * <AUTHOR>
 */
public class MailTemplates {

    ///////////
    // FRONTEND
    ///////////

    // cambio proprietario pagina
    public static final String PAGE_OWNER_CHANGED                   = "fe/messages/page-owner-changed.html";
    
    // verifica email
    public static final String ACCOUNT_VERIFICATION                 = "fe/messages/account-verification.html";

    // benvenuto e completamento profilo
    public static final String ACCOUNT_WELCOME                      = "fe/messages/account-welcome.html";

    // recupera password - vengono inviate nuove credenziali
    public static final String ACCOUNT_FORGOT                       = "fe/messages/account-password-reset.html";
    
    // elimina account
    public static final String ACCOUNT_DELETED                      = "fe/messages/account-deleted.html";
    
    // cambia email account
    public static final String ACCOUNT_EMAIL_CHANGED                = "fe/messages/account-email-changed.html";
    
    // conferma parola chiave antifrode
    public static final String ACCOUNT_KEYWORD                      = "fe/messages/account-keyword.html";
    
    // notifica numero di telefono confermato ok
    public static final String ACCOUNT_PHONE_VERIFICATION           = "fe/messages/account-phone-verification.html";
    
    // account sospeso
    public static final String ACCOUNT_SUSPENDED                    = "fe/messages/account-phone-suspended.html";
    
    // account riattivato
    public static final String ACCOUNT_REACTIVATED                  = "fe/messages/account-phone-reactivated.html";

    // account business
    public static final String ACCOUNT_B2B_PASSWORD_SEND            = "fe/messages/account-b2b-password-send.html";
    public static final String ACCOUNT_B2B_SUBSCRIPTION_SEND        = "fe/messages/account-b2b-subscription-send.html";
    public static final String ACCOUNT_B2B_SUBSCRIPTION_PAID        = "fe/messages/account-b2b-subscription-paid.html";
    public static final String ACCOUNT_B2B_SUBSCRIPTION_DELETED     = "fe/messages/account-b2b-subscription-deleted.html";
    public static final String ACCOUNT_B2B_SUBSCRIPTION_HEAD_DELETED= "fe/messages/account-b2b-subscription-head-deleted.html";
    
    // annunci (per cliente)
    public static final String ANNOUNCE_PUBLISHED                   = "fe/messages/announce-published.html";    
    public static final String ANNOUNCE_DELETED                     = "fe/messages/announce-deleted.html";
    public static final String ANNOUNCE_REFUSED                     = "fe/messages/announce-refused.html";
    public static final String ANNOUNCE_FREE                        = "fe/messages/announce-free.html";       
    
    // annunci (per cliente)
    public static final String EVENT_PUBLISHED                   = "fe/messages/event-published.html";
    public static final String EVENT_DELETED                     = "fe/messages/event-deleted.html";
    public static final String EVENT_REFUSED                     = "fe/messages/event-refused.html";
    public static final String EVENT_ADMIN_NOTIFICATION          = "fe/messages/event-admin-notification.html";
    public static final String EVENT_GLOBAL                      = "fe/messages/event-global.html";

    // richiesta approvazione pagina per evento
    public static final String EVENT_PAGE_APPROVAL               = "fe/messages/event-page-approval.html";
    
    // modulo di contatto
    public static final String CONTACT                              = "fe/messages/contact.html";
    public static final String CONTACT_PAGE_CLAIM                   = "fe/messages/contact-page-claim.html";
    public static final String CONTACT_BUSINESS                     = "fe/messages/contact-business.html";
    public static final String CONTACT_CONFIRMATION                 = "fe/messages/contact-confirmation.html";

    // richiesta download
    public static final String DOWNLOAD                             = "fe/messages/download.html";
    public static final String DOWNLOAD_CONFIRMATION                = "fe/messages/download-confirmation.html";

    // ricevuto ordine
    public static final String ORDER_CONFIRMATION                   = "fe/messages/order-confirmation.html";
    public static final String ORDER_RETURN_CONFIRMATION            = "fe/messages/order-return-confirmation.html";

    // notifica update ordine
    public static final String ORDER_UPDATE_NOTIFICATION            = "fe/messages/order-update.html";        

    //////////
    // BACKEND
    //////////

    // recupera password - vengono inviate nuove credenziali
    public static final String FORGOT                               = "be/messages/forgot.html";

    // modulo privacy
    public static final String PRIVACY                              = "be/messages/privacy.html";

    // notifica ordine
    public static final String ORDER_NOTIFICATION                   = "be/messages/order-notification.html";
    public static final String ORDER_RETURN_NOTIFICATION            = "be/messages/order-return-notification.html";
    
}
