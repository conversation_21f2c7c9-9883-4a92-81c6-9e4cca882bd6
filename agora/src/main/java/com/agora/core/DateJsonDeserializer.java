package com.agora.core;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.ObjectCodec;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.agora.util.TimeUtils;
import java.io.IOException;
import java.util.Date;
import java.util.TimeZone;

/**
 *
 * <AUTHOR>
 */
public class DateJsonDeserializer extends JsonDeserializer<Date> {

    private static final long GMT_WORKAROUND = (1000 * 60 * 60);
    
    @Override
    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        ObjectCodec oc = p.getCodec();
        JsonNode node = oc.readTree(p);
        
        Date date = null;
        if (node.has("$date")) {
            if (node.get("$date").isLong()) {
                long millies = node.get("$date").asLong();
                if (millies != 0L) {
                    // dates can be a negative long value
                    date = new Date(millies - GMT_WORKAROUND);
                    if (TimeZone.getDefault().inDaylightTime(date)) {
                        date = new Date(millies - GMT_WORKAROUND - GMT_WORKAROUND);
                    }
                } else {
                    date = new Date(0L);
                }
            } else if (node.get("$date").isInt()) {
                // 01/01/1970 in "0", considered as int
                long millies = node.get("$date").asInt();
                if (millies != 0L) {
                    // dates can be a negative long value
                    date = new Date(millies - GMT_WORKAROUND);
                    if (TimeZone.getDefault().inDaylightTime(date)) {
                        date = new Date(millies - GMT_WORKAROUND - GMT_WORKAROUND);
                    }
                } else {
                    date = new Date(0L);
                }
            } else {
                date = TimeUtils.toDate(node.get("$date").asText(), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            }
        } else if (node.isTextual()) {
            date = TimeUtils.toDate(node.asText(), "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        } else {
            // ??????
        }

        return date;
    }
    
}
