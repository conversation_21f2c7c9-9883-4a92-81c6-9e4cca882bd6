package com.agora.dashboard;

import com.agora.commons.CustomerCommons;
import com.agora.commons.EntityCommons;
import com.agora.commons.ProfileCommons;
import com.agora.core.Defaults;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.CustomerDao;
import com.agora.dao.EventDao;
import com.agora.dao.PageDao;
import com.agora.dao.PageDao.BestPerformer;
import com.agora.home.HomeController;
import com.agora.pojo.Customer;
import com.agora.pojo.User;
import com.agora.pojo.types.ProfileType;
import com.agora.util.RouteUtils;
import com.agora.util.TimeUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class DashboardController {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(DashboardController.class.getName());

    private enum AnalysisType {
        trend,
        bestPerformerCustomer
    }
    
    public static TemplateViewRoute dashboard = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) && 
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }        
        
        // avatar
        ObjectId entityImageId = EntityCommons.imageIdFromUser(user);
        attributes.put("entityImageId", entityImageId);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        // today's pages
        long pageCount = PageDao.loadPageCountByDate(TimeUtils.today());
        attributes.put("pageCount", pageCount);
        
        // today's events
        long eventCount = EventDao.loadEventCountByDate(TimeUtils.today());
        attributes.put("eventCount", eventCount);
        
        // today's customers
        long customerCount = CustomerDao.loadCustomerCountByDate(TimeUtils.today());
        attributes.put("customerCount", customerCount);
        
        // pageviews counter
        attributes.put("pageviews", 0);
        
        // analysis
        AnalysisType analysis = EnumUtils.getEnum(AnalysisType.class, request.queryParams("analysis"));
        if (analysis == null) {
            analysis = AnalysisType.trend;
        }
        attributes.put("analysis", analysis);
        
        // analysis description
        String analysisDescription = analysisDescription(analysis);
        attributes.put("analysisDescription", analysisDescription);
        
        // analysis year
        int year = NumberUtils.toInt(request.queryParams("year"));
        if (year == 0) {
            year = TimeUtils.year(TimeUtils.today());
        }
        attributes.put("year", year);
        
        // analysis data
        switch (analysis) {
            
            case trend:
                
                Map<String, Integer> newbies = null;
                if (ProfileCommons.isHead(user)) {
                    newbies = CustomerDao.loadCustomerCountBySinceDate();
                } else {
                    ObjectId vendorId = EntityCommons.vendorIdFromUserWhereVendor(user);
                    if (vendorId != null) {
                        newbies = CustomerDao.loadCustomerCountByVendorAndSinceDate(vendorId);
                    }
                }

                int monthTo = TimeUtils.month(TimeUtils.today());
                if (year < NumberUtils.toInt(TimeUtils.toString(TimeUtils.today(), "yyyy"))) {
                    monthTo = 12;
                }

                int[] customerNewCounters = new int[12];
                int[] customerOldCounters = new int[12];

                if ((newbies != null) &&
                        (!newbies.isEmpty())) {

                    for (int m = 0; m < monthTo; m++) {

                        String month = "" + (m + 1);
                        month = StringUtils.leftPad(month, 2, "0");

                        String current = year + "-" + month;

                        for (String key : newbies.keySet()) {

                            if (key.compareTo(current) < 0) {
                                // add to old customers
                                customerOldCounters[m] += newbies.get(key);
                            } else if (key.compareTo(current) > 0) {
                                // ignore future
                                // ...
                            } else {
                                // add to new customers
                                customerNewCounters[m] += newbies.get(key);
                            }

                        }
                    }
                }
                
                newbies = PageDao.loadPageCountByDate();
                

                int[] pageNewCounters = new int[12];

                if ((newbies != null) &&
                        (!newbies.isEmpty())) {

                    for (int m = 0; m < 12; m++) {

                        String month = "" + (m + 1);
                        month = StringUtils.leftPad(month, 2, "0");

                        String current = year + "-" + month;

                        if (newbies.containsKey(current)) {
                            pageNewCounters[m] = newbies.get(current);
                        }

                    }
                }

                attributes.put("customerNewCounters", customerNewCounters);
                attributes.put("customerOldCounters", customerOldCounters);
                attributes.put("pageNewCounters", pageNewCounters);
                
                break;
                
            case bestPerformerCustomer:
                
                List<BestPerformer> performers = PageDao.loadBestPerformerByCustomer();

                
                List<String> bestNames = new ArrayList<>();
                List<Double> bestValues = new ArrayList<>();
                
                if ((performers != null) && !performers.isEmpty()) {
                    for (BestPerformer performer : performers) {
                        Customer customer = null;
                        try {
                            customer = CustomerDao.loadCustomerByUserId(performer.getId());
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                        bestNames.add(CustomerCommons.fullname(customer));
                        bestValues.add(performer.getCount() != null ? performer.getCount().doubleValue() : 0D);

                    }
                }
                
                attributes.put("bestNames", bestNames.toArray());
                attributes.put("bestValues", bestValues.toArray());
                
                break;
            
                
            default:
                LOGGER.error("unexpected analysis type " + analysis);
                break;
                
        }

        // years
        int fromYear = 2023;
        int toYear = TimeUtils.year(TimeUtils.today());
        List<String> yearList = new ArrayList<>();
        for (int y = fromYear; y < (toYear + 1); y++) {
            yearList.add("" + y);
        }
        Collections.reverse(yearList);
        if (yearList.isEmpty()) {
            yearList = null;
        }
        attributes.put("yearList", yearList);
        
        return Manager.render(Templates.DASHBOARD, attributes, RouteUtils.pathType(request));
    };

    
    
    ////////////
    // internals
    
    private static String analysisDescription(AnalysisType analysis) {
        String description = null;
        switch (analysis) {
            case trend:
                description = "Andamento clienti e pagine";
                break;
            case bestPerformerCustomer:
                description = "Miglior performer / clienti pagine";
                break;
            default:
                break;
        }
        return description;
    }
    
}
