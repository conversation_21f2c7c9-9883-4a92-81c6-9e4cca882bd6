package com.agora.profile;

import com.agora.core.Defaults;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.CustomerDao;
import com.agora.dao.EventDao;
import com.agora.dao.FirmDao;
import com.agora.dao.PageDao;
import com.agora.dao.SmtpDao;
import com.agora.dao.UserDao;
import com.agora.dao.VendorDao;
import com.agora.dashboard.login.PasswordHash;
import com.agora.extensions.LabelFunction;
import com.agora.pojo.Customer;
import com.agora.pojo.Event;
import com.agora.pojo.Firm;
import com.agora.pojo.Page;
import com.agora.pojo.Smtp;
import com.agora.pojo.User;
import com.agora.pojo.Vendor;
import com.agora.pojo.types.ProfileType;
import com.agora.support.geocoding.Geocoder;
import com.agora.support.image.slim.SlimImage;
import com.agora.util.ParamUtils;
import com.agora.util.PojoUtils;
import com.agora.util.RouteUtils;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class ProfileController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProfileController.class.getName());

    public static TemplateViewRoute profile = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.PROFILE, attributes, RouteUtils.pathType(request));
    };

    public static Route profile_save = (Request request, Response response) -> {
        // params
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        String name = request.queryParams("name");
        String password = request.queryParams("password");
        String passwordConfirmation = request.queryParams("password-confirm");
        String cashingType = request.queryParams("cashingType");
        String cashingKey = request.queryParams("cashingKey");
        String cashingComNumber = request.queryParams("cashingComNumber");

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (oid != null) {
            if (isValidPassword(
                    password,
                    passwordConfirmation)) {

                if (StringUtils.isNotBlank(name)) {
                    user.setCashingKey(cashingKey);
                    user.setCashingType(cashingType);
                    user.setCashingComNumber(cashingComNumber);
                    user.setName(name);
                    UserDao.updateUser(user);
                    user = UserDao.loadUser(user.getId());
                    if (user != null) {
                        // image
                        String slim = request.queryParams("uploaded-files");
                        if (StringUtils.isNotBlank(slim)) {
                            SlimImage uploaded = null;
                            try {
                                uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                            } catch (Exception ex) {
                                LOGGER.error("suppressed", ex);
                            }
                            if (uploaded != null) {
                                try {
                                    UserDao.updateUserImage(user.getUsername(), oid, uploaded);
                                } catch (Exception ex) {
                                    LOGGER.error("unable to save property image, exception is " + ex);
                                    return false;
                                }
                            }
                        } else {
                            UserDao.removeUserImage(oid);
                        }
                        if (!StringUtils.equals(user.getPassword(), password)) {
                            String newPasswordHash = PasswordHash.createHash(password);
                            UserDao.updateUserPassword(user.getId(), newPasswordHash);
                        }
                        user = UserDao.loadUser(user.getId());
                        // update user on redis
                        Manager.putSession(token, "user", user);
                    }

                    response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.PROFILE));
                } else {
                    response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.PROFILE));
                }
            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.PROFILE));
            }
        }

        return null;
    };

    public static TemplateViewRoute firm = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        Firm firm = FirmDao.loadFirm();
        attributes.put("firm", firm);

        // caches
        attributes.put("labelsSize", LabelFunction.size());
        attributes.put("geocoderSize", Geocoder.size());

        return Manager.render(Templates.FIRM, attributes, RouteUtils.pathType(request));
    };

    public static Route firm_save = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        Firm firm = FirmDao.loadFirm();
        if (firm == null) {
            firm = new Firm();
        }
        firm = PojoUtils.mergeFromRequest(request, firm);

        if (isValidFirm(firm)) {

            if (firm.getId() != null) {
                FirmDao.updateFirm(firm);
            } else {
                FirmDao.insertFirm(firm);
            }

            response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.FIRM));

        } else {
            response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.FIRM));
        }

        return null;
    };

    public static Route firm_reload_pages_identifier = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        List<Page> pages = PageDao.loadPageList();
        if (pages != null && !pages.isEmpty()) {
            for (Page page : pages) {
                PageDao.updatePage(page);
            }
        }

        response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.FIRM));

        return null;
    };

    public static Route firm_reload_events_identifier = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        List<Event> events = EventDao.loadEventList();
        if (events != null && !events.isEmpty()) {
            for (Event event : events) {
                EventDao.updateEvent(event);
            }
        }

        response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.FIRM));

        return null;
    };

    public static TemplateViewRoute smtp = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        Smtp smtpData = SmtpDao.loadSmtp();
        attributes.put("smtp", smtpData);

        return Manager.render(Templates.SMTP, attributes, RouteUtils.pathType(request));
    };

    public static Route smtp_save = (Request request, Response response) -> {
        // params
        ObjectId smtpId = ParamUtils.toObjectId(request.queryParams("smtpId"));
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        if (smtpId != null) {
            Smtp smtpOld = SmtpDao.loadSmtp();
            Smtp smtpData = PojoUtils.mergeFromRequest(request, smtpOld);
            if (isValidSmtp(smtpData)) {
                SmtpDao.updateSmtp(smtpData);
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.SMTP));
            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.SMTP));
            }
        } else {
            // params
            Smtp smtpData = PojoUtils.createFromRequest(request, Smtp.class);
            if (isValidSmtp(smtpData)) {
                SmtpDao.insertSmtp(smtpData);

                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.SMTP));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.SMTP));
            }
        }

        return null;
    };

    ////////////
    // internals
    private static boolean isValidCustomer(Customer entity) {
        boolean valid = (entity != null)
                && StringUtils.isNotBlank(entity.getLastname())
                && StringUtils.isNotBlank(entity.getName())
                && StringUtils.isNotBlank(entity.getPhoneNumber())
                && StringUtils.isNotBlank(entity.getEmail())
                && StringUtils.isNotBlank(entity.getAddress())
                && StringUtils.isNotBlank(entity.getCity())
                && StringUtils.isNotBlank(entity.getPostalCode())
                && StringUtils.isNotBlank(entity.getProvinceCode());

        if (!valid) {
            if (entity != null) {
                LOGGER.warn(
                        "customer validation problem:\n"
                        + "- lastname " + entity.getLastname() + "\n"
                        + "- name " + entity.getName() + "\n"
                        + "- phoneNumber " + entity.getPhoneNumber() + "\n"
                        + "- email " + entity.getEmail() + "\n"
                        + "- address " + entity.getAddress() + "\n"
                        + "- city " + entity.getCity() + "\n"
                        + "- postalCode " + entity.getPostalCode() + "\n"
                        + "- provinceCode " + entity.getProvinceCode() + "\n"
                );
            } else {
                LOGGER.warn(
                        "customer validation problem:\n"
                        + "- empty"
                );
            }
        }

        return valid;
    }

    private static boolean isValidVendor(Vendor entity) {
        boolean valid = (entity != null)
                && StringUtils.isNotBlank(entity.getLastname())
                && StringUtils.isNotBlank(entity.getName())
                && StringUtils.isNotBlank(entity.getPhoneNumber())
                && StringUtils.isNotBlank(entity.getEmail())
                && StringUtils.isNotBlank(entity.getAddress())
                && StringUtils.isNotBlank(entity.getCity())
                && StringUtils.isNotBlank(entity.getPostalCode())
                && StringUtils.isNotBlank(entity.getProvinceCode());

        if (!valid) {
            if (entity != null) {
                LOGGER.warn(
                        "vendor validation problem:\n"
                        + "- lastname " + entity.getLastname() + "\n"
                        + "- name " + entity.getName() + "\n"
                        + "- phoneNumber " + entity.getPhoneNumber() + "\n"
                        + "- email " + entity.getEmail() + "\n"
                        + "- address " + entity.getAddress() + "\n"
                        + "- city " + entity.getCity() + "\n"
                        + "- postalCode " + entity.getPostalCode() + "\n"
                        + "- provinceCode " + entity.getProvinceCode() + "\n"
                );
            } else {
                LOGGER.warn(
                        "vendor validation problem:\n"
                        + "- empty"
                );
            }
        }

        return valid;
    }

    private static boolean isValidPassword(
            String password,
            String passwordConfirmation) {

        boolean valid = StringUtils.isNotBlank(password)
                && StringUtils.isNotBlank(passwordConfirmation)
                && StringUtils.equals(password, passwordConfirmation);
        if (!valid) {
            LOGGER.warn("password " + password);
            LOGGER.warn("passwordConfirmation " + passwordConfirmation);
        }

        return valid;
    }

    private static boolean updateCustomer(Request request, ObjectId oid, String username) {
        boolean done = false;
        Customer customerOld;
        try {
            customerOld = CustomerDao.loadCustomer(oid);
        } catch (Exception ex) {
            LOGGER.error("unable to load customer, exception is " + ex);
            return false;
        }
        Customer customer = PojoUtils.mergeFromRequest(request, customerOld);
        if (isValidCustomer(customer)) {

            String birthDay = request.queryParams("birthDay");
            String birthMonth = request.queryParams("birthMonth");
            String birthYear = request.queryParams("birthYear");

            Date birthDate = null;
            if (StringUtils.isNotBlank(birthDay)
                    && StringUtils.isNotBlank(birthMonth)
                    && StringUtils.isNotBlank(birthYear)) {
                birthDate = new Date();
                birthDate = DateUtils.setYears(birthDate, NumberUtils.toInt(birthYear));
                birthDate = DateUtils.setMonths(birthDate, NumberUtils.toInt(birthMonth, 1) - 1);
                birthDate = DateUtils.setDays(birthDate, NumberUtils.toInt(birthDay));
                birthDate = DateUtils.setHours(birthDate, 0);
                birthDate = DateUtils.setMinutes(birthDate, 0);
                birthDate = DateUtils.setSeconds(birthDate, 0);
                birthDate = DateUtils.setMilliseconds(birthDate, 0);
            }
            customer.setBirthDate(birthDate);
            try {
                CustomerDao.updateCustomer(customer);
            } catch (Exception ex) {
                LOGGER.error("unable to update customer, exception is " + ex);
                return false;
            }

            // image
            String slim = request.queryParams("uploaded-files");
            if (StringUtils.isNotBlank(slim)) {
                SlimImage uploaded = null;
                try {
                    uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if ((uploaded != null)) {
                    try {
                        CustomerDao.updateCustomerImage(username, oid, uploaded);
                    } catch (Exception ex) {
                        LOGGER.error("unable to save customer image, exception is " + ex);
                        return false;
                    }
                }
            }
            done = true;

        }
        return done;
    }

    private static boolean updateVendor(Request request, ObjectId oid, String username) {
        boolean done = false;
        Vendor vendorOld;
        try {
            vendorOld = VendorDao.loadVendor(oid);
        } catch (Exception ex) {
            LOGGER.error("unable to load vendor, exception is " + ex);
            return false;
        }
        Vendor vendor = PojoUtils.mergeFromRequest(request, vendorOld);
        if (isValidVendor(vendor)) {

            String birthDay = request.queryParams("birthDay");
            String birthMonth = request.queryParams("birthMonth");
            String birthYear = request.queryParams("birthYear");

            Date birthDate = null;
            if (StringUtils.isNotBlank(birthDay)
                    && StringUtils.isNotBlank(birthMonth)
                    && StringUtils.isNotBlank(birthYear)) {
                birthDate = new Date();
                birthDate = DateUtils.setYears(birthDate, NumberUtils.toInt(birthYear));
                birthDate = DateUtils.setMonths(birthDate, NumberUtils.toInt(birthMonth, 1) - 1);
                birthDate = DateUtils.setDays(birthDate, NumberUtils.toInt(birthDay));
                birthDate = DateUtils.setHours(birthDate, 0);
                birthDate = DateUtils.setMinutes(birthDate, 0);
                birthDate = DateUtils.setSeconds(birthDate, 0);
                birthDate = DateUtils.setMilliseconds(birthDate, 0);
            }
            vendor.setBirthDate(birthDate);
            try {
                VendorDao.updateVendor(vendor);
            } catch (Exception ex) {
                LOGGER.error("unable to update customer, exception is " + ex);
                return false;
            }

            // image
            String slim = request.queryParams("uploaded-files");
            if (StringUtils.isNotBlank(slim)) {
                SlimImage uploaded = null;
                try {
                    uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if ((uploaded != null)) {
                    try {
                        VendorDao.updateVendorImage(username, oid, uploaded);
                    } catch (Exception ex) {
                        LOGGER.error("unable to save vendor image, exception is " + ex);
                        return false;
                    }
                }
            }
            // navigation
            done = true;

        }
        return done;
    }

    private static boolean isValidFirm(Firm entity) {
        boolean valid = (entity != null) //                &&
                //                (StringUtils.isBlank(entity.getSiteEmail()) || MessageSender.validEmailAddress(entity.getSiteEmail())) &&
                //                (StringUtils.isBlank(entity.getShopEmail()) || MessageSender.validEmailAddress(entity.getShopEmail())) &&
                //                StringUtils.isNotBlank(entity.getShopNotification()) &&
                //                (StringUtils.equalsIgnoreCase(entity.getShopNotification(), NotificationType.never.toString()) || MessageSender.validEmailAddress(entity.getShopNotificationEmail())) &&
                //                StringUtils.isNotBlank(entity.getVendorPortalNotification()) &&
                //                (StringUtils.equalsIgnoreCase(entity.getVendorPortalNotification(), NotificationType.never.toString()) || MessageSender.validEmailAddress(entity.getVendorPortalNotificationEmail())) &&
                //                StringUtils.isNotBlank(entity.getCustomerNotification()) &&
                //                StringUtils.isNotBlank(entity.getVendorNotification())
                ;

        if (!valid) {
            if (entity != null) {
                LOGGER.warn(
                        "firm validation problem:\n"
                        + "- siteEmail " + entity.getSiteEmail() + "\n"
                        + "- shopEmail " + entity.getShopEmail() + "\n"
                        + "- shopNotification " + entity.getShopNotification() + "\n"
                        + "- shopNotificationEmail " + entity.getShopNotificationEmail() + "\n"
                        + "- vendorPortalNotification " + entity.getShopNotification() + "\n"
                        + "- vendorPortalNotificationEmail " + entity.getShopNotificationEmail() + "\n"
                        + "- customerNotification " + entity.getCustomerNotification() + "\n"
                        + "- vendorNotification " + entity.getVendorNotification() + "\n"
                );
            } else {
                LOGGER.warn(
                        "firm validation problem:\n"
                        + "- empty"
                );
            }
        }

        return valid;
    }

    private static boolean isValidSmtp(Smtp entity) {
        boolean valid = (entity != null)
                && StringUtils.isNotBlank(entity.getHostname())
                && StringUtils.isNotBlank(entity.getPassword())
                && (entity.getPort() != null && entity.getPort() > 0)
                && StringUtils.isNotBlank(entity.getSender())
                && StringUtils.isNotBlank(entity.getUsername());

        if (!valid) {
            if (entity != null) {
                LOGGER.warn(
                        "smtp validation problem:\n"
                        + "- hostname " + entity.getHostname() + "\n"
                        + "- password " + entity.getPassword() + "\n"
                        + "- sender " + entity.getSender() + "\n"
                        + "- port " + entity.getPort() + "\n"
                        + "- username " + entity.getUsername() + "\n"
                );
            } else {
                LOGGER.warn(
                        "smtp validation problem:\n"
                        + "- empty"
                );
            }
        }

        return valid;
    }

}
