package com.agora.commons;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;
import org.apache.batik.svggen.SVGGraphics2D;
import static org.apache.poi.util.DocumentHelper.createDocument;

/**
 *
 * <AUTHOR>
 */
public class QrcodeCommons {
    
    public static String generateQRCodeImage(String barcodeText) throws Exception {
        QRCodeWriter barcodeWriter = new QRCodeWriter();
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.MARGIN, 0);        
        BitMatrix bitMatrix = barcodeWriter.encode(barcodeText, BarcodeFormat.QR_CODE, 100, 100, hints);
        return convertToSVG(bitMatrix);
    }
    
    public static String convertToSVG(BitMatrix bitMatrix) {
        // Creare un oggetto SVGGraphics2D utilizzando Batik
        SVGGraphics2D svgGraphics2D = new SVGGraphics2D(createDocument());

        // Disegnare il QR code direttamente dalla matrice di bit nel contesto SVGGraphics2D
        drawQRCode(bitMatrix, svgGraphics2D);

        // Convertire l'oggetto SVGGraphics2D in una stringa contenente XML SVG
        StringWriter writer = new StringWriter();
        try {
            svgGraphics2D.stream(writer, true);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return writer.toString();
    }

    private static void drawQRCode(BitMatrix bitMatrix, SVGGraphics2D svgGraphics2D) {
        int width = bitMatrix.getWidth();
        int height = bitMatrix.getHeight();

        svgGraphics2D.setPaint(java.awt.Color.BLACK); // Colore per i moduli neri

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                if (bitMatrix.get(x, y)) {
                    svgGraphics2D.fillRect(x, y, 1, 1); // Disegna un modulo nero nella posizione (x, y)
                }
            }
        }
    }    
}
