package com.agora.commons;

import com.agora.util.TimeUtils;
import java.util.Date;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class StorageCommons {

    public static final String STORAGE_ROOT = "/opt/agora/storage";
    
    public enum StorageType {
        doc (STORAGE_ROOT + "/doc"),
        img (STORAGE_ROOT + "/img"),
        thb (STORAGE_ROOT + "/thb"),
        vid (STORAGE_ROOT + "/vid"),
        prn (STORAGE_ROOT + "/prn"),
        upl (STORAGE_ROOT + "/upl"),
        eml (STORAGE_ROOT + "/eml"),
        sms (STORAGE_ROOT + "/sms"),
        qrc  (STORAGE_ROOT + "/qrc"),
        ;
        
        private final String path;
        
        private StorageType(String path) {
            this.path = path;
        }

        public String getPath() {
            return path;
        }
        
    }
    
    public static String composePath(StorageType type, Date date, String filename) {
        if (type == null) {
            return null;
        }
        if (date == null) {
            return null;
        }
        if (StringUtils.isBlank(filename)) {
            return null;
        }
        
        return type.getPath() + "/" + StringUtils.right("0000" + TimeUtils.year(date), 4) + "/" + StringUtils.right("00" + TimeUtils.month(date), 2) + "/" + filename;
    }
    
}
