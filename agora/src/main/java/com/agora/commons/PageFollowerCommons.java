package com.agora.commons;

import com.agora.dao.PageDao;
import com.agora.dao.PageFollowerDao;
import com.agora.dao.UserDao;
import com.agora.page.PageFollowerEntry;
import com.agora.pojo.Page;
import com.agora.pojo.PageFollower;
import com.agora.pojo.User;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class PageFollowerCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(PageFollowerCommons.class.getName());

    public static List<PageFollowerEntry> toEntries(List<PageFollower> pageFollowerList) {
        List<PageFollowerEntry> pageFollowerEntryList = null;
        if ((pageFollowerList != null) &&
            (pageFollowerList.size() > 0)) {
            for (PageFollower pageFollower : pageFollowerList) {
                PageFollowerEntry entry = toEntry(pageFollower);
                if (pageFollowerEntryList == null) {
                    pageFollowerEntryList = new ArrayList<>();
                }
                pageFollowerEntryList.add(entry);
            }   
        }
        return pageFollowerEntryList;
    }

    public static PageFollowerEntry toEntry(PageFollower pageFollower) {
        PageFollowerEntry entry = null;
        if (pageFollower != null) {
            
            entry = new PageFollowerEntry();
            entry.setPageFollower(pageFollower);
            
            if (pageFollower.getUserId() != null) {
                User user = null;
                try {
                    user = UserDao.loadUser(pageFollower.getUserId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if (user != null) {
                    entry.setUser(user);
                }
            }
            if (pageFollower.getPageId() != null) {
                Page page = null;
                try {
                    page = PageDao.loadPage(pageFollower.getPageId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if (page != null) {
                    entry.setPage(page);
                    long followerCount = 0;

                    try {
                        followerCount = PageFollowerDao.loadPageFollowerCount(page.getId());
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    if (page.getFollowers() != null) {
                        followerCount += page.getFollowers();
                    }
                    entry.setFollowerCount(followerCount);
                }
            
            }
        }
        return entry;
    }
    
}
