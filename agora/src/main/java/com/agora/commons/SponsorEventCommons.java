package com.agora.commons;

import com.agora.dao.EventDao;
import com.agora.dao.SponsorEventDao;
import com.agora.pojo.Event;
import com.agora.pojo.SponsorEvent;
import java.util.ArrayList;
import java.util.List;

/**
 * Commons utilities for SponsorEvent functionality
 * 
 * <AUTHOR> Agent
 */
public class SponsorEventCommons {

    public static class SponsorEventEntry {
        private SponsorEvent sponsorEvent;
        private Event event;

        public SponsorEventEntry(SponsorEvent sponsorEvent, Event event) {
            this.sponsorEvent = sponsorEvent;
            this.event = event;
        }

        public SponsorEvent getSponsorEvent() {
            return sponsorEvent;
        }

        public void setSponsorEvent(SponsorEvent sponsorEvent) {
            this.sponsorEvent = sponsorEvent;
        }

        public Event getEvent() {
            return event;
        }

        public void setEvent(Event event) {
            this.event = event;
        }
    }

    /**
     * Load sponsor events with their associated event data
     */
    public static List<SponsorEventEntry> loadSponsorEventEntries() throws Exception {
        List<SponsorEvent> sponsorEvents = SponsorEventDao.loadSponsorEventList();
        List<SponsorEventEntry> entries = new ArrayList<>();

        for (SponsorEvent sponsorEvent : sponsorEvents) {
            if (sponsorEvent.getEventId() != null) {
                Event event = EventDao.loadEvent(sponsorEvent.getEventId());
                if (event != null) {
                    entries.add(new SponsorEventEntry(sponsorEvent, event));
                }
            }
        }

        return entries;
    }

    /**
     * Load active sponsor events with their associated event data
     */
    public static List<SponsorEventEntry> loadActiveSponsorEventEntries() throws Exception {
        List<SponsorEvent> sponsorEvents = SponsorEventDao.loadActiveSponsorEventList();
        List<SponsorEventEntry> entries = new ArrayList<>();

        for (SponsorEvent sponsorEvent : sponsorEvents) {
            if (sponsorEvent.getEventId() != null) {
                Event event = EventDao.loadEvent(sponsorEvent.getEventId());
                if (event != null) {
                    entries.add(new SponsorEventEntry(sponsorEvent, event));
                }
            }
        }

        return entries;
    }
}
