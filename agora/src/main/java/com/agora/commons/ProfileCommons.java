package com.agora.commons;

import com.agora.pojo.User;
import com.agora.pojo.types.ProfileType;
import java.util.Objects;
import org.apache.commons.lang3.EnumUtils;

/**
 *
 * <AUTHOR>
 */
public class ProfileCommons {

    public static boolean isHead(User user) {
        return isHead(user.getProfileType());
    }
    
    public static boolean isHead(String profileType) {
        return isHead(EnumUtils.getEnum(ProfileType.class, profileType));
    }
    
    public static boolean isHead(ProfileType profileType) {
        return
                Objects.equals(ProfileType.system, profileType) ||
                Objects.equals(ProfileType.admin, profileType) ||
                Objects.equals(ProfileType.head, profileType)
                ;
    }
    
    public static boolean isNotHead(User user) {
        return isNotHead(user.getProfileType());
    }
    
    public static boolean isNotHead(String profileType) {
        return isNotHead(EnumUtils.getEnum(ProfileType.class, profileType));
    }
    
    public static boolean isNotHead(ProfileType profileType) {
        return !isHead(profileType);
    }
    
    public static boolean isNetwork(User user) {
        return isNetwork(user.getProfileType());
    }
    
    public static boolean isNetwork(String profileType) {
        return isNetwork(EnumUtils.getEnum(ProfileType.class, profileType));
    }
    
    public static boolean isNetwork(ProfileType profileType) {
        return
                Objects.equals(ProfileType.vendor, profileType);
    }
    
    public static boolean isNotNetwork(User user) {
        return isNotNetwork(user.getProfileType());
    }
    
    public static boolean isNotNetwork(String profileType) {
        return isNotNetwork(EnumUtils.getEnum(ProfileType.class, profileType));
    }
    
    public static boolean isNotNetwork(ProfileType profileType) {
        return !isNetwork(profileType);
    }
    
    public static boolean isCustomer(User user) {
        return isCustomer(user.getProfileType());
    }
    
    public static boolean isCustomer(String profileType) {
        return isCustomer(EnumUtils.getEnum(ProfileType.class, profileType));
    }
    
    public static boolean isCustomer(ProfileType profileType) {
        return
                Objects.equals(ProfileType.customer, profileType) ||
                Objects.equals(ProfileType.unconfirmed, profileType)
                ;
    }
    
    public static boolean isNotCustomer(User user) {
        return isNotCustomer(user.getProfileType());
    }
    
    public static boolean isNotCustomer(String profileType) {
        return isNotCustomer(EnumUtils.getEnum(ProfileType.class, profileType));
    }
    
    public static boolean isNotCustomer(ProfileType profileType) {
        return !isCustomer(profileType);
    }
    
    public static boolean isConfirmed(User user) {
        return isConfirmed(user.getProfileType());
    }
    
    public static boolean isConfirmed(String profileType) {
        return isConfirmed(EnumUtils.getEnum(ProfileType.class, profileType));
    }
    
    public static boolean isConfirmed(ProfileType profileType) {
        return
                Objects.equals(ProfileType.system, profileType) ||
                Objects.equals(ProfileType.admin, profileType) ||
                Objects.equals(ProfileType.head, profileType) ||
                Objects.equals(ProfileType.customer, profileType)
                ;
    }
    
    public static boolean isNotConfirmed(User user) {
        return isNotConfirmed(user.getProfileType());
    }
    
    public static boolean isNotConfirmed(String profileType) {
        return isNotConfirmed(EnumUtils.getEnum(ProfileType.class, profileType));
    }
    
    public static boolean isNotConfirmed(ProfileType profileType) {
        return !isConfirmed(profileType);
    }
    
}
