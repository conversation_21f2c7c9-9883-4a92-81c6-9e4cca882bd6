package com.agora.commons;

import com.agora.pojo.types.MailnotificationStatusType;
import com.agora.dao.CustomerNotificationDao;
import com.agora.dao.EntityNotificationDao;
import com.agora.dao.FirmDao;
import com.agora.dao.MailnotificationDao;
import com.agora.dao.UserDao;
import com.agora.pojo.CustomerNotification;
import com.agora.pojo.EntityNotification;
import com.agora.pojo.Firm;
import com.agora.pojo.Mailnotification;
import com.agora.pojo.MailnotificationInOut;
import com.agora.pojo.User;
import com.agora.support.Geocoder;
import com.agora.util.TimeUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

/**
 *
 * <AUTHOR>
 */
public class MailnotificationCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(MailnotificationCommons.class.getName());
    private static final String APIKEY_USER = "<EMAIL>";

    public enum OperationType {
        sendnotification
    }
    
    public static boolean alignmentStart(OperationType operation, ObjectId userId) {
        Mailnotification mailnotification = loadMailnotification();
        if (mailnotification == null) {
            mailnotification = new Mailnotification();
        }
        if (StringUtils.equalsIgnoreCase(mailnotification.getStatus(), MailnotificationStatusType.processing.toString())) {
            return false;
        }
        if (userId == null) {
            return false;
        }
        
        mailnotification.setStatus(MailnotificationStatusType.processing.toString());
        mailnotification.setOperation(operation.toString());
        mailnotification.setCount(null);
        mailnotification.setRow(null);
        mailnotification.setItem(null);
        mailnotification.setErrors(null);
        
        mailnotification.setUserId(userId);

        saveMailnotification(mailnotification);
        
        return true;
    }

    public static boolean alignmentProcessing() {
        Mailnotification mailnotification = loadMailnotification();
        if (mailnotification == null) {
            return false;
        }
        
        return StringUtils.equalsIgnoreCase(mailnotification.getStatus(), MailnotificationStatusType.processing.toString());
    }

    public static boolean alignmentError(String error) {
        return alignmentError(error, null);
    }
    
    public static boolean alignmentError(String error, String exception) {
        Mailnotification mailnotification = loadMailnotification();
        if (mailnotification == null) {
            return false;
        }
        if (StringUtils.isBlank(error)) {
            return false;
        }
        if (!StringUtils.equalsIgnoreCase(mailnotification.getStatus(), MailnotificationStatusType.processing.toString())) {
            return false;
        }
        
        if (mailnotification.getErrors() == null) {
            mailnotification.setErrors(new ArrayList<>());
        }
        
        String text = error;
        if (StringUtils.isNotBlank(exception)) {
            text += ";" + exception;
        }
        mailnotification.getErrors().add(text);

        saveMailnotification(mailnotification);
        return true;
    }
    
    public static boolean alignmentCount(int count) {
        Mailnotification mailnotification = loadMailnotification();
        if (mailnotification == null) {
            return false;
        }
        if (!StringUtils.equalsIgnoreCase(mailnotification.getStatus(), MailnotificationStatusType.processing.toString())) {
            return false;
        }
        if (count < 0) {
            return false;
        }
        
        mailnotification.setCount(count);

        saveMailnotification(mailnotification);
        return true;
    }
    
    public static boolean alignmentInOutErrors(MailnotificationInOut inout, List<String> errors) {
        return alignmentInOutErrors(inout != null ? Arrays.asList(new MailnotificationInOut[] {inout}) : null, errors);
    }
    
    public static boolean alignmentInOutErrors(List<MailnotificationInOut> inouts, List<String> errors) {
        Mailnotification mailnotification = loadMailnotification();
        if (mailnotification == null) {
            return false;
        }
        if (!StringUtils.equalsIgnoreCase(mailnotification.getStatus(), MailnotificationStatusType.processing.toString())) {
            return false;
        }
        
        mailnotification.setInOuts(inouts);
        mailnotification.setErrors(errors);

        saveMailnotification(mailnotification);
        return true;
    }
    
    public static boolean alignmentProgress(int row, String value) {
        Mailnotification mailnotification = loadMailnotification();
        if (mailnotification == null) {
            return false;
        }
        if (row <= 0) {
            return false;
        }
        if (StringUtils.isBlank(value)) {
            return false;
        }
        if (!StringUtils.equalsIgnoreCase(mailnotification.getStatus(), MailnotificationStatusType.processing.toString())) {
            return false;
        }
        
        mailnotification.setRow(row);
        mailnotification.setItem(value);

        saveMailnotification(mailnotification);
        return true;
    }
    
    private static List<String> _errors = new ArrayList<>();
    public static boolean alignmentRowError(int row, String item, String error) {
        return alignmentRowError(row, item, error, null);
    }
    
    public static boolean alignmentRowError(int row, String value, String error, String exception) {
        String text = (row + 1 + 1) + ";" + error + ";" + value;
        if (StringUtils.isNotBlank(exception)) {
            text += ";" + exception;
        }
        _errors.add(text);
        return true;
    }
    
    public static List<String> alignmentRowErrorGet() {
        return _errors;
    }
    
    public static void alignmentRowErrorClear() {
        _errors = new ArrayList<>();
    }
    
    public static boolean alignmentFinish() {
        return alignmentFinish(MailnotificationStatusType.done);
    }

    public static boolean alignmentFinish(MailnotificationStatusType status) {
        Mailnotification mailnotification = loadMailnotification();
        if (mailnotification == null) {
            return false;
        }
        if (status == null) {
            return false;
        }
        if (!StringUtils.equalsIgnoreCase(mailnotification.getStatus(), MailnotificationStatusType.processing.toString())) {
            return false;
        }

        mailnotification.setStatus(status.toString());

        int errorCount = (mailnotification.getErrors() != null) ? mailnotification.getErrors().size() : 0;
        long millies = TimeUtils.now().getTime() - mailnotification.getCreation().getTime();
        mailnotification.setErrorCount(errorCount);
        mailnotification.setMillies((int) millies);

        saveMailnotification(mailnotification);
        return true;
    }

    /**
     * Process mail notifications for a given frequency
     * @param request HTTP request (can be null for automated calls)
     * @param userId User ID performing the operation
     * @param username Username performing the operation
     * @param frequency Frequency type: "day", "week", or "month"
     * @return true if processing was successful
     */
    public static boolean processMailNotifications(Request request, ObjectId userId, String username, String frequency) {
        return processMailNotifications(request, userId, username, frequency, false);
    }

    /**
     * Process mail notifications for a given frequency with optional time checking
     * @param request HTTP request (can be null for automated calls)
     * @param userId User ID performing the operation
     * @param username Username performing the operation
     * @param frequency Frequency type: "day", "week", or "month"
     * @param checkTimeElapsed If true, check if enough time has passed since last send
     * @return true if processing was successful
     */
    public static boolean processMailNotifications(Request request, ObjectId userId, String username, String frequency, boolean checkTimeElapsed) {
        if (userId == null) {
            return false;
        }

        if (username == null) {
            return false;
        }

        // Check if enough time has elapsed since last notification
        if (checkTimeElapsed && !shouldSendNotification(frequency)) {
            LOGGER.info("Skipping {} notification - not enough time elapsed since last send", frequency);
            return true; // Return true as this is not an error condition
        }

        List<CustomerNotification> customerNotificationList = null;
        try {
            customerNotificationList = CustomerNotificationDao.loadCustomerNotificationActiveListByFrequency(frequency);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }

        MailnotificationInOut inout = new MailnotificationInOut();

        // count
        if (customerNotificationList != null && !customerNotificationList.isEmpty()) {
            int count = customerNotificationList.size();
            alignmentCount(count);
            alignmentRowErrorClear();

            Date endDate = new Date();
            Date startDate = DateUtils.addDays(new Date(), -1);

            if (StringUtils.equalsIgnoreCase(frequency, "week")) {
                startDate = DateUtils.addWeeks(new Date(), -1);
            }
            if (StringUtils.equalsIgnoreCase(frequency, "month")) {
                startDate = DateUtils.addMonths(new Date(), -1);
            }

            // mappa eventId, EntityNotification
            for (int r = 0; r < customerNotificationList.size(); r++) {
                CustomerNotification customerNotification = customerNotificationList.get(r);
                Map<ObjectId, EntityNotification> mapEvent = new HashMap<>();
                List<EntityNotification> entityNotificationList = null;
                List<ObjectId> entityNotificationIds = new ArrayList<>();
                try {
                    entityNotificationList = EntityNotificationDao.loadEntityNotificationListByUserIdAndDateRangeForNotify(customerNotification.getUserId(), startDate, endDate);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if (entityNotificationList != null) {
                    int maxEvent = 0;
                    int maxKm = customerNotification.getRangeKm();
                    double distanceKm;
                    for (EntityNotification entityNotification : entityNotificationList) {
                        distanceKm = Geocoder.distance(entityNotification.getLat(), entityNotification.getLng(), customerNotification.getLat(), customerNotification.getLng());
                        if (distanceKm <= maxKm) {
                            if (!mapEvent.containsKey(entityNotification.getEventId())) {
                                mapEvent.put(entityNotification.getEventId(), entityNotification);
                                entityNotificationIds.add(entityNotification.getId());
                                maxEvent++;
                                if (maxEvent == 6) {
                                    break;
                                }
                            }
                        }
                    }

                    // progress
                    alignmentProgress(r, customerNotification.getEmail());

                    if (!entityNotificationIds.isEmpty()) {
                        NotificationCommons.notifyEvent(request, customerNotification, entityNotificationIds);

                        try {
                            EntityNotificationDao.updateEntityNotificationsSent(entityNotificationIds);
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }

                    }

                }

            }

            alignmentFinish();

            // Update last notification sent date in Firm
            updateLastNotificationDate(frequency);
        }

        return true;
    }

    /**
     * Check if enough time has elapsed since the last notification of the given frequency
     * @param frequency Frequency type: "day", "week", or "month"
     * @return true if notification should be sent, false otherwise
     */
    public static boolean shouldSendNotification(String frequency) {
        try {
            Firm firm = FirmDao.loadFirm();
            if (firm == null) {
                return false;
            }

            Date now = TimeUtils.now();
            Date lastSent = null;
            long requiredHours = 0;

            if (StringUtils.equalsIgnoreCase(frequency, "day")) {
                lastSent = firm.getLastDailyNotificationSent();
                requiredHours = 24; // 24 hours for daily
            } else if (StringUtils.equalsIgnoreCase(frequency, "week")) {
                lastSent = firm.getLastWeeklyNotificationSent();
                requiredHours = 24 * 7; // 7 days for weekly
            } else {
                // For monthly or other frequencies, always allow
                return true;
            }

            if (lastSent == null) {
                // Never sent before, allow sending
                return true;
            }

            long hoursElapsed = TimeUtils.diffInHours(lastSent, now);
            return hoursElapsed >= requiredHours;

        } catch (Exception ex) {
            LOGGER.error("Error checking notification timing", ex);
            return false;
        }
    }

    /**
     * Update the last notification sent date for the given frequency
     * @param frequency Frequency type: "day", "week", or "month"
     */
    private static void updateLastNotificationDate(String frequency) {
        try {
            Firm firm = FirmDao.loadFirm();
            if (firm == null) {
                return;
            }

            Date now = TimeUtils.now();

            if (StringUtils.equalsIgnoreCase(frequency, "day")) {
                firm.setLastDailyNotificationSent(now);
            } else if (StringUtils.equalsIgnoreCase(frequency, "week")) {
                firm.setLastWeeklyNotificationSent(now);
            }
            // For monthly or other frequencies, we don't track the date

            FirmDao.updateFirm(firm);
            LOGGER.info("Updated last {} notification sent date to {}", frequency, now);

        } catch (Exception ex) {
            LOGGER.error("Error updating last notification date for frequency: " + frequency, ex);
        }
    }

    /**
     * Process automated mail notifications for both daily and weekly frequencies
     * This method handles authentication automatically and checks timing constraints
     * @return true if at least one notification type was processed successfully
     */
    public static boolean processAutomatedMailNotifications() {
        try {
            // Check if mail notifications are enabled
            Firm firm = FirmDao.loadFirm();
            if (firm == null || BooleanUtils.isNotTrue(firm.getMailNotificationEnabled())) {
                LOGGER.info("Mail notifications are disabled, skipping automated processing");
                return false;
            }

            // Check if another notification process is already running
            if (alignmentProcessing()) {
                LOGGER.info("Another notification process is running, skipping automated processing");
                return false;
            }

            // Get the API user for automated processing
            User apiUser = UserDao.loadUserByUsername(APIKEY_USER);
            if (apiUser == null) {
                LOGGER.error("Could not load API user: {}", APIKEY_USER);
                return false;
            }

            boolean anyProcessed = false;

            // Process daily notifications if needed
            if (shouldSendNotification("day")) {
                LOGGER.info("Processing automated daily notifications");
                if (processAutomatedNotificationsByFrequency("day", apiUser)) {
                    anyProcessed = true;
                }
            }

            // Process weekly notifications if needed
            if (shouldSendNotification("week")) {
                LOGGER.info("Processing automated weekly notifications");
                if (processAutomatedNotificationsByFrequency("week", apiUser)) {
                    anyProcessed = true;
                }
            }

            if (!anyProcessed) {
                LOGGER.info("No automated notifications needed at this time");
            }

            return anyProcessed;

        } catch (Exception ex) {
            LOGGER.error("Error in automated mail notification processing", ex);
            return false;
        }
    }

    /**
     * Process automated notifications for a specific frequency
     * @param frequency The frequency type ("day" or "week")
     * @param apiUser The API user to use for processing
     * @return true if processing was successful
     */
    private static boolean processAutomatedNotificationsByFrequency(String frequency, User apiUser) {
        try {
            // Start the notification process
            if (!alignmentStart(OperationType.sendnotification, apiUser.getId())) {
                LOGGER.error("Could not start notification process for frequency: {}", frequency);
                return false;
            }

            // Process the notifications with time checking enabled
            boolean success = processMailNotifications(null, apiUser.getId(), apiUser.getUsername(), frequency, true);

            if (success) {
                alignmentFinish();
                LOGGER.info("Successfully processed automated {} notifications", frequency);
            } else {
                alignmentFinish(MailnotificationStatusType.error);
                LOGGER.error("Failed to process automated {} notifications", frequency);
            }

            return success;

        } catch (Exception ex) {
            LOGGER.error("Error processing automated notifications for frequency: " + frequency, ex);
            try {
                alignmentFinish(MailnotificationStatusType.error);
            } catch (Exception finishEx) {
                LOGGER.error("Error finishing alignment after exception", finishEx);
            }
            return false;
        }
    }


    ////////////
    // internals
    
    private static Mailnotification loadMailnotification() {
        Mailnotification mailnotification = null;
        try {
            mailnotification = MailnotificationDao.loadProcessingMailnotification();
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        return mailnotification;
    }
    
    private static Mailnotification saveMailnotification(Mailnotification mailnotification) {
        if (mailnotification == null) {
            return null;
        }
        if (mailnotification.getId() == null) {
            try {
                ObjectId mailnotificationId = MailnotificationDao.insertMailnotification(mailnotification);
                mailnotification.setId(mailnotificationId);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        } else {
            try {
                MailnotificationDao.updateMailnotification(mailnotification);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }
        return mailnotification;
    }
    
}
