package com.agora.commons;

import com.agora.dao.*;
import com.agora.event.EventEntry;
import com.agora.pojo.Customer;
import com.agora.pojo.Event;
import com.agora.pojo.EventFollower;
import com.agora.pojo.Page;
import com.agora.pojo.User;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class EventCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(EventCommons.class.getName());

    public static boolean isValidEvent(Event event) {
        boolean valid = (event != null)
                && (StringUtils.isNotBlank(event.getName()));

        if (!valid) {
            if (event != null) {
                LOGGER.warn(
                        "page validation problem:\n"
                        + "- Name " + event.getName() + "\n"
                );
            } else {
                LOGGER.warn(
                        "page validation problem:\n"
                        + "- empty"
                );
            }
        }

        return valid;
    }

    public static List<EventEntry> toEntries(List<Event> eventList) {
        return toEntries(eventList, null, null);
    }

    public static List<EventEntry> toEntries(List<Event> eventList, ObjectId userId) {
        return toEntries(eventList, userId, null);
    }

    public static List<EventEntry> toEntries(List<Event> eventList, ObjectId userId, List<String> fields) {
        List<EventEntry> eventEntryList = null;
        if (eventList != null && !eventList.isEmpty()) {
            List<ObjectId> userIds = new ArrayList<>();
            for (Event event : eventList) {
                if (event.getUserId() != null) {
                    if (!userIds.contains(event.getUserId())) {
                        userIds.add(event.getUserId());
                    }
                }
            }

            Map<ObjectId, User> userMap = new HashMap<>();
            Map<ObjectId, Customer> customerMap = new HashMap<>();
            if (!userIds.isEmpty()) {
                try {
                    List<User> users = null;
                    if (fields == null || fields.contains("user")) {
                        users = UserDao.loadUserList(userIds);
                    }
                    List<Customer> customers = null;
                    if (fields == null || fields.contains("customer")) {
                        customers = CustomerDao.loadCustomersByUserId(userIds);
                    }

                    if ((users != null && !users.isEmpty()) || (customers != null && !customers.isEmpty())) {
                        if (users != null) {
                            for (User user : users) {
                                userMap.put(user.getId(), user);
                            }
                        }
                        if (customers != null) {
                            for (Customer customer : customers) {
                                customerMap.put(customer.getUserId(), customer);
                            }
                        }
                    }
                } catch (Exception ex) {
                    // suppressed
                }
            }

            for (Event event : eventList) {
                EventEntry entry = toEntry(event, userId, userMap, customerMap, fields);
                if (eventEntryList == null) {
                    eventEntryList = new ArrayList<>();
                }
                eventEntryList.add(entry);
            }
        }
        return eventEntryList;
    }

    public static EventEntry toEntry(Event event) {
        return toEntry(event, null, null);
    }

    public static EventEntry toEntry(Event event, ObjectId userId) {
        return toEntry(event, userId, null);
    }

    public static EventEntry toEntry(Event event, ObjectId userId, Map<ObjectId, User> users, Map<ObjectId, Customer> customers, List<String> fields) {
        EventEntry entry = null;
        if (event != null) {
            entry = new EventEntry();
            entry.setEvent(event);
            if (event.getUserId() != null) {
                if (users.containsKey(event.getUserId())) {
                    entry.setUser(users.get(event.getUserId()));
                }
                if (customers.containsKey(event.getUserId())) {
                    entry.setCustomer(customers.get(event.getUserId()));
                }
            }

            if (fields == null || fields.contains("follower")) {
                long followerCount = 0;
                try {
                    followerCount = EventFollowerDao.loadEventFollowerCount(event.getId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                entry.setFollowerCount(followerCount);
            }

            if (fields == null || fields.contains("iFollow")) {
                Boolean iFollow = false;
                if (userId != null) {
                    try {
                        iFollow = EventFollowerDao.loadEventFollowerCountByUserAndEvent(userId, event.getId()) > 0;
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                }
                entry.setiFollow(iFollow);
            }

            if (fields == null || fields.contains("eventFollower")) {
                List<EventFollower> eventFollowerList = null;
                if (userId != null) {
                    try {
                        eventFollowerList = EventFollowerDao.loadEventFollowerExcludeOne(event.getId(), userId, 5);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                } else {
                    try {
                        eventFollowerList = EventFollowerDao.loadEventFollowerExcludeOne(event.getId(), 5);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                }

                if (eventFollowerList != null && !eventFollowerList.isEmpty()) {
                    List<Page> followerPages = new ArrayList<>();
                    Page userPage;
                    for (EventFollower eventFollower : eventFollowerList) {
                        try {
                            userPage = PageDao.loadUserPage(eventFollower.getUserId());
                            if (userPage != null) {
                                followerPages.add(userPage);
                            }
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                    }
                    entry.setFollowerPages(followerPages);
                }
            }

        }
        return entry;
    }

    public static EventEntry toEntry(Event event, ObjectId userId, List<String> fields) {
        EventEntry entry = null;
        if (event != null) {
            entry = new EventEntry();
            entry.setEvent(event);
            // user
            User user = null;
            if (event.getUserId() != null) {
                try {
                    user = UserDao.loadUser(event.getUserId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
            }
            if (user != null) {
                entry.setUser(user);
            }

            // customer
            Customer customer = null;
            if (user != null) {
                try {
                    customer = CustomerDao.loadCustomerByUserId(user.getId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
            }
            if (customer != null) {
                entry.setCustomer(customer);
            }

            long followerCount = 0;

            try {
                followerCount = EventFollowerDao.loadEventFollowerCount(event.getId());
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            entry.setFollowerCount(followerCount);
            Boolean iFollow = false;

            if (userId != null) {
                try {
                    iFollow = EventFollowerDao.loadEventFollowerCountByUserAndEvent(userId, event.getId()) > 0;
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
            }
            entry.setiFollow(iFollow);

            List<EventFollower> eventFollowerList = null;
            if (userId != null) {
                try {
                    eventFollowerList = EventFollowerDao.loadEventFollowerExcludeOne(event.getId(), userId, 5);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
            } else {
                try {
                    eventFollowerList = EventFollowerDao.loadEventFollowerExcludeOne(event.getId(), 5);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
            }

            if (eventFollowerList != null && !eventFollowerList.isEmpty()) {
                List<Page> followerPages = new ArrayList<>();
                Page userPage;
                for (EventFollower eventFollower : eventFollowerList) {
                    try {
                        userPage = PageDao.loadUserPage(eventFollower.getUserId());
                        if (userPage != null) {
                            followerPages.add(userPage);
                        }
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                }
                entry.setFollowerPages(followerPages);
            }

        }
        return entry;
    }

    public static List<EventEntry> toWallEntries(List<Event> eventList) {
        return toWallEntries(eventList, null);
    }

    public static List<EventEntry> toWallEntries(List<Event> eventList, ObjectId userId) {
        List<EventEntry> eventEntryList = null;
        if ((eventList != null)
                && (eventList.size() > 0)) {
            for (Event event : eventList) {
                EventEntry entry = toWallEntry(event, userId);
                if (eventEntryList == null) {
                    eventEntryList = new ArrayList<>();
                }
                eventEntryList.add(entry);
            }
        }
        return eventEntryList;
    }

    public static EventEntry toWallEntry(Event event) {
        return toWallEntry(event, null);
    }

    public static EventEntry toWallEntry(Event event, ObjectId userId) {
        EventEntry entry = null;
        if (event != null) {
            entry = new EventEntry();
            entry.setEvent(event);

            long followerCount = 0;

            try {
                followerCount = EventFollowerDao.loadEventFollowerCount(event.getId());
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            entry.setFollowerCount(followerCount);
            Boolean iFollow = false;
            if (userId != null) {
                try {
                    iFollow = EventFollowerDao.loadEventFollowerCountByUserAndEvent(userId, event.getId()) > 0;
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
            }
            entry.setiFollow(iFollow);

            // gestione pagine collegate (avatar-xs)
            List<ObjectId> alreadyLoadedPages = new ArrayList<>();
            List<Page> extraPages = new ArrayList<>();
            ObjectId eventAvatarId = null;
            if (event.getValidPageIds() != null && !event.getValidPageIds().isEmpty()) {
                eventAvatarId = event.getValidPageIds().get(0);
            } else if (event.getPageIds() != null && !event.getPageIds().isEmpty()) {
                eventAvatarId = event.getPageIds().get(0);
            }

            if (event.getValidPageIds() != null && !event.getValidPageIds().isEmpty()) {
                for (ObjectId pageId : event.getValidPageIds()) {
                    try {
                        if (!alreadyLoadedPages.contains(pageId)) {
                            alreadyLoadedPages.add(pageId);
                            if (!Objects.equals(eventAvatarId, pageId)) {
                                Page page = PageDao.loadPage(pageId);
                                if (page != null && page.getProfileImageId() != null) {
                                // if (page != null) {
                                    extraPages.add(page);
                                }
                            }
                        }
                    } catch (Exception ex) {
                        // suppressed
                    }
                }
            }
            if (event.getPageIds() != null && !event.getPageIds().isEmpty()) {
                for (ObjectId pageId : event.getPageIds()) {
                    try {
                        if (!alreadyLoadedPages.contains(pageId)) {
                            alreadyLoadedPages.add(pageId);
                            if (!Objects.equals(eventAvatarId, pageId)) {
                                Page page = PageDao.loadPage(pageId);
                                if (page != null && page.getProfileImageId() != null) {
                                // if (page != null) {
                                    extraPages.add(page);
                                }
                            }
                        }
                    } catch (Exception ex) {
                        // suppressed
                    }
                }
            }
            entry.setExtraPages(extraPages);

            // nuova gestione container
            if (StringUtils.equalsIgnoreCase(event.getType(), "container")) {
                try {
                    List<ObjectId> followedPageIds = PageFollowerDao.loadPageFollowedIdListByUser(userId);
                    if (!followedPageIds.isEmpty()) {
                        entry.setChildEvents(EventDao.loadEventListByParentIdAndPageIds(event.getId(), followedPageIds, 2));
                    }
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
            }
        }
        return entry;
    }
}
