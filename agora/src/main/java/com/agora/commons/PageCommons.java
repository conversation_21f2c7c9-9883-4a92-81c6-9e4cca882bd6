package com.agora.commons;

import com.agora.dao.CustomerDao;
import com.agora.dao.PageFollowerDao;
import com.agora.dao.UserDao;
import com.agora.page.PageEntry;
import com.agora.pojo.Customer;
import com.agora.pojo.Page;
import com.agora.pojo.User;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class PageCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(PageCommons.class.getName());

    public static boolean isValidPage(Page page) {
        boolean valid = (page != null)
                && (StringUtils.isNotBlank(page.getName()));

        if (!valid) {
            if (page != null) {
                LOGGER.warn(
                        "page validation problem:\n"
                        + "- Name " + page.getName() + "\n"
                );
            } else {
                LOGGER.warn(
                        "page validation problem:\n"
                        + "- empty"
                );
            }
        }

        return valid;
    }

    public static List<PageEntry> toEntries(List<Page> pageList) {
        return toEntries(pageList, null);
    }

    public static List<PageEntry> toEntries(List<Page> pageList, List<String> fields) {
        List<PageEntry> pageEntryList = null;
        if (pageList != null && !pageList.isEmpty()) {
            List<ObjectId> userIds = new ArrayList<>();
            for (Page page : pageList) {
                if (page.getUserId() != null) {
                    if (!userIds.contains(page.getUserId())) {
                        userIds.add(page.getUserId());
                    }
                }
            }

            Map<ObjectId, User> userMap = new HashMap<>();
            Map<ObjectId, Customer> customerMap = new HashMap<>();
            if (!userIds.isEmpty()) {
                try {
                    List<User> users = null;
                    if (fields == null || fields.contains("user")) {
                        users = UserDao.loadUserList(userIds);
                    }
                    List<Customer> customers = null;
                    if (fields == null || fields.contains("customer")) {
                        customers = CustomerDao.loadCustomersByUserId(userIds);
                    }

                    if ((users != null && !users.isEmpty()) || (customers != null && !customers.isEmpty())) {
                        if (users != null) {
                            for (User user : users) {
                                userMap.put(user.getId(), user);
                            }
                        }
                        if (customers != null) {
                            for (Customer customer : customers) {
                                customerMap.put(customer.getUserId(), customer);
                            }
                        }
                    }
                } catch (Exception ex) {
                    // suppressed
                }
            }

            for (Page page : pageList) {
                PageEntry entry = toEntry(page, userMap, customerMap, fields);
                if (pageEntryList == null) {
                    pageEntryList = new ArrayList<>();
                }
                pageEntryList.add(entry);
            }
        }
        return pageEntryList;
    }

    private static PageEntry toEntry(Page page, Map<ObjectId, User> users, Map<ObjectId, Customer> customers, List<String> fields) {
        PageEntry entry = null;
        if (page != null) {

            entry = new PageEntry();
            entry.setPage(page);

            if (page.getUserId() != null) {
                if (users.containsKey(page.getUserId())) {
                    entry.setUser(users.get(page.getUserId()));
                }
                if (customers.containsKey(page.getUserId())) {
                    entry.setCustomer(customers.get(page.getUserId()));
                }
            }

            if (fields == null || fields.contains("follower")) {
                long followerCount = 0;
                try {
                    followerCount = PageFollowerDao.loadPageFollowerCount(page.getId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }

                entry.setFollowerCount(followerCount);
            }

        }
        return entry;
    }

    public static PageEntry toEntry(Page page) {
        PageEntry entry = null;
        if (page != null) {

            entry = new PageEntry();
            entry.setPage(page);

            User user = null;
            if (page.getUserId() != null) {
                try {
                    user = UserDao.loadUser(page.getUserId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if (user != null) {
                    entry.setUser(user);
                }
            }
            if (user != null) {
                Customer customer = null;
                try {
                    customer = CustomerDao.loadCustomerByUserId(user.getId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if (customer != null) {
                    entry.setCustomer(customer);
                }
            }

            long followerCount = 0;
            try {
                followerCount = PageFollowerDao.loadPageFollowerCount(page.getId());
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            entry.setFollowerCount(followerCount);

        }
        return entry;
    }
}
