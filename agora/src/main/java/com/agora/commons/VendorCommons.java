package com.agora.commons;

import com.agora.dao.UserDao;
import com.agora.dao.VendorDao;
import com.agora.pojo.User;
import com.agora.pojo.Vendor;
import com.agora.vendor.VendorEntry;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class VendorCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(VendorCommons.class.getName());

    public static String fullname(Vendor vendor) {
        String fullname = "*Nome* *Cognome*";
        if (vendor != null) {
            if (StringUtils.isNotBlank(vendor.getFullname())) {
                fullname = vendor.getFullname();
            } else {
                if (StringUtils.isNotBlank(vendor.getLastname())) {
                    fullname = vendor.getLastname();
                }
                if (StringUtils.isNotBlank(vendor.getName())) {
                    if (StringUtils.isBlank(fullname)) {
                        fullname = "";
                    } else {
                        fullname += " ";
                    }
                    fullname += vendor.getName();
                }
            }
        }
        return fullname;
    }

    public static List<VendorEntry> toEntries(List<Vendor> vendorList) {
        List<VendorEntry> vendorEntryList = null;
        if ((vendorList != null) &&
            (vendorList.size() > 0)) {
            for (Vendor vendor : vendorList) {
                VendorEntry entry = toEntry(vendor);
                if (vendorEntryList == null) {
                    vendorEntryList = new ArrayList<>();
                }
                vendorEntryList.add(entry);                
            }   
        }
        return vendorEntryList;
    }
    
    public static VendorEntry toEntry(Vendor vendor) {
        VendorEntry entry = null;
        if (vendor != null) {
            
            entry = new VendorEntry();
            entry.setVendor(vendor);
            
            if (vendor.getUserId() != null) {
                try {
                    User user = UserDao.loadUser(vendor.getUserId());
                    entry.setUser(user);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                    return null;
                }
            }
        }
        return entry;
    }
    
    public static Vendor defaultVendor() {
        Vendor vendor = null;
        try {
            vendor = VendorDao.loadVendorByEmail("<EMAIL>");
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        return vendor;
    }
    
}
