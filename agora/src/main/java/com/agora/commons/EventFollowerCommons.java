package com.agora.commons;

import com.agora.dao.EventDao;
import com.agora.dao.EventFollowerDao;
import com.agora.dao.UserDao;
import com.agora.event.EventFollowerEntry;
import com.agora.pojo.Event;
import com.agora.pojo.EventFollower;
import com.agora.pojo.User;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class EventFollowerCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(EventFollowerCommons.class.getName());

    public static List<EventFollowerEntry> toEntries(List<EventFollower> eventFollowerList) {
        List<EventFollowerEntry> eventFollowerEntryList = null;
        if ((eventFollowerList != null) &&
            (eventFollowerList.size() > 0)) {
            for (EventFollower eventFollower : eventFollowerList) {
                EventFollowerEntry entry = toEntry(eventFollower);
                if (eventFollowerEntryList == null) {
                    eventFollowerEntryList = new ArrayList<>();
                }
                eventFollowerEntryList.add(entry);
            }   
        }
        return eventFollowerEntryList;
    }

    public static EventFollowerEntry toEntry(EventFollower eventFollower) {
        EventFollowerEntry entry = null;
        if (eventFollower != null) {
            
            entry = new EventFollowerEntry();
            entry.setEventFollower(eventFollower);
            
            if (eventFollower.getUserId() != null) {
                User user = null;
                try {
                    user = UserDao.loadUser(eventFollower.getUserId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if (user != null) {
                    entry.setUser(user);
                }
            }
            if (eventFollower.getEventId() != null) {
                Event event = null;
                try {
                    event = EventDao.loadEvent(eventFollower.getEventId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if (event != null) {
                    entry.setEvent(event);
                }
                long followerCount = 0;
        
                try {
                    followerCount = EventFollowerDao.loadEventFollowerCount(event.getId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                entry.setFollowerCount(followerCount);
            
            }
        }
        return entry;
    }
    
}
