package com.agora.commons;

import com.agora.dao.PageDao;
import com.agora.dao.SponsorPageDao;
import com.agora.pojo.Page;
import com.agora.pojo.SponsorPage;
import java.util.ArrayList;
import java.util.List;

/**
 * Commons utilities for SponsorPage functionality
 * 
 * <AUTHOR> Agent
 */
public class SponsorPageCommons {

    public static class SponsorPageEntry {
        private SponsorPage sponsorPage;
        private Page page;

        public SponsorPageEntry(SponsorPage sponsorPage, Page page) {
            this.sponsorPage = sponsorPage;
            this.page = page;
        }

        public SponsorPage getSponsorPage() {
            return sponsorPage;
        }

        public void setSponsorPage(SponsorPage sponsorPage) {
            this.sponsorPage = sponsorPage;
        }

        public Page getPage() {
            return page;
        }

        public void setPage(Page page) {
            this.page = page;
        }
    }

    /**
     * Load sponsor pages with their associated page data
     */
    public static List<SponsorPageEntry> loadSponsorPageEntries() throws Exception {
        List<SponsorPage> sponsorPages = SponsorPageDao.loadSponsorPageList();
        List<SponsorPageEntry> entries = new ArrayList<>();

        for (SponsorPage sponsorPage : sponsorPages) {
            if (sponsorPage.getPageId() != null) {
                Page page = PageDao.loadPage(sponsorPage.getPageId());
                if (page != null) {
                    entries.add(new SponsorPageEntry(sponsorPage, page));
                }
            }
        }

        return entries;
    }

    /**
     * Load active sponsor pages with their associated page data
     */
    public static List<SponsorPageEntry> loadActiveSponsorPageEntries() throws Exception {
        List<SponsorPage> sponsorPages = SponsorPageDao.loadActiveSponsorPageList();
        List<SponsorPageEntry> entries = new ArrayList<>();

        for (SponsorPage sponsorPage : sponsorPages) {
            if (sponsorPage.getPageId() != null) {
                Page page = PageDao.loadPage(sponsorPage.getPageId());
                if (page != null) {
                    entries.add(new SponsorPageEntry(sponsorPage, page));
                }
            }
        }

        return entries;
    }
}
