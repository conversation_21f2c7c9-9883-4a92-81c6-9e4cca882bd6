package com.agora.commons;

import com.agora.dao.CustomerDao;
import com.agora.dao.VendorDao;
import com.agora.pojo.Customer;
import com.agora.pojo.User;
import com.agora.pojo.Vendor;
import com.agora.pojo.types.ProfileType;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class EntityCommons {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(EntityCommons.class.getName());

    public static ObjectId imageIdFromUser(User user) {
        ObjectId imageId = null;
        if (user != null) {
            try {
                ProfileType profileType = EnumUtils.getEnum(ProfileType.class, user.getProfileType());
                
                Vendor vendor;
                Customer customer;
                switch (profileType) {
                    case customer:
                        customer = CustomerDao.loadCustomerByUserId(user.getId());
                        if (customer != null) {
                            imageId = customer.getImageId();
                        }
                        break;
                    case system:
                    case admin:
                    case head:
                    case vendor:
                        vendor = VendorDao.loadVendorByUserId(user.getId());
                        if (vendor != null) {
                            imageId = vendor.getImageId();
                        }
                        break;        
                }
            } catch (Exception ex) {
                LOGGER.error("unable to load entity, exception is " + ex);
            }
        }
        return imageId;
    }
    
    public static ObjectId vendorIdFromUserWhereVendor(User user) {
        ObjectId vendorId = null;
        if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            Vendor vendor = null;
            try {
                vendor = VendorDao.loadVendorByUserId(user.getId());
            } catch (Exception ex) {
                LOGGER.error("unable to load vendor, exception is " + ex);
            }
            if (vendor != null) {
                vendorId = vendor.getId();
            }

        }
        return vendorId;
    }    
    
}
