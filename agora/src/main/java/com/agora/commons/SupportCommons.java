package com.agora.commons;

import com.agora.support.CityEntry;
import java.util.Iterator;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class SupportCommons {
    
    public static List<CityEntry> cleanCityList(List<CityEntry> list) {
        if ((list != null) &&
                (!list.isEmpty())) {
            Iterator<CityEntry> item = list.listIterator();
            while(item.hasNext()){
                if(StringUtils.isBlank(item.next().getCity())){
                    item.remove();
                }
            }            
        }
        return list;
    }
    
}
