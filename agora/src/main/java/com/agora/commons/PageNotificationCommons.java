package com.agora.commons;

import com.agora.dao.PageDao;
import com.agora.dao.PageNotificationDao;
import com.agora.dao.UserDao;
import com.agora.page.PageNotificationEntry;
import com.agora.pojo.Page;
import com.agora.pojo.PageNotification;
import com.agora.pojo.User;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class PageNotificationCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(PageNotificationCommons.class.getName());

    public static List<PageNotificationEntry> toEntries(List<PageNotification> pageNotificationList) {
        List<PageNotificationEntry> pageNotificationEntryList = null;
        if ((pageNotificationList != null) &&
            (pageNotificationList.size() > 0)) {
            for (PageNotification pageNotification : pageNotificationList) {
                PageNotificationEntry entry = toEntry(pageNotification);
                if (pageNotificationEntryList == null) {
                    pageNotificationEntryList = new ArrayList<>();
                }
                pageNotificationEntryList.add(entry);
            }   
        }
        return pageNotificationEntryList;
    }

    public static PageNotificationEntry toEntry(PageNotification pageNotification) {
        PageNotificationEntry entry = null;
        if (pageNotification != null) {
            
            entry = new PageNotificationEntry();
            entry.setPageNotification(pageNotification);
            
            if (pageNotification.getUserId() != null) {
                User user = null;
                try {
                    user = UserDao.loadUser(pageNotification.getUserId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if (user != null) {
                    entry.setUser(user);
                }
            }
            if (pageNotification.getPageId() != null) {
                Page page = null;
                try {
                    page = PageDao.loadPage(pageNotification.getPageId());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if (page != null) {
                    entry.setPage(page);
                    long followerCount = 0;

                    try {
                        followerCount = PageNotificationDao.loadPageNotificationCount(page.getId());
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    /*if (page.getFollowers() != null) {
                        followerCount += page.getFollowers();
                    }*/
                    entry.setFollowerCount(followerCount);
                }
            
            }
        }
        return entry;
    }
    
}
