package com.agora.commons;

import com.agora.core.Defaults;
import com.agora.core.MailTemplates;
import com.agora.core.Manager;
import com.agora.core.Manager.MailExtras;
import com.agora.core.Paths;
import com.agora.dao.CustomerDao;
import com.agora.dao.EntityNotificationDao;
import com.agora.dao.EventDao;
import com.agora.dao.FirmDao;
import com.agora.dao.NotificationDao;
import com.agora.dao.PageDao;
import com.agora.dao.SmtpDao;
import com.agora.dao.UserDao;
import com.agora.dashboard.login.PasswordHash;
import com.agora.event.EventEntry;
import com.agora.message.MessageSender;
import com.agora.message.SmtpService;
import com.agora.notification.EntityNotificationEntry;
import com.agora.pojo.Customer;
import com.agora.pojo.CustomerNotification;
import com.agora.pojo.EntityNotification;
import com.agora.pojo.Event;
import com.agora.pojo.Firm;
import com.agora.pojo.Notification;
import com.agora.pojo.Page;
import com.agora.pojo.Smtp;
import com.agora.pojo.User;
import com.agora.pojo.types.NotificationType;
import com.agora.util.EnvironmentUtils;
import com.agora.util.RouteUtils;
import com.agora.util.TimeUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

/**
 *
 * <AUTHOR>
 */
public class NotificationCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationCommons.class.getName());

    public static enum AnnouncementNotificationType {
        published,
        refused,
        reminder,
        deleted,
        free
    }

    public static enum EventNotificationType {
        published,
        refused,
        reminder,
        deleted,
        free
    }

    public static boolean notifyEvent(Request request, CustomerNotification customerNotification, List<ObjectId> entityNotificationIds) {
        if (customerNotification == null) {
            throw new NullPointerException("empty customerNotification");
        }
        if (entityNotificationIds == null) {
            throw new NullPointerException("empty entityNotificationIds");
        }

        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }

        // template
        String template = MailTemplates.EVENT_GLOBAL;

        Customer customer = null;
        try {
            customer = CustomerDao.loadCustomer(customerNotification.getCustomerId());
        } catch (Exception ex) {
            //
        }

        if (customer == null) {
            return false;
        }
        // entry
        List<EntityNotificationEntry> entries = toEntries(entityNotificationIds);

        // language
        String language = StringUtils.defaultIfBlank(RouteUtils.language(request), Defaults.LANGUAGE);

        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();

        // headoffice notification from e-commerce
        // ...
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification())
                && !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString())
                && (customer.getEmail() != null)) {
            senders.add(firm.getShopNotificationEmail());
            recipients.add(customer.getEmail());
            templates.add(template);
        }

        // vendor notification
        // ...
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }

        // carico lista delle pagine a cui sono interessato
        for (EntityNotificationEntry entry : entries) {
            if (entry.getEntityNotification() != null) {
                if (entry.getEntityNotification().getValidPageIds() != null && !entry.getEntityNotification().getValidPageIds().isEmpty()) {
                    List<Page> pageList = new ArrayList<>();
                    for (ObjectId pageId : entry.getEntityNotification().getValidPageIds()) {
                        if (pageList.size() < 2) {
                            try {
                                pageList.add(PageDao.loadPage(pageId));
                            } catch (Exception ex) {
                                // suppressed
                            }
                        } else {
                            break;
                        }
                    }

                    entry.setValidPageList(pageList);
                }
            }
        }

        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("customer", customer);
        messageFields.put("entries", entries);
        messageFields.put("contextPath", RouteUtils.contextPath(request));
        messageFields.put("baseUrl", RouteUtils.baseUrl(request));

        messageFields.put("accountCalendarUrl", RouteUtils.language(RouteUtils.baseUrl(request) + Paths.ACCOUNT_CALENDAR, Defaults.LANGUAGE));
        messageFields.put("basePageUrl", RouteUtils.language(RouteUtils.baseUrl(request) + Paths.PAGE_BASE, Defaults.LANGUAGE));
        messageFields.put("baseEventUrl", RouteUtils.language(RouteUtils.baseUrl(request) + Paths.EVENT_BASE, Defaults.LANGUAGE));
        messageFields.put("baseImageUrl", RouteUtils.baseUrl(request) + Paths.IMAGE_SYSTEM);        
        messageFields.put("baseAboutUrl", RouteUtils.baseUrl(request) + Paths.ABOUT);        
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);

        // result
        return sent;
    }

    public static List<EntityNotificationEntry> toEntries(List<ObjectId> entityNotificationIds) {
        List<EntityNotificationEntry> entityNotificationEntryList = null;
        if ((entityNotificationIds != null)
                && (entityNotificationIds.size() > 0)) {
            entityNotificationEntryList = new ArrayList<>();
            for (ObjectId entityNotificationId : entityNotificationIds) {
                EntityNotificationEntry entry = new EntityNotificationEntry();
                EntityNotification entityNotification = null;
                try {
                    entityNotification = EntityNotificationDao.loadEntityNotification(entityNotificationId);
                } catch (Exception ex) {
                    //
                }

                if (entityNotification != null) {
                    try {
                        Event event = EventDao.loadEvent(entityNotification.getEventId());
                        if (event != null) {
                            entry.setEvent(event);
                            entry.setEntityNotification(entityNotification);
                            entityNotificationEntryList.add(entry);
                        }
                    } catch (Exception ex) {
                        //
                    }
                }

            }
        }
        return entityNotificationEntryList;
    }

    public static boolean notifyEventAdmin(Request request, Event event) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (event == null) {
            throw new NullPointerException("empty event");
        }

        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }

        // template
        String template = MailTemplates.EVENT_ADMIN_NOTIFICATION;
        if (StringUtils.isBlank(template)) {
            throw new NullPointerException("missing template");
        }

        // entry
        EventEntry entry = EventCommons.toEntry(event);

        // language
        String language = StringUtils.defaultIfBlank(RouteUtils.language(request), Defaults.LANGUAGE);

        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();

        // headoffice notification from e-commerce
        // ...
        // customer notification
        senders.add(firm.getShopNotificationEmail());
        recipients.add(firm.getShopNotificationEmail());
        templates.add(template);

        // vendor notification
        // ...
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }

        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("event", entry);
        messageFields.put("hostUrl", RouteUtils.hostUrl(request));
        messageFields.put("baseUrl", RouteUtils.baseUrl(request));

        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);

        // result
        return sent;
    }

    public static enum AccountNotificationType {
        verification,
        welcome,
        forgot,
        deleted,
        keyword,
        suspended,
        reactivated,
        emailChanged
    }

    public static boolean notifyAccount(Request request, AccountNotificationType type, Customer customer, User user, String password) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (user == null) {
            throw new NullPointerException("empty user");
        }
        if (customer == null) {
            throw new NullPointerException("empty customer");
        }
        if (customer.getEmail() == null) {
            throw new NullPointerException("empty customer");
        }

        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }

        // template
        String template = null;
        String headTemplate = null;
        switch (type) {
            case verification:
                // verifica email
                template = MailTemplates.ACCOUNT_VERIFICATION;
                break;
            case welcome:
                // benvenuto e completamento profilo
                template = MailTemplates.ACCOUNT_WELCOME;
                break;
            case forgot:
                // recupera password - vengono inviate nuove credenziali
                template = MailTemplates.ACCOUNT_FORGOT;
                break;
            case deleted:
                // elimina account
                template = MailTemplates.ACCOUNT_DELETED;
                break;
            case keyword:
                // conferma parola chiave antifrode
                template = MailTemplates.ACCOUNT_KEYWORD;
                break;
            case suspended:
                // account sospeso
                template = MailTemplates.ACCOUNT_SUSPENDED;
                break;
            case reactivated:
                // account riattivato
                template = MailTemplates.ACCOUNT_REACTIVATED;
                break;
            case emailChanged:
                // account riattivato
                template = MailTemplates.ACCOUNT_EMAIL_CHANGED;
                break;
            default:
                break;
        }
        if (StringUtils.isBlank(template)) {
            throw new NullPointerException("missing template");
        }

        // language
        String language = StringUtils.defaultIfBlank(RouteUtils.language(request), Defaults.LANGUAGE);

        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();

        // headoffice notification from e-commerce
        if (StringUtils.isNotBlank(headTemplate)) {
            if (StringUtils.isNotBlank(firm.getShopNotification())
                    && !StringUtils.equalsIgnoreCase(firm.getShopNotification(), NotificationType.never.toString())
                    && StringUtils.isNotBlank(firm.getShopNotificationEmail())) {
                senders.add(firm.getShopNotificationEmail());
                recipients.add(firm.getShopNotificationEmail());
                templates.add(headTemplate);
            }
        }

        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification())
                && !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString())) {
            senders.add(firm.getShopNotificationEmail());
            recipients.add(customer.getEmail());
            templates.add(template);
        }

        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }

        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("user", user);
        messageFields.put("customer", customer);
        messageFields.put("password", password);
        messageFields.put("hostUrl", RouteUtils.hostUrl(request));
        messageFields.put("baseUrl", RouteUtils.baseUrl(request));

        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);

        // result
        return sent;
    }

    public static boolean notifyContact(Request request, String email, String name, String subject, String message, String language) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (StringUtils.isBlank(email)) {
            throw new NullPointerException("empty email");
        }

        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }

        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();

        // headoffice notification from site
        if (StringUtils.isNotBlank(firm.getShopNotification())
                && !StringUtils.equalsIgnoreCase(firm.getShopNotification(), NotificationType.never.toString())
                && StringUtils.isNotBlank(firm.getShopNotificationEmail())) {
            senders.add(firm.getSiteEmail());
            recipients.add(firm.getSiteEmail());
            templates.add(MailTemplates.CONTACT);
        }

        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification())
                && !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString())
                && (email != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(email);
            templates.add(MailTemplates.CONTACT_CONFIRMATION);
        }

        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }

        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("email", email);
        messageFields.put("name", name);
        messageFields.put("subject", subject);
        messageFields.put("message", message);
        messageFields.put("baseUrl", RouteUtils.baseUrl(request));

        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);

        // result
        return sent;
    }

    public static boolean notifyUserRegistration(Request request, Customer customer, User user, String language) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (user == null) {
            throw new NullPointerException("empty user");
        }
        if (customer == null) {
            throw new NullPointerException("empty customer");
        }
        if (customer.getEmail() == null) {
            throw new NullPointerException("empty customer");
        }

        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }

        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();

        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification())
                && !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString())) {
            senders.add(firm.getShopNotificationEmail());
            recipients.add(customer.getEmail());
            templates.add(MailTemplates.ACCOUNT_VERIFICATION);
        }

        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }

        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("user", user);
        messageFields.put("userUrl", RouteUtils.language(RouteUtils.baseUrl(request) + Paths.ACCOUNT_CONFIRM, language));
        messageFields.put("baseUrl", RouteUtils.baseUrl(request));

        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);

        // result
        return sent;
    }

    public static boolean notifyPageOwnerChange(Request request, User user, Customer customer, String language) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (user == null) {
            throw new NullPointerException("empty user");
        }
        if (customer == null) {
            throw new NullPointerException("empty customer");
        }
        if (customer.getEmail() == null) {
            throw new NullPointerException("empty customer");
        }

        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }

        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();

        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification())
                && !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString())) {
            senders.add(firm.getShopNotificationEmail());
            recipients.add(customer.getEmail());
            templates.add(MailTemplates.PAGE_OWNER_CHANGED);
        }

        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }

        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("user", user);
        messageFields.put("customer", customer);
        messageFields.put("userUrl", RouteUtils.language(RouteUtils.baseUrl(request) + Paths.ACCOUNT_PAGES, language));
        messageFields.put("baseUrl", RouteUtils.baseUrl(request));

        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);

        // result
        return sent;
    }

    public static boolean notifyUserConfirmation(Request request, Customer customer, User user, String language) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (user == null) {
            throw new NullPointerException("empty user");
        }
        if (customer == null) {
            throw new NullPointerException("empty customer");
        }
        if (customer.getEmail() == null) {
            throw new NullPointerException("empty customer");
        }

        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }

        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();

        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification())
                && !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString())) {
            senders.add(firm.getShopNotificationEmail());
            recipients.add(customer.getEmail());
            templates.add(MailTemplates.ACCOUNT_WELCOME);
        }

        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }

        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("user", user);
        messageFields.put("baseUrl", RouteUtils.baseUrl(request));
        //messageFields.put("customerCareUrl", RouteUtils.language(RouteUtils.baseUrl(request) + Paths.CUSTOMER_CARE, language));
        messageFields.put("contactsUrl", RouteUtils.language(RouteUtils.baseUrl(request) + Paths.CONTACTS, language));
        messageFields.put("userUrl", RouteUtils.language(RouteUtils.baseUrl(request) + Paths.ACCOUNT, language));

        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);

        // result
        return sent;
    }

    public static boolean notifyUserRecovery(Request request, User user, String email, String language) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (user == null) {
            throw new NullPointerException("empty user");
        }
        if (email == null) {
            throw new NullPointerException("empty customer");
        }

        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }

        // vendor
        boolean isVendor = StringUtils.containsIgnoreCase("vendor|head|admin|system|", user.getProfileType() + "|");
        boolean isShop = !isVendor;

        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();

        // vendor notification
        if (isVendor) {
            if (StringUtils.isNotBlank(firm.getVendorPortalNotification())
                    && !StringUtils.equalsIgnoreCase(firm.getVendorPortalNotification(), NotificationType.never.toString())
                    && StringUtils.isNotBlank(firm.getVendorPortalNotificationEmail())) {
                senders.add(firm.getVendorPortalNotificationEmail());
                recipients.add(email);
                templates.add(MailTemplates.FORGOT);
            }
        }

        // customer notification
        if (isShop) {
            if (StringUtils.isNotBlank(firm.getCustomerNotification())
                    && !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString())) {
                senders.add(firm.getShopNotificationEmail());
                recipients.add(email);
                templates.add(MailTemplates.ACCOUNT_FORGOT);
            }
        }

        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }

        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("user", user);
        if (isVendor) {
            messageFields.put("userUrl", RouteUtils.baseUrl(request) + Paths.LOGIN);
        }
        if (isShop) {
            messageFields.put("userUrl", RouteUtils.language(RouteUtils.baseUrl(request) + Paths.ACCESS, language));
        }
        messageFields.put("baseUrl", RouteUtils.baseUrl(request));

        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);

        try {
            user.setPassword(PasswordHash.createHash(user.getPassword()));
            UserDao.updateUser(user);
        } catch (Exception ex) {
            //
        }

        // result
        return sent;
    }

    private static boolean notify(Request request, Map<String, Object> messageFields, List<String> senders, List<String> recipients, List<String> templates) {
        if (request == null) {
            return false;
        }
        if (messageFields == null) {
            return false;
        }
        if (senders == null) {
            return false;
        }
        if (recipients == null) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }

        // smtp
        SmtpService smtp = null;
        Smtp smtpConfig = null;
        try {
            smtpConfig = SmtpDao.loadSmtp();
        } catch (Exception ex) {
            LOGGER.error("unable to load smtp", ex);
        }
        if (smtpConfig != null) {
            smtp = new SmtpService(
                    smtpConfig.getHostname(),
                    smtpConfig.getPort(),
                    smtpConfig.getAuthentication(),
                    smtpConfig.getUsername(),
                    smtpConfig.getPassword(),
                    smtpConfig.getEncryption(),
                    smtpConfig.getStartTls(),
                    smtpConfig.getApikey(),
                    smtpConfig.getSender()
            );
        }
        if ((smtp != null) && !MessageSender.isValidHost(
                smtp.getHostname(),
                smtp.getPort(),
                smtp.getAuthentication(),
                smtp.getUsername(),
                smtp.getPassword(),
                smtp.getEncryption(),
                smtp.getStartTls(),
                smtp.getApikey())) {
            LOGGER.info("wrong smtp config");
        }
        if (smtp == null) {
            LOGGER.info("switching to default smtp");
            smtp = Manager.defaultSmtpService();
        }
        if ((smtp != null) && !MessageSender.isValidHost(
                smtp.getHostname(),
                smtp.getPort(),
                smtp.getAuthentication(),
                smtp.getUsername(),
                smtp.getPassword(),
                smtp.getEncryption(),
                smtp.getStartTls(),
                smtp.getApikey())) {
            LOGGER.error("wrong default smtp config");
            smtp = null;
        }
        if (smtp == null) {
            return false;
        }

        // notify all
        int count = 0;
        for (int i = 0; i < recipients.size(); i++) {
            if (MessageSender.validEmailAddress(recipients.get(i))) {

                // local safety!
                if (EnvironmentUtils.isLocal()) {
                    LOGGER.info("hiding original email address of " + recipients.get(i) + " due to local environment");
                    recipients.set(i, EnvironmentUtils.localEmail());
                }

                MailExtras mailExtras = new MailExtras();
                String emlname = filename();
                String fileurl = StorageCommons.composePath(StorageCommons.StorageType.eml, TimeUtils.now(), filename());
                if (StringUtils.isNotBlank(emlname) && StringUtils.isNotBlank(fileurl)) {
                    mailExtras.setFileurl(fileurl);
                }

                boolean sent = Manager.sendMail(smtp,
                        senders.get(i),
                        "Agorapp",
                        recipients.get(i),
                        templates.get(i),
                        messageFields,
                        RouteUtils.pathType(request),
                        null,
                        mailExtras
                );

                store(senders.get(i),
                        "Agorapp",
                        recipients.get(i),
                        null,
                        null,
                        templates.get(i),
                        mailExtras.getSubject(),
                        null,
                        fileurl,
                        sent,
                        null
                );

                if (sent) {
                    count++;
                }
            } else {
                LOGGER.error("wrong recipient", recipients.get(i));
            }
        }

        return (count > 0);
    }

    public static boolean notifyPageApproval(Request request, Event event, Page page, User pageOwner, User eventOwner, String language) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (event == null) {
            throw new NullPointerException("empty event");
        }
        if (page == null) {
            throw new NullPointerException("empty page");
        }
        if (pageOwner == null) {
            throw new NullPointerException("empty page owner");
        }
        if (eventOwner == null) {
            throw new NullPointerException("empty event owner");
        }

        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }

        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();

        // page owner notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification())
                && !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString())) {
            senders.add(firm.getShopNotificationEmail());
            recipients.add(pageOwner.getEmail());
            templates.add(MailTemplates.EVENT_PAGE_APPROVAL);
        }

        // none to notify
        if (senders.isEmpty() || recipients.isEmpty() || templates.isEmpty()) {
            return false;
        }

        // Build approval URLs
        String baseUrl = RouteUtils.baseUrl(request);
        String approveUrl = baseUrl + RouteUtils.language(Paths.EVENT_PAGE_APPROVAL, language)
                + "?registrationToken=" + pageOwner.getRegistrationToken()
                + "&eventId=" + event.getId().toString()
                + "&pageId=" + page.getId().toString()
                + "&action=accept";

        String declineUrl = baseUrl + RouteUtils.language(Paths.EVENT_PAGE_APPROVAL, language)
                + "?registrationToken=" + pageOwner.getRegistrationToken()
                + "&eventId=" + event.getId().toString()
                + "&pageId=" + page.getId().toString()
                + "&action=decline";

        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("event", event);
        messageFields.put("page", page);
        messageFields.put("pageOwner", pageOwner);
        messageFields.put("eventOwner", eventOwner);
        messageFields.put("approveUrl", approveUrl);
        messageFields.put("declineUrl", declineUrl);
        messageFields.put("baseUrl", baseUrl);

        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);

        // result
        return sent;
    }

    private static String filename() {
        return TimeUtils.toString(TimeUtils.now(), "yyyyMMdd_HHmmss_SSS") + ".eml";
    }

    private static Notification store(String from, String fromName, String to, String cc, String bcc, String template, String subject, List<String> attachments, String fileurl, boolean sent, String status) {

        Notification notification = new Notification();

        notification.setFrom(from);
        notification.setFromName(fromName);
        notification.setTo(to);
        notification.setCc(cc);
        notification.setBcc(bcc);
        notification.setTemplate(template);
        notification.setSubject(subject);
        notification.setAttachments(attachments);
        notification.setFileurl(fileurl);
        notification.setSent(sent);
        notification.setStatus(status);

        try {
            ObjectId id = NotificationDao.insertNotification(notification);
            notification.setId(id);
        } catch (Exception ex) {
            LOGGER.error("unable to store notification", ex);
        }

        return notification;
    }

    private static Firm firm() {
        Firm firm = null;
        try {
            firm = FirmDao.loadFirm();
        } catch (Exception ex) {
            LOGGER.error("unable to load firm");
        }
        return firm;
    }

}
