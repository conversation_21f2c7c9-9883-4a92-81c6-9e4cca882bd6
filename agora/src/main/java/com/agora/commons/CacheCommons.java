package com.agora.commons;

import com.agora.core.Manager;
import com.agora.dao.EventDao;
import com.agora.dao.EventFollowerDao;
import com.agora.dao.PageDao;
import com.agora.dao.PageFollowerDao;
import com.agora.pojo.Event;
import com.agora.pojo.Page;
import com.mongodb.client.MongoCollection;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.CompletableFuture;

public class CacheCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(CacheCommons.class.getName());

    // contiene i 10 tag più usati
    private static final List<String> MOST_USED_TAGS = new ArrayList<>();

    public static void reloadMostUsedTags() {
        CompletableFuture.supplyAsync(() -> {
            try {
                /*List<Event> events = EventDao.loadEventList();
                if (events != null && !events.isEmpty()) {
                    // count the occurrences of each tag
                    Map<String, Integer> tagCount = new HashMap<>();
                    for (Event event : events) {
                        if (event.getTags() != null) {
                            for (String tag : event.getTags()) {
                                tagCount.put(tag, tagCount.getOrDefault(tag, 0) + 1);
                            }
                        }
                    }

                    // sort the tags by their count in descending order
                    MOST_USED_TAGS.clear();
                    tagCount.entrySet().stream()
                            .sorted((entry1, entry2) -> entry2.getValue().compareTo(entry1.getValue()))
                            .limit(10) // limit to top 10 tags
                            .forEach(entry -> MOST_USED_TAGS.add(entry.getKey()));
                }*/

                MOST_USED_TAGS.clear();
                List<String> loadedMostUsedTags = EventDao.loadMostUsedTagsForCache();
                for (String tag : loadedMostUsedTags) {
                    if (StringUtils.isNotBlank(tag)) {
                        MOST_USED_TAGS.add(tag);
                    } else {
                        LOGGER.warn("Invalid tag found in most used tags");
                    }
                }
            } catch (Exception ex) {
                LOGGER.error("Unable to reload most used tags", ex);
            }
            return null;
        });
    }

    public static void reloadHomePages() {
        try {
            double upcomingEventsMaxAmount = 10;
            double followersMaxAmount = 500;
            double eventWeight = 2, followerWeight = 4;
            int minFollowers = 5;

            List<Page> pages = PageDao.loadPageListForCache(minFollowers);
            Map<Page, Double> validPages = new HashMap<>();

            if (pages != null && !pages.isEmpty()) {
                for (Page page : pages) {
                    // 1. check if the page is eligible for home page
                    // IsPubliclyTaggable = True → la pagina può essere taggata da chiunque
                    boolean isEligible = StringUtils.equalsIgnoreCase(page.getPageTagging(), "everyone");

                    if (isEligible) {
                        // HasPictureAndBio = True → ha un'immagine e una bio
                        if (page.getProfileImageId() == null || StringUtils.isBlank(page.getDescription())) {
                            isEligible = false;
                        }
                    }

                    if (isEligible) {
                        // HasMinEvents = True → almeno 3 eventi (su date distinte distanti almeno 2 giorni)
                        List<Event> pageEvents = EventDao.loadEventListByPageId(page.getId());
                        if (pageEvents != null && pageEvents.size() >= 3) {
                            // check if the events are on different dates
                            // order the event by the start date
                            pageEvents.sort((e1, e2) -> {
                                if (e1 == null || e2 == null || e1.getStartDate() == null || e2.getStartDate() == null) {
                                    return 0;
                                }
                                return e1.getStartDate().compareTo(e2.getStartDate());
                            });
                            // check if the difference between the first and the last event is at least 2 days
                            Event firstEvent = pageEvents.get(0);
                            Event lastEvent = pageEvents.get(pageEvents.size() - 1);
                            if (firstEvent == null || lastEvent == null ||
                                    firstEvent.getStartDate() == null || lastEvent.getStartDate() == null ||
                                    lastEvent.getStartDate().getTime() - firstEvent.getStartDate().getTime() < 2 * 24 * 60 * 60 * 1000) {
                                isEligible = false;
                            }
                        } else {
                            isEligible = false;
                        }

                        if (isEligible) {
                            // HasUpcomingEvent = True → almeno 1 evento futuro
                            boolean hasUpcomingEvent = false;
                            for (Event event : pageEvents) {
                                if (event.getStartDate().after(new Date())) {
                                    hasUpcomingEvent = true;
                                    break;
                                }
                            }

                            isEligible = hasUpcomingEvent;
                        }

                        // non posso toglierlo perchè mi serve dopo per il peso
                        long followers = 0;
                        if (isEligible) {
                            // HasMinFollowers = True → almeno 5 followers
                            followers = PageFollowerDao.loadPageFollowerCount(page.getId());
                            if (followers < minFollowers) {
                                isEligible = false;
                            }
                        }

                        if (isEligible) {
                            double eventScore = 0, followerScore = 0;

                            List<Event> upcomingEvents = new ArrayList<>();
                            for (Event event : pageEvents) {
                                if (event.getStartDate().after(new Date())) {
                                    upcomingEvents.add(event);
                                }
                            }

                            // UpcomingEventsScore = Min(UpcomingEvents / UE_max, 1)
                            eventScore = Math.min(upcomingEvents.size() / upcomingEventsMaxAmount, 1.0);

                            // FollowersScore = Min(Followers / F_max, 1)
                            followerScore = Math.min(followers / followersMaxAmount, 1.0);

                            validPages.put(page, ((eventWeight * eventScore) + (followerWeight * followerScore)));

                            // le prendo tutte perchè l'utente potrebbe già seguire una di queste
                            /*if (validPages.size() >= 4) {
                                break;
                            }*/
                        }
                    }
                }

                // save pages to mongodb
                // first let's delete all the existing ones
                MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("wall_page_ranking");
                collection.deleteMany(new Document());
                // now let's insert the new ones
                for (Map.Entry<Page, Double> entry : validPages.entrySet()) {
                    PageDao.insertWallPage(entry.getKey().getId(), entry.getValue());
                }
            }
        } catch (Exception ex) {
            LOGGER.error("Unable to reload home pages", ex);
        }
    }

    public static void reloadHomeEvents() {
        try {
            List<Event> events = EventDao.loadEventListForCache();

            if (events != null && !events.isEmpty()) {
                Map<ObjectId, Page> loadedPages = new HashMap<>();
                double maxFollowes = 100, followerWeight = 5;
                double maxPageTypes = 4;
                double maxParticipants = 20, participantWeight = 4;

                Map<Event, Double> validEvents = new HashMap<>();

                for (Event event : events) {
                    // IsEligible = [HasCoverPhoto AND IsInFuture]
                    // già controllato in query, quindi sempre true

                    double ranking = 0;
                    // FollowersScore = Min(TotalInvolvedPagesFollowers / F_max, 1)
                    // bisogna caricare tutte le pagine coinvolte
                    if (event.getPageIds() != null && !event.getPageIds().isEmpty()) {
                        long totalFollowersInvolved = 0;

                        for (ObjectId pageId : event.getPageIds()) {
                            if (!loadedPages.containsKey(pageId)) {
                                Page page = PageDao.loadPage(pageId);
                                if (page != null) {
                                    // questo considera sia follower fake che follower reali
                                    long followers = PageFollowerDao.loadPageFollowerCount(page.getId());
                                    page.setFollowers(followers);
                                    loadedPages.put(pageId, page);
                                }
                            }

                            totalFollowersInvolved += loadedPages.get(pageId).getFollowers();
                        }
                        ranking += (Math.min(totalFollowersInvolved / maxFollowes, 1) * followerWeight);
                    }

                    // W_description: Weight for having a description (low, e.g., 1)
                    if (StringUtils.isNotBlank(event.getDescription())) {
                        ranking += 1;
                    }

                    // W_tags: Weight for having tags (low, e.g., 1)
                    if (event.getTags() != null && !event.getTags().isEmpty()) {
                        ranking += 1;
                    }

                    // W_diversity: Weight for diversity of page types (medium, e.g., 2)
                    if (event.getPageIds() != null && !event.getPageIds().isEmpty()) {
                        Set<String> pageTypes = new HashSet<>();
                        for (ObjectId pageId : event.getPageIds()) {
                            Page page = loadedPages.get(pageId);
                            if (page != null) {
                                if (StringUtils.isNotBlank(page.getPageType())) {
                                    pageTypes.add(page.getPageType());
                                }
                            }
                        }
                        ranking += Math.min(pageTypes.size() / maxPageTypes, 1);
                    }

                    // W_participants: Weight for participants (high, e.g., 4)
                    // questo considera anche i followers fake
                    long eventFollowers = EventFollowerDao.loadEventFollowerCount(event.getId());
                    ranking += Math.min(eventFollowers / maxParticipants, 1) * participantWeight;

                    validEvents.put(event, ranking);
                }

                // save pages to mongodb
                // first let's delete all the existing ones
                MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("wall_event_ranking");
                collection.deleteMany(new Document());
                // now let's insert the new ones
                for (Map.Entry<Event, Double> entry : validEvents.entrySet()) {
                    EventDao.insertWallEvent(entry.getKey().getId(), entry.getValue());
                }
            }
        } catch (Exception ex) {
            LOGGER.error("Unable to reload home events", ex);
        }
    }

    public static List<String> getMostUsedTags() {
        return new ArrayList<>(MOST_USED_TAGS);
    }
}
