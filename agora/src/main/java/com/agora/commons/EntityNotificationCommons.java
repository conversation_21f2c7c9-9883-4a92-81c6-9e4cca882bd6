package com.agora.commons;

import com.agora.dao.EntityNotificationDao;
import com.agora.dao.EventDao;
import com.agora.dao.PageNotificationDao;
import com.agora.notification.EntityNotificationEntry;
import com.agora.pojo.EntityNotification;
import com.agora.pojo.Event;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public class EntityNotificationCommons {
    private static final Logger LOGGER = LoggerFactory.getLogger(EntityNotificationCommons.class.getName());

    public static enum EventNotificationType {
        published,
        changed,
        reminder,
        deleted
    }

    public static boolean notifyEntityEvent(EventNotificationType type, Event event) {
        if (type == null) {
            throw new NullPointerException("empty type");
        }
        if (event == null) {
            throw new NullPointerException("empty event");
        }

        Map<ObjectId, List<ObjectId>> userIdMap = null;

        // nuovi controlli per container
        List<ObjectId> validPageIds = event.getPageIds();
        if (validPageIds != null && !validPageIds.isEmpty() && event.getParentId() != null) {
            try {
                Event parent = EventDao.loadEvent(event.getParentId());
                if (parent != null) {
                    if (parent.getInitialPageIds() != null) {
                        validPageIds.removeAll(parent.getInitialPageIds());
                    }
                }
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }

        if (validPageIds != null && !validPageIds.isEmpty()) {
            try {
                //estrarre tutti gli utenti che seguono le pagine coinvolte
                userIdMap = EntityNotificationDao.loadUserIdPageIdMap(validPageIds);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (userIdMap != null && !userIdMap.isEmpty()) {
                List<EntityNotification> entityNotificationList = new ArrayList<>();
                EntityNotification entityNotification;

                for (ObjectId userId : userIdMap.keySet()) {
                    Boolean notify = false;
                    try {
                        notify = PageNotificationDao.hasPageNotificationByUserAndPage(userId, event.getPageIds());
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    // Crea un nuovo oggetto EntityNotification per ogni userId
                    entityNotification = new EntityNotification();

                    entityNotification.setEventId(event.getId());
                    entityNotification.setType(type.toString());
                    Date now = new Date();
                    entityNotification.setDate(now);
                    entityNotification.setLat(event.getLat());
                    entityNotification.setLng(event.getLng());
                    entityNotification.setMailActive(notify);
                    // internals
                    entityNotification.setCreation(now);
                    entityNotification.setLastUpdate(now);
                    entityNotification.setUserId(userId);
                    entityNotification.setValidPageIds(userIdMap.get(userId));

                    // Aggiungi l'oggetto appena creato alla lista
                    entityNotificationList.add(entityNotification);
                }

                if (!entityNotificationList.isEmpty()) {
                    try {
                        EntityNotificationDao.insertEntityNotificationList(entityNotificationList);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                }
            }
        }

        return true;
    }

    public static void notifyMassiveEntityEvent(EventNotificationType type, Event event, Map<ObjectId, Integer> notificationCounterMap) {
        if (type == null) {
            throw new NullPointerException("empty type");
        }
        if (event == null) {
            throw new NullPointerException("empty event");
        }

        Map<ObjectId, List<ObjectId>> userIdMap = null;
        try {
            // Estrarre tutti gli utenti che seguono le pagine coinvolte
            userIdMap = EntityNotificationDao.loadUserIdPageIdMap(event.getPageIds());
        } catch (Exception ex) {
            LOGGER.error("Errore caricando la mappa degli utenti", ex);
        }

        if (userIdMap != null && !userIdMap.isEmpty()) {
            List<EntityNotification> entityNotificationList = new ArrayList<>();
            EntityNotification entityNotification;

            for (ObjectId userId : userIdMap.keySet()) {
                Boolean notify = false;
                try {
                    notify = PageNotificationDao.hasPageNotificationByUserAndPage(userId, event.getPageIds());
                } catch (Exception ex) {
                    LOGGER.error("Errore controllando le notifiche per l'utente " + userId, ex);
                }

                // Ottieni la lista di pagine collegate all'utente
                List<ObjectId> validPageIds = userIdMap.get(userId);

                // Inizializza il contatore delle notifiche per l'utente se non è già presente
                notificationCounterMap.putIfAbsent(userId, 0);

                // Numero di notifiche già inviate a questo utente
                int notificationCount = notificationCounterMap.get(userId);

                int maxNotifications = 5;

                /*for (ObjectId pageId : validPageIds) {*/

                entityNotification = new EntityNotification();
                entityNotification.setEventId(event.getId());
                entityNotification.setType(type.toString());
                Date now = new Date();
                entityNotification.setDate(now);
                entityNotification.setLat(event.getLat());
                entityNotification.setLng(event.getLng());
                entityNotification.setMailActive(notify);
                entityNotification.setCreation(now);
                entityNotification.setLastUpdate(now);
                entityNotification.setUserId(userId);
                entityNotification.setValidPageIds(userIdMap.get(userId));

                entityNotificationList.add(entityNotification);
                notificationCount++;  // Incrementa il contatore di notifiche per questo utente
                /*}*/

                // Aggiorna il contatore nella mappa
                notificationCounterMap.put(userId, notificationCount);
            }

            if (!entityNotificationList.isEmpty()) {
                try {
                    EntityNotificationDao.insertEntityNotificationList(entityNotificationList);
                } catch (Exception ex) {
                    LOGGER.error("Errore inserendo la lista di notifiche", ex);
                }
            }
        }

    }

    public static List<EntityNotificationEntry> toEntries(List<EntityNotification> eventNotificationList, ObjectId userId) {
        List<EntityNotificationEntry> pageFollowerEntryList = null;
        if ((eventNotificationList != null) &&
                (eventNotificationList.size() > 0)) {
            for (EntityNotification eventNotification : eventNotificationList) {
                EntityNotificationEntry entry = toEntry(eventNotification, userId);
                if (pageFollowerEntryList == null) {
                    pageFollowerEntryList = new ArrayList<>();
                }
                pageFollowerEntryList.add(entry);
            }
        }
        return pageFollowerEntryList;
    }


    public static EntityNotificationEntry toEntry(EntityNotification eventNotification, ObjectId userId) {
        EntityNotificationEntry entry = null;
        if (eventNotification != null) {
            entry = new EntityNotificationEntry();
            entry.setEntityNotification(eventNotification);

            try {
                entry.setEvent(EventDao.loadEvent(eventNotification.getEventId()));
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

        }
        return entry;
    }
}
