package com.agora.account;

import com.agora.commons.CustomerCommons;
import com.agora.commons.EntityNotificationCommons;
import com.agora.commons.EventCommons;
import com.agora.commons.EventFollowerCommons;
import com.agora.commons.NotificationCommons;
import com.agora.commons.NotificationCommons.AccountNotificationType;
import com.agora.commons.PageCommons;
import com.agora.commons.PageFollowerCommons;
import com.agora.commons.PageNotificationCommons;
import com.agora.commons.UserCommons;
import com.agora.core.Defaults;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.CustomerDao;
import com.agora.dao.CustomerNotificationDao;
import com.agora.dao.EntityNotificationDao;
import com.agora.dao.EventDao;
import com.agora.dao.EventFollowerDao;
import com.agora.dao.PageDao;
import com.agora.dao.PageFollowerDao;
import com.agora.dao.PageNotificationDao;
import com.agora.dao.UserDao;
import com.agora.dashboard.login.PasswordHash;
import com.agora.pojo.Customer;
import com.agora.pojo.CustomerNotification;
import com.agora.pojo.EntityNotification;
import com.agora.pojo.Event;
import com.agora.pojo.EventFollower;
import com.agora.pojo.Page;
import com.agora.pojo.PageFollower;
import com.agora.pojo.PageNotification;
import com.agora.pojo.User;
import com.agora.pojo.types.ProfileType;
import com.agora.support.file.posted.PostedFile;
import com.agora.support.image.slim.SlimImage;
import com.agora.util.PojoUtils;
import com.agora.util.RouteUtils;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URIBuilder;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class AccountController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class.getName());

    public static TemplateViewRoute account = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // allow logged user only
        if (user == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, language));
            return Manager.renderEmpty();
        }

//        // update user
        user = UserDao.loadUser(user.getId());
        Manager.putSession(token, "user", user);
//
//        // allow customer only users
//        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString()) &&
//                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.customer.toString())) {
//            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
//            return Manager.renderEmpty();
//        }

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // customer
        Customer customer = CustomerDao.loadCustomerByUserId(user.getId());
        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        return Manager.render(Templates.ACCOUNT, attributes, RouteUtils.pathType(request));
    };

    public static Route account_remove = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401, "Eliminazione non autorizzata");
        }

        user = UserDao.loadUser(user.getId());
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401, "Eliminazione non autorizzata");
        }

        // logged customer
        Customer customer = CustomerDao.loadCustomerByUserId(user.getId());
        if (customer == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Eliminazione non autorizzata");
        }

        // send registration message
        if (!NotificationCommons.notifyAccount(request, AccountNotificationType.deleted, customer, user, null)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Invio notifica non riuscita");
        }

        // delete personal page
        Page personalPage = PageDao.loadUserPage(user.getId());
        if (personalPage != null) {
            PageDao.updatePageCancelled(personalPage.getId(), true);
            // remove personal page from all event
            EventDao.removePageIdFromEvent(personalPage.getId());
            
        }
        
        // remove all the other pages
        List<Page> userPages = PageDao.loadPageListByOwnerId(user.getId());
        User userDefault = UserCommons.defaultUser();
        if (userPages != null && !userPages.isEmpty()) {
            for (Page page : userPages) {
                page.setOwnerId(userDefault.getId());
                page.setUserId(userDefault.getId());
                PageDao.updatePage(page);
            }
        }

        // remove events
        List<Event> userEvents = EventDao.loadEventListByOwnerId(user.getId());
        if (userEvents != null && !userEvents.isEmpty()) {
            for (Event event : userEvents) {
                event.setOwnerId(userDefault.getId());
                event.setUserId(userDefault.getId());
                EventDao.updateEvent(event);
            }
        }

        // remove user
        user.setCancelled(true);
        UserDao.updateUser(user);

        // remove customer
        customer.setCancelled(true);
        CustomerDao.updateCustomer(customer);

        // update user on redis
        Manager.destroySession(response, token);

        return "ok";
    };

    public static TemplateViewRoute account_deleted = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer = null;
        if (user != null) {
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        }
        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        return Manager.render(Templates.ACCOUNT_DELETED, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute account_info = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // allow logged user only
        if (user == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, language));
            return Manager.renderEmpty();
        }

        // update user
        user = UserDao.loadUser(user.getId());
        Manager.putSession(token, "user", user);

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // customer
        Customer customer = CustomerDao.loadCustomerByUserId(user.getId());
        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        return Manager.render(Templates.ACCOUNT_INFO, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute account_pages = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // allow logged user only
        if (user == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, language));
            return Manager.renderEmpty();
        }

        // update user
        user = UserDao.loadUser(user.getId());
        Manager.putSession(token, "user", user);

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // customer
        Customer customer = CustomerDao.loadCustomerByUserId(user.getId());
        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        String selectedTab = "";
        if (StringUtils.isNotBlank(request.queryParams("tab"))) {
            selectedTab = request.queryParams("tab");
        }

        if (StringUtils.isBlank(selectedTab) || StringUtils.equalsIgnoreCase(selectedTab, "mypages")) {
            int pagination = 12;
            int skip = NumberUtils.toInt(request.queryParams("skip"));
            int limit = NumberUtils.toInt(request.queryParams("limit"));
            if (limit <= 0) {
                limit = pagination;
            }
            attributes.put("skip", skip);
            attributes.put("limit", limit);
            attributes.put("pagination", pagination);
            
            List<Page> myPages = PageDao.loadPageListByOwnerId(user.getId(), skip, limit + 1);
            
            // loadmore
            boolean loadmore = false;
            if ((myPages != null) && (!myPages.isEmpty())) {
                loadmore = myPages.size() > limit;
            }
            if ((myPages != null) && (!myPages.isEmpty())) {
                if (myPages.size() > limit) {
                    myPages = myPages.subList(0, limit);
                }
            }
            
            attributes.put("myPageEntries", PageCommons.toEntries(myPages));
            
            attributes.put("loadmore", loadmore);
            
            URIBuilder uriBuilder;
            try {
                if (RouteUtils.publicQueryString(request) != null) {
                    uriBuilder = new URIBuilder(RouteUtils.publicUrl(request) + "?" + RouteUtils.publicQueryString(request));
                    List<NameValuePair> queryParameters = uriBuilder.getQueryParams();
                    for (Iterator<NameValuePair> queryParameterItr = queryParameters.iterator(); queryParameterItr.hasNext();) {
                        NameValuePair queryParameter = queryParameterItr.next();
                        if (queryParameter.getName().equals("skip")) {
                            queryParameterItr.remove();
                        }
                        if (queryParameter.getName().equals("tab")) {
                            queryParameterItr.remove();
                        }
                        if (queryParameter.getName().equals("limit")) {
                            queryParameterItr.remove();
                        }
                    }
                    uriBuilder.setParameters(queryParameters);

                    String resultUrl = uriBuilder.build().toString();
                    attributes.put("resultUrl", resultUrl);
                } else {
                    attributes.put("resultUrl", RouteUtils.publicUrl(request));
                }
            } catch (URISyntaxException ex) {
                //
            }
            
        }
        
        if (StringUtils.isBlank(selectedTab) || StringUtils.equalsIgnoreCase(selectedTab, "follow")) {
            int paginationFollow = 12;
            int skipFollow = NumberUtils.toInt(request.queryParams("skipFollow"));
            int limitFollow = NumberUtils.toInt(request.queryParams("limitFollow"));
            if (limitFollow <= 0) {
                limitFollow = paginationFollow;
            }
            attributes.put("skipFollow", skipFollow);
            attributes.put("limitFollow", limitFollow);
            attributes.put("paginationFollow", paginationFollow);


            List<PageFollower> followPages = PageFollowerDao.loadPageFollowerListByUser(user.getId(), skipFollow, limitFollow + 1);
            
            // loadmore
            boolean loadmoreFollow = false;
            if ((followPages != null) && (!followPages.isEmpty())) {
                loadmoreFollow = followPages.size() > limitFollow;
            }
            if ((followPages != null) && (!followPages.isEmpty())) {
                if (followPages.size() > limitFollow) {
                    followPages = followPages.subList(0, limitFollow);
                }
            }          
            
            attributes.put("followPages", PageFollowerCommons.toEntries(followPages));
        
            attributes.put("loadmoreFollow", loadmoreFollow);

            URIBuilder uriBuilderFollow;
            try {
                if (RouteUtils.publicQueryString(request) != null) {
                    uriBuilderFollow = new URIBuilder(RouteUtils.publicUrl(request) + "?" + RouteUtils.publicQueryString(request));
                    List<NameValuePair> queryParameters = uriBuilderFollow.getQueryParams();
                    for (Iterator<NameValuePair> queryParameterItr = queryParameters.iterator(); queryParameterItr.hasNext();) {
                        NameValuePair queryParameter = queryParameterItr.next();
                        if (queryParameter.getName().equals("skipFollow")) {
                            queryParameterItr.remove();
                        }
                        if (queryParameter.getName().equals("limitFollow")) {
                            queryParameterItr.remove();
                        }
                        if (queryParameter.getName().equals("tab")) {
                            queryParameterItr.remove();
                        }
                    }
                    uriBuilderFollow.setParameters(queryParameters);

                    String resultUrlFollow = uriBuilderFollow.build().toString();
                    attributes.put("resultUrlFollow", resultUrlFollow);
                } else {
                    attributes.put("resultUrlFollow", RouteUtils.publicUrl(request));
                }
            } catch (URISyntaxException ex) {
                //
            }            
        }
        
        if (StringUtils.isBlank(selectedTab) || StringUtils.equalsIgnoreCase(selectedTab, "mynotifications")) {
            int paginationNotification = 12;
            int skipNotification = NumberUtils.toInt(request.queryParams("skipNotification"));
            int limitNotification = NumberUtils.toInt(request.queryParams("limitNotification"));
            if (limitNotification <= 0) {
                limitNotification = paginationNotification;
            }
            attributes.put("skipNotification", skipNotification);
            attributes.put("limitNotification", limitNotification);
            attributes.put("paginationNotification", paginationNotification);


            List<PageNotification> notificationPages = PageNotificationDao.loadPageNotificationListByUser(user.getId(), skipNotification, limitNotification + 1);
            
            // loadmore
            boolean loadmoreNotification = false;
            if ((notificationPages != null) && (!notificationPages.isEmpty())) {
                loadmoreNotification = notificationPages.size() > limitNotification;
            }
            if ((notificationPages != null) && (!notificationPages.isEmpty())) {
                if (notificationPages.size() > limitNotification) {
                    notificationPages = notificationPages.subList(0, limitNotification);
                }
            }          
            
            attributes.put("notificationPages", PageNotificationCommons.toEntries(notificationPages));
        
            attributes.put("loadmoreNotification", loadmoreNotification);

            URIBuilder uriBuilderNotification;
            try {
                if (RouteUtils.publicQueryString(request) != null) {
                    uriBuilderNotification = new URIBuilder(RouteUtils.publicUrl(request) + "?" + RouteUtils.publicQueryString(request));
                    List<NameValuePair> queryParameters = uriBuilderNotification.getQueryParams();
                    for (Iterator<NameValuePair> queryParameterItr = queryParameters.iterator(); queryParameterItr.hasNext();) {
                        NameValuePair queryParameter = queryParameterItr.next();
                        if (queryParameter.getName().equals("skipNotification")) {
                            queryParameterItr.remove();
                        }
                        if (queryParameter.getName().equals("limitNotification")) {
                            queryParameterItr.remove();
                        }
                        if (queryParameter.getName().equals("tab")) {
                            queryParameterItr.remove();
                        }
                    }
                    uriBuilderNotification.setParameters(queryParameters);

                    String resultUrlNotification = uriBuilderNotification.build().toString();
                    attributes.put("resultUrlNotification", resultUrlNotification);
                } else {
                    attributes.put("resultUrlNotification", RouteUtils.publicUrl(request));
                }
            } catch (URISyntaxException ex) {
                //
            }              
        }

        // caricamento numero di pagine duplicate
        List<Page> duplicatePages = PageDao.loadDuplicatePages(user.getId());
        attributes.put("duplicatePagesAmount", duplicatePages.size());
        
        return Manager.render(Templates.ACCOUNT_PAGES, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute account_calendar = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // allow logged user only
        if (user == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, language));
            return Manager.renderEmpty();
        }

        // update user
        user = UserDao.loadUser(user.getId());
        Manager.putSession(token, "user", user);

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // customer
        Customer customer = CustomerDao.loadCustomerByUserId(user.getId());
        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        String selectedTab = "";
        if (StringUtils.isNotBlank(request.queryParams("tab"))) {
            selectedTab = request.queryParams("tab");
        }

        if (StringUtils.isBlank(selectedTab) || StringUtils.equalsIgnoreCase(selectedTab, "myevents")) {
            int pagination = 12;
            int skip = NumberUtils.toInt(request.queryParams("skip"));
            int limit = NumberUtils.toInt(request.queryParams("limit"));
            if (limit <= 0) {
                limit = pagination;
            }
            attributes.put("skip", skip);
            attributes.put("limit", limit);
            attributes.put("pagination", pagination);
            
            
            List<Event> myEvents = EventDao.loadEventListByOwnerId(user.getId(), skip, limit + 1);
            
            // loadmore
            boolean loadmore = false;
            if ((myEvents != null) && (!myEvents.isEmpty())) {
                loadmore = myEvents.size() > limit;
            }
            if ((myEvents != null) && (!myEvents.isEmpty())) {
                if (myEvents.size() > limit) {
                    myEvents = myEvents.subList(0, limit);
                }
            }
            
            attributes.put("myEventEntries", EventCommons.toEntries(myEvents));
            
            attributes.put("loadmore", loadmore);
            
            URIBuilder uriBuilder;
            try {
                if (RouteUtils.publicQueryString(request) != null) {
                    uriBuilder = new URIBuilder(RouteUtils.publicUrl(request) + "?" + RouteUtils.publicQueryString(request));
                    List<NameValuePair> queryParameters = uriBuilder.getQueryParams();
                    for (Iterator<NameValuePair> queryParameterItr = queryParameters.iterator(); queryParameterItr.hasNext();) {
                        NameValuePair queryParameter = queryParameterItr.next();
                        if (queryParameter.getName().equals("skip")) {
                            queryParameterItr.remove();
                        }
                        if (queryParameter.getName().equals("tab")) {
                            queryParameterItr.remove();
                        }
                        if (queryParameter.getName().equals("limit")) {
                            queryParameterItr.remove();
                        }
                    }
                    uriBuilder.setParameters(queryParameters);

                    String resultUrl = uriBuilder.build().toString();
                    attributes.put("resultUrl", resultUrl);
                } else {
                    attributes.put("resultUrl", RouteUtils.publicUrl(request));
                }
            } catch (URISyntaxException ex) {
                //
            }
            
        }
        
        if (StringUtils.isBlank(selectedTab) || StringUtils.equalsIgnoreCase(selectedTab, "follow")) {
            int paginationFollow = 12;
            int skipFollow = NumberUtils.toInt(request.queryParams("skipFollow"));
            int limitFollow = NumberUtils.toInt(request.queryParams("limitFollow"));
            if (limitFollow <= 0) {
                limitFollow = paginationFollow;
            }
            attributes.put("skipFollow", skipFollow);
            attributes.put("limitFollow", limitFollow);
            attributes.put("paginationFollow", paginationFollow);

            List<EventFollower> followEvents = EventFollowerDao.loadEventFollowerFutureListByUser(user.getId(), skipFollow, limitFollow + 1);
            
            // loadmore
            boolean loadmoreFollow = false;
            if ((followEvents != null) && (!followEvents.isEmpty())) {
                loadmoreFollow = followEvents.size() > limitFollow;
            }
            if ((followEvents != null) && (!followEvents.isEmpty())) {
                if (followEvents.size() > limitFollow) {
                    followEvents = followEvents.subList(0, limitFollow);
                }
            }            
            attributes.put("followEvents", EventFollowerCommons.toEntries(followEvents));
            
            attributes.put("loadmoreFollow", loadmoreFollow);

            URIBuilder uriBuilderFollow;
            try {
                if (RouteUtils.publicQueryString(request) != null) {
                    uriBuilderFollow = new URIBuilder(RouteUtils.publicUrl(request) + "?" + RouteUtils.publicQueryString(request));
                    List<NameValuePair> queryParameters = uriBuilderFollow.getQueryParams();
                    for (Iterator<NameValuePair> queryParameterItr = queryParameters.iterator(); queryParameterItr.hasNext();) {
                        NameValuePair queryParameter = queryParameterItr.next();
                        if (queryParameter.getName().equals("skipFollow")) {
                            queryParameterItr.remove();
                        }
                        if (queryParameter.getName().equals("limitFollow")) {
                            queryParameterItr.remove();
                        }
                        if (queryParameter.getName().equals("tab")) {
                            queryParameterItr.remove();
                        }
                    }
                    uriBuilderFollow.setParameters(queryParameters);

                    String resultUrlFollow = uriBuilderFollow.build().toString();
                    attributes.put("resultUrlFollow", resultUrlFollow);
                } else {
                    attributes.put("resultUrlFollow", RouteUtils.publicUrl(request));
                }
            } catch (URISyntaxException ex) {
                //
            }            
        }
        
        if (StringUtils.isBlank(selectedTab) || StringUtils.equalsIgnoreCase(selectedTab, "followPast")) {
            int paginationFollowPast = 12;
            int skipFollowPast = NumberUtils.toInt(request.queryParams("skipFollowPast"));
            int limitFollowPast = NumberUtils.toInt(request.queryParams("limitFollowPast"));
            if (limitFollowPast <= 0) {
                limitFollowPast = paginationFollowPast;
            }
            attributes.put("skipFollowPast", skipFollowPast);
            attributes.put("limitFollowPast", limitFollowPast);
            attributes.put("paginationFollowPast", paginationFollowPast);

            List<EventFollower> followEventsPast = EventFollowerDao.loadEventFollowerPastListByUser(user.getId(), skipFollowPast, limitFollowPast + 1);
            
            // loadmore
            boolean loadmoreFollowPast = false;
            if ((followEventsPast != null) && (!followEventsPast.isEmpty())) {
                loadmoreFollowPast = followEventsPast.size() > limitFollowPast;
            }
            if ((followEventsPast != null) && (!followEventsPast.isEmpty())) {
                if (followEventsPast.size() > limitFollowPast) {
                    followEventsPast = followEventsPast.subList(0, limitFollowPast);
                }
            }            
            attributes.put("followEventsPast", EventFollowerCommons.toEntries(followEventsPast));
            
            attributes.put("loadmoreFollowPast", loadmoreFollowPast);

            URIBuilder uriBuilderFollowPast;
            try {
                if (RouteUtils.publicQueryString(request) != null) {
                    uriBuilderFollowPast = new URIBuilder(RouteUtils.publicUrl(request) + "?" + RouteUtils.publicQueryString(request));
                    List<NameValuePair> queryParameters = uriBuilderFollowPast.getQueryParams();
                    for (Iterator<NameValuePair> queryParameterItr = queryParameters.iterator(); queryParameterItr.hasNext();) {
                        NameValuePair queryParameter = queryParameterItr.next();
                        if (queryParameter.getName().equals("skipFollowPast")) {
                            queryParameterItr.remove();
                        }
                        if (queryParameter.getName().equals("limitFollowPast")) {
                            queryParameterItr.remove();
                        }
                        if (queryParameter.getName().equals("tab")) {
                            queryParameterItr.remove();
                        }
                    }
                    uriBuilderFollowPast.setParameters(queryParameters);

                    String resultUrlFollowPast = uriBuilderFollowPast.build().toString();
                    attributes.put("resultUrlFollowPast", resultUrlFollowPast);
                } else {
                    attributes.put("resultUrlFollowPast", RouteUtils.publicUrl(request));
                }
            } catch (URISyntaxException ex) {
                //
            }            
        }


        return Manager.render(Templates.ACCOUNT_CALENDAR, attributes, RouteUtils.pathType(request));
    };

    public static Route account_security_username_save = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // allow customer only users
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.customer.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // customer
        Customer customer = CustomerDao.loadCustomerByUserId(user.getId());
        if (customer == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        Map<String, String> params = PojoUtils.paramsFromRequest(request);

        String password = params.get("currentPassword");
        String userName = params.get("email");

        if (UserCommons.isAuthenticated(user, password)) {
            // update user
            User exist = UserDao.loadUserByEmail(userName);
            if (exist != null) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Email già presente");
            }
            user.setEmail(userName);
            user.setUsername(userName);

            UserDao.updateUser(user);

            customer.setEmail(userName);
            CustomerDao.updateCustomer(customer);;

            // reload
            user = UserDao.loadUser(user.getId());

            if (!NotificationCommons.notifyAccount(request, AccountNotificationType.emailChanged, customer, user, null)) {
                //            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Notifica reso errata");
            }

            // update user on redis
            Manager.putSession(token, "user", user);

        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Password errata");
        }

        return "ok";
    };

    public static TemplateViewRoute account_notifications = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // allow logged user only
        if (user == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, language));
            return Manager.renderEmpty();
        }

        // update user
        user = UserDao.loadUser(user.getId());
        Manager.putSession(token, "user", user);

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // customer
        Customer customer = CustomerDao.loadCustomerByUserId(user.getId());
        attributes.put("customerEntry", CustomerCommons.toEntry(customer));

        List<CustomerNotification> customerNotificationList = CustomerNotificationDao.loadCustomerNotificationListByUserId(user.getId());
        attributes.put("customerNotificationList", customerNotificationList);

        int pagination = 12;
        int skip = NumberUtils.toInt(request.queryParams("skip"));
        int limit = NumberUtils.toInt(request.queryParams("limit"));
        if (limit <= 0) {
            limit = pagination;
        }
        attributes.put("skip", skip);
        attributes.put("limit", limit);
        attributes.put("pagination", pagination);

        List<EntityNotification> entityNotificationList = null;
        try {
            entityNotificationList = EntityNotificationDao.loadEntityNotificationListByUserId(user.getId(), skip, limit + 1);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }

        // loadmore
        boolean loadmore = false;
        if ((entityNotificationList != null) && (!entityNotificationList.isEmpty())) {
            loadmore = entityNotificationList.size() > limit;
        }
        if ((entityNotificationList != null) && (!entityNotificationList.isEmpty())) {
            if (entityNotificationList.size() > limit) {
                entityNotificationList = entityNotificationList.subList(0, limit);
            }
        }

        attributes.put("entityNotificationEntryList", EntityNotificationCommons.toEntries(entityNotificationList, user.getId()));

        if (skip == 0) {
            long newNotificationCount = EntityNotificationDao.countEntityNotificationByUserId(user.getId(), true);
            attributes.put("newNotificationCount", newNotificationCount);
            long totalNotificationCount = EntityNotificationDao.countEntityNotificationByUserId(user.getId(), null);
            attributes.put("totalNotificationCount", totalNotificationCount);
        }

        attributes.put("loadmore", loadmore);

        URIBuilder uriBuilder;
        try {
            if (RouteUtils.publicQueryString(request) != null) {
                uriBuilder = new URIBuilder(RouteUtils.publicUrl(request) + "?" + RouteUtils.publicQueryString(request));
                List<NameValuePair> queryParameters = uriBuilder.getQueryParams();
                for (Iterator<NameValuePair> queryParameterItr = queryParameters.iterator(); queryParameterItr.hasNext();) {
                    NameValuePair queryParameter = queryParameterItr.next();
                    if (queryParameter.getName().equals("skip")) {
                        queryParameterItr.remove();
                    }
                    if (queryParameter.getName().equals("limit")) {
                        queryParameterItr.remove();
                    }
                }
                uriBuilder.setParameters(queryParameters);

                String resultUrl = uriBuilder.build().toString();
                attributes.put("resultUrl", resultUrl);
            } else {
                attributes.put("resultUrl", RouteUtils.publicUrl(request));
            }
        } catch (URISyntaxException ex) {
            //
        }

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));

        return Manager.render(Templates.ACCOUNT_NOTIFICATIONS, attributes, RouteUtils.pathType(request));
    };

    public static Route account_security_change_password_save = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // allow customer only users
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.customer.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // per utente system salto controllo customer (manfredo non ha il customer per qualche motivo)
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())) {
            // customer
            Customer customer = CustomerDao.loadCustomerByUserId(user.getId());
            if (customer == null) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
        }

        Map<String, String> params = PojoUtils.paramsFromRequest(request);

        String currentPassword = params.get("currentPassword");
        String newPassword = params.get("newPassword");
        String confirmNewPassword = params.get("confirmNewPassword");

        if (StringUtils.equals(newPassword, confirmNewPassword)) {
            if (UserCommons.isAuthenticated(user, currentPassword)) {
                // update user
                if (!StringUtils.equals(user.getPassword(), newPassword)) {
                    user.setPassword(PasswordHash.createHash(newPassword));

                    UserDao.updateUser(user);

                    // reload
                    user = UserDao.loadUser(user.getId());

                    // update user on redis
                    Manager.putSession(token, "user", user);
                } else {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "La nuova password non può essere uguale a quella attuale.");
                }
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "La password attuale è errata.");
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Le password non coincidono. Verifica i dati inseriti.");
        }

        return "ok";
    };

    public static Route account_security_keyword_save = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // allow customer only users
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.customer.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // customer
        Customer customer = CustomerDao.loadCustomerByUserId(user.getId());
        if (customer == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        Map<String, String> params = PojoUtils.paramsFromRequest(request);

        String keyword = params.get("keyword");

        // update user
        if (user.getKeyword() == null || StringUtils.isBlank(user.getKeyword()) || !StringUtils.equals(user.getKeyword(), keyword)) {
            user.setKeyword(keyword);

            UserDao.updateUser(user);

            // reload
            user = UserDao.loadUser(user.getId());

            if (!NotificationCommons.notifyAccount(request, AccountNotificationType.keyword, customer, user, null)) {
                //            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Notifica reso errata");
            }

            // update user on redis
            Manager.putSession(token, "user", user);
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "La nuova parola chiave non può essere uguale a quella attuale");
        }

        return "ok";
    };

    public static Route account_info_edit_save = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // allow customer only users
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.customer.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // customer
        Customer customer = CustomerDao.loadCustomerByUserId(user.getId());
        if (customer == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // params
        List<SlimImage> slims = new ArrayList<>();
        List<PostedFile> files = new ArrayList<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, slims, files);
        // merge
        PojoUtils.mergeFromParams(params, customer);

        String name = params.get("name");
        String lastname = params.get("lastname");

        customer.setName(StringUtils.trim(name));
        customer.setLastname(StringUtils.trim(lastname));

        String birthDay = params.get("birthDay");
        String birthMonth = params.get("birthMonth");
        String birthYear = params.get("birthYear");

        Date birthDate = null;
        if (StringUtils.isNotBlank(birthDay)
                && StringUtils.isNotBlank(birthMonth)
                && StringUtils.isNotBlank(birthYear)) {
            birthDate = new Date();
            birthDate = DateUtils.setYears(birthDate, NumberUtils.toInt(birthYear));
            birthDate = DateUtils.setMonths(birthDate, NumberUtils.toInt(birthMonth, 1) - 1);
            birthDate = DateUtils.setDays(birthDate, NumberUtils.toInt(birthDay));
            birthDate = DateUtils.setHours(birthDate, 0);
            birthDate = DateUtils.setMinutes(birthDate, 0);
            birthDate = DateUtils.setSeconds(birthDate, 0);
            birthDate = DateUtils.setMilliseconds(birthDate, 0);
        }
        customer.setBirthDate(birthDate);

        CustomerDao.updateCustomer(customer);
        // images
        if (!slims.isEmpty()) {
            CustomerDao.updateCustomerImage(user.getUsername(), customer.getId(), slims.get(0));
        } else {
            CustomerDao.removeCustomerImage(customer.getId());
        }

        String passwordOld = params.get("password-old");
        String passwordNew = params.get("password-new");

        if (StringUtils.isNotBlank(passwordOld) || StringUtils.isNotBlank(passwordNew)) {
            if (UserCommons.isValidUserCustomerPasswordChange(user.getPassword(), passwordOld, passwordNew)) {
                String encodedPassword = PasswordHash.createHash(passwordNew);

                // update user
                user = UserDao.loadUser(user.getId());
                user.setPassword(encodedPassword);

                UserDao.updateUser(user);

                // reload
                user = UserDao.loadUser(user.getId());

                // update user on redis
                Manager.putSession(token, "user", user);

            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Cambio password non riuscito");
            }

        }

        return "ok";
    };

    ////////////
    // internals
}
