package com.agora.admin;

import com.agora.core.Defaults;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.LabelDao;
import com.agora.extensions.LabelFunction;
import com.agora.handson.HandsonTable;
import com.agora.pojo.Label;
import com.agora.pojo.LabelItem;
import com.agora.pojo.User;
import com.agora.pojo.types.ProfileType;
import com.agora.support.geocoding.Geocoder;
import com.agora.util.RouteUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class AdminController {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(AdminController.class.getName());

    public static TemplateViewRoute labels = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        attributes.put("user", user);
        
        String language = StringUtils.defaultIfBlank(request.queryParams("language"), Defaults.LANGUAGE);
        attributes.put("language", language);                
        
        // labels
        // ... json asynchronously loaded by labels_data route
        
        return Manager.render(Templates.LABELS, attributes, RouteUtils.pathType(request));
    };
    
    public static Route labels_data = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        List<Label> lbls = LabelDao.loadLabelList();
        List<PlainLabel> plains = new ArrayList<>();
        if ((lbls != null) && (!lbls.isEmpty())) {
            for (Label lbl : lbls) {
                // key
                PlainLabel plain = new PlainLabel();
                plain.setKey(lbl.getKey());
                // languages
                if (lbl.getItems() != null) {
                    for (LabelItem itm : lbl.getItems()) {
                        switch (itm.getLanguage()) {
                            case "it":
                                plain.setIt(itm.getDescription());
                                break;
                            case "en":
                                plain.setEn(itm.getDescription());
                                break;
                            default:
                                break;
                        }
                    }
                }
                // add
                plains.add(plain);
            }
        }
        return plains;
    };
    
    public static Route labels_save = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        // params
        List<FileItem> fields = new ServletFileUpload(new DiskFileItemFactory()).parseRequest(request.raw());
        String json = null;
        if (fields != null) {
            // multipart fields parsing
            for (FileItem field : fields) {
                if (field.isFormField()) {
                    switch (field.getFieldName()) {
                        case "json":
                            json = field.getString("UTF-8");
                            break;
                        default:
                            LOGGER.warn("received unknown field " + field.getFieldName());
                            break;
                    } 
                }
            }
        }
        
        // parse table data
        HandsonTable table = null;
        if (StringUtils.isNotBlank(json)) {
            table = Manager.deserializeFromJson(json, HandsonTable.class);
        }
        
        if (table != null) {
            
            // convert labels
            List<Label> lbls = null;
            if (table.getData() != null) {
                lbls = new ArrayList<>();
                for (String[] row : table.getData()) {
                    if (row.length > 0) {
                        
                        Label label = new Label();
                        label.setKey(row[0]);
                        label.setItems(new ArrayList<>());

                        // it
                        LabelItem item = new LabelItem();
                        item.setLanguage("it");
                        if (row.length >= 2) {
                            item.setDescription(row[1]);
                        }
                        label.getItems().add(item);
                        
                        // en
                        item = new LabelItem();
                        item.setLanguage("en");
                        if (row.length >= 3) {
                            item.setDescription(row[2]);
                        }
                        label.getItems().add(item);
                        
                        lbls.add(label);
                    }
                }
            }
            
            // clear pre-existing labels
            LabelDao.removeLabels();
            
            // save labels
            for (Label label : lbls) {
                LabelDao.insertLabel(label);
            }
            
            // clear labels cache
            LabelFunction.clear();
            
        } else {
            Spark.halt(HttpStatus.BAD_REQUEST_400, "Labels not updated");
        }
        
        return "ok";
    };
    
    public static Route labels_clear = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        LabelFunction.clear();
        
        return "ok";
    };

    public static Route geocoder_clear = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        Geocoder.clear();
        
        return "ok";
    };

}
