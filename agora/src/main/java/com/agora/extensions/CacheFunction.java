package com.agora.extensions;


import com.agora.commons.CacheCommons;
import com.agora.commons.EntityNotificationCommons;
import com.agora.dao.EntityNotificationDao;
import com.agora.notification.EntityNotificationEntryWithCount;
import com.agora.pojo.EntityNotification;
import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CacheFunction implements Function {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("what");
        names.add("howmany");
        return names;
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        String what = args.get("what").toString();
        Integer amount = null;
        if (args.get("howmany") != null) {
            amount = Integer.valueOf(args.get("howmany").toString());
        }

        if (StringUtils.equalsIgnoreCase(what, "tags")) {
            List<String> tags = CacheCommons.getMostUsedTags();
            if (amount != null && amount > 0) {
                if (tags.size() > amount) {
                    tags = tags.subList(0, amount);
                }
            }
            return tags;
        }

        return null;
    }

}
