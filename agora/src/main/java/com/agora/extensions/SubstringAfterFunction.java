package com.agora.extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class SubstringAfterFunction implements Function {
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("str");
        names.add("separator");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        String str = (String) args.get("str");
        String separator = (String) args.get("separator");
        
        if (StringUtils.isNotEmpty(separator)) {
            str = StringUtils.substringAfter(str, separator);
        }
        
        return str;
    }
    
}
