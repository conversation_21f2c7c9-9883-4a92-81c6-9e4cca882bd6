package com.agora.extensions;

/**
 *
 * <AUTHOR>
 */

import java.util.HashMap;
import java.util.Map;

import com.mitchellbosecke.pebble.extension.AbstractExtension;
import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.extension.Filter;

public class Extensions extends AbstractExtension {

    @Override
    public Map<String, Filter> getFilters() {
        Map<String, Filter> filters = new HashMap<>();
        // text
        filters.put("striphtml", new StripHtmlFilter());
        filters.put("stripcrlf", new StripCrLfFilter());
        filters.put("json", new JsonFilter());
        filters.put("jsonescape", new JsonEscapeFilter());
        filters.put("jsonldescape", new JsonLDEscapeFilter());
        filters.put("intvalue", new IntValueFilter());
        // lists
        filters.put("shuffle", new ShuffleFilter());
        filters.put("sortby", new SortByFilter());
        filters.put("seoquery", new SeoQueryFilter());
        return filters;
    }
    
    @Override
    public Map<String, Function> getFunctions() {
        Map<String, Function> functions = new HashMap<>();
        // path
        functions.put("paths", new PathsFunction());
        // lookup
        functions.put("lookup", new LookupFunction());
        functions.put("decode", new DecodeFunction());
        functions.put("toseo", new ToSeoFunction());
        // date
        functions.put("before", new BeforeFunction());
        functions.put("after", new AfterFunction());
        functions.put("now", new NowFunction());
        functions.put("today", new TodayFunction());
        functions.put("daysbetween", new DaysBetweenFunction());
        functions.put("hoursbetween", new HoursBetweenFunction());
        functions.put("addminutes", new AddMinutesFunction());
        functions.put("addhours", new AddHoursFunction());
        functions.put("adddays", new AddDaysFunction());
        functions.put("addmonths", new AddMonthsFunction());
        functions.put("formatDate", new FormatDateFunction());
        // text
        functions.put("newline", new NewLineFunction());
        functions.put("pad", new PadFunction());
        functions.put("substringbefore", new SubstringBeforeFunction());
        functions.put("substringafter", new SubstringAfterFunction());
        // numbers
        functions.put("round", new RoundFunction());
        // objects
        functions.put("get", new GetFunction());
        // localization
        functions.put("label", new LabelFunction());
        
        functions.put("follow", new FollowFunction());
        
        functions.put("notification", new NotificationFunction());

        // cache
        functions.put("cache", new CacheFunction());

        return functions;
    }

}
