package com.agora.extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class PadFunction implements Function {
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("str");
        names.add("size");
        names.add("padStr");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        String str = (String) args.get("str");
        int size = ((Long) args.get("size")).intValue();
        String padStr = (String) args.get("padStr");
        
        if (size < 0) {
            str = StringUtils.leftPad(str, Math.abs(size), padStr);
        } else {
            str = StringUtils.leftPad(str, size, padStr);
        }
        
        return str;
    }
    
}
