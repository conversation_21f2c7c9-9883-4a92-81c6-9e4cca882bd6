package com.agora.extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.agora.core.Defaults;
import com.agora.dao.LabelDao;
import com.agora.pojo.Label;
import com.agora.pojo.LabelItem;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class LabelFunction implements Function {

    private static final Logger LOGGER = LoggerFactory.getLogger(LabelFunction.class.getName());

    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("key");
        names.add("params");
        return names;
    }

    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {

        String key = (String) args.get("key");
        Object params = args.get("params");

        // language
        String language = null;
        if (StringUtils.isBlank(language)) {
            language = (String) context.getVariable("language");
        }
        
        // search label by language
        String description = labels().containsKey(key) ? labels().get(key).get(language) : null;
        
        // search label by default language
        if (StringUtils.isBlank(description)) {
            description = labels().containsKey(key) ? labels().get(key).get(Defaults.LANGUAGE) : null;
        }

        // parameter parsing
        if (StringUtils.isNotBlank(description)) {
            if (params != null) {
                if (params instanceof List) {
                    List<?> list = (List<?>) params;
                    return MessageFormat.format(description, list.toArray());
                } else {
                    return MessageFormat.format(description, params);
                }
            }
        }
        
        // not found
        if (StringUtils.isBlank(description)) {
            if (StringUtils.isNotBlank(key)) {
                // not found - show key        
                description = "?!?" + key + "?!?";
            } else {
                // not found - show ?!?
                description = "?!?MISSING?!?";
            }
        }
        
        return description;
    }

    public static String description(String language, String key) {
        // search label by language
        String description = labels().containsKey(key) ? labels().get(key).get(language) : null;
        
        // search label by default language
        if (StringUtils.isBlank(description)) {
            description = labels().containsKey(key) ? labels().get(key).get(Defaults.LANGUAGE) : null;
        }
        
        // not found
        if (StringUtils.isBlank(description)) {
            if (StringUtils.isNotBlank(key)) {
                // not found - show key        
                description = "?!?" + key + "?!?";
            } else {
                // not found - show ?!?
                description = "?!?MISSING?!?";
            }
        }
        
        return description;
    }
    
    public static void clear() {
        // ?????? @mike: qui serve un semaforo
        _labels = null;
    }
    
    public static int size() {
        return (_labels != null) ? _labels.size() : 0;
    }
    
    private static Map<String, Map<String, String>> _labels;
    private static Map<String, Map<String, String>> labels() {
        if (_labels == null) {
            
            // init
            _labels = new HashMap<>();
            
            // load from database
            List<Label> labels = null;
            try {
                labels = LabelDao.loadLabelList();
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            
            // populate maps
            if ((labels != null) &&
                    (!labels.isEmpty())) {
                
                for (Label label : labels) {
                    
                    String key = label.getKey();
                    if (!_labels.containsKey(key)) {
                        
                        if ((label.getItems() != null) &&
                                (!label.getItems().isEmpty())) {
                            
                            Map<String, String> items = new HashMap<>();
                            for (LabelItem item : label.getItems()) {
                                items.put(item.getLanguage(), item.getDescription());
                            }
                            _labels.put(label.getKey(), items);
                            
                        } else {
                            LOGGER.error("empty items " + key);
                        }
                        
                    } else {
                        LOGGER.error("duplicated key " + key);
                    }
                }
                
            }
        }
        return _labels;
    }
    
}
