package com.agora.extensions;

import com.agora.commons.PageCommons;
import com.agora.core.Defaults;
import com.agora.dao.PageDao;
import com.agora.page.PageEntry;
import com.agora.pojo.Page;
import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class FollowFunction implements Function {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("type");
        names.add("userId");
        names.add("provinceCode");
        names.add("quantity");
        names.add("sort");
        names.add("pageId");
        return names;
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        String type = (String) args.get("type");
        ObjectId userId = (ObjectId) args.get("userId");
        String provinceCode = (String) args.get("provinceCode");
        Long quantityLong = (Long) args.get("quantity");
        String sort = (String) args.get("sort");
        ObjectId pageId = (ObjectId) args.get("pageId");
        
        // contextPath
        String contextPath = (String) context.getVariable("contextPath");

        // language
        String language = (String) context.getVariable("language");
        if (StringUtils.isBlank(language)) {
            language = Defaults.LANGUAGE;
        }
        
        List items = null;

        Integer quantity = 0;
        if (quantityLong != null) {
            quantity = Math.toIntExact(quantityLong);
        }

        switch (type) {
            case "whopeople":
                items = loadWhoPeople(provinceCode, userId, quantity, sort);
                break;
            case "whopage":
                items = loadWhoPage(provinceCode, userId, quantity, sort, pageId);
                break;
            
            default:
                logger.error("unrecognized who type " + type);
                break;
        }
        
        return items;
    }
    
    private List<PageEntry> loadWhoPeople(String provinceCode, ObjectId userId, Integer quantity, String sort) {
        List<Page> whoPeople = null;
        
        try {
            whoPeople = PageDao.loadWhoPeople(provinceCode, userId, quantity, sort);
        } catch (Exception ex) {
            logger.error("suppressed", ex);
        }
        
        
        return PageCommons.toEntries(whoPeople);
    }
    
    private List<PageEntry> loadWhoPage(String provinceCode, ObjectId userId, Integer quantity, String sort, ObjectId pageId) {
        List<Page> whoPage = null;

        if (pageId != null) {
            // nuovo test carico pagine che hanno senso per l'utente
            try {
                Page page = PageDao.loadPage(pageId);
                if (page != null) {
                    List<String> tags = page.getTags();
                    if (tags != null && !tags.isEmpty()) {
                        // solo se ha tags carico le pagine che hanno almeno un tag in comune
                        List<Page> pageToCheck = PageDao.loadCorrelatedPages(page, userId, quantity);
                        whoPage = pageToCheck;
                    }
                }
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
        } else {
            try {
                whoPage = PageDao.loadWhoPage(provinceCode, userId, quantity, sort);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
        }
        
        return PageCommons.toEntries(whoPage);
    }

}
