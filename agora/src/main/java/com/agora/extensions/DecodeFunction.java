package com.agora.extensions;

import com.agora.dao.AreaDao;
import com.agora.dao.CategoryDao;
import com.agora.dao.CountryDao;
import com.agora.dao.ProvinceDao;
import com.agora.dao.SubcategoryDao;
import com.agora.pojo.Country;
import com.agora.pojo.Province;
import com.mitchellbosecke.pebble.extension.Function;
import com.agora.dao.VendorDao;
import com.agora.dao.UserDao;
import com.agora.pojo.Area;
import com.agora.pojo.Category;
import com.agora.pojo.Subcategory;
import com.agora.pojo.User;
import com.agora.pojo.Vendor;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import com.agora.commons.VendorCommons;
import com.agora.core.Defaults;
import com.agora.pojo.types.EventStatusType;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class DecodeFunction implements Function {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("table");
        names.add("value");
        names.add("language");
        return names;
    }

    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {

        String table = (String) args.get("table");
        String value = (args.get("value") != null) ? args.get("value").toString() : null;
        String language = (String) args.get("language");
        if (StringUtils.isBlank(language)) {
            language = (String) context.getVariable("language");
        }
        if (StringUtils.isBlank(language)) {
            language = Defaults.LANGUAGE;
        }

        String description = "-";

        switch (table) {
            case "event-status":
                description = decodeEnum(EventStatusType.class, value);
                break;
            case "area":
                description = decodeArea(value, language);
                break;
            case "category":
                description = decodeCategory(value, language);
                break;
            case "subcategory":
                description = decodeSubcategory(value, language);
                break;
            case "country":
                description = decodeCountry(value, language);
                break;
            case "prefix":
                description = decodePrefix(value);
                break;
            case "province":
                description = decodeProvince(value);
                break;
            case "vendor":
                description = decodeVendor(value);
                break;
            case "user":
                description = decodeUser(value);
                break;
                
            default:
                logger.error("unrecognized table " + table);
                break;
        }

        return description;
    }

    private String decodeCountry(String value, String language) {
        String description = "-";
        if (StringUtils.isNotBlank(value)) {
            Country item = null;
            try {
                item = CountryDao.loadCountry(value);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                if (StringUtils.equalsIgnoreCase(language, "en")) {
                    if (StringUtils.isNotBlank(item.getDescriptionEnglish())) {
                        description = item.getDescriptionEnglish();
                    }
                } else {
                    if (StringUtils.isNotBlank(item.getDescription())) {
                        description = item.getDescription();
                    }
                }
                
            } else {
                logger.error("unrecognized code ", value);
            }
        }
        return description;
    }
    
    private String decodePrefix(String value) {
        String description = "-";
        if (StringUtils.isNotBlank(value)) {
            Country item = null;
            try {
                item = CountryDao.loadCountry(value);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                description = item.getPrefix();
            } else {
                logger.error("unrecognized code ", value);
            }
        }
        return description;
    }
    
    private String decodeProvince(String value) {
        String description = "-";
        if (StringUtils.isNotBlank(value)) {
            Province item = null;
            try {
                item = ProvinceDao.loadProvince(value);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                if (StringUtils.isNotBlank(item.getDescription())) {
                    description = item.getDescription();
                }
            } else {
                logger.error("unrecognized code ", value);
            }
        }
        return description;
    }

    private String decodeVendor(String value) {
        String description = "-";
        if (StringUtils.isNotBlank(value)) {
            Vendor item = null;
            try {
                item = VendorDao.loadVendor(new ObjectId(value));
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                description = VendorCommons.fullname(item);
            } else {
                logger.error("unrecognized code ", value);
            }
        }
        return description;
    }
    
    private String decodeUser(String value) {
        String description = "-";
        if (StringUtils.isNotBlank(value)) {
            User item = null;
            try {
                item = UserDao.loadUser(new ObjectId(value));
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                if (StringUtils.isNotBlank(item.getName())) {
                    description = item.getName();
                }
            } else {
                logger.error("unrecognized code ", value);
            }
        }
        return description;
    }

    private String decodeArea(String value, String language) {
        String description = "-";
        if (StringUtils.isNotBlank(value)) {
            Area item = null;
            try {
                item = AreaDao.loadAreaByCode(value);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                if (StringUtils.equalsIgnoreCase(language, "en")) {
                    if (StringUtils.isNotBlank(item.getTitleEnglish())) {
                        description = item.getTitleEnglish();
                    }
                } else {
                    if (StringUtils.isNotBlank(item.getTitle())) {
                        description = item.getTitle();
                    }
                }
            } else {
                logger.error("unrecognized code ", value);
            }
        }
        return description;
    }

    private String decodeCategory(String value, String language) {
        String description = "-";
        if (StringUtils.isNotBlank(value)) {
            Category item = null;
            try {
                item = CategoryDao.loadCategoryByCode(value);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                if (StringUtils.equalsIgnoreCase(language, "en")) {
                    if (StringUtils.isNotBlank(item.getTitleEnglish())) {
                        description = item.getTitleEnglish();
                    }
                } else {
                    if (StringUtils.isNotBlank(item.getTitle())) {
                        description = item.getTitle();
                    }
                }
            } else {
                logger.error("unrecognized code ", value);
            }
        }
        return description;
    }

    private String decodeSubcategory(String value, String language) {
        String description = "-";
        if (StringUtils.isNotBlank(value)) {
            Subcategory item = null;
            try {
                item = SubcategoryDao.loadSubcategoryByCode(value);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                if (StringUtils.equalsIgnoreCase(language, "en")) {
                    if (StringUtils.isNotBlank(item.getTitleEnglish())) {
                        description = item.getTitleEnglish();
                    }
                } else {
                    if (StringUtils.isNotBlank(item.getTitle())) {
                        description = item.getTitle();
                    }
                }
            } else {
                logger.error("unrecognized code ", value);
            }
        }
        return description;
    }

    private <E extends Enum<E>> String decodeEnum(Class<E> enumClass, String value) {
        String description = "-";
        if (StringUtils.isNotBlank(value)) {
            E item = null;
            try {
                item = Enum.valueOf(enumClass, value);
            } catch (IllegalArgumentException ignored) {
                // ignored
            }
            if (item != null) {
                try {
                    String dsc = BeanUtils.getProperty(item, "description");
                    if (StringUtils.isNotBlank(dsc)) {
                        description = dsc;
                    }
                } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException | RuntimeException ex) {
                    // ignored
                }
            } else {
                logger.error("unrecognized code ", value);
            }
        }
        return description;
    }
    
}
