package com.agora.extensions;

import com.agora.util.TimeUtils;
import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class NowFunction implements Function {
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        return TimeUtils.now();
    }
    
}
