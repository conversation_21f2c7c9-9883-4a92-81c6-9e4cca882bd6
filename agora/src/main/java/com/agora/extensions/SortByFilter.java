package com.agora.extensions;

/**
 *
 * <AUTHOR>
 */

import com.mitchellbosecke.pebble.extension.Filter;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import com.agora.util.FieldComparator;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import spark.utils.StringUtils;

public class SortByFilter implements Filter {

    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("propertyName");
        names.add("descending");
        return names;        
    }

    @Override
    public Object apply(Object input, Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        if (input == null) {
            return null;
        }
        if (!(input instanceof List)) {
            return input;
        }
        
        String propertyName = (String) args.get("propertyName");
        if (StringUtils.isBlank(propertyName)) {
            return input;
        }
        
        boolean descending = BooleanUtils.toBoolean((String) args.get("descending"));
        
        List list = (List) input;
        
        FieldComparator comparator = new FieldComparator(propertyName, descending);
        Collections.sort(list, comparator);
        
        return list;
    }

}