package com.agora.extensions;

import com.mitchellbosecke.pebble.extension.Filter;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import com.agora.core.Manager;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class JsonFilter implements Filter {

    @Override
    public List<String> getArgumentNames() {
        return null;
    }

    @Override
    public Object apply(Object input, Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        if (input == null) {
            return null;
        }
        return Manager.serializeToJson(input);
    }
    
}
