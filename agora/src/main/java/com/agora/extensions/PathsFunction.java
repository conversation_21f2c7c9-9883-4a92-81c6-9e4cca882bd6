package com.agora.extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.agora.core.Paths;
import com.agora.util.RouteUtils;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class PathsFunction implements Function {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("key");
        names.add("language");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        String key = (String) args.get("key");
        String language = (String) args.get("language");
        
        // contextPath
        String contextPath = (String) context.getVariable("contextPath");
        
        // language
        if (StringUtils.isBlank(language)) {
            language = (String) context.getVariable("language");
        }
        
        String path = null;
        String localizedKey = key + "_" + StringUtils.upperCase(language);
        
        // constant path (localized)
        if (Paths.paths().containsKey(localizedKey)) {
            path = contextPath + Paths.paths().get(localizedKey);
        }
        
        // constant path (base)
        if (StringUtils.isBlank(path)) {
            if (Paths.paths().containsKey(key)) {
                path = contextPath + Paths.paths().get(key);
            }
        }
        
        if (StringUtils.isBlank(path)) {
            
            // error - path not found
            logger.error("unable to find path for route " + key);
            
        } else {
            
            // if path need language replacement
            if (StringUtils.containsIgnoreCase(path, ":language")) {

                if (StringUtils.isBlank(language)) {
                    
                    // error - language not found
                    logger.error("unable to find language for route " + key);
                    
                } else {
                    
                    path = RouteUtils.language(path, language);
                    
                }

            }
            
        }
        
        return path;
    }
    
}
