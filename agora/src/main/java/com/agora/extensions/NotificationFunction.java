package com.agora.extensions;


import com.agora.commons.EntityNotificationCommons;
import com.agora.dao.EntityNotificationDao;
import com.agora.notification.EntityNotificationEntryWithCount;
import com.agora.pojo.EntityNotification;
import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class NotificationFunction implements Function {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("userId");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        ObjectId userId = (ObjectId) args.get("userId");
        
        // contextPath
        String contextPath = (String) context.getVariable("contextPath");

        return loadNotification(userId);
    }
    

    private EntityNotificationEntryWithCount loadNotification(ObjectId userId) {
        EntityNotificationEntryWithCount entityNotificationEntryWithCount = new EntityNotificationEntryWithCount();

        List<EntityNotification> items = null;
        if (userId != null) {
            try {
                items = EntityNotificationDao.loadDistinctEntityNotificationListByUserId(userId, 0, 6);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
        }
        
        entityNotificationEntryWithCount.setEntityNotificationEntryList(EntityNotificationCommons.toEntries(items, userId));
        
        try {
            entityNotificationEntryWithCount.setNewNotificationCount(EntityNotificationDao.countEntityNotificationByUserId(userId, true));
            entityNotificationEntryWithCount.setTotalNotificationCount(EntityNotificationDao.countEntityNotificationByUserId(userId, null));
        } catch (Exception ex) {
            logger.error("suppressed", ex);
        }
        
        
        return entityNotificationEntryWithCount;
    }
    

}
