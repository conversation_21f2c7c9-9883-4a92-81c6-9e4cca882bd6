package com.agora.extensions;

import com.agora.core.Defaults;
import com.agora.dao.AreaDao;
import com.agora.dao.CategoryDao;
import com.agora.dao.SubcategoryDao;
import com.agora.pojo.Area;
import com.agora.pojo.Category;
import com.agora.pojo.Subcategory;
import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import com.agora.dao.CityDao;
import com.agora.pojo.City;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class ToSeoFunction implements Function {
    
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("table");
        names.add("code");
        names.add("language");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        String table = (String) args.get("table");
        String code = (args.get("code") != null) ? args.get("code").toString() : null;
        String language = (args.get("language") != null) ? args.get("language").toString() : null;
        
        // language
        if (StringUtils.isBlank(language)) {
            language = (String) context.getVariable("language");
            
            if (StringUtils.isBlank(language)) {
                language = StringUtils.defaultString(language, Defaults.LANGUAGE);
            }
        } 
        
        String identifierSeo = "";
        
        switch (table) {
            case "area":
                identifierSeo = decodeSeoArea(code, language);
                break;
            case "category":
                identifierSeo = decodeSeoCategory(code, language);
                break;
            case "subcategory":
                identifierSeo = decodeSeoSubcategory(code, language);
                break;
            case "city":
                identifierSeo = decodeSeoCity(code, language);
                break;
            default:
                logger.error("unrecognized table " + table);
                break;
        }
        if (identifierSeo == null) {
            identifierSeo = "";
        }
        return identifierSeo;
    }

    private String decodeSeoArea(String code, String language) {
        String identifierSeo = "";
        if (StringUtils.isNotBlank(code)) {
            Area item = null;
            try {
                item = AreaDao.loadAreaByCode(code);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                identifierSeo = item.getIdentifier();
                if (language != null) {
                    if (StringUtils.equalsIgnoreCase(language, "en")) {
                        identifierSeo = item.getIdentifierEnglish();
                    }
                }
            } else {
                logger.error("unrecognized code ", code);
            }
        }
        return identifierSeo;
    }
    
    private String decodeSeoCategory(String code, String language) {
        String identifierSeo = "";
        if (StringUtils.isNotBlank(code)) {
            Category item = null;
            try {
                item = CategoryDao.loadCategoryByCode(code);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                identifierSeo = item.getIdentifier();
                if (language != null) {
                    if (StringUtils.equalsIgnoreCase(language, "en")) {
                        identifierSeo = item.getIdentifierEnglish();
                    }
                }
            } else {
                logger.error("unrecognized code ", code);
            }
        }
        return identifierSeo;
    }
    
    private String decodeSeoSubcategory(String code, String language) {
        String identifierSeo = "";
        if (StringUtils.isNotBlank(code)) {
            Subcategory item = null;
            try {
                item = SubcategoryDao.loadSubcategoryByCode(code);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                identifierSeo = item.getIdentifier();
                if (language != null) {
                    if (StringUtils.equalsIgnoreCase(language, "en")) {
                        identifierSeo = item.getIdentifierEnglish();
                    }
                }
            } else {
                logger.error("unrecognized code ", code);
            }
        }
        return identifierSeo;
    }
    
    private String decodeSeoCity(String city, String language) {
        String identifierSeo = "";
        if (StringUtils.isNotBlank(city)) {
            City item = null;
            try {
                item = CityDao.loadCityByCity(city);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                identifierSeo = item.getIdentifier();
                if (language != null) {
                    if (StringUtils.equalsIgnoreCase(language, "en")) {
                        identifierSeo = item.getIdentifierEnglish();
                    }
                }
            } else {
                logger.error("unrecognized code ", city);
            }
        }
        return identifierSeo;
    }
    
}
