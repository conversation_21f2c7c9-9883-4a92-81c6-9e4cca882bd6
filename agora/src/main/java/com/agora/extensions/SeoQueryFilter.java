package com.agora.extensions;

import com.mitchellbosecke.pebble.extension.Filter;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class SeoQueryFilter implements Filter {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("selecteds");
        return names;        
    }

    @Override
    public Object apply(Object input, Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        if (input == null) {
            return null;
        }
        if (!(input instanceof String)) {
            return input;
        }
        // querystring
        String querystring = (String) input;
        if (StringUtils.isBlank(querystring)) {
            return input;
        }
        
        // query params
        String[] queryTokens = StringUtils.splitPreserveAllTokens(querystring, "&");
        if (queryTokens == null) {
            return input;
        }
        if (queryTokens.length == 0) {
            return input;
        }
        Map<String, String> queryParams = new HashMap<>();
        for (String token : queryTokens) {
            if (StringUtils.isBlank(token)) {
                continue;
            }
            String key = StringUtils.substringBefore(token, "=");
            if (StringUtils.isBlank(key)) {
                key = token;
            }
            queryParams.put(key, token);
        }
        
        // selected query params
        if (args == null) {
            return input;
        }
        if (args.isEmpty()) {
            return input;
        }
        if (args.get("selecteds") == null) {
            return input;
        }
        if (!(args.get("selecteds") instanceof List)) {
            return input;
        }
        List selecteds = (List) args.get("selecteds");
        
        // no selected query params, remove querystring
        if (selecteds.isEmpty()) {
            return "";
        }
        
        // keep selected query params
        String seoquerystring = "";
        for (Object selected : selecteds) {
            String key = (String) selected;
            if (StringUtils.isBlank(key)) {
                continue;
            }
            if (queryParams.containsKey(key)) {
                if (StringUtils.isNotBlank(seoquerystring)) {
                    seoquerystring += "&";
                }
                seoquerystring += queryParams.get(key);
            }
        }
        
        return seoquerystring;
    }
    
}
