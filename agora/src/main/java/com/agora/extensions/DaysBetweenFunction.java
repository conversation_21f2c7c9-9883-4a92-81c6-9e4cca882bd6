package com.agora.extensions;

import com.agora.util.TimeUtils;
import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class DaysBetweenFunction implements Function {
    
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("before");
        names.add("after");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        Integer days = 0;

        Date before = null;
        Date after = null;
        try {
            before = (Date) args.get("before");
        } catch (Exception ex) {
            logger.error("unconvertable date " + args.get("before"), ex);
        }
        try {
            after = (Date) args.get("after");
        } catch (Exception ex) {
            logger.error("unconvertable date " + args.get("after"), ex);
        }
        
        if ((before != null) && (after != null)) {
            days = (int) TimeUtils.diffInDays(before, after);
        }
        
        return days;
    }
    
}
