package com.agora.extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import com.agora.util.TimeUtils;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.apache.commons.lang3.LocaleUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class FormatDateFunction implements Function {
    
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("date");
        names.add("format");
        names.add("language");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        String format = (String) args.get("format");
        if (StringUtils.isBlank(format)) {
            format = (String) context.getVariable("format");
        }
        if (StringUtils.isBlank(format)) {
            format = "dd/MM/yyyy";
        }

        String language = (String) args.get("language");
        Locale locale = Locale.ITALIAN;
        if (StringUtils.isNotBlank(language)) {
            try {
                if (context.getVariable("language") != null) {
                    language = (String) context.getVariable("language");
                }
                locale = LocaleUtils.toLocale(language);
            } catch (IllegalArgumentException e) {
                logger.warn("Invalid locale format: {}", language, e);
                locale = Locale.ITALIAN;
            }
        }

        Date date = null;
        try {
            date = (Date) args.get("date");
        } catch (Exception ex) {
            logger.error("Unconvertable date " + args.get("date"), ex);
        }

        String dateStr = "";
        if (date != null) {
            dateStr = TimeUtils.toString(date, format, locale);
        }

        return dateStr;
    }
    
}
