package com.agora.extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;

/**
 *
 * <AUTHOR>
 */
public class AddHoursFunction implements Function {
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("date");
        names.add("amount");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        Date date = (Date) args.get("date");
        int amount = NumberUtils.toInt(args.get("amount").toString(), 0);
        
        return (date != null) ? DateUtils.addHours(date, amount) : null;
    }
    
}
