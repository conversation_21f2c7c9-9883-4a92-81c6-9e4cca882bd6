package com.agora.extensions;

import com.agora.dao.AreaDao;
import com.agora.dao.CategoryDao;
import com.agora.dao.CountryDao;
import com.agora.dao.ProvinceDao;
import com.agora.dao.SubcategoryDao;
import com.agora.pojo.Area;
import com.agora.pojo.Category;
import com.agora.pojo.Country;
import com.agora.pojo.Province;
import com.agora.pojo.Subcategory;
import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import com.agora.core.Defaults;
import com.agora.dao.CityDao;
import com.agora.dao.UserDao;
import com.agora.dao.VendorDao;
import com.agora.pojo.City;
import com.agora.pojo.User;
import com.agora.pojo.Vendor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.utils.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class LookupFunction implements Function {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("table");
        names.add("filter1");
        names.add("filter2");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        String table = (String) args.get("table");
        String filter1 = (String) args.get("filter1");
        String filter2 = (String) args.get("filter2");
        
        // contextPath
        String contextPath = (String) context.getVariable("contextPath");

        // language
        String language = (String) context.getVariable("language");
        if (StringUtils.isBlank(language)) {
            language = Defaults.LANGUAGE;
        }
        
        List items = null;
        
        switch (table) {
            case "area":
                items = loadArea();
                break;
            case "category":
                items = loadCategory(filter1);
                break;
            case "subcategory":
                items = loadSubcategory(filter1, filter2);
                break;
            case "city":
                items = loadCity();
                break;
            case "country":
                items = loadCountry(language);
                break;
            case "province":
                items = loadProvince();
                break;
            case "vendor":
                items = loadVendor();
                break;
            case "user":
                items = loadUser();
                break;
            default:
                logger.error("unrecognized table " + table);
                break;
        }
        
        return items;
    }

    private List<Country> loadCountry(String language) {
        List<Country> items = null;
        try {
            items = CountryDao.loadCountryList(language);
        } catch (Exception ex) {
            logger.error("suppressed", ex);
        }
        return items;
    }
    
    private List<Province> loadProvince() {
        List<Province> items = null;
        try {
            items = ProvinceDao.loadProvinceList();
        } catch (Exception ex) {
            logger.error("suppressed", ex);
        }
        return items;
    }
    
    private List<City> loadCity() {
        List<City> items = null;
        try {
            items = CityDao.loadCityList();
        } catch (Exception ex) {
            logger.error("suppressed", ex);
        }
        return items;
    }

    private List<Vendor> loadVendor() {
        List<Vendor> items = null;
        try {
            items = VendorDao.loadVendorList();
        } catch (Exception ex) {
            logger.error("suppressed", ex);
        }
        return items;
    }

    private List<User> loadUser() {
        List<User> items = null;
        try {
            items = UserDao.loadUserList();
        } catch (Exception ex) {
            logger.error("suppressed", ex);
        }
        return items;
    }

    private List<Area> loadArea() {
        List<Area> items = null;
        try {
            items = AreaDao.loadAreaList();
        } catch (Exception ex) {
            logger.error("suppressed", ex);
        }
        return items;
    }

    private List<Category> loadCategory(String filter1) {
        List<Category> items = null;
        if (StringUtils.isBlank(filter1)) {
            try {
                items = CategoryDao.loadCategoryList();
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
        } else {
            try {
                items = CategoryDao.loadCategoryListBy(filter1);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
        }
        return items;
    }

    private List<Subcategory> loadSubcategory(String filter1, String filter2) {
        List<Subcategory> items = null;
        if (StringUtils.isBlank(filter1) && StringUtils.isBlank(filter2)) {
            try {
                items = SubcategoryDao.loadSubcategoryList();
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
        } else {
            try {
                items = SubcategoryDao.loadSubcategoryListBy(filter1, filter2);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
        }
        return items;
    }

    private <E extends Enum<E>> List<String[]> loadEnum(Class<E> enumClass) {
        return loadEnum(enumClass, null);
    }
    
    private <E extends Enum<E>> List<String[]> loadEnum(Class<E> enumClass, String extra) {
        List<String[]> items = new ArrayList<>();
        
        List<E> values = new ArrayList<>(Arrays.asList(enumClass.getEnumConstants()));
        for (E item : values) {
            String[] kv = new String[2 + (StringUtils.isNotBlank(extra) ? 1 : 0)];
            kv[0] = item.name();

            String dsc = null;
            try {
                dsc = BeanUtils.getProperty(item, "description");
            } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException | RuntimeException ex) {
                // ignored
            }
            kv[1] = dsc;
            
            // get "extra" property from enum
            if (StringUtils.isNotBlank(extra)) {
                
                String extraDsc = null;
                try {
                    extraDsc = BeanUtils.getProperty(item, extra);
                } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException | RuntimeException ex) {
                    // ignored
                }
                kv[2] = extraDsc;
                
            }
            
            items.add(kv);
        }
        
        return items;
    }

}
