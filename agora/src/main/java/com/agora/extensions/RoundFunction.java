package com.agora.extensions;

import com.agora.util.MoneyUtils;
import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class RoundFunction implements Function {

    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("value");
        names.add("decimals");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        Double value = null;
        if (args.get("value") != null) {
            if (args.get("value") instanceof Double) {
                value = (Double) args.get("value");
            }
        }
        
        Integer decimals = null;
        if (args.get("decimals") != null) {
            if (args.get("decimals") instanceof Long) {
                decimals = ((Long) args.get("decimals")).intValue();
            } else {
                decimals = (Integer) args.get("decimals");
            }
        }
        if (decimals == null) {
            decimals = 2;
        }
        
        if (value != null) {
            value = MoneyUtils.round(value, decimals);
        }
        
        return value;
    }
    
}
