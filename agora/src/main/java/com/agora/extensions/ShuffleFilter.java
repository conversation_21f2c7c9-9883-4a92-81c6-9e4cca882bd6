package com.agora.extensions;

/**
 *
 * <AUTHOR>
 */

import com.mitchellbosecke.pebble.extension.Filter;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class ShuffleFilter implements Filter {

    @Override
    public List<String> getArgumentNames() {
        return null;
    }

    @Override
    public Object apply(Object input, Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        if (input == null) {
            return null;
        }
        if (!(input instanceof List)) {
            return input;
        }
        List list = (List) input;
        Collections.shuffle(list);
        return list;
    }

}