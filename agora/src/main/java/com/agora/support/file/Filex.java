package com.agora.support.file;

/**
 *
 * <AUTHOR>
 */
public class Filex {

    private String filename;
    private String originalFilename;
    private String contentType;
    private byte[] bytes;
    private String filepath;

    public Filex() {
    }

    public Filex(String filename, String originalFilename, String contentType, byte[] bytes, String filepath) {
        this.filename = filename;
        this.originalFilename = originalFilename;
        this.contentType = contentType;
        this.bytes = bytes;
        this.filepath = filepath;
    }
    
    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getOriginalFilename() {
        return originalFilename;
    }

    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }
    
    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public byte[] getBytes() {
        return bytes;
    }

    public void setBytes(byte[] bytes) {
        this.bytes = bytes;
    }

    public String getFilepath() {
        return filepath;
    }

    public void setFilepath(String filepath) {
        this.filepath = filepath;
    }
    
}
