package com.agora.support.file.posted;

import java.io.File;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class PostedFile {

    private static final Logger LOGGER = LoggerFactory.getLogger(PostedFile.class.getName());
    
    private String filename;
    private String name;
    private String contentType;
    private String extension;
    private String classFieldName;

    public PostedFile(String filename, String name, String contentType, String extension) {
        this.filename = filename;
        this.name = name;
        this.contentType = contentType;
        this.extension = extension;
    }
    
    public PostedFile(String filename, String name, String contentType, String extension, String classFieldName) {
        this.filename = filename;
        this.name = name;
        this.contentType = contentType;
        this.extension = extension;
        this.classFieldName = classFieldName;
    }

    public long getLength() {
        long length = 0L;
        
        if (StringUtils.isNotBlank(filename)) {
            
            File file = new File(filename);
            if (file.exists() && file.canRead()) {
                length = FileUtils.sizeOf(file);
            }
            
        }
        
        return length;
    }
    
    public byte[] getBytes() {
        byte[] bytes = null;
        
        if (StringUtils.isNotBlank(filename)) {
            
            File file = new File(filename);
            if (file.exists() && file.canRead()) {
                try {
                    bytes = FileUtils.readFileToByteArray(file);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
            }
            
        }
        
        return bytes;
    }
    
    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }
    
    public String getClassFieldName() {
        return classFieldName;
    }

    public void setClassFieldName(String classFieldName) {
        this.classFieldName = classFieldName;
    }
}
