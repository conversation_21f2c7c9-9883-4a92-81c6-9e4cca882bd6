package com.agora.support.image;

/**
 *
 * <AUTHOR>
 */
public class Image {

    private String filename;
    private String originalFilename;
    private String contentType;
    private byte[] bytes;
    private Integer width, height;

    public Image() {
    }

    public Image(String filename, String originalFilename, String contentType, byte[] bytes, Integer width, Integer height) {
        this.filename = filename;
        this.originalFilename = originalFilename;
        this.contentType = contentType;
        this.bytes = bytes;
        this.width = width;
        this.height = height;
    }

    public Image(String filename, String originalFilename, String contentType, byte[] bytes) {
        this.filename = filename;
        this.originalFilename = originalFilename;
        this.contentType = contentType;
        this.bytes = bytes;
    }

    public Image(String filename, String contentType, byte[] bytes) {
        this.filename = filename;
        this.contentType = contentType;
        this.bytes = bytes;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getOriginalFilename() {
        return originalFilename;
    }

    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public byte[] getBytes() {
        return bytes;
    }

    public void setBytes(byte[] bytes) {
        this.bytes = bytes;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public boolean isVertical() {
        if (width != null && height != null) {
            return height > width;
        } else {
            // se non ho le dimensioni la trattiamo come orizzontale
            return false;
        }
    }
}
