package com.agora.support.print;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.openhtmltopdf.swing.NaiveUserAgent;
import java.io.ByteArrayOutputStream;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class PdfGenerator {
    
    final NaiveUserAgent.DefaultUriResolver defaultUriResolver = new NaiveUserAgent.DefaultUriResolver();
    
    public static boolean isValidText(String text) {
        return StringUtils.isNotBlank(text);
    }
    
    public static byte[] convert(String url) throws Exception {
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        try {
            PdfRendererBuilder builder = new PdfRendererBuilder();
            builder.withUri(url);
            builder.toStream(out);
            builder.run();
        } finally {
            IOUtils.closeQuietly(out);
        }
        
        return out.toByteArray();
    }

}
