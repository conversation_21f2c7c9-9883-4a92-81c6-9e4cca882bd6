package com.agora.support;

import com.agora.core.Defaults;
import com.agora.dao.FileDao;
import com.agora.support.file.Filex;
import com.agora.util.EnvironmentUtils;
import com.agora.util.ParamUtils;
import java.util.Date;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;

/**
 *
 * <AUTHOR>
 */
public class FileController {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileController.class.getName());

    public static Route file = (Request request, Response response) -> {

        // params
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));

        if (oid != null) {

            Filex filex = null;
            try {
                filex = FileDao.loadFileV2(oid);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (filex != null) {
                // Determina il content type basato sull'estensione
                String contentType = filex.getContentType(); // Inizializza con l'estensione di default
                if (StringUtils.equalsIgnoreCase(contentType, "pdf")) {
                    contentType = "application/pdf"; // Cambia in "application/pdf" se l'estensione è pdf
                }
                response.type(contentType);

                if (EnvironmentUtils.hasNotResourcesHotDeploy()) {
                    // file caching
                    response.header("Cache-Control", "private, max-age=" + Defaults.FILE_RESOURCE_EXPIRATION_TIME);
                    response.header("Expires", new Date(System.currentTimeMillis() + (Defaults.FILE_RESOURCE_EXPIRATION_TIME * 1000)).toString());
                }

                String filename = filex.getOriginalFilename();
                filename = sanitizeFilename(filename);
                response.header("Content-Disposition", "inline; filename=\"" + filename + "\"");

                response.raw().getOutputStream().write(filex.getBytes());
                response.raw().getOutputStream().close();
            } else {
                LOGGER.warn("empty file oid " + oid);
            }
        } else {
            LOGGER.warn("unexistent file oid " + oid);
        }

        // MUST retrieve "", otherwise you'll get an "The requested route [...] has not been mapped in Spark for Accept: [*/*]"
        return "";
    };



    ////////////
    // internals

    private static String sanitizeFilename(String name) {
        return StringUtils.replace(StringUtils.replacePattern(name, "[\\\\/:*?\"<>|]", ""), " ", "_");
    }

}
