package com.agora.support;

import com.agora.pojo.Location;
import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.model.GeocodingResult;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class Geocoder {

    private static final Logger LOGGER = LoggerFactory.getLogger(Geocoder.class.getName());
    private static final double EARTH_RADIUS = 6371; // in km
        
    public static Location geocode(String address) {
        Location location = null;

        if (StringUtils.isNotBlank(address)) {

            GeoApiContext context = new GeoApiContext()
                    .setApiKey("AIzaSyBzbbAKQoJEad0bNS1qFMksTDfT60qJUnU")
                    .setConnectTimeout(1000, TimeUnit.MILLISECONDS)
                    .setReadTimeout(1000, TimeUnit.MILLISECONDS)
                    .setMaxRetries(3);

            try {
                GeocodingResult[] results = GeocodingApi.geocode(context, address)
                        .language("it-IT")
                        .await();

                if (results != null && results.length > 0) {
                    location = new Location();
                    location.setType("Point");
                    location.setCoordinates(new Double[]{
                            results[0].geometry.location.lat,
                            results[0].geometry.location.lng
                    });
                } else {
                    LOGGER.error("Cannot geocode address: " + address);
                }

            } catch (Exception ex) {
                LOGGER.error("Geocoding exception", ex);
            }
            
        }

        return location;
    }
    
    public static double distance(String lat1Str, String lon1Str, String lat2Str, String lon2Str) {
        // Converti le stringhe in double
        double lat1 = Double.parseDouble(lat1Str);
        double lon1 = Double.parseDouble(lon1Str);
        double lat2 = Double.parseDouble(lat2Str);
        double lon2 = Double.parseDouble(lon2Str);

        // Converti le latitudini e le longitudini da gradi a radianti
        double lat1Rad = Math.toRadians(lat1);
        double lon1Rad = Math.toRadians(lon1);
        double lat2Rad = Math.toRadians(lat2);
        double lon2Rad = Math.toRadians(lon2);

        // Calcola la differenza tra le latitudini e le longitudini
        double deltaLat = lat2Rad - lat1Rad;
        double deltaLon = lon2Rad - lon1Rad;

        // Calcola la distanza utilizzando la formula dell'emissione inversa
        double a = Math.pow(Math.sin(deltaLat / 2), 2) +
                   Math.cos(lat1Rad) * Math.cos(lat2Rad) *
                   Math.pow(Math.sin(deltaLon / 2), 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        double distance = EARTH_RADIUS * c;

        return distance;
    }
    
}
