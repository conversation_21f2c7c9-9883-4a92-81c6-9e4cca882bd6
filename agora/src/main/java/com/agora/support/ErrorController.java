package com.agora.support;

import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.CustomerDao;
import com.agora.pojo.Customer;
import com.agora.pojo.User;
import com.agora.util.RouteUtils;
import java.util.HashMap;
import java.util.Map;
import spark.ModelAndView;
import spark.Request;
import spark.Response;
import spark.Route;

/**
 *
 * <AUTHOR>
 */
public class ErrorController {

    public static Route notFound = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();  
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // logged customer
        Customer customer = null;
        if (user != null) {
            customer = CustomerDao.loadCustomerByUserId(user.getId());
        }               
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // public url
        attributes.put("publicUrl", RouteUtils.publicUrlWww(request));
        attributes.put("publicQueryString", RouteUtils.publicQueryString(request));      
        
        // manual rendering
        ModelAndView mv = Manager.render(Templates.PAGE_404, null, RouteUtils.pathType(request));
        String html = Manager.engine.render(mv);
        
        // return html as string
        return html;
    };
    
}
