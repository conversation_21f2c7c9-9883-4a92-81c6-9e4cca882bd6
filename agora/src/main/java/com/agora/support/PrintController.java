package com.agora.support;

import com.agora.dao.PrintDao;
import com.agora.support.print.Print;
import com.agora.util.ParamUtils;
import java.io.File;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;

/**
 *
 * <AUTHOR>
 */
public class PrintController {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(PrintController.class.getName());

    public static Route print = (Request request, Response response) -> {

        // params
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));

        if (oid != null) {

            Print prn = null;
            try {
                prn = PrintDao.loadPrint(oid);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            
            if (prn != null) {
                
                String filename = prn.getFilename();
                
                response.type(prn.getContentType());
                response.header("Content-Disposition", "inline; filename=\"" + filename + "\"");
                response.raw().getOutputStream().write(prn.getBytes());
                response.raw().getOutputStream().close();
            }
        } else {
            LOGGER.warn("unexistent print oid " + oid);
        }

        // MUST retrieve "", otherwise you'll get an "The requested route [...] has not been mapped in Spark for Accept: [*/*]"
        return "";
    };
    
    public static Route print_temp = (Request request, Response response) -> {

        // params
        String filename = request.queryParams("filename");
        
        if (StringUtils.isNotBlank(filename)) {
            
            // grab temp folder...
            File temp = File.createTempFile("temp", ".tmp");

            // 
            File file = new File(FilenameUtils.getFullPath(temp.getAbsolutePath()) + filename);
            if (file.exists() &&
                    file.canRead()) {
                
                // load pdf as byte array
                byte[] bytes = FileUtils.readFileToByteArray(file);

                // return pdf stream
                response.type("application/pdf");
                response.header("Content-Disposition", "inline; filename=\"" + filename + "\"");
                response.raw().getOutputStream().write(bytes);
                response.raw().getOutputStream().close();
                
            } else {
                LOGGER.warn("unexistent temporary print filename " + filename);
            }
            
        } else {
            LOGGER.warn("empty temporary print filename");
        }
        
        // MUST retrieve "", otherwise you'll get an "The requested route [...] has not been mapped in Spark for Accept: [*/*]"
        return "";
    };
    
    public static Route print_temp_txt = (Request request, Response response) -> {

        // params
        String filename = request.queryParams("filename");
        
        if (StringUtils.isNotBlank(filename)) {
            
            // grab temp folder...
            File temp = File.createTempFile("temp", ".tmp");

            // 
            File file = new File(FilenameUtils.getFullPath(temp.getAbsolutePath()) + filename);
            if (file.exists() &&
                    file.canRead()) {
                
                // load pdf as byte array
                byte[] bytes = FileUtils.readFileToByteArray(file);

                // return pdf stream
                response.type("application/txt");
                response.header("Content-Disposition", "inline; filename=\"" + filename + "\"");
                response.raw().getOutputStream().write(bytes);
                response.raw().getOutputStream().close();
                
            } else {
                LOGGER.warn("unexistent temporary print filename " + filename);
            }
            
        } else {
            LOGGER.warn("empty temporary print filename");
        }
        
        // MUST retrieve "", otherwise you'll get an "The requested route [...] has not been mapped in Spark for Accept: [*/*]"
        return "";
    };
    
    public static Route print_temp_csv = (Request request, Response response) -> {

        // params
        String filename = request.queryParams("filename");
        
        if (StringUtils.isNotBlank(filename)) {
            
            // grab temp folder...
            File temp = File.createTempFile("temp", ".tmp");

            // 
            File file = new File(FilenameUtils.getFullPath(temp.getAbsolutePath()) + filename);
            if (file.exists() &&
                    file.canRead()) {
                
                // load pdf as byte array
                byte[] bytes = FileUtils.readFileToByteArray(file);

                // return pdf stream
                response.type("application/csv");
                response.header("Content-Disposition", "inline; filename=\"" + filename + "\"");
                response.raw().getOutputStream().write(bytes);
                response.raw().getOutputStream().close();
                
            } else {
                LOGGER.warn("unexistent temporary print filename " + filename);
            }
            
        } else {
            LOGGER.warn("empty temporary print filename");
        }
        
        // MUST retrieve "", otherwise you'll get an "The requested route [...] has not been mapped in Spark for Accept: [*/*]"
        return "";
    };
    
    ////////////
    // internals
    
    private static String sanitizeFilename(String name) {
        return StringUtils.replace(StringUtils.replacePattern(name, "[\\\\/:*?\"<>|]", ""), " ", "_");
    }
    
}
