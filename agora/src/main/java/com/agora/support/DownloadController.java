package com.agora.support;

import com.agora.core.Defaults;
import com.agora.dao.FileDao;
import com.agora.support.file.Filex;
import com.agora.util.EnvironmentUtils;
import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileReader;
import java.util.Date;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tika.Tika;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;

/**
 *
 * <AUTHOR>
 */
public class DownloadController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DownloadController.class.getName());
    
    public static Route download = (Request request, Response response) -> {
        
        // params
        String path = request.queryParams("path");

        // path
        if (StringUtils.isBlank(path)) {
            LOGGER.warn("unexistent download path " + path);
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        File file = new File(path);
        if (!file.exists()) {
            LOGGER.warn("unexistent download path " + path);
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // size
        if (file.length() <= 0L) {
            LOGGER.warn("empty download " + path);
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        Tika tika = new Tika();
        String mimeyype = tika.detect(file);
        
        response.type(mimeyype);

        String filename = FilenameUtils.getName(path);
        filename = sanitizeFilename(filename);
        response.header("Content-Disposition", "inline; filename=\"" + filename + "\"");

        try (BufferedInputStream in = new BufferedInputStream(new FileInputStream(file))) {
            byte[] buffer = new byte[4096];
            int len;
            while ((len = in.read(buffer)) != -1) {
                response.raw().getOutputStream().write(buffer, 0, len);
            }
        }
        
        response.raw().getOutputStream().close();
        
        // MUST retrieve "", otherwise you'll get an "The requested route [...] has not been mapped in Spark for Accept: [*/*]"
        return "";
    };

    
    
    ////////////
    // internals
    
    private static String sanitizeFilename(String name) {
        return StringUtils.replace(StringUtils.replacePattern(name, "[\\\\/:*?\"<>|]", ""), " ", "_");
    }
    
}
