package com.agora.support.geocoding;

import com.agora.core.Manager;
import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.model.ComponentFilter;
import com.google.maps.model.GeocodingResult;
import com.agora.pojo.Location;
import com.agora.util.EnvironmentUtils;
import java.io.IOException;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class Geocoder {
    
    private static final String APIKEY               = "AIzaSyA2d_3rLM01CMj-u5lTJZLmR8SITSIHs9E";
    
    private static final String IPAPI_ENDPOINT       = "http://ip-api.com/json";
    private static final int    HTTP_TIMEOUT_MILLIES = 5000;
    
    private static final Logger LOGGER = LoggerFactory.getLogger(Geocoder.class.getName());

    public static Location geocode(String countryCode, String address) {
        Location location = null;
        
        if (StringUtils.isNotBlank(countryCode) &&
                StringUtils.isNotBlank(address)) {
            
            GeoApiContext context = new GeoApiContext()
                    .setApiKey(APIKEY)
                    .setConnectTimeout(1000, TimeUnit.MILLISECONDS)
                    .setReadTimeout(1000, TimeUnit.MILLISECONDS)
                    .setMaxRetries(3);

            try {

                GeocodingResult[] results =  GeocodingApi.geocode(context, address)
                        .components(ComponentFilter.country(countryCode.toLowerCase()))
                        .language("en-GB")
                        .await();

                if ((results != null) && (results.length > 0)) {
                    location = new Location();
                    location.setType("Point");
                    location.setCoordinates(new Double[] {results[0].geometry.location.lat, results[0].geometry.location.lng});
                } else {
                    LOGGER.error("cannot geocode address " + address);
                }

            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            
        }
        
        return location;
    }

    private static final int MAX_COUNTRIES_CACHE_SIZE = 1000;
    private static final int COUNTRIES_REMOVE_CACHE_SIZE = 500;
    private static final Map<String, String> _ips = new LinkedHashMap<>();
    private static boolean _countries_clearing = false;
    
    public static String countryLookupByIp(String ip) {
        if (StringUtils.isBlank(ip)) {
            return "";
        }
        
        if (!_ips.containsKey(ip)) {
            
            // find
            IpApiLocation location = lookupByIp(ip);
            String countryCode = (location != null) ? StringUtils.defaultIfBlank(location.getCountryCode(), "") : "";
            
            // localhost trick
            if (EnvironmentUtils.isLocal()) {
                countryCode = "IT";
            }
            
            // remove cache older entries
            if (_ips.size() >= MAX_COUNTRIES_CACHE_SIZE) {
                if (!_countries_clearing) {
                    _countries_clearing = true;
                    if (_ips.size() >= COUNTRIES_REMOVE_CACHE_SIZE) {
                        try {
                            _ips.keySet().removeAll(Arrays.asList(_ips.keySet().toArray()).subList(0, COUNTRIES_REMOVE_CACHE_SIZE));
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                    }
                    _countries_clearing = false;
                }
            }
            
            // put value
            _ips.put(ip, countryCode);
        }
        
        return _ips.get(ip);
    }
    
    public static IpApiLocation lookupByIp(String ip) {
        if (EnvironmentUtils.isLocal()) {
            return null;
        }
        IpApiLocation location = null;
        
        String url = IPAPI_ENDPOINT;
        
        url += "/" + ip;

        int timeout = HTTP_TIMEOUT_MILLIES;
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(timeout)
                .setConnectionRequestTimeout(timeout)
                .setSocketTimeout(timeout).build();
        CloseableHttpClient client =
                HttpClientBuilder.create().setDefaultRequestConfig(config).build();

        HttpGet getMethod = new HttpGet(url);
        HttpResponse response = null;
        try {
            response = client.execute(getMethod);
        } catch (IOException | RuntimeException ex) {
            LOGGER.error("unable to contact endpoint " + url, ex);
        }

        if (response != null) {
            if (response.getStatusLine() != null) {
                if ((response.getStatusLine().getStatusCode() >= 200) &&
                        (response.getStatusLine().getStatusCode() < 300)) {
                    if (response.getEntity() != null) {
                        String json = null;
                        try {
                            json = StringUtils.removeStart(StringUtils.removeEnd(EntityUtils.toString(response.getEntity()), "\""), "\"");
                        } catch (IOException ex) {
                            LOGGER.error("unable to read response body", ex);
                        }
                        if (StringUtils.isNotBlank(json)) {
                            location = Manager.deserializeFromJson(json, IpApiLocation.class);
                            if (location != null) {
                                if (!StringUtils.equalsIgnoreCase(location.getStatus(), "success")) {
                                    LOGGER.error("ipapi error " + json);
                                    location = null;
                                }
                            }
                        }
                    }
                }
            }
        }
        
        return location;
    }

    public static int size() {
        return (_ips != null) ? _ips.size() : 0;
    }
    
    public static void clear() {
        // ?????? @mike: qui serve un semaforo
        _ips.clear();
    }
    
}
