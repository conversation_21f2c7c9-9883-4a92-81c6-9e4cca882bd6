package com.agora.support;

import com.agora.commons.EntityCommons;
import com.agora.commons.EntityNotificationCommons;
import com.agora.commons.EntityNotificationCommons.EventNotificationType;
import com.agora.commons.EventCommons;
import com.agora.commons.PageCommons;
import com.agora.commons.StorageCommons;
import com.agora.core.Defaults;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.EventDao;
import com.agora.dao.FileDao;
import com.agora.dao.ImageDao;
import com.agora.dao.PageDao;
import com.agora.dao.XlsUploadDao;
import com.agora.pojo.Event;
import com.agora.pojo.Page;
import com.agora.pojo.User;
import com.agora.pojo.types.ImageType;
import com.agora.pojo.types.ProfileType;
import com.agora.support.xlsupload.XlsUpload;
import com.agora.util.ParamUtils;
import com.agora.util.RouteUtils;
import com.agora.util.TimeUtils;
import com.github.slugify.Slugify;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.text.DecimalFormat;
import java.util.*;
import javax.imageio.ImageIO;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class UploadController {

    private static final Logger LOGGER = LoggerFactory.getLogger(UploadController.class.getName());

    public static Route pages_upload_verify = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        ObjectId uploadId = null;

        String filename = null;
        String extension = null;
        byte[] bytes = null;

        Boolean onlyFoto = false;
        if (request.queryParams().contains("onlyFoto")) {
            onlyFoto = Boolean.parseBoolean(request.queryParams("onlyFoto"));
        }

        // posted values
        List<FileItem> fields = new ServletFileUpload(new DiskFileItemFactory()).parseRequest(request.raw());
        // multipart fields parsing
        for (FileItem field : fields) {
            if (field.isFormField()) {
                // posted fields
                LOGGER.warn("received unknown field " + field.getFieldName());
            } else {
                // posted files
                switch (field.getFieldName()) {
                    case "uploaded-file":
                        // skip empty ones
                        if (field.getSize() > 0L) {
                            filename = FilenameUtils.getName(field.getName());
                            extension = FilenameUtils.getExtension(field.getName());
                            try ( InputStream content = field.getInputStream()) {
                                bytes = IOUtils.toByteArray(content);
                                //
                            }
                        } else {
                            LOGGER.warn("received 0-sized file " + field.getName());
                        }
                        break;
                    default:
                        LOGGER.warn("received unknown file field " + field.getFieldName());
                        break;
                }
            }
        }

        if (bytes != null) {

            XlsUpload upload = null;
            try {
                upload = verifyUploadPage(bytes, onlyFoto);
            } catch (Exception ex) {
                LOGGER.error("unparsable xls/xlsx file " + filename, ex);
            }

            if (upload != null) {
                uploadId = XlsUploadDao.insertXlsUpload(XlsUploadDao.composeFilename(extension), upload.getCount(), upload.getErrors(), upload.getInfos(), bytes);
            }

        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // returns file key
        return (uploadId != null) ? uploadId.toString() : null;
    };

    public static Route pages_upload_save = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }

        ObjectId uploadId = ParamUtils.toObjectId(request.queryParams("uploadId"));
        Boolean onlyFoto = false;
        if (request.queryParams().contains("onlyFoto")) {
            onlyFoto = Boolean.parseBoolean(request.queryParams("onlyFoto"));
        }
        XlsUpload upload = XlsUploadDao.loadXlsUpload(uploadId);

        if ((upload != null)
                && (upload.getBytes() != null)) {

            boolean done = false;
            try {
                done = insertUploadPage(upload.getBytes(), user.getId(), onlyFoto);
            } catch (Exception ex) {
                LOGGER.error("unparsable xls/xlsx file " + upload.getFilename(), ex);
            }

            if (done) {
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.BE_PAGES));
            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.BE_PAGES));
            }

        } else {
            response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.BE_PAGES));
        }

        return null;
    };

    public static TemplateViewRoute pages_upload = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME);
            return Manager.renderEmpty();
        }

        // avatar
        ObjectId entityImageId = EntityCommons.imageIdFromUser(user);
        attributes.put("entityImageId", entityImageId);

        // localized versions of current path
        attributes.put("paths", request.servletPath());

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // xls upload
        ObjectId uploadId = ParamUtils.toObjectId(request.queryParams("uploadId"));
        if (uploadId != null) {
            XlsUpload upload = XlsUploadDao.loadXlsUpload(uploadId);
            if (upload != null) {
                attributes.put("uploadId", uploadId);
                attributes.put("uploadCount", (upload.getCount() != null) ? upload.getCount() : 0);
                attributes.put("uploadErrors", upload.getErrors());
                attributes.put("uploadInfos", upload.getInfos());
            }
        }

        return Manager.render(Templates.PAGES_UPLOAD, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute pages_upload_foto = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME);
            return Manager.renderEmpty();
        }

        // avatar
        ObjectId entityImageId = EntityCommons.imageIdFromUser(user);
        attributes.put("entityImageId", entityImageId);

        // localized versions of current path
        attributes.put("paths", request.servletPath());

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // xls upload
        ObjectId uploadId = ParamUtils.toObjectId(request.queryParams("uploadId"));
        if (uploadId != null) {
            XlsUpload upload = XlsUploadDao.loadXlsUpload(uploadId);
            if (upload != null) {
                attributes.put("uploadId", uploadId);
                attributes.put("uploadCount", (upload.getCount() != null) ? upload.getCount() : 0);
                attributes.put("uploadErrors", upload.getErrors());
                attributes.put("uploadInfos", upload.getInfos());
            }
        }

        return Manager.render(Templates.PAGES_UPLOAD_FOTO, attributes, RouteUtils.pathType(request));
    };

    public static Route events_upload_verify = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        ObjectId uploadId = null;

        String filename = null;
        String extension = null;
        byte[] bytes = null;

        // posted values
        List<FileItem> fields = new ServletFileUpload(new DiskFileItemFactory()).parseRequest(request.raw());
        // multipart fields parsing
        for (FileItem field : fields) {
            if (field.isFormField()) {
                // posted fields
                LOGGER.warn("received unknown field " + field.getFieldName());
            } else {
                // posted files
                switch (field.getFieldName()) {
                    case "uploaded-file":
                        // skip empty ones
                        if (field.getSize() > 0L) {
                            filename = FilenameUtils.getName(field.getName());
                            extension = FilenameUtils.getExtension(field.getName());
                            try ( InputStream content = field.getInputStream()) {
                                bytes = IOUtils.toByteArray(content);
                                //
                            }
                        } else {
                            LOGGER.warn("received 0-sized file " + field.getName());
                        }
                        break;
                    default:
                        LOGGER.warn("received unknown file field " + field.getFieldName());
                        break;
                }
            }
        }

        if (bytes != null) {

            XlsUpload upload = null;
            try {
                upload = verifyUploadEvent(bytes);
            } catch (Exception ex) {
                LOGGER.error("unparsable xls/xlsx file " + filename, ex);
            }

            if (upload != null) {
                uploadId = XlsUploadDao.insertXlsUpload(XlsUploadDao.composeFilename(extension), upload.getCount(), upload.getErrors(), upload.getInfos(), bytes);
            }

        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // returns file key
        return (uploadId != null) ? uploadId.toString() : null;
    };

    public static Route events_upload_save = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }

        ObjectId uploadId = ParamUtils.toObjectId(request.queryParams("uploadId"));
        XlsUpload upload = XlsUploadDao.loadXlsUpload(uploadId);

        if ((upload != null)
                && (upload.getBytes() != null)) {

            boolean done = false;
            try {
                done = insertUploadEvent(upload.getBytes(), user.getId());
            } catch (Exception ex) {
                LOGGER.error("unparsable xls/xlsx file " + upload.getFilename(), ex);
            }

            if (done) {
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.BE_EVENTS));
            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.BE_EVENTS));
            }

        } else {
            response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.BE_EVENTS));
        }

        return null;
    };

    public static TemplateViewRoute events_upload = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString())
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME);
            return Manager.renderEmpty();
        }

        // avatar
        ObjectId entityImageId = EntityCommons.imageIdFromUser(user);
        attributes.put("entityImageId", entityImageId);

        // localized versions of current path
        attributes.put("paths", request.servletPath());

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // xls upload
        ObjectId uploadId = ParamUtils.toObjectId(request.queryParams("uploadId"));
        if (uploadId != null) {
            XlsUpload upload = XlsUploadDao.loadXlsUpload(uploadId);
            if (upload != null) {
                attributes.put("uploadId", uploadId);
                attributes.put("uploadCount", (upload.getCount() != null) ? upload.getCount() : 0);
                attributes.put("uploadErrors", upload.getErrors());
                attributes.put("uploadInfos", upload.getInfos());
            }
        }

        return Manager.render(Templates.EVENTS_UPLOAD, attributes, RouteUtils.pathType(request));
    };

    ////////////
    // internals
    private static XlsUpload verifyUploadPage(byte[] bytes, Boolean onlyFoto) throws Exception {
        XlsUpload upload = new XlsUpload();

        // init
        upload.setCount(0);
        upload.setErrors(null);
        upload.setBytes(bytes);

        if (bytes != null) {

            try ( ByteArrayInputStream stream = new ByteArrayInputStream(bytes)) {
                try ( Workbook workbook = WorkbookFactory.create(stream)) {

                    Sheet sheet = workbook.getSheetAt(0);
                    if (sheet != null) {
                        // row parsing (skip header)
                        final int MAX_ROWS = 60000;
                        for (int i = (0 + 1); i < MAX_ROWS; i++) {

                            Row row = sheet.getRow(i);

                            if ((row != null) && StringUtils.isNotBlank(parseCell(row.getCell(1)))) {

                                Page page = parseRowPage(row, false, onlyFoto);

                                if (PageCommons.isValidPage(page)) {
                                    upload.setCount(upload.getCount() + 1);
                                } else {
                                    upload.setErrors("errore alla riga " + (i + 1));
                                }

                            } else {
                                // break on empty
                                break;
                            }

                        }
                    }

                }
            }

        }

        return upload;
    }

    private static boolean insertUploadPage(byte[] bytes, ObjectId ownerId, Boolean onlyFoto) throws Exception {
        boolean done = false;

        if ((bytes != null)
                && (ownerId != null)) {

            List<Page> pageList = new ArrayList<>();

            try ( ByteArrayInputStream stream = new ByteArrayInputStream(bytes)) {
                try ( Workbook workbook = WorkbookFactory.create(stream)) {

                    Sheet sheet = workbook.getSheetAt(0);
                    if (sheet != null) {
                        // row parsing (skip header)
                        final int MAX_ROWS = 60000;
                        for (int i = (0 + 1); i < MAX_ROWS; i++) {

                            Row row = sheet.getRow(i);

                            if ((row != null) && StringUtils.isNotBlank(parseCell(row.getCell(1)))) {

                                Page page = parseRowPage(row, true, onlyFoto);
                                if (page != null) {
                                    if (page.getId() != null) {
                                        PageDao.updatePage(page);
                                    } else {
                                        // setto owner solo in inserimento
                                        page.setUserId(ownerId);
                                        page.setOwnerId(ownerId);
                                        PageDao.insertPage(page);
                                    }
                                }
                            } else {
                                // break on empty
                                break;
                            }

                        }

                    }

                }
            }

            done = true;

        }

        return done;
    }

    private static Page parseRowPage(Row row, Boolean forSave, Boolean onlyFoto) throws Exception {
        Page page = null;

        // columns
        final int VALID_INDEXES = 0;        //A
        final int NOME = 1;                 //B
        final int DESCRIZIONE_BREVE = 2;    //C
        final int TIPO_PAGINA = 3;          //D
        final int URL_IMG_PROFILO = 4;      //E
        final int TWITTER_URL = 5;          //F
        final int FACEBOOK_URL = 6;         //G
        final int LINKEDIN_URL = 7;         //H
        final int INSTAGRAM_URL = 8;        //I
        final int SITE_URL = 9;             //J
        final int PINTEREST_URL = 10;       //K
        final int YOUTUBE_URL = 11;         //L
        final int REDDIT_URL = 12;          //M
        final int MEDIUM_URL = 13;          //N
        final int TIKTOK_URL = 14;          //O
        final int SPOTIFY_URL = 15;         //P
        final int BIO = 16;                 //Q
        final int IDENTIFIER = 17;          //R
        final int TAGS = 18;                //S

        if (row != null) {
            page = new Page();

            List<Integer> validIndexes = new ArrayList<>();
            if (parseCellAsString(row.getCell(VALID_INDEXES)) != null) {
                for (String index : StringUtils.split(parseCellAsString(row.getCell(VALID_INDEXES)), ",")) {
                    validIndexes.add(Integer.valueOf(index));
                }
            }

            String identifier = parseCell(row.getCell(IDENTIFIER));

            if (BooleanUtils.isTrue(onlyFoto) && StringUtils.isBlank(identifier)) {
                return null;
            }

            if (StringUtils.isBlank(identifier)) {
                Slugify slg = new Slugify();
                identifier = slg.slugify(parseCell(row.getCell(NOME)));
                if (StringUtils.isNotBlank(identifier)) {
                    Page exist = PageDao.loadPageByIdentifier(identifier);
                    if (exist != null) {
                        identifier = identifier + "-" + RouteUtils.generateIdentifier();
                    }
                }

                page.setIdentifier(slg.slugify(identifier));
            } else {
                Page exist = PageDao.loadPageByIdentifier(identifier);
                if (exist != null) {
                    page = exist;
                }
            }

            if (BooleanUtils.isFalse(onlyFoto)) {
                // se non voglio aggiornare solo la foto aggiorno tutto
                if (validIndexes.isEmpty() || validIndexes.contains(NOME)) {
                    page.setName(parseCell(row.getCell(NOME)));
                }
                if (validIndexes.isEmpty() || validIndexes.contains(BIO)) {
                    page.setDescription(parseCell(row.getCell(BIO)));
                }
                if (validIndexes.isEmpty() || validIndexes.contains(DESCRIZIONE_BREVE)) {
                    String shortDescription = parseCell(row.getCell(DESCRIZIONE_BREVE));
                    if (StringUtils.isNotBlank(shortDescription)) {
                        shortDescription = StringUtils.left(shortDescription, 50);
                    }
                    page.setShortDescription(shortDescription);
                }
                if (validIndexes.isEmpty() || validIndexes.contains(TIPO_PAGINA)) {
                    page.setPageType(parseCell(row.getCell(TIPO_PAGINA)));
                }
                if (validIndexes.isEmpty() || validIndexes.contains(LINKEDIN_URL)) {
                    page.setLinkedinUrl(parseCell(row.getCell(LINKEDIN_URL)));
                }
                if (validIndexes.isEmpty() || validIndexes.contains(FACEBOOK_URL)) {
                    page.setFacebookUrl(parseCell(row.getCell(FACEBOOK_URL)));
                }
                if (validIndexes.isEmpty() || validIndexes.contains(TWITTER_URL)) {
                    page.setTwitterUrl(parseCell(row.getCell(TWITTER_URL)));
                }
                if (validIndexes.isEmpty() || validIndexes.contains(SITE_URL)) {
                    page.setWebsiteUrl(parseCell(row.getCell(SITE_URL)));
                }
                if (validIndexes.isEmpty() || validIndexes.contains(INSTAGRAM_URL)) {
                    page.setInstagramUrl(parseCell(row.getCell(INSTAGRAM_URL)));
                }
                if (validIndexes.isEmpty() || validIndexes.contains(PINTEREST_URL)) {
                    page.setPinterestUrl(parseCell(row.getCell(PINTEREST_URL)));
                }
                if (validIndexes.isEmpty() || validIndexes.contains(YOUTUBE_URL)) {
                    page.setYoutubeUrl(parseCell(row.getCell(YOUTUBE_URL)));
                }
                if (validIndexes.isEmpty() || validIndexes.contains(REDDIT_URL)) {
                    page.setRedditUrl(parseCell(row.getCell(REDDIT_URL)));
                }
                if (validIndexes.isEmpty() || validIndexes.contains(MEDIUM_URL)) {
                    page.setMediumUrl(parseCell(row.getCell(MEDIUM_URL)));
                }
                if (validIndexes.isEmpty() || validIndexes.contains(TIKTOK_URL)) {
                    page.setTiktok(parseCell(row.getCell(TIKTOK_URL)));
                }
                if (validIndexes.isEmpty() || validIndexes.contains(SPOTIFY_URL)) {
                    page.setSpotifiyUrl(parseCell(row.getCell(SPOTIFY_URL)));
                }
                page.setIsUserPage(false);
                page.setStatus("published");
                page.setPageTagging("everyone");

                if (validIndexes.isEmpty() || validIndexes.contains(TAGS)) {
                    if (parseCellAsString(row.getCell(TAGS)) != null) {
                        page.setTags(Arrays.asList(StringUtils.split(StringUtils.lowerCase(parseCellAsString(row.getCell(TAGS))), ",")));
                    }
                }
            }

            if (forSave) {
                if (validIndexes.isEmpty() || validIndexes.contains(URL_IMG_PROFILO)) {
                    String imageUrl = parseCell(row.getCell(URL_IMG_PROFILO));

                    if (StringUtils.isNotBlank(imageUrl)) {
                        // Scarica l'immagine dall'URL
                        byte[] imageBytes = null;
                        int maxIterations = 3;
                        // riprovo un pò di volte nel caso in cui non trovo il contenuto di una immagine
                        while ((imageBytes == null || imageBytes.length == 0) && maxIterations > 0) {
                            maxIterations--;
                            try {
                                disableSslVerification();
                                Authenticator.setDefault(
                                        new Authenticator() {
                                            @Override
                                            public PasswordAuthentication getPasswordAuthentication() {
                                                return new PasswordAuthentication(Defaults.PROXY_USER, Defaults.PROXY_PASSWORD.toCharArray());
                                            }
                                        }
                                );

                                System.setProperty("http.proxyUser", Defaults.PROXY_USER);
                                System.setProperty("http.proxyPassword", Defaults.PROXY_PASSWORD);
                                System.setProperty("jdk.http.auth.tunneling.disabledSchemes", "");

                                Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(Defaults.PROXY_HOST, Defaults.PROXY_PORT));
                                URL url = new URL(imageUrl);
                                URLConnection connection = url.openConnection(proxy);

                                try (InputStream inputStream = connection.getInputStream()) {
                                    imageBytes = IOUtils.toByteArray(inputStream);
                                }
                            } catch (Exception ex) {
                                LOGGER.error("unable to import image, exception is ", ex);
                            }
                        }

                        if (imageBytes != null) {

                            // defaults
                            URL url = new URL(imageUrl);
                            String originalFilename = FilenameUtils.getBaseName(url.getPath());
                            if (StringUtils.isBlank(originalFilename)) {
                                originalFilename = page.getName() != null ? page.getName() : "immagine";
                            }

                            String extension = FilenameUtils.getExtension(url.getPath());
                            String imageName = ImageDao.composeFilenameV2(ImageType.page, FilenameUtils.getBaseName(originalFilename), extension);

                            String imageType = null;
                            switch (extension.toLowerCase()) {
                                case "jpg":
                                case "jpeg":
                                    imageType = "image/jpeg";
                                    break;
                                case "png":
                                    imageType = "image/png";
                                    break;
                                case "gif":
                                    imageType = "image/gif";
                                    break;
                                case "bmp":
                                    imageType = "image/bmp";
                                    break;
                                case "webp":
                                    imageType = "image/webp";
                                    break;
                                default:
                                    imageType = "image/jpeg";
                                    // Gestione per estensioni non riconosciute
                                    break;
                            }

                            // save image
                            Date now = new Date();
                            File savedFile = new File(StorageCommons.composePath(StorageCommons.StorageType.img, now, imageName));
                            savedFile.getParentFile().mkdirs();
                            try ( OutputStream out = new FileOutputStream(savedFile)) {
                                out.write(imageBytes);
                            }

                            // serve per trovare libreria per leggere file webp
                            ImageIO.scanForPlugins();
                            Integer width = null, height = null;
                            BufferedImage bimg = ImageIO.read(savedFile);

                            if (bimg != null) {
                                width = bimg.getWidth();
                                height = bimg.getHeight();
                            }

                            // save image
                            ObjectId imageId = ImageDao.insertImageV2(savedFile,
                                    originalFilename,
                                    imageType,
                                    width,
                                    height
                            );

                            // update imageId
                            page.setProfileImageId(imageId);
                        }
                    }
                }
            }
        }

        return page;
    }

    private static XlsUpload verifyUploadEvent(byte[] bytes) throws Exception {
        XlsUpload upload = new XlsUpload();

        // init
        upload.setCount(0);
        upload.setErrors(null);
        upload.setBytes(bytes);

        if (bytes != null) {

            try ( ByteArrayInputStream stream = new ByteArrayInputStream(bytes)) {
                try ( Workbook workbook = WorkbookFactory.create(stream)) {

                    Sheet sheet = workbook.getSheetAt(0);
                    if (sheet != null) {
                        // row parsing (skip header)
                        final int MAX_ROWS = 60000;
                        for (int i = (0 + 1); i < MAX_ROWS; i++) {

                            Row row = sheet.getRow(i);

                            if ((row != null) && StringUtils.isNotBlank(parseCell(row.getCell(1)))) {

                                Event event = parseRowEvent(row, false);

                                if (EventCommons.isValidEvent(event)) {
                                    upload.setCount(upload.getCount() + 1);
                                } else {
                                    upload.setErrors("errore alla riga " + (i + 1));
                                }

                            } else {
                                // break on empty
                                break;
                            }

                        }
                    }

                }
            }

        }

        return upload;
    }

    private static boolean insertUploadEvent(byte[] bytes, ObjectId ownerId) throws Exception {
        boolean done = false;

        if ((bytes != null)
                && (ownerId != null)) {

            List<Event> eventList = new ArrayList<>();

            try ( ByteArrayInputStream stream = new ByteArrayInputStream(bytes)) {
                try ( Workbook workbook = WorkbookFactory.create(stream)) {

                    Sheet sheet = workbook.getSheetAt(0);
                    if (sheet != null) {
                        // row parsing (skip header)
                        final int MAX_ROWS = 60000;
                        Map<ObjectId, Integer> notificationCounterMap = new HashMap<>();
                        for (int i = (0 + 1); i < MAX_ROWS; i++) {

                            Row row = sheet.getRow(i);

                            if ((row != null) && StringUtils.isNotBlank(parseCell(row.getCell(1)))) {

                                Event event = parseRowEvent(row, true);
                                if (event != null) {
                                    if (event.getId() != null) {
                                        Event eventOld = EventDao.loadEvent(event.getId());
                                        if (event.getStartDate() != null && !event.getStartDate().before(new Date())) {
                                            if (needNotifyChangeEvent(eventOld, event)) {
                                                EntityNotificationCommons.notifyMassiveEntityEvent(EventNotificationType.changed, event, notificationCounterMap);
                                            }
                                        }
                                        EventDao.updateEvent(event);
                                    } else {
                                        // setto owner solo in inserimento
                                        if (event.getOwnerId() == null) {
                                            event.setUserId(ownerId);
                                            event.setOwnerId(ownerId);
                                        }
                                        event.setId(EventDao.insertEvent(event));

                                        if (event.getStartDate() != null && !event.getStartDate().before(new Date())) {
                                            EntityNotificationCommons.notifyMassiveEntityEvent(EventNotificationType.published, event, notificationCounterMap);
                                        }
                                    }
                                }
                            } else {
                                // break on empty
                                break;
                            }

                        }

                    }

                }
            }

            done = true;

        }

        return done;
    }

    private static Event parseRowEvent(Row row, Boolean forSave) throws Exception {
        Event event = null;

        final int VALID_INDEXES = 0;        //A
        final int EVENT_URL = 1;            //B
        final int NAME = 2;                 //C
        final int DESCRIPTION = 3;          //D
        final int CATEGORY = 4;             //E
        final int STATUS = 5;               //F
        final int TICKET_MIN_PRICE = 6;     //G
        final int FREE_ENTRY = 7;           //H
        final int START_DATE = 8;           //I
        final int START_HOUR = 9;           //J
        final int END_HOUR = 10;            //K
        final int COVER_IMAGE_ID = 11;      //L
        final int FULL_ADDRESS = 12;        //M
        final int ADDRESS = 13;             //N
        final int CITY = 14;                //O
        final int POSTALCODE = 15;          //P
        final int PROVINCECODE = 16;        //Q
        final int COUNTRYCODE = 17;         //R
        final int LAT = 18;                 //S
        final int LNG = 19;                 //T
        final int PAGE_IDS = 20;            //U
        final int OWNER_ID = 21;            //V
        final int IDENTIFIER = 22;          //W
        final int TAGS = 23;                //X
        final int EXTRA_ADDRESS = 24;       //Y
        final int LOCANDINA = 25;           //Z
        final int TYPE = 26;                //AA
        final int PARENT_ID = 27;           //AB

        if (row != null) {
            event = new Event();

            List<Integer> validIndexes = new ArrayList<>();
            if (parseCellAsString(row.getCell(VALID_INDEXES)) != null) {
                for (String index : StringUtils.split(parseCellAsString(row.getCell(VALID_INDEXES)), ",")) {
                    validIndexes.add(Integer.valueOf(index));
                }
            }

            Slugify slg = new Slugify();
            String identifier = parseCell(row.getCell(IDENTIFIER));

            Event exist = null;
            if (StringUtils.isBlank(identifier)) {
                identifier = slg.slugify(parseCell(row.getCell(NAME)));
                if (StringUtils.isNotBlank(identifier)) {
                    exist = EventDao.loadEventByIdentifier(identifier);
                    if (exist != null) {
                        identifier = identifier + "-" + RouteUtils.generateIdentifier();
                    }
                }

                event.setIdentifier(slg.slugify(identifier));
            } else {
                exist = EventDao.loadEventByIdentifier(identifier);
                if (exist != null) {
                    event = exist;
                } else {
                    return null;
                }
            }

            if (validIndexes.isEmpty() || validIndexes.contains(NAME)) {
                event.setName(parseCell(row.getCell(NAME)));
            }
            if (validIndexes.isEmpty() || validIndexes.contains(DESCRIPTION)) {
                event.setDescription(parseCell(row.getCell(DESCRIPTION)));
            }
            if (validIndexes.isEmpty() || validIndexes.contains(CATEGORY)) {
                event.setCategory(parseCell(row.getCell(CATEGORY)));
            }
            if (validIndexes.isEmpty() || validIndexes.contains(STATUS)) {
                event.setStatus(parseCell(row.getCell(STATUS)));
            }
            if (validIndexes.isEmpty() || validIndexes.contains(EVENT_URL)) {
                event.setTicketsUrl(parseCell(row.getCell(EVENT_URL)));
            }
            if (validIndexes.isEmpty() || validIndexes.contains(TICKET_MIN_PRICE)) {
                event.setTicketsMinPrice(parseCellAsDouble(row.getCell(TICKET_MIN_PRICE)));
            }
            if (validIndexes.isEmpty() || validIndexes.contains(FREE_ENTRY)) {
                event.setFreeEntry(BooleanUtils.toBoolean(parseCell(row.getCell(FREE_ENTRY))));
            }

            String dateStr = StringUtils.substring(parseCell(row.getCell(START_DATE)), 0, 10);
            Date startDate = TimeUtils.toDate(dateStr, "yyyy-MM-dd");
            if (validIndexes.isEmpty() || validIndexes.contains(START_DATE)) {
                if (startDate != null) {
                    event.setStartDate(startDate);
                } else {
                    return null;
                }
            }
            if (validIndexes.isEmpty() || validIndexes.contains(START_DATE)) {
                event.setEndDate(startDate);
            }
            if (validIndexes.isEmpty() || validIndexes.contains(START_HOUR)) {
                event.setStartHour(parseCell(row.getCell(START_HOUR)));
            }
            if (validIndexes.isEmpty() || validIndexes.contains(END_HOUR)) {
                event.setEndHour(parseCell(row.getCell(END_HOUR)));
            }
            if (validIndexes.isEmpty() || validIndexes.contains(FULL_ADDRESS)) {
                event.setFulladdress(parseCell(row.getCell(FULL_ADDRESS)));
            }
            if (validIndexes.isEmpty() || validIndexes.contains(ADDRESS)) {
                event.setAddress(parseCell(row.getCell(ADDRESS)));
            }
            if (validIndexes.isEmpty() || validIndexes.contains(CITY)) {
                event.setCity(parseCell(row.getCell(CITY)));
            }
            if (validIndexes.isEmpty() || validIndexes.contains(POSTALCODE)) {
                event.setPostalCode(parseCell(row.getCell(POSTALCODE)));
            }
            if (validIndexes.isEmpty() || validIndexes.contains(PROVINCECODE)) {
                event.setProvinceCode(parseCell(row.getCell(PROVINCECODE)));
            }
            if (validIndexes.isEmpty() || validIndexes.contains(COUNTRYCODE)) {
                event.setCountryCode(parseCell(row.getCell(COUNTRYCODE)));
            }
            if (validIndexes.isEmpty() || validIndexes.contains(LAT)) {
                event.setLat(parseCell(row.getCell(LAT)));
            }
            if (validIndexes.isEmpty() || validIndexes.contains(LNG)) {
                event.setLng(parseCell(row.getCell(LNG)));
            }
            if (validIndexes.isEmpty() || validIndexes.contains(TAGS)) {
                if (parseCellAsString(row.getCell(TAGS)) != null) {
                    event.setTags(Arrays.asList(StringUtils.split(StringUtils.lowerCase(parseCellAsString(row.getCell(TAGS))), ",")));
                }
            }
            if (validIndexes.isEmpty() || validIndexes.contains(EXTRA_ADDRESS)) {
                event.setExtraAddress(parseCell(row.getCell(EXTRA_ADDRESS)));
            }

            if (forSave) {
                if (validIndexes.isEmpty() || validIndexes.contains(COVER_IMAGE_ID)) {
                    String imageUrl = parseCell(row.getCell(COVER_IMAGE_ID));

                    if (StringUtils.isNotBlank(imageUrl)) {
                        byte[] imageBytes = null;
                        int maxIterations = 3;
                        // riprovo un pò di volte nel caso in cui non trovo il contenuto di una immagine
                        while ((imageBytes == null || imageBytes.length == 0) && maxIterations > 0) {
                            maxIterations--;
                            try {
                                disableSslVerification();
                                Authenticator.setDefault(
                                        new Authenticator() {
                                            @Override
                                            public PasswordAuthentication getPasswordAuthentication() {
                                                return new PasswordAuthentication(Defaults.PROXY_USER, Defaults.PROXY_PASSWORD.toCharArray());
                                            }
                                        }
                                );

                                System.setProperty("http.proxyUser", Defaults.PROXY_USER);
                                System.setProperty("http.proxyPassword", Defaults.PROXY_PASSWORD);
                                System.setProperty("jdk.http.auth.tunneling.disabledSchemes", "");

                                Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(Defaults.PROXY_HOST, Defaults.PROXY_PORT));
                                URL url = new URL(imageUrl);
                                URLConnection connection = url.openConnection(proxy);

                                try (InputStream inputStream = connection.getInputStream()) {
                                    imageBytes = IOUtils.toByteArray(inputStream);
                                }
                            } catch (IOException ex) {
                                // Gestisci l'eccezione in base alla tua logica
                                LOGGER.error("unable to load image, exception is ", ex);
                            }
                        }

                        if (imageBytes != null) {

                            // defaults
                            URL url = new URL(imageUrl);
                            String originalFilename = FilenameUtils.getBaseName(url.getPath());
                            if (StringUtils.isBlank(originalFilename)) {
                                originalFilename = event.getName() != null ? event.getName() : "immagine";
                            }
                            String extension = FilenameUtils.getExtension(url.getPath());
                            String imageName = ImageDao.composeFilenameV2(ImageType.eventCover, FilenameUtils.getBaseName(originalFilename), extension);

                            String imageType = null;
                            switch (extension.toLowerCase()) {
                                case "jpg":
                                case "jpeg":
                                    imageType = "image/jpeg";
                                    break;
                                case "png":
                                    imageType = "image/png";
                                    break;
                                case "gif":
                                    imageType = "image/gif";
                                    break;
                                case "bmp":
                                    imageType = "image/bmp";
                                    break;
                                case "webp":
                                    imageType = "image/webp";
                                    break;
                                default:
                                    imageType = "image/jpeg";
                                    // Gestione per estensioni non riconosciute
                                    break;
                            }

                            // save image
                            Date now = new Date();
                            File savedFile = new File(StorageCommons.composePath(StorageCommons.StorageType.img, now, imageName));
                            savedFile.getParentFile().mkdirs();
                            try ( OutputStream out = new FileOutputStream(savedFile)) {
                                out.write(imageBytes);
                            }

                            // serve per trovare libreria per leggere file webp
                            ImageIO.scanForPlugins();
                            Integer width = null, height = null;
                            BufferedImage bimg = ImageIO.read(savedFile);

                            if (bimg != null) {
                                width = bimg.getWidth();
                                height = bimg.getHeight();
                            }

                            // save image
                            ObjectId imageId = ImageDao.insertImageV2(savedFile,
                                    originalFilename,
                                    imageType,
                                    width,
                                    height
                            );

                            // update imageId
                            event.setCoverImageId(imageId);
                        }
                    }
                }

                if (validIndexes.isEmpty() || validIndexes.contains(LOCANDINA)) {
                    String locandinaUrl = parseCell(row.getCell(LOCANDINA));
                    if (StringUtils.isNotBlank(locandinaUrl)) {
                        byte[] fileBytes = null;
                        int maxIterations = 3;
                        // riprovo un pò di volte nel caso in cui non trovo il contenuto di una immagine
                        while ((fileBytes == null || fileBytes.length == 0) && maxIterations > 0) {
                            maxIterations--;
                            try {
                                disableSslVerification();
                                try ( InputStream inputStream = new URL(locandinaUrl).openStream()) {
                                    fileBytes = IOUtils.toByteArray(inputStream);
                                }
                            } catch (IOException ex) {
                                // Gestisci l'eccezione in base alla tua logica
                                LOGGER.error("unable to load file, exception is ", ex);
                            }
                        }

                        if (fileBytes != null) {
                            Date now = new Date();
                            // defaults
                            URL url = new URL(locandinaUrl);
                            String originalFilename = FilenameUtils.getBaseName(url.getPath());
                            if (StringUtils.isBlank(originalFilename)) {
                                originalFilename = event.getName() != null ? event.getName() : "locandina";
                            }
                            String extension = FilenameUtils.getExtension(url.getPath());
                            if (StringUtils.isNotBlank(extension) && !originalFilename.contains(extension)) {
                                originalFilename += "." + extension;
                            }

                            File savedFile = new File(StorageCommons.composePath(StorageCommons.StorageType.doc, now, originalFilename));
                            savedFile.getParentFile().mkdirs();
                            try ( OutputStream out = new FileOutputStream(savedFile)) {
                                out.write(fileBytes);
                            }

                            // salvataggio mongo
                            ObjectId fileId = FileDao.insertFileV2(savedFile, originalFilename, extension);
                            if (fileId != null) {
                                event.setLocandina(fileId);
                            }
                        }
                    }
                }
            }

            if (validIndexes.isEmpty() || validIndexes.contains(PAGE_IDS)) {
                List<String> pageIdentifierList = Arrays.asList(StringUtils.split(parseCellAsString(row.getCell(PAGE_IDS)), ","));
                List<ObjectId> pageIds = new ArrayList<>();

                Page page;
                for (String pageIdentifier : pageIdentifierList) {
                    page = PageDao.loadPageByIdentifier(StringUtils.trim(pageIdentifier));
                    if (page != null) {
                        pageIds.add(page.getId());
                    }
                }
                event.setPageIds(pageIds);
            }

            if (validIndexes.isEmpty() || validIndexes.contains(OWNER_ID)) {
                Page page = PageDao.loadPageByIdentifier(parseCellAsString(row.getCell(OWNER_ID)));
                event.setOwnerId(page.getOwnerId());
                event.setUserId(page.getOwnerId());
            }

            // CREAZIONE CONTAINER
            if (validIndexes.isEmpty() || validIndexes.contains(TYPE)) {
                event.setType(StringUtils.lowerCase(parseCell(row.getCell(TYPE))));
            }

            // AGGIUNTA EVENTO A CONTAINER
            if (validIndexes.isEmpty() || validIndexes.contains(PARENT_ID)) {
                if (StringUtils.isNotBlank(parseCellAsString(row.getCell(PARENT_ID)))) {
                    ObjectId parentId = new ObjectId(parseCellAsString(row.getCell(PARENT_ID)));
                    if (parentId != null) {
                        Event parent = EventDao.loadEvent(parentId);
                        if (parent != null) {
                            if (StringUtils.equalsIgnoreCase(parent.getType(), "container")) {
                                event.setParentId(parentId);
                            } else {
                                return null;
                            }
                        } else {
                            return null;
                        }
                    } else {
                        return null;
                    }
                }
            }
        }

        return event;
    }

    private static String parseCell(Cell cell) {
        String value = null;

        if (cell != null) {

            CellType type = null;
            try {
                type = CellType.forInt(cell.getCellType());
            } catch (Exception ex) {
                LOGGER.error("unrecognizable cell type " + cell.getCellType());
            }

            if (type != null) {
                switch (type) {
                    case STRING:
                        value = cell.getStringCellValue();
                        break;
                    case NUMERIC:
                        value = "" + cell.getNumericCellValue();
                        value = StringUtils.remove(value, ".");
                        value = StringUtils.substringBeforeLast(value, "E");
                        break;
                    default:
                        LOGGER.warn("unparsable cell type " + type.toString());
                        break;
                }
            }
        }

        return value;
    }

    private static String parseCellAsString(Cell cell) {
        String value = null;

        final String pattern = "####################.###############";
        final DecimalFormat decimalFormat = new DecimalFormat(pattern);

        if (cell != null) {

            CellType type = null;
            try {
                type = CellType.forInt(cell.getCellType());
            } catch (Exception ex) {
                LOGGER.error("unrecognizable cell type " + cell.getCellType());
            }

            if (type != null) {
                switch (type) {
                    case BLANK:
                        value = "";
                        break;
                    case STRING:
                        try {
                        value = cell.getStringCellValue();
                    } catch (Exception ex) {
                        LOGGER.warn("unparsable value " + value, ex);
                    }
                    break;
                    case NUMERIC:
                        try {
                        value = decimalFormat.format(cell.getNumericCellValue());
                    } catch (Exception ex) {
                        LOGGER.warn("unparsable value " + value, ex);
                    }
                    value = StringUtils.remove(value, ".0");
                    break;
                    case FORMULA:
                        try {
                        value = cell.getStringCellValue();
                    } catch (Exception ex) {
                        try {
                            value = "" + cell.getNumericCellValue();
                        } catch (Exception exx) {
                            LOGGER.warn("unparsable value " + value, exx);
                        }
                        value = StringUtils.remove(value, ".0");
                    }
                    break;
                    default:
                        LOGGER.warn("unparsable cell type " + type.toString());
                        break;
                }
            }
        }

        return value;
    }

    private static ObjectId parseCellAsObjectId(Cell cell) {
        ObjectId value = null;
        String s = parseCellAsString(cell);
        if (s != null) {
            try {
                value = new ObjectId(s);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

        }
        return value;
    }

    private static Integer parseCellAsInteger(Cell cell) {
        Integer value = null;
        Double dbl = parseCellAsDouble(cell);
        if (dbl != null) {
            value = dbl.intValue();
        }
        return value;
    }

    private static Double parseCellAsDouble(Cell cell) {
        Double value = null;

        if (cell != null) {

            CellType type = null;
            try {
                type = CellType.forInt(cell.getCellType());
            } catch (Exception ex) {
                LOGGER.error("unrecognizable cell type " + cell.getCellType());
            }

            if (type != null) {
                switch (type) {
                    case BLANK:
                        value = null;
                        break;
                    case STRING:
                        String text = null;
                        try {
                            text = cell.getStringCellValue();
                        } catch (Exception ex) {
                            LOGGER.warn("unparsable value " + text, ex);
                        }
                        if (StringUtils.isNotBlank(text)) {
                            text = StringUtils.replace(text, ",", ".");
                            try {
                                value = Double.valueOf(text);
                            } catch (Exception ex) {
                                LOGGER.warn("unparsable value " + text, ex);
                            }
                        }
                        break;
                    case NUMERIC:
                        try {
                        value = cell.getNumericCellValue();
                    } catch (Exception ex) {
                        LOGGER.warn("unparsable value " + value, ex);
                    }
                    break;
                    case FORMULA:
                        try {
                        text = cell.getStringCellValue();
                        try {
                            value = Double.valueOf(text);
                        } catch (Exception ex) {
                            LOGGER.warn("unparsable value " + text, ex);
                        }
                    } catch (Exception ex) {
                        try {
                            value = cell.getNumericCellValue();
                        } catch (Exception exx) {
                            LOGGER.warn("unparsable value " + value, exx);
                        }
                    }
                    break;
                    default:
                        LOGGER.warn("unparsable cell type " + type.toString());
                        break;
                }
            }
        }

        return value;
    }

    public static boolean needNotifyChangeEvent(Event previous, Event updating) {
        return !(updating.getStartDate() == previous.getStartDate())
                || !StringUtils.equals(updating.getStartHour(), previous.getStartHour())
                || !StringUtils.equals(updating.getStatus(), previous.getStatus());
    }

    public static void disableSslVerification() throws Exception {
        TrustManager[] trustAllCerts = new TrustManager[]{
            new X509TrustManager() {
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }

                public void checkClientTrusted(X509Certificate[] certs, String authType) {
                }

                public void checkServerTrusted(X509Certificate[] certs, String authType) {
                }
            }
        };

        SSLContext sc = SSLContext.getInstance("TLS");
        sc.init(null, trustAllCerts, new java.security.SecureRandom());
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

        // Disabilita anche la verifica del nome dell'host
        HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
    }

}
