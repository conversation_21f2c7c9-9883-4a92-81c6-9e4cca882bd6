package com.agora.support.xlsupload;

/**
 *
 * <AUTHOR>
 */
public class XlsUpload {

    private String filename;
    private Integer count;
    private String errors;
    private String infos;
    private byte[] bytes;

    public XlsUpload() {
    }

    public XlsUpload(String filename, Integer count, String errors,  String infos, byte[] bytes) {
        this.filename = filename;
        this.count = count;
        this.errors = errors;
        this.infos = infos;
        this.bytes = bytes;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getErrors() {
        return errors;
    }

    public void setErrors(String errors) {
        this.errors = errors;
    }

    public String getInfos() {
        return infos;
    }

    public void setInfos(String infos) {
        this.infos = infos;
    }

    public byte[] getBytes() {
        return bytes;
    }

    public void setBytes(byte[] bytes) {
        this.bytes = bytes;
    }

}
