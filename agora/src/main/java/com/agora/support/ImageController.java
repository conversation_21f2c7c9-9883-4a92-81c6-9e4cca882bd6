package com.agora.support;

import com.agora.core.Defaults;
import com.agora.dao.CustomerDao;
import com.agora.dao.ImageDao;
import com.agora.dao.VendorDao;
import com.agora.pojo.Customer;
import com.agora.pojo.Vendor;
import com.agora.support.image.Image;
import com.agora.util.EnvironmentUtils;
import com.agora.util.ParamUtils;
import java.awt.AlphaComposite;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.security.InvalidParameterException;
import java.util.Date;
import javax.imageio.ImageIO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;

/**
 *
 * <AUTHOR>
 */
public class ImageController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImageController.class.getName());

    public static Route image = (Request request, Response response) -> {

        // params
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        
        // alternatively, load image by vendor id
        if (oid == null) {
            ObjectId vendorId = ParamUtils.toObjectId(request.queryParams("vendorId"));
            if (vendorId != null) {
                Vendor vendor = null;
                try {
                    vendor = VendorDao.loadVendor(vendorId);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                oid = (vendor != null) ? vendor.getImageId() : null;
            }
        }
        
        // alternatively, load image by customer id
        if (oid == null) {
            ObjectId customerId = ParamUtils.toObjectId(request.queryParams("customerId"));
            if (customerId != null) {
                Customer customer = null;
                try {
                    customer = CustomerDao.loadCustomer(customerId);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                oid = (customer != null) ? customer.getImageId() : null;
            }
        }

        if (oid != null) {

            Image img = null;
            try {
                img = ImageDao.loadImage(oid);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (img != null) {
                response.type(img.getContentType());
                
                if (EnvironmentUtils.hasNotResourcesHotDeploy()) {
                    // image caching
                    response.header("Cache-Control", "private, max-age=" + Defaults.IMAGE_RESOURCE_EXPIRATION_TIME);
                    response.header("Expires", new Date(System.currentTimeMillis() + (Defaults.IMAGE_RESOURCE_EXPIRATION_TIME * 1000)).toString());
                }
                
                try {
                    response.raw().getOutputStream().write(img.getBytes());
                    response.raw().getOutputStream().flush();
                    // avoid "java.io.IOException: An existing connection was forcibly closed by the remote host"
                    //response.raw().getOutputStream().close();
                } catch (IOException | RuntimeException ex) {
                    LOGGER.error("cannot return image; id " + oid + " exception class is " + ex.getClass().getSimpleName(), ex);
                }
                
            } else {
                LOGGER.warn("empty image oid " + oid);
            }
        } else {
            LOGGER.warn("empty oid " + oid);
        }

        // status needed to avoid spark filter exception
        return "";
    };
    
    public static Route imagenew = (Request request, Response response) -> {

        // params
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));

        // alternatively, load image by vendor id
        if (oid == null) {
            ObjectId vendorId = ParamUtils.toObjectId(request.queryParams("vendorId"));
            if (vendorId != null) {
                Vendor vendor = null;
                try {
                    vendor = VendorDao.loadVendor(vendorId);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                oid = (vendor != null) ? vendor.getImageId() : null;
            }
        }
        
        // alternatively, load image by customer id
        if (oid == null) {
            ObjectId customerId = ParamUtils.toObjectId(request.queryParams("customerId"));
            if (customerId != null) {
                Customer customer = null;
                try {
                    customer = CustomerDao.loadCustomer(customerId);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                oid = (customer != null) ? customer.getImageId() : null;
            }
        }

        if (oid != null) {

            Image img = null;
            try {
                img = ImageDao.loadImageV2(oid);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (img != null) {
                response.type(img.getContentType());
                
                if (EnvironmentUtils.hasNotResourcesHotDeploy()) {
                    // image caching
                    response.header("Cache-Control", "private, max-age=" + Defaults.IMAGE_RESOURCE_EXPIRATION_TIME);
                    response.header("Expires", new Date(System.currentTimeMillis() + (Defaults.IMAGE_RESOURCE_EXPIRATION_TIME * 1000)).toString());
                }
                
                try {
                    response.raw().getOutputStream().write(img.getBytes());
                    response.raw().getOutputStream().flush();
                    // avoid "java.io.IOException: An existing connection was forcibly closed by the remote host"
                    //response.raw().getOutputStream().close();
                } catch (IOException | RuntimeException ex) {
                    LOGGER.error("cannot return image; id " + oid + " exception class is " + ex.getClass().getSimpleName(), ex);
                }
                
            } else {
                LOGGER.warn("empty image oid " + oid);
            }
        } else {
            LOGGER.warn("empty oid " + oid);
        }

        // status needed to avoid spark filter exception
        return "";
    };

    public static Route thumbnail = (Request request, Response response) -> {

        // params
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        double scaleFactor = NumberUtils.toDouble(request.queryParams("scaleFactor"), 1D);
        int width = NumberUtils.toInt(request.queryParams("width"), 0);

        if (oid != null) {

            String key = oid.toString() + "-" + scaleFactor;
            if (width > 0) {
                key = oid.toString() + "-" + width + "px";
            }
            
            // try loading from cache...
            Image thumb = null;
            try {
                thumb = ImageDao.loadThumbnail(key);
            } catch (Exception ignored) {
                // ignore on missing key
            }
            
            if (thumb == null) {
                
                /////////////////////
                // creating thumbnail
                Image img = null;
                try {
                    img = ImageDao.loadImage(oid);
                    if (img == null) {
                        img = ImageDao.loadImageV2(oid);
                    }
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }

                if (img != null) {

                    byte[] bytes = img.getBytes();
                    if (scaleFactor != 1.0D) {
                        // thumbnail by scale factor
                        try {
                            bytes = thumbnail(scaleFactor, img.getContentType(), img.getBytes());
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                    } else if (width > 0) {
                        // thumbnail by width
                        try {
                            bytes = thumbnailByWidth(width, img.getContentType(), img.getBytes());
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                    } else {
                        // ...
                    }

                    // store thumbnail in cache
                    ImageDao.insertThumbnail(key, img.getContentType(), bytes);
                    
                    // prepare thumbnail
                    thumb = new Image(img.getFilename(), img.getContentType(), bytes);
                }
                
            }
            
            if ((thumb != null) &&
                    (thumb.getBytes() != null)) {
                
                // mime type
                response.type(thumb.getContentType());
                
                // image caching
                if (EnvironmentUtils.hasNotResourcesHotDeploy()) {
                    response.header("Cache-Control", "private, max-age=" + Defaults.IMAGE_RESOURCE_EXPIRATION_TIME);
                    response.header("Expires", new Date(System.currentTimeMillis() + (Defaults.IMAGE_RESOURCE_EXPIRATION_TIME * 1000)).toString());
                }
                
                // bytes
                byte[] bytes = thumb.getBytes();
                if (bytes != null) {
                    response.raw().getOutputStream().write(bytes);
                    response.raw().getOutputStream().close();
                }
                
            } else {
                LOGGER.warn("wrong image oid " + oid);
            }
            
        } else {
            LOGGER.warn("empty image oid");
        }

        // status needed to avoid spark filter exception
        return "";
    };


    
    ////////////
    // internals
    private static byte[] thumbnail(double scaleFactor, String contentType, byte[] bytes) throws Exception {
        if (scaleFactor <= 0.0D) {
            throw new InvalidParameterException("invalid scaleFactor");
        }
        if (scaleFactor > 1.0D) {
            throw new InvalidParameterException("invalid scaleFactor");
        }

        byte[] thumb = null;

        // source image
        BufferedImage originalImage = null;
        try {

            originalImage = ImageIO.read(new ByteArrayInputStream(bytes));

            // scaled width and height
            int widthToScale = (int) (originalImage.getWidth() * scaleFactor);
            int heightToScale = (int) (originalImage.getHeight() * scaleFactor);

            ByteArrayOutputStream out = null;
            if ((widthToScale > 0) && (heightToScale > 0)) {

                // destination image
                BufferedImage resizedImage = null;
                try {

                    resizedImage = new BufferedImage(widthToScale, heightToScale, originalImage.getType());
                    Graphics2D g = resizedImage.createGraphics();

                    // draw resized image
                    g.setComposite(AlphaComposite.Src);
                    g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                    g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                    g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    g.drawImage(originalImage, 0, 0, widthToScale, heightToScale, null);
                    g.dispose();

                    // identify image type
                    String type = "jpg";
                    if (StringUtils.containsIgnoreCase(contentType, "png")) {
                        type = "png";
                    }

                    // retrieving thumbnail bytes
                    out = new ByteArrayOutputStream();
                    ImageIO.write(resizedImage, type, out);

                    // result
                    if (out.size() > 0) {
                        thumb = out.toByteArray();
                    }
                    
                } finally {
                    // flush resources
                    if (resizedImage != null) {
                        try {
                            resizedImage.flush();
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                    }
                }

            }
        } finally {
            if (originalImage != null) {
                // flush resources
                try {
                    originalImage.flush();
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
            }
        }

        return thumb;
    }
    
    private static byte[] thumbnailByWidth(int width, String contentType, byte[] bytes) throws Exception {
        if (width <= 0) {
            throw new InvalidParameterException("invalid width");
        }

        byte[] thumb = null;

        // source image
        BufferedImage originalImage = null;
        try {

            originalImage = ImageIO.read(new ByteArrayInputStream(bytes));

            // fixed width, scaled height
            int widthToScale = width;
            
            double scaleFactor = 0D;
            if (originalImage.getWidth() > 0) {
                scaleFactor = (double) widthToScale / (double) originalImage.getWidth();
            }
            if (scaleFactor <= 0.0D) {
                throw new InvalidParameterException("invalid scaleFactor");
            }
            if (scaleFactor > 2.0D) {
                throw new InvalidParameterException("invalid scaleFactor");
            }
            int heightToScale = (int) (originalImage.getHeight() * scaleFactor);

            ByteArrayOutputStream out;
            if ((widthToScale > 0) && (heightToScale > 0)) {

                // destination image
                BufferedImage resizedImage = null;
                try {

                    resizedImage = new BufferedImage(widthToScale, heightToScale, originalImage.getType());
                    Graphics2D g = resizedImage.createGraphics();

                    // draw resized image
                    g.setComposite(AlphaComposite.Src);
                    g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                    g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                    g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    g.drawImage(originalImage, 0, 0, widthToScale, heightToScale, null);
                    g.dispose();

                    // identify image type
                    String type = "jpg";
                    if (StringUtils.containsIgnoreCase(contentType, "png")) {
                        type = "png";
                    }

                    // retrieving thumbnail bytes
                    out = new ByteArrayOutputStream();
                    ImageIO.write(resizedImage, type, out);

                    // result
                    if (out.size() > 0) {
                        thumb = out.toByteArray();
                    }
                    
                } finally {
                    // flush resources
                    if (resizedImage != null) {
                        try {
                            resizedImage.flush();
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                    }
                }

            }
        } finally {
            if (originalImage != null) {
                // flush resources
                try {
                    originalImage.flush();
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
            }
        }

        return thumb;
    }
    
}
