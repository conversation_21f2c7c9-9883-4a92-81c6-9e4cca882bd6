package com.agora.customers;

import com.agora.pojo.Customer;
import com.agora.pojo.Page;
import com.agora.pojo.User;
import com.agora.pojo.Vendor;

/**
 *
 * <AUTHOR>
 */
public class CustomerEntry {
    
    private User user;
    private Customer customer;
    private Vendor vendor;
    private Page page;
    private Long userEventCount;
    private Long pageEventCount;
    private Long pageFollower;
    private Long pageFollowed;
            
    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Customer getCustomer() {
        return customer;
    }

    public void setCustomer(Customer customer) {
        this.customer = customer;
    }

    public Vendor getVendor() {
        return vendor;
    }

    public void setVendor(Vendor vendor) {
        this.vendor = vendor;
    }

    public Long getPageEventCount() {
        return pageEventCount;
    }

    public void setPageEventCount(Long pageEventCount) {
        this.pageEventCount = pageEventCount;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public Long getUserEventCount() {
        return userEventCount;
    }

    public void setUserEventCount(Long userEventCount) {
        this.userEventCount = userEventCount;
    }

    public Long getPageFollower() {
        return pageFollower;
    }

    public void setPageFollower(Long pageFollower) {
        this.pageFollower = pageFollower;
    }

    public Long getPageFollowed() {
        return pageFollowed;
    }

    public void setPageFollowed(Long pageFollowed) {
        this.pageFollowed = pageFollowed;
    }

}
