package com.agora.customers;

import com.agora.commons.CustomerCommons;
import com.agora.support.CityEntry;
import com.agora.commons.SupportCommons;
import com.agora.commons.EntityCommons;
import com.agora.commons.EventFollowerCommons;
import com.agora.commons.NotificationCommons;
import com.agora.commons.NotificationCommons.AccountNotificationType;
import com.agora.commons.PageFollowerCommons;
import com.agora.commons.VendorCommons;
import com.agora.core.Defaults;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.CounterDao;
import com.agora.dao.CustomerDao;
import com.agora.dao.CustomerNotificationDao;
import com.agora.dao.EventDao;
import com.agora.dao.EventFollowerDao;
import com.agora.dao.PageDao;
import com.agora.dao.PageFollowerDao;
import com.agora.dao.VendorDao;
import com.agora.dao.UserDao;
import com.agora.dashboard.login.PasswordHash;
import com.agora.pojo.Customer;
import com.agora.pojo.CustomerAddress;
import com.agora.pojo.CustomerNotification;
import com.agora.pojo.Event;
import com.agora.pojo.EventFollower;
import com.agora.pojo.Page;
import com.agora.pojo.PageFollower;
import com.agora.pojo.User;
import com.agora.pojo.Vendor;
import com.agora.pojo.types.ProfileType;
import com.agora.support.datatable.Datatable;
import com.agora.support.image.slim.SlimImage;
import com.agora.util.FieldComparator;
import com.agora.util.ParamUtils;
import com.agora.util.PojoUtils;
import com.agora.util.RouteUtils;
import com.agora.util.TimeUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class CustomerController {
 
    private static final Logger LOGGER = LoggerFactory.getLogger(CustomerController.class.getName());

    public static TemplateViewRoute customer_view = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) && 
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        // avatar
        ObjectId entityImageId = EntityCommons.imageIdFromUser(user);
        attributes.put("entityImageId", entityImageId);        
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        // customer
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        if (oid == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.BE_LOGIN);
            return Manager.renderEmpty();
        }
        
        Customer customer = CustomerDao.loadCustomer(oid);
        attributes.put("customerEntry", CustomerCommons.toEntry(customer));
        
        User userCustomer = UserDao.loadUser(customer.getUserId());
        attributes.put("userCustomer", userCustomer);
      
        // sectors
        List<Page> pageList = null;
        if (userCustomer != null) {
            try {
                pageList = PageDao.loadPageListByOwnerId(userCustomer.getId());
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            attributes.put("pageList", pageList);
            // sectors
            List<PageFollower> pageFollowList = PageFollowerDao.loadPageFollowerListByUser(userCustomer.getId());
            attributes.put("pageFollowList", PageFollowerCommons.toEntries(pageFollowList));
        }
      
        // sectors
        List<Event> eventList = null;
        if (userCustomer != null) {
            try {
                eventList = EventDao.loadEventListByOwnerId(userCustomer.getId());
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            attributes.put("eventList", eventList);
            // sectors
            List<EventFollower> eventFollowList = EventFollowerDao.loadEventFollowerListByUser(userCustomer.getId());
            attributes.put("eventFollowList", EventFollowerCommons.toEntries(eventFollowList));
        }
        
        List<CustomerNotification> customerNotificationList = CustomerNotificationDao.loadCustomerNotificationListByUserId(userCustomer.getId());
        attributes.put("customerNotificationList", customerNotificationList);
        
        return Manager.render(Templates.CUSTOMER_VIEW, attributes, RouteUtils.pathType(request));
    };
        
    public static Route customer_address_save = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
		if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) &&
				!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        ObjectId addressId = ParamUtils.toObjectId(request.queryParams("addressId"));
        if (addressId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        int addressIndex = NumberUtils.toInt(request.queryParams("addressIndex"), -2);
        if (addressIndex < -1) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        Customer customer = CustomerDao.loadCustomer(addressId);
        if (customer == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        boolean inserting = addressIndex == -1;
        if (inserting) {
            customer.setAddresses(new CustomerAddress[1]);
            addressIndex = 0;
        }
        
        if (customer.getAddresses() == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        if (customer.getAddresses().length == 0) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        if (addressIndex >= customer.getAddresses().length) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // params
        Map<String, String> params = PojoUtils.paramsFromRequest(request);
        CustomerAddress address = PojoUtils.createFromParams(params, CustomerAddress.class);

        // validation
        if (!CustomerCommons.isValidCustomerAddress(address)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // update
        customer.getAddresses()[addressIndex] = address;
        CustomerDao.updateCustomer(customer);

        return "ok";
    };
    
    public static Route customer_address_remove = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // allow customer only users
		if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) &&
				!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        ObjectId addressId = ParamUtils.toObjectId(request.queryParams("addressId"));
        if (addressId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        int addressIndex = NumberUtils.toInt(request.queryParams("addressIndex"), -1);
        if (addressIndex < 0) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        Customer customer = CustomerDao.loadCustomer(addressId);
        if (customer == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        if (customer.getAddresses() == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        if (customer.getAddresses().length == 0) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        if (addressIndex >= customer.getAddresses().length) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // demoting additional address
        customer.setAddresses(ArrayUtils.remove(customer.getAddresses(), addressIndex));
        if (customer.getAddresses().length == 0) {
            customer.setAddresses(null);
        }

        CustomerDao.updateCustomer(customer);

        return "ok";
    };

    public static TemplateViewRoute customers = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) && 
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        // avatar
        ObjectId entityImageId = EntityCommons.imageIdFromUser(user);
        attributes.put("entityImageId", entityImageId);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        // date filter
        Date startDate = TimeUtils.toDate("01/01/2022");
        Date endDate = TimeUtils.today();
        
        if (StringUtils.isNotBlank(request.queryParams("startDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("startDate"));
            if (tmp != null) {
                startDate = tmp;
            }
        }
        if (StringUtils.isNotBlank(request.queryParams("endDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("endDate"));
            if (tmp != null) {
                endDate = tmp;
            }
        }
        
        attributes.put("startDate", startDate);
        attributes.put("endDate", endDate);
        
        ObjectId[] selectedVendors = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedVendors"))) {
            selectedVendors = ParamUtils.toObjectIds(request.queryParams("selectedVendors"));
        }
        attributes.put("selectedVendors", selectedVendors);        

        List<Vendor> vendorList = VendorDao.loadVendorList();
        attributes.put("vendorList", vendorList);
        
        String[] selectedCities = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedCities"))) {
            selectedCities = StringUtils.split(request.queryParams("selectedCities"), "|");
        }
        attributes.put("selectedCities", selectedCities);
        
        List<CityEntry> cityList;
        cityList = CustomerDao.loadCustomerCityList();
        attributes.put("cityList", SupportCommons.cleanCityList(cityList));
        
        return Manager.render(Templates.CUSTOMERS, attributes, RouteUtils.pathType(request));
    };
    
    public static Route customers_data = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
		if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) &&
				!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        // date filter
        Date startDate = TimeUtils.toDate("01/01/2022");
        Date endDate = TimeUtils.today();
        
        if (StringUtils.isNotBlank(request.queryParams("startDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("startDate"));
            if (tmp != null) {
                startDate = tmp;
            }
        }
        if (StringUtils.isNotBlank(request.queryParams("endDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("endDate"));
            if (tmp != null) {
                endDate = tmp;
            }
        }

        ObjectId[] selectedVendors = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedVendors"))) {
            selectedVendors = ParamUtils.toObjectIds(request.queryParams("selectedVendors"));
        }
        
        String[] selectedCities = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedCities"))) {
            selectedCities = StringUtils.split(request.queryParams("selectedCities"), "|");
        }
        
        List<Customer> custs;
        Map<ObjectId, Vendor> vendors = new HashMap<>();
        custs = CustomerDao.loadCustomerListByDateRange(startDate, endDate);

        List<Vendor> vnds = VendorDao.loadVendorList();
        if ((vnds != null) && (!vnds.isEmpty())) {
            for (Vendor vnd : vnds) {
                vendors.put(vnd.getId(), vnd);
            }
        }
        
        List<CustomerEntry> customerList = null;
        if ((custs != null) &&
                (custs.size() > 0)) {

            for (Customer customer : custs) {
                
                boolean add = true;
                
                // filtering
//                if (selectedStatuses != null) {
//                    // bypass filter when all checked
//                    if (selectedStatuses.length < 2) {
//                        Map<ObjectId, String> actives = OrderDao.loadCustomerStatuses(vendorId != null ? vendorId : null);
//                        String status = CustomerStatusType.inactive.toString();
//                        if (actives != null) {
//                            status = actives.containsKey(customer.getId()) ? CustomerStatusType.active.toString() : CustomerStatusType.inactive.toString();
//                        }
//                        if (!Arrays.stream(selectedStatuses).anyMatch(status::equalsIgnoreCase)) {
//                            add = false;
//                        }
//                    }
//                }
                if (selectedVendors != null) {
//                    if (customer.getVendorId() != null) {
//                        if (!Arrays.stream(selectedVendors).anyMatch(customer.getVendorId()::equals)) {
//                            add = false;
//                        }
//                    } else {
//                        add = false;
//                    }
                }
                if (selectedCities != null) {
                    if (customer.getCity() != null) {
                        if (!Arrays.stream(selectedCities).anyMatch(customer.getCity()::equals)) {
                            add = false;
                        }
                    } else {
                        add = false;
                    }
                }                
                if (add) {
                    if (customerList == null) {
                        customerList = new ArrayList<>();
                    }

                    CustomerEntry entry = new CustomerEntry();
                    entry.setCustomer(customer);
                    if (customer.getVendorId() != null) {
                        entry.setVendor(vendors.get(customer.getVendorId()));
                    }
                    
                    if (customer.getUserId() != null) {
                        User userCustomer = UserDao.loadUser(customer.getUserId());
                        entry.setUser(userCustomer);
                    }
                    
                    customerList.add(entry);
                }

            }
        }        

        // result
        Datatable table = new Datatable();
        table.setData(customerList != null ? customerList.toArray() : (new Customer[0]));
        
        return table;
    };
    
    public static TemplateViewRoute customers_add = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) && 
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        // avatar
        ObjectId entityImageId = EntityCommons.imageIdFromUser(user);
        attributes.put("entityImageId", entityImageId);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        List<Vendor> vendorList;
        // vendors
        if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            Vendor vendor;
            vendor = VendorDao.loadVendorByUserId(user.getId());
            vendorList = new ArrayList<>();
            if (vendor != null) {
                vendorList.add(vendor);
            }    
        } else {
            vendorList = VendorDao.loadVendorList();
        }

        attributes.put("vendorList", vendorList);
        // assure order by provinceCode
        FieldComparator comparator = new FieldComparator("provinceCode", false);
        Collections.sort(vendorList, comparator);
        
        return Manager.render(Templates.CUSTOMERS_ADD, attributes, RouteUtils.pathType(request));
    };
    
    public static Route customers_add_save = (Request request, Response response) -> {
        // params
        Customer customer = PojoUtils.createFromRequest(request, Customer.class);
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) && 
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        if (CustomerCommons.isValidCustomer(customer, true)) {
            
            String birthDay = request.queryParams("birthDay");
            String birthMonth = request.queryParams("birthMonth");
            String birthYear = request.queryParams("birthYear");

            Date birthDate = null;
            if (StringUtils.isNotBlank(birthDay) &&
                    StringUtils.isNotBlank(birthMonth) &&
                    StringUtils.isNotBlank(birthYear)) {
                birthDate = new Date();
                birthDate = DateUtils.setYears(birthDate, NumberUtils.toInt(birthYear));
                birthDate = DateUtils.setMonths(birthDate, NumberUtils.toInt(birthMonth, 1) - 1);
                birthDate = DateUtils.setDays(birthDate, NumberUtils.toInt(birthDay));
                birthDate = DateUtils.setHours(birthDate, 0);
                birthDate = DateUtils.setMinutes(birthDate, 0);
                birthDate = DateUtils.setSeconds(birthDate, 0);
                birthDate = DateUtils.setMilliseconds(birthDate, 0);
            }
            customer.setBirthDate(birthDate);
            
            String sinceDay = request.queryParams("sinceDay");
            String sinceMonth = request.queryParams("sinceMonth");
            String sinceYear = request.queryParams("sinceYear");

            Date sinceDate = new Date();
            if (StringUtils.isNotBlank(sinceDay) &&
                    StringUtils.isNotBlank(sinceMonth) &&
                    StringUtils.isNotBlank(sinceYear)) {
                sinceDate = DateUtils.setYears(sinceDate, NumberUtils.toInt(sinceYear));
                sinceDate = DateUtils.setMonths(sinceDate, NumberUtils.toInt(sinceMonth, 1) - 1);
                sinceDate = DateUtils.setDays(sinceDate, NumberUtils.toInt(sinceDay));
                sinceDate = DateUtils.setHours(sinceDate, 0);
                sinceDate = DateUtils.setMinutes(sinceDate, 0);
                sinceDate = DateUtils.setSeconds(sinceDate, 0);
                sinceDate = DateUtils.setMilliseconds(sinceDate, 0);
            }
            customer.setSinceDate(sinceDate);
            customer.setCity(StringUtils.capitalize(StringUtils.lowerCase(StringUtils.trim(customer.getCity()))));
            
            if (StringUtils.isBlank(customer.getFullname())) {
                String fullname = StringUtils.trim(StringUtils.defaultIfBlank(customer.getName(), "") + " " + StringUtils.defaultIfBlank(customer.getLastname(), ""));
                customer.setFullname(StringUtils.trim(fullname));
            }
            
            int next = CounterDao.next("customer-protocol");
            customer.setCode("" + next);
            
            if (customer!= null && customer.getVendorId() == null) {
                customer.setVendorId(VendorCommons.defaultVendor().getId());
            }
            
            ObjectId customerId = CustomerDao.insertCustomer(customer);

            // image
            String slim = request.queryParams("uploaded-files");
            if (StringUtils.isNotBlank(slim)) {
                SlimImage uploaded = null;
                try {
                    uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if ((uploaded != null)) {
                    CustomerDao.updateCustomerImage(user.getUsername(), customerId, uploaded);
                }
            }
            
            // logo
            String slimlogo = request.queryParams("logo-uploaded-files");
            if (StringUtils.isNotBlank(slimlogo)) {
                SlimImage uploaded = null;
                try {
                    uploaded = Manager.deserializeFromJson(slimlogo, SlimImage.class);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if ((uploaded != null)) {
                    CustomerDao.updateCustomerLogoImage(user.getUsername(), customerId, uploaded);
                }
            }
            
            // user
            User userCustomer = new User();
            userCustomer.setEmail(customer.getEmail());
            userCustomer.setName(customer.getName());
            userCustomer.setUsername(customer.getEmail());
            userCustomer.setPassword(UUID.randomUUID().toString());                     // setting random pwd to avoid unintentionally login
            userCustomer.setProfileType(ProfileType.unconfirmed.toString());
            userCustomer.setRegistered(false);
            userCustomer.setSmsVerified(false);
            ObjectId userCustomerId = null;
            try {
                userCustomerId = UserDao.insertUser(userCustomer);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            if ((userCustomerId != null)) {
                customer = CustomerDao.loadCustomer(customerId);
                customer.setUserId(userCustomerId);
                CustomerDao.updateCustomer(customer);
            }
            
            // navigation            
            response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.CUSTOMER_VIEW) + "?oid=" + customerId);            

        } else {
            response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.CUSTOMERS_ADD));
        }

        return null;
    };  
    
    public static Route customers_edit_save = (Request request, Response response) -> {
        
        // params
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        Boolean isNote = BooleanUtils.toBoolean(request.queryParams("isNote"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) && 
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        if (oid != null) {
            Customer customerOld = CustomerDao.loadCustomer(oid);
            Customer customer = PojoUtils.mergeFromRequest(request, customerOld);
            if (CustomerCommons.isValidCustomer(customer, false)) {

                String birthDay = request.queryParams("birthDay");
                String birthMonth = request.queryParams("birthMonth");
                String birthYear = request.queryParams("birthYear");

                Date birthDate = null;
                if (StringUtils.isNotBlank(birthDay) &&
                        StringUtils.isNotBlank(birthMonth) &&
                        StringUtils.isNotBlank(birthYear)) {
                    birthDate = new Date();
                    birthDate = DateUtils.setYears(birthDate, NumberUtils.toInt(birthYear));
                    birthDate = DateUtils.setMonths(birthDate, NumberUtils.toInt(birthMonth, 1) - 1);
                    birthDate = DateUtils.setDays(birthDate, NumberUtils.toInt(birthDay));
                    birthDate = DateUtils.setHours(birthDate, 0);
                    birthDate = DateUtils.setMinutes(birthDate, 0);
                    birthDate = DateUtils.setSeconds(birthDate, 0);
                    birthDate = DateUtils.setMilliseconds(birthDate, 0);
                }
                customer.setBirthDate(birthDate);
                
                String sinceDay = request.queryParams("sinceDay");
                String sinceMonth = request.queryParams("sinceMonth");
                String sinceYear = request.queryParams("sinceYear");

                Date sinceDate = new Date();
                if (StringUtils.isNotBlank(sinceDay) &&
                        StringUtils.isNotBlank(sinceMonth) &&
                        StringUtils.isNotBlank(sinceYear)) {
                    sinceDate = DateUtils.setYears(sinceDate, NumberUtils.toInt(sinceYear));
                    sinceDate = DateUtils.setMonths(sinceDate, NumberUtils.toInt(sinceMonth, 1) - 1);
                    sinceDate = DateUtils.setDays(sinceDate, NumberUtils.toInt(sinceDay));
                    sinceDate = DateUtils.setHours(sinceDate, 0);
                    sinceDate = DateUtils.setMinutes(sinceDate, 0);
                    sinceDate = DateUtils.setSeconds(sinceDate, 0);
                    sinceDate = DateUtils.setMilliseconds(sinceDate, 0);
                }
                customer.setSinceDate(sinceDate);
                customer.setCity(StringUtils.capitalize(StringUtils.lowerCase(StringUtils.trim(customer.getCity()))));
                
                if (StringUtils.isBlank(customer.getFullname())) {
                    String fullname = StringUtils.trim(StringUtils.defaultIfBlank(customer.getLastname(), "") + " " + StringUtils.defaultIfBlank(customer.getName(), ""));
                    customer.setFullname(StringUtils.trim(fullname));
                }
                
                if (customer.getVendorId() == null) {
                    customer.setVendorId(VendorCommons.defaultVendor().getId());
                }

                CustomerDao.updateCustomer(customer);

                if (!isNote) {
                    
                    // image
                    String slim = request.queryParams("uploaded-files");
                    if (StringUtils.isNotBlank(slim)) {
                        SlimImage uploaded = null;
                        try {
                            uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                        if ((uploaded != null)) {
                            CustomerDao.updateCustomerImage(user.getUsername(), oid, uploaded);
                        }
                    } else {
                        CustomerDao.removeCustomerImage(oid);
                    }
                    
                    // logo
                    String slimlogo = request.queryParams("logo-uploaded-files");
                    if (StringUtils.isNotBlank(slimlogo)) {
                        SlimImage uploaded = null;
                        try {
                            uploaded = Manager.deserializeFromJson(slimlogo, SlimImage.class);
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                        if ((uploaded != null)) {
                            CustomerDao.updateCustomerLogoImage(user.getUsername(), oid, uploaded);
                        }
                    } else {
                        CustomerDao.removeCustomerLogoImage(oid);
                    }
                    
                    // user
                    if (customer.getUserId() == null) {
                        User userCustomer = new User();
                        userCustomer.setEmail(customer.getEmail());
                        userCustomer.setName(customer.getName());
                        userCustomer.setUsername(customer.getEmail());
                        userCustomer.setPassword(UUID.randomUUID().toString());                     // setting random pwd to avoid unintentionally login
                        userCustomer.setProfileType(ProfileType.unconfirmed.toString());
                        userCustomer.setRegistered(false);
                        userCustomer.setSmsVerified(false);
                        ObjectId userCustomerId = null;
                        try {
                            userCustomerId = UserDao.insertUser(userCustomer);
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                        if ((userCustomerId != null)) {
                            customer = CustomerDao.loadCustomer(customer.getId());
                            customer.setUserId(userCustomerId);
                            CustomerDao.updateCustomer(customer);
                        }
                    }
                }
                
                
                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.CUSTOMER_VIEW) + "?oid=" + customer.getId());

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.CUSTOMER_VIEW) + "?oid=" + customer.getId());
            }
        }

        return null;
    };    
    
    public static Route customer_remove = (Request request, Response response) -> {
        
        ObjectId customerId = ParamUtils.toObjectId(request.queryParams("customerId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
		if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) &&
				!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.vendor.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        if (customerId != null) {
            // params
            Customer customer = CustomerDao.loadCustomer(customerId);
            if (customer != null) {
                CustomerDao.updateCustomerCancelled(customerId, true);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };      

    
    ////////////
    // internals

}
