package com.agora.util;

import java.net.IDN;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class MailUtils {

//    private static IDNEmailAddressConverter _IDNEmailAddressConverter = new IDNEmailAddressConverter();
    
    public static String toUnicode(String email) {
        if (StringUtils.isBlank(email)) {
            return email;
        }
        String domain = StringUtils.substringAfterLast(email, "@");
        if (StringUtils.isBlank(domain)) {
            return email;
        }
        if (!StringUtils.startsWithIgnoreCase(domain, "xn--")) {
            return email;
        }
        return StringUtils.substringBeforeLast(email, "@") + "@" + IDN.toUnicode(domain);
    }

}
