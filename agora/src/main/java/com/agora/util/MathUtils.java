package com.agora.util;

/**
 *
 * <AUTHOR>
 */
public class MathUtils {
    
    public static boolean lessThan(Double value1, Double value2) {
        value1 = value1 != null ? value1 : 0D;
        value2 = value2 != null ? value2 : 0D;
        return value1 < value2;
    }
    
    public static boolean greaterThan(Double value1, Double value2) {
        value1 = value1 != null ? value1 : 0D;
        value2 = value2 != null ? value2 : 0D;
        return value1 > value2;
    }
    
}
