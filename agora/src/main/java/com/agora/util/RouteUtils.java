package com.agora.util;

import com.agora.core.Defaults;
import com.agora.core.PathType;
import com.agora.support.geocoding.Geocoder;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

/**
 *
 * <AUTHOR>
 */
public class RouteUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(RouteUtils.class.getName());

    // static mime mappings
    private static final Map<String, String> MIME_MAPPINGS;
    static {
        MIME_MAPPINGS = new HashMap<>();
        MIME_MAPPINGS.put("css", "text/css");
        MIME_MAPPINGS.put("js" , "text/javascript");
        MIME_MAPPINGS.put("ico" , "image/x-icon");
        MIME_MAPPINGS.put("gif" , "image/gif");
        MIME_MAPPINGS.put("jpg" , "image/jpeg");
        MIME_MAPPINGS.put("png" , "image/png");
        MIME_MAPPINGS.put("svg" , "image/svg+xml");
    }
    
    public static String getIp(Request request) {
        String ip = null;
        if (request != null) {
            ip = StringUtils.defaultIfBlank(request.headers("X-Forwarded-For"), StringUtils.defaultString(request.ip()));
            if (StringUtils.contains(ip, ",")) {
                ip = StringUtils.substringBefore(ip, ",");
            }
        }
        return ip;
    }

    public static String hostUrl(Request request) {
        String hostUrl = null;
        if (request != null) {
            String url = request.headers("origin");
            if (StringUtils.isBlank(url)) {
                String scheme = request.headers("x-forwarded-proto");
                if (StringUtils.isBlank(scheme)) {
                    scheme = request.scheme();
                }
                url = scheme + "://" +
                        request.host();
            }
            hostUrl = url;
        }
        return hostUrl;
    }
    
    public static String baseUrl(Request request) {
        String base = null;
        if (request != null) {
            String url = request.headers("origin");
            if (StringUtils.isBlank(url)) {
                String scheme = request.headers("x-forwarded-proto");
                if (StringUtils.isBlank(scheme)) {
                    scheme = request.scheme();
                }
                url = scheme + "://" +
                        request.host();
            }
            base = url + contextPath(request);
        }
        return base;
    }
    
    public static String publicUrl(Request request) {
        String result = null;
        if (request != null) {
            String url = request.headers("origin");
            if (StringUtils.isBlank(url)) {
                String scheme = request.headers("x-forwarded-proto");
                if (StringUtils.isBlank(scheme)) {
                    scheme = request.scheme();
                }
                url = scheme + "://" + request.host();
            }
            result = url + contextPath(request);
            result += request.pathInfo();
        }
        return result;
    }
    
    public static String publicUrlWww(Request request) {
        String url = publicUrl(request);
        if (StringUtils.isNotBlank(url)) {
            if (!StringUtils.containsIgnoreCase(url, "www.")) {
                if (StringUtils.countMatches(request.host(), ".") == 1) {
                    url = StringUtils.replaceIgnoreCase(url, "http://", "http://www.");
                    url = StringUtils.replaceIgnoreCase(url, "https://", "https://www.");
                }
            }
        }
        return url;
    }
    
    // 2021-11-15, useless because www is the reference site
    //public static String publicUrlNoWww(Request request) {
    //    return StringUtils.remove(publicUrl(request), "www.");
    //}
    
    public static String publicQueryString(Request request) {
        return (request != null) ? request.queryString() : null;
    }
    
    public static String backUrl(Request request) throws Exception {
        return URLEncoder.encode(request.raw().getRequestURL().toString() + "?" + request.raw().getQueryString(), "UTF-8");
    }

    public static String contextPath(Request request) {
        return (EnvironmentUtils.isLocal() || EnvironmentUtils.hasNotDomainRedirect(request)) ? request.contextPath() : "";
    }

    public static PathType pathType(Request request) {
        return needContextPath(request) ? PathType.servlet : PathType.bare;
    }

    public static boolean needContextPath(Request request) {
        return EnvironmentUtils.isLocal() || EnvironmentUtils.hasNotDomainRedirect(request);
    }
    
    public static boolean isResource(ServletRequest request) {
        return StringUtils.isNotBlank(mimetype(request));
    }
    
    public static boolean isResource(Request request) {
        return StringUtils.isNotBlank(mimetype(request));
    }
    
    public static boolean isResource(String path) {
        return StringUtils.isNotBlank(mimetype(path));
    }
    
    public static boolean isNotResource(ServletRequest request) {
        return !isResource(request);
    }

    public static boolean isNotResource(Request request) {
        return !isResource(request);
    }

    public static boolean isNotResource(String path) {
        return !isResource(path);
    }

    public static String mimetype(ServletRequest request) {
        String mimetype = null;
        if (request != null) {
            mimetype = mimetype(((HttpServletRequest) request).getRequestURI());
        }
        return mimetype;
    }
    
    public static String mimetype(Request request) {
        String mimetype = null;
        if (request != null) {
            mimetype = mimetype(request.servletPath());
        }
        return mimetype;
    }
    
    public static String mimetype(String path) {
        String mimetype = null;
        
        String ext = StringUtils.substringAfterLast(path, ".");
        if (StringUtils.isNotBlank(ext)) {
            mimetype = MIME_MAPPINGS.get(ext);
        }
        return mimetype;
    }
    
    public static String language(Request request) {
        String language = StringUtils.substringBetween((request != null ? request.servletPath() : ""), "/", "/");
        language = StringUtils.defaultIfBlank(language, Defaults.LANGUAGE);
        return language;
    }
    
    public static String language(String path) {
        return StringUtils.substringBetween(path, "/", "/");
    }
    
    public static String language(String path, String language) {
        return StringUtils.replace(path, ":language", StringUtils.defaultIfBlank(language, Defaults.LANGUAGE));
    }

    public static String unlanguage(String path, String language) {
        return StringUtils.replace(path, ("/" + language + "/"), ("/" + ":language" + "/"));
    }
    
    public static String generateIdentifier() {
        final int digits = 8;

        long rnd = UUID.randomUUID().getLeastSignificantBits();
        rnd = Math.abs(rnd);

        String identifier = rnd + "";
        identifier = StringUtils.left(identifier, digits);
        identifier = StringUtils.leftPad(identifier, digits, '0');

        return identifier;
    }

    public static boolean blockByCountry(Request request) {
        if (request == null) {
            return false;
        }
        
        // ip
        String ip = getIp(request);
        if (StringUtils.isBlank(ip)) {
            return false;
        }
        
        // country
        String countryCode = Geocoder.countryLookupByIp(ip);
        if (StringUtils.isBlank(countryCode)) {
            return false;
        }
        
        boolean blocked = 
                StringUtils.equalsIgnoreCase(countryCode, "CN")
                ;
        
        return blocked;
    }
    
}
