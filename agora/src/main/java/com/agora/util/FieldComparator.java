package com.agora.util;

import java.lang.reflect.InvocationTargetException;
import java.text.DecimalFormat;
import java.util.Comparator;
import java.util.Date;
import org.apache.commons.beanutils.PropertyUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class FieldComparator implements Comparator {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass().getName());
    
    private final String fieldname;
    private final Boolean descending;

    public FieldComparator(String fieldname, boolean descending) {
        this.fieldname = fieldname;
        this.descending = descending;
    }
    
    @Override
    public int compare(Object o1, Object o2) {
        String s1 = valueOf(o1);
        String s2 = valueOf(o2);
        return descending ? -s1.compareTo(s2) : s1.compareTo(s2);
    }

    private String valueOf(Object o) {
        Object value = null;
        try {
            value = PropertyUtils.getNestedProperty(o, fieldname);
        } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException | RuntimeException ex) {
            LOGGER.error("suppressed", ex);
        }
        if (value != null) {
            if (value instanceof Date) {
                value = ((Date) value).getTime();
            }
            if (value instanceof Double) {
                double number = (Double) value;
                DecimalFormat df = new DecimalFormat("000,000,000,000.00");
                value = df.format(number);
            }
        }
        return value != null ? value.toString() : "";
    }
    
    public String getFieldname() {
        return fieldname;
    }

    public Boolean getDescending() {
        return descending;
    }
    
}
