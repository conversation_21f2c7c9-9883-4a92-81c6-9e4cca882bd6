package com.agora.util;

import java.math.BigDecimal;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

/**
 *
 * <AUTHOR>
 */
public class MoneyUtils {

    public static double toDouble(String value) {
        value = StringUtils.remove(value, " ");
        if (StringUtils.contains(StringUtils.right(value, 3), ".")) {
            value = StringUtils.remove(value, ",");
        } else if (StringUtils.contains(StringUtils.right(value, 3), ",")) {
            value = StringUtils.remove(value, ".");
            value = StringUtils.replace(value, ",",  ".");
        } else {
        }
        return NumberUtils.toDouble(value);
    }
    
    public static double round(Double value) {
        return round(value, 2);
    }

    public static double round(Double value, int decimals) {
        double rounded = 0D;
        if (value != null) {
            BigDecimal tmp = BigDecimal.valueOf(value);
            tmp = tmp.setScale(decimals, BigDecimal.ROUND_HALF_EVEN);
            rounded = tmp.doubleValue();
        }
        return rounded;
    }
    
}
