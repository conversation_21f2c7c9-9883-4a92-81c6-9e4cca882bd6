package com.agora.util;

public class TextUtils {

    /**
     * Converts a string to camelCase format.
     * For example: "hello world" becomes "helloWorld"
     *
     * @param input the string to convert
     * @return the camelCase formatted string or null if input is null
     */
    public static String toCamelCase(String input) {
        if (input == null) {
            return null;
        }
        if (input.isEmpty()) {
            return "";
        }

        String[] words = input.split("\\s+");
        if (words.length == 0) {
            return ""; // Handle case where input might be just spaces
        }

        // Usa StringBuilder per efficienza [[3]](https://stackoverflow.com/questions/74213789/i-need-help-converting-strings-into-camelcase-with-multiple-using-loop)
        StringBuilder result = new StringBuilder();

        // Capitalizza la prima parola
        String firstWord = words[0];
        if (!firstWord.isEmpty()) {
            result.append(Character.toUpperCase(firstWord.charAt(0)));
            if (firstWord.length() > 1) {
                result.append(firstWord.substring(1)); // Aggiungi il resto della prima parola
            }
        }

        // Elabora le parole successive
        for (int i = 1; i < words.length; i++) {
            result.append(" "); // Mantieni lo spazio
            String word = words[i];
            if (!word.isEmpty()) {
                // Capitalizza la prima lettera e aggiungi il resto della parola
                result.append(Character.toUpperCase(word.charAt(0)));
                if (word.length() > 1) {
                    result.append(word.substring(1)); // Aggiungi il resto della parola
                    // Se vuoi forzare il resto della parola in minuscolo:
                    // result.append(word.substring(1).toLowerCase());
                }
            }
        }

        return result.toString();

    }
}
