package com.agora.homeslider;

import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.HomeSliderDao;
import com.agora.pojo.HomeSlider;
import com.agora.pojo.User;
import com.agora.pojo.types.ProfileType;
import com.agora.support.image.slim.SlimImage;
import com.agora.util.ParamUtils;
import com.agora.util.PojoUtils;
import com.agora.util.RouteUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class HomeSliderController {
    private static final Logger LOGGER = LoggerFactory.getLogger(HomeSliderController.class.getName());

    public static TemplateViewRoute homeSliders = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME) ;
            return Manager.renderEmpty();
        }
        
        attributes.put("user", user);
        
        List<HomeSlider> homeSliderList = HomeSliderDao.loadHomeSliderList();
        attributes.put("homeSliderList", homeSliderList);        
        
        return Manager.render(Templates.HOME_SLIDERS, attributes, RouteUtils.pathType(request));
    };
    
    public static TemplateViewRoute homeSlider_edit = (Request request, Response response) -> {
        
        ObjectId homeSliderId = ParamUtils.toObjectId(request.queryParams("homeSliderId"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME) ;
            return Manager.renderEmpty();
        }
        
        HomeSlider homeSlider;
        if (homeSliderId != null) {
            homeSlider = HomeSliderDao.loadHomeSlider(homeSliderId);
        } else {
            homeSlider = new HomeSlider();
        }
        attributes.put("homeSlider", homeSlider);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.HOME_SLIDER_EDIT, attributes, RouteUtils.pathType(request));
    };
    
    public static Route homeSlider_edit_save = (Request request, Response response) -> {
        // params
        ObjectId homeSliderId = ParamUtils.toObjectId(request.queryParams("homeSliderId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME) ;
            return Manager.renderEmpty();
        }
        
        if (homeSliderId != null) {
            HomeSlider homeSliderOld = HomeSliderDao.loadHomeSlider(homeSliderId);
            HomeSlider homeSlider = PojoUtils.mergeFromRequest(request, homeSliderOld);
            
            if (isValidHomeSlider(homeSlider)) {
                
                HomeSliderDao.updateHomeSlider(homeSlider);

                // image
                String slim = request.queryParams("uploaded-files");
                if (StringUtils.isNotBlank(slim)) {
                    SlimImage uploaded = null;
                    try {
                        uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    if (uploaded != null) {
                        HomeSliderDao.updateHomeSliderImage(user.getUsername(), homeSliderId, uploaded);
                    } else {
                        HomeSliderDao.removeHomeSliderImage(homeSliderId);
                    }
                }
                    
                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.HOME_SLIDERS));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.HOME_SLIDER_EDIT) + "?homeSliderId=" + homeSlider.getId());
            }
        } else {
            // params
            HomeSlider homeSlider = PojoUtils.createFromRequest(request, HomeSlider.class);
            
            if (isValidHomeSlider(homeSlider)) {
                
                homeSliderId = HomeSliderDao.insertHomeSlider(homeSlider);

                // image
                String slim = request.queryParams("uploaded-files");
                if (StringUtils.isNotBlank(slim)) {
                    SlimImage uploaded = null;
                    try {
                        uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    if (uploaded != null) {
                        HomeSliderDao.updateHomeSliderImage(user.getUsername(), homeSliderId, uploaded);
                    }
                }
                
                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.HOME_SLIDERS));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.HOME_SLIDER_EDIT));
            }
        }        

        return null;
    };  
    
    public static Route homeSlider_remove = (Request request, Response response) -> {
        
        ObjectId homeSliderId = ParamUtils.toObjectId(request.queryParams("homeSliderId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            Spark.halt(HttpStatus.UNAUTHORIZED_401);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            Spark.halt(HttpStatus.UNAUTHORIZED_401);
            return Manager.renderEmpty();
        }
        
        if (homeSliderId != null) {
            // params
            HomeSlider homeSlider = HomeSliderDao.loadHomeSlider(homeSliderId);
            if (homeSlider != null) {
                HomeSliderDao.updateHomeSliderCancelled(homeSliderId, true);
            } else {
                Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    }; 
    
    private static boolean isValidHomeSlider(HomeSlider entity) {
        boolean valid = (entity != null) &&
                StringUtils.isNotBlank(entity.getTitle())
                ;
        
        // ?????? @leonardo: inserire gli attuali vincoli
        if (!valid) {
            if (entity != null) {
                LOGGER.warn(
                        "homeSlider validation problem:\n" +
                        "- title " + entity.getTitle()+ "\n"
                );
            } else {
                LOGGER.warn(
                        "homeSlider validation problem:\n" +
                        "- empty"
                );
            }
        }
        
        return valid;
    }
}