package com.agora.pojo;

import java.util.List;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Page extends Pojo {

    private String name;
    private String description;
    private String shortDescription;
    private String pageType;
    private Boolean isUserPage;
    private String category;
    private String websiteUrl;
    private String twitterUrl;
    private String facebookUrl;
    private String instagramUrl;
    private String pinterestUrl;
    private String linkedinUrl;
    private String youtubeUrl;
    private String redditUrl;
    private String mediumUrl;
    private String dribbleUrl;
    private String spotifiyUrl;
    private String goodreads;
    private String tiktok;
    private String substack;

    private String status;

    // images
    private ObjectId profileImageId;
    private ObjectId coverImageId;

    private Boolean editorChoice;

    private String fulladdress;
    private String address;
    private String extraAddress;
    private String city;
    private String postalCode;
    private String provinceCode;
    private String countryCode;
    private String lat;
    private String lng;


    private ObjectId userId;                // inserito da
    private ObjectId ownerId;               // proprietario pagina

    private String pageTagging;             // all, everyone
    private List<String> tags;              // elenco di tag

    private String identifier;
    private String titleIdentifier;         // usato per il cerca (dato che l'identifier può cambiare)
    private String qrcode;
    private ObjectId qrcodeFileId;
    private Boolean cancelled;
    private Long followers;                 // campo fake per mostrare un numero random di follower
    private Boolean isFake;                 // per
    private Boolean showFollowers;          // campo per attivare la visualizzazione dei follower
    private Boolean claimed;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getShortDescription() {
        return shortDescription;
    }

    public void setShortDescription(String shortDescription) {
        this.shortDescription = shortDescription;
    }

    public String getPageType() {
        return pageType;
    }

    public void setPageType(String pageType) {
        this.pageType = pageType;
    }

    public Boolean getIsUserPage() {
        return isUserPage;
    }

    public void setIsUserPage(Boolean isUserPage) {
        this.isUserPage = isUserPage;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getWebsiteUrl() {
        return websiteUrl;
    }

    public void setWebsiteUrl(String websiteUrl) {
        this.websiteUrl = websiteUrl;
    }

    public String getTwitterUrl() {
        return twitterUrl;
    }

    public void setTwitterUrl(String twitterUrl) {
        this.twitterUrl = twitterUrl;
    }

    public String getFacebookUrl() {
        return facebookUrl;
    }

    public void setFacebookUrl(String facebookUrl) {
        this.facebookUrl = facebookUrl;
    }

    public String getInstagramUrl() {
        return instagramUrl;
    }

    public void setInstagramUrl(String instagramUrl) {
        this.instagramUrl = instagramUrl;
    }

    public String getPinterestUrl() {
        return pinterestUrl;
    }

    public void setPinterestUrl(String pinterestUrl) {
        this.pinterestUrl = pinterestUrl;
    }

    public String getLinkedinUrl() {
        return linkedinUrl;
    }

    public void setLinkedinUrl(String linkedinUrl) {
        this.linkedinUrl = linkedinUrl;
    }

    public String getYoutubeUrl() {
        return youtubeUrl;
    }

    public void setYoutubeUrl(String youtubeUrl) {
        this.youtubeUrl = youtubeUrl;
    }

    public String getRedditUrl() {
        return redditUrl;
    }

    public void setRedditUrl(String redditUrl) {
        this.redditUrl = redditUrl;
    }

    public String getMediumUrl() {
        return mediumUrl;
    }

    public void setMediumUrl(String mediumUrl) {
        this.mediumUrl = mediumUrl;
    }

    public String getDribbleUrl() {
        return dribbleUrl;
    }

    public void setDribbleUrl(String dribbleUrl) {
        this.dribbleUrl = dribbleUrl;
    }

    public String getSpotifiyUrl() {
        return spotifiyUrl;
    }

    public void setSpotifiyUrl(String spotifiyUrl) {
        this.spotifiyUrl = spotifiyUrl;
    }

    public String getGoodreads() {
        return goodreads;
    }

    public void setGoodreads(String goodreads) {
        this.goodreads = goodreads;
    }

    public String getTiktok() {
        return tiktok;
    }

    public void setTiktok(String tiktok) {
        this.tiktok = tiktok;
    }

    public String getSubstack() {
        return substack;
    }

    public void setSubstack(String substack) {
        this.substack = substack;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public ObjectId getProfileImageId() {
        return profileImageId;
    }

    public void setProfileImageId(ObjectId profileImageId) {
        this.profileImageId = profileImageId;
    }

    public ObjectId getCoverImageId() {
        return coverImageId;
    }

    public void setCoverImageId(ObjectId coverImageId) {
        this.coverImageId = coverImageId;
    }

    public Boolean getEditorChoice() {
        return editorChoice;
    }

    public void setEditorChoice(Boolean editorChoice) {
        this.editorChoice = editorChoice;
    }

    public String getFulladdress() {
        return fulladdress;
    }

    public void setFulladdress(String fulladdress) {
        this.fulladdress = fulladdress;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getExtraAddress() {
        return extraAddress;
    }

    public void setExtraAddress(String extraAddress) {
        this.extraAddress = extraAddress;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

    public ObjectId getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(ObjectId ownerId) {
        this.ownerId = ownerId;
    }

    public String getPageTagging() {
        return pageTagging;
    }

    public void setPageTagging(String pageTagging) {
        this.pageTagging = pageTagging;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public String getTitleIdentifier() {
        return titleIdentifier;
    }

    public void setTitleIdentifier(String titleIdentifier) {
        this.titleIdentifier = titleIdentifier;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public String getQrcode() {
        return qrcode;
    }

    public void setQrcode(String qrcode) {
        this.qrcode = qrcode;
    }

    public ObjectId getQrcodeFileId() {
        return qrcodeFileId;
    }

    public void setQrcodeFileId(ObjectId qrcodeFileId) {
        this.qrcodeFileId = qrcodeFileId;
    }

    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }

    public Long getFollowers() {
        return followers;
    }

    public void setFollowers(Long followers) {
        this.followers = followers;
    }

    public Boolean getIsFake() {
        return isFake;
    }

    public void setIsFake(Boolean isFake) {
        this.isFake = isFake;
    }

    public Boolean getShowFollowers() {
        return showFollowers;
    }

    public void setShowFollowers(Boolean showFollowers) {
        this.showFollowers = showFollowers;
    }

    public Boolean getClaimed() {
        return claimed;
    }

    public void setClaimed(Boolean claimed) {
        this.claimed = claimed;
    }
}
