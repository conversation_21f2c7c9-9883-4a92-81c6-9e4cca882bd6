package com.agora.pojo;

import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class PageNotification extends Pojo {

    // management
    private ObjectId userId;
    private ObjectId pageId;
    private Boolean cancelled;

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

    public ObjectId getPageId() {
        return pageId;
    }

    public void setPageId(ObjectId pageId) {
        this.pageId = pageId;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(<PERSON>ole<PERSON> cancelled) {
        this.cancelled = cancelled;
    }
    
}
