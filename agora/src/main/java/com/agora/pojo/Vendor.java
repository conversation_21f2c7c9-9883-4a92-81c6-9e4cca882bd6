package com.agora.pojo;

import java.util.Date;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Vendor extends Pojo {

    // basic info
    private String lastname;
    private String name;
    private String fullname;
    private String genderType;      // male, female, other (i.e. companies)
    private Date birthDate;
    private String birthCity;
    private String tin;
    private String vatNumber;
    private String sdiNumber;
    private String code;            // i.e. accounting code

    // address 1
    private String city;
    private String address;
    private String provinceCode;
    private String postalCode;
    private String countryCode;

    // tax
    private String tax;                                     // tax, when different from product default
    
    // invoice address
    private String invoiceFullname;
    private String invoiceCity;
    private String invoiceAddress;
    private String invoiceProvinceCode;
    private String invoicePostalCode;
    private String invoiceCountryCode;
    
    private String bankAccount;

    // contact
    private String email;
    private String emailAdditional;
    private String pec;
    private String phoneNumber;
    private String phoneNumberAdditional;

    // statement
    private Boolean statement;
    
    // vendor since
    private Date sinceDate;
    
    // note
    private String note;
    
    // reference
    private ObjectId userId;

    
    // image
    private ObjectId imageId;

    private Boolean active;
    private Boolean cancelled;

    public String getLastname() {
        return lastname;
    }

    public void setLastname(String lastname) {
        this.lastname = lastname;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getGenderType() {
        return genderType;
    }

    public void setGenderType(String genderType) {
        this.genderType = genderType;
    }

    public Date getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(Date birthDate) {
        this.birthDate = birthDate;
    }

    public String getBirthCity() {
        return birthCity;
    }

    public void setBirthCity(String birthCity) {
        this.birthCity = birthCity;
    }

    public String getTin() {
        return tin;
    }

    public void setTin(String tin) {
        this.tin = tin;
    }

    public String getVatNumber() {
        return vatNumber;
    }

    public void setVatNumber(String vatNumber) {
        this.vatNumber = vatNumber;
    }

    public String getSdiNumber() {
        return sdiNumber;
    }

    public void setSdiNumber(String sdiNumber) {
        this.sdiNumber = sdiNumber;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getTax() {
        return tax;
    }

    public void setTax(String tax) {
        this.tax = tax;
    }

    public String getInvoiceFullname() {
        return invoiceFullname;
    }

    public void setInvoiceFullname(String invoiceFullname) {
        this.invoiceFullname = invoiceFullname;
    }

    public String getInvoiceCity() {
        return invoiceCity;
    }

    public void setInvoiceCity(String invoiceCity) {
        this.invoiceCity = invoiceCity;
    }

    public String getInvoiceAddress() {
        return invoiceAddress;
    }

    public void setInvoiceAddress(String invoiceAddress) {
        this.invoiceAddress = invoiceAddress;
    }

    public String getInvoiceProvinceCode() {
        return invoiceProvinceCode;
    }

    public void setInvoiceProvinceCode(String invoiceProvinceCode) {
        this.invoiceProvinceCode = invoiceProvinceCode;
    }

    public String getInvoicePostalCode() {
        return invoicePostalCode;
    }

    public void setInvoicePostalCode(String invoicePostalCode) {
        this.invoicePostalCode = invoicePostalCode;
    }

    public String getInvoiceCountryCode() {
        return invoiceCountryCode;
    }

    public void setInvoiceCountryCode(String invoiceCountryCode) {
        this.invoiceCountryCode = invoiceCountryCode;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEmailAdditional() {
        return emailAdditional;
    }

    public void setEmailAdditional(String emailAdditional) {
        this.emailAdditional = emailAdditional;
    }

    public String getPec() {
        return pec;
    }

    public void setPec(String pec) {
        this.pec = pec;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getPhoneNumberAdditional() {
        return phoneNumberAdditional;
    }

    public void setPhoneNumberAdditional(String phoneNumberAdditional) {
        this.phoneNumberAdditional = phoneNumberAdditional;
    }

    public Boolean getStatement() {
        return statement;
    }

    public void setStatement(Boolean statement) {
        this.statement = statement;
    }

    public Date getSinceDate() {
        return sinceDate;
    }

    public void setSinceDate(Date sinceDate) {
        this.sinceDate = sinceDate;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

    public ObjectId getImageId() {
        return imageId;
    }

    public void setImageId(ObjectId imageId) {
        this.imageId = imageId;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }

}
