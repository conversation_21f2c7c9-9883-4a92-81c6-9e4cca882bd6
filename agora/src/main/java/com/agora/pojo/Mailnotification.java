package com.agora.pojo;

import java.util.List;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Mailnotification extends Pojo {
    
    // status
    private String status;          // processing, done, error, abort
    // operation
    private String operation;       // availability, order
    private List<MailnotificationInOut> inOuts;
    // counters
    private Integer count;          // total rows
    private Integer row;            // current row
    private String item;            // item key (customer name, order numebr, etc.)
    private Integer millies;
    // errors
    private List<String> errors;
    private Integer errorCount;
    // management
    private ObjectId userId;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public List<MailnotificationInOut> getInOuts() {
        return inOuts;
    }

    public void setInOuts(List<MailnotificationInOut> inOuts) {
        this.inOuts = inOuts;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Integer getRow() {
        return row;
    }

    public void setRow(Integer row) {
        this.row = row;
    }

    public String getItem() {
        return item;
    }

    public void setItem(String item) {
        this.item = item;
    }

    public Integer getMillies() {
        return millies;
    }

    public void setMillies(Integer millies) {
        this.millies = millies;
    }

    public List<String> getErrors() {
        return errors;
    }

    public void setErrors(List<String> errors) {
        this.errors = errors;
    }

    public Integer getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(Integer errorCount) {
        this.errorCount = errorCount;
    }

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

}
