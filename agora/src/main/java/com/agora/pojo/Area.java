package com.agora.pojo;

import java.util.List;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Area extends Pojo {
    
    private String code;
    private String title;
    private String titleEnglish;
    private String description;
    private String descriptionEnglish;
    
    // sorting
    private String sorting;

    // images
    private String icon;
    private List<ObjectId> imageIds;

    private String identifier;
    private String identifierEnglish;
    
    private Boolean cancelled;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleEnglish() {
        return titleEnglish;
    }

    public void setTitleEnglish(String titleEnglish) {
        this.titleEnglish = titleEnglish;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescriptionEnglish() {
        return descriptionEnglish;
    }

    public void setDescriptionEnglish(String descriptionEnglish) {
        this.descriptionEnglish = descriptionEnglish;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public List<ObjectId> getImageIds() {
        return imageIds;
    }

    public void setImageIds(List<ObjectId> imageIds) {
        this.imageIds = imageIds;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public String getIdentifierEnglish() {
        return identifierEnglish;
    }

    public void setIdentifierEnglish(String identifierEnglish) {
        this.identifierEnglish = identifierEnglish;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }

    public String getSorting() {
        return sorting;
    }

    public void setSorting(String sorting) {
        this.sorting = sorting;
    }
    
}
