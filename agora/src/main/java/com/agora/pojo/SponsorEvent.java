package com.agora.pojo;

import org.bson.types.ObjectId;
import java.util.Date;

/**
 * SponsorEvent model for sponsored events functionality
 * 
 * <AUTHOR> Agent
 */
public class SponsorEvent extends Pojo {
    
    private ObjectId eventId;           // Reference to the sponsored event
    private Date expirationDate;        // When the sponsorship expires
    private Integer sort;               // Sort order for display
    private Boolean cancelled;          // Soft delete flag
    
    public ObjectId getEventId() {
        return eventId;
    }
    
    public void setEventId(ObjectId eventId) {
        this.eventId = eventId;
    }
    
    public Date getExpirationDate() {
        return expirationDate;
    }
    
    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }
    
    public Integer getSort() {
        return sort;
    }
    
    public void setSort(Integer sort) {
        this.sort = sort;
    }
    
    public Boolean getCancelled() {
        return cancelled;
    }
    
    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }
}
