package com.agora.pojo;

import java.util.Date;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Customer extends Pojo {

    // basic info
    private String lastname;
    private String name;
    private String fullname;
    private String genderType;                              // male, female, other (i.e. companies)
    private Date birthDate;
    private String birthCity;
    private String tin;
    private String tinAgency;
    private String vatNumber;
    private String sdiNumber;
    private String code;                                    // i.e. accounting code
    private String bio;
    private String apikey;

    // language
    private String language;                                // language, when different from system default

    // address
    private String city;
    private String address;
    private String provinceCode;
    private String postalCode;
    private String countryCode;
    private CustomerAddress[] addresses;                    // indirizzi, oltre al primo
    private String district;
    private Boolean fastAddress;

    // tax
    private String tax;                                     // tax, when different from product default

    // invoice address
    private Boolean einvoice;
    private Boolean isAgency;
    private String invoiceFirstname;
    private String invoiceLastname;
    private String invoiceFullname;                         // ragione sociale
    private String invoiceCity;
    private String invoiceCityAgency;
    private String invoiceAddress;
    private String invoiceAddressAgency;
    private String invoiceProvinceCode;
    private String invoiceProvinceCodeAgency;
    private String invoicePostalCode;
    private String invoicePostalCodeAgency;
    private String invoiceCountryCode;
    private String invoiceCountryCodeAgency;
    private String invoicePhoneNumber;
    private String invoicePhoneNumberAgency;
    private String invoiceFax;

    private String bankAccount;
    private String paymentCode;
    private Boolean receiveInvoice;
    
    //security
    private String secretKey;
    
    // contact
    private String email;
    private String emailAdditional;
    private String pec;
    private String phoneNumber;
    private String phoneNumberAdditional;

    // customer since
    private Date sinceDate;

    // customer privacy
    private Boolean privacy;

    // customer third parties
    private Boolean thirdParties;

    // customer third parties
    private Boolean commercial;

    // newsletter
    private Boolean newsletter;
    // magazine
    private Boolean magazine;
    // events
    private Boolean events;

    // terms and conditions
    private Boolean terms;

    // whatsapp
    private Boolean whatsapp;
    
    // links
    private String websiteUrl;
    private String facebookUrl;
    private String linkedinUrl;
    private String instagramUsername;
    private String twitterUsername;

    // note
    private String note;

    // reference
    private String channel;                                 // b2c, b2b, vendor
    private ObjectId vendorId;                              // venditore
    private ObjectId userId;                                // utente del cliente

    // image
    private ObjectId imageId;                               // cover
    private ObjectId logoImageId;                           // logo

    private Boolean active;
    
    private Boolean cancelled;

    private String mainContact;                             // referente ( su fatturazione )
    
    public String getLastname() {
        return lastname;
    }

    public void setLastname(String lastname) {
        this.lastname = lastname;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getGenderType() {
        return genderType;
    }

    public void setGenderType(String genderType) {
        this.genderType = genderType;
    }

    public Date getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(Date birthDate) {
        this.birthDate = birthDate;
    }

    public String getBirthCity() {
        return birthCity;
    }

    public void setBirthCity(String birthCity) {
        this.birthCity = birthCity;
    }

    public String getTin() {
        return tin;
    }

    public void setTin(String tin) {
        this.tin = tin;
    }

    public String getVatNumber() {
        return vatNumber;
    }

    public void setVatNumber(String vatNumber) {
        this.vatNumber = vatNumber;
    }

    public String getSdiNumber() {
        return sdiNumber;
    }

    public void setSdiNumber(String sdiNumber) {
        this.sdiNumber = sdiNumber;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getApikey() {
        return apikey;
    }

    public void setApikey(String apikey) {
        this.apikey = apikey;
    }

    public String getTinAgency() {
        return tinAgency;
    }

    public void setTinAgency(String tinAgency) {
        this.tinAgency = tinAgency;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public CustomerAddress[] getAddresses() {
        return addresses;
    }

    public void setAddresses(CustomerAddress[] addresses) {
        this.addresses = addresses;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public Boolean getFastAddress() {
        return fastAddress;
    }

    public void setFastAddress(Boolean fastAddress) {
        this.fastAddress = fastAddress;
    }

    public String getTax() {
        return tax;
    }

    public void setTax(String tax) {
        this.tax = tax;
    }

    public Boolean getEinvoice() {
        return einvoice;
    }

    public void setEinvoice(Boolean einvoice) {
        this.einvoice = einvoice;
    }

    public Boolean getIsAgency() {
        return isAgency;
    }

    public void setIsAgency(Boolean isAgency) {
        this.isAgency = isAgency;
    }

    public String getInvoiceFirstname() {
        return invoiceFirstname;
    }

    public void setInvoiceFirstname(String invoiceFirstname) {
        this.invoiceFirstname = invoiceFirstname;
    }

    public String getInvoiceLastname() {
        return invoiceLastname;
    }

    public void setInvoiceLastname(String invoiceLastname) {
        this.invoiceLastname = invoiceLastname;
    }

    public String getInvoiceFullname() {
        return invoiceFullname;
    }

    public void setInvoiceFullname(String invoiceFullname) {
        this.invoiceFullname = invoiceFullname;
    }

    public String getInvoiceCity() {
        return invoiceCity;
    }

    public void setInvoiceCity(String invoiceCity) {
        this.invoiceCity = invoiceCity;
    }

    public String getInvoiceCityAgency() {
        return invoiceCityAgency;
    }

    public void setInvoiceCityAgency(String invoiceCityAgency) {
        this.invoiceCityAgency = invoiceCityAgency;
    }

    public String getInvoiceAddress() {
        return invoiceAddress;
    }

    public void setInvoiceAddress(String invoiceAddress) {
        this.invoiceAddress = invoiceAddress;
    }

    public String getInvoiceAddressAgency() {
        return invoiceAddressAgency;
    }

    public void setInvoiceAddressAgency(String invoiceAddressAgency) {
        this.invoiceAddressAgency = invoiceAddressAgency;
    }

    public String getInvoiceProvinceCode() {
        return invoiceProvinceCode;
    }

    public void setInvoiceProvinceCode(String invoiceProvinceCode) {
        this.invoiceProvinceCode = invoiceProvinceCode;
    }

    public String getInvoiceProvinceCodeAgency() {
        return invoiceProvinceCodeAgency;
    }

    public void setInvoiceProvinceCodeAgency(String invoiceProvinceCodeAgency) {
        this.invoiceProvinceCodeAgency = invoiceProvinceCodeAgency;
    }

    public String getInvoicePostalCode() {
        return invoicePostalCode;
    }

    public void setInvoicePostalCode(String invoicePostalCode) {
        this.invoicePostalCode = invoicePostalCode;
    }

    public String getInvoicePostalCodeAgency() {
        return invoicePostalCodeAgency;
    }

    public void setInvoicePostalCodeAgency(String invoicePostalCodeAgency) {
        this.invoicePostalCodeAgency = invoicePostalCodeAgency;
    }

    public String getInvoiceCountryCode() {
        return invoiceCountryCode;
    }

    public void setInvoiceCountryCode(String invoiceCountryCode) {
        this.invoiceCountryCode = invoiceCountryCode;
    }

    public String getInvoiceCountryCodeAgency() {
        return invoiceCountryCodeAgency;
    }

    public void setInvoiceCountryCodeAgency(String invoiceCountryCodeAgency) {
        this.invoiceCountryCodeAgency = invoiceCountryCodeAgency;
    }

    public String getInvoicePhoneNumber() {
        return invoicePhoneNumber;
    }

    public void setInvoicePhoneNumber(String invoicePhoneNumber) {
        this.invoicePhoneNumber = invoicePhoneNumber;
    }

    public String getInvoicePhoneNumberAgency() {
        return invoicePhoneNumberAgency;
    }

    public void setInvoicePhoneNumberAgency(String invoicePhoneNumberAgency) {
        this.invoicePhoneNumberAgency = invoicePhoneNumberAgency;
    }

    public String getInvoiceFax() {
        return invoiceFax;
    }

    public void setInvoiceFax(String invoiceFax) {
        this.invoiceFax = invoiceFax;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getPaymentCode() {
        return paymentCode;
    }

    public void setPaymentCode(String paymentCode) {
        this.paymentCode = paymentCode;
    }

    public Boolean getReceiveInvoice() {
        return receiveInvoice;
    }

    public void setReceiveInvoice(Boolean receiveInvoice) {
        this.receiveInvoice = receiveInvoice;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEmailAdditional() {
        return emailAdditional;
    }

    public void setEmailAdditional(String emailAdditional) {
        this.emailAdditional = emailAdditional;
    }

    public String getPec() {
        return pec;
    }

    public void setPec(String pec) {
        this.pec = pec;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getPhoneNumberAdditional() {
        return phoneNumberAdditional;
    }

    public void setPhoneNumberAdditional(String phoneNumberAdditional) {
        this.phoneNumberAdditional = phoneNumberAdditional;
    }

    public Date getSinceDate() {
        return sinceDate;
    }

    public void setSinceDate(Date sinceDate) {
        this.sinceDate = sinceDate;
    }

    public Boolean getPrivacy() {
        return privacy;
    }

    public void setPrivacy(Boolean privacy) {
        this.privacy = privacy;
    }

    public Boolean getThirdParties() {
        return thirdParties;
    }

    public void setThirdParties(Boolean thirdParties) {
        this.thirdParties = thirdParties;
    }

    public Boolean getCommercial() {
        return commercial;
    }

    public void setCommercial(Boolean commercial) {
        this.commercial = commercial;
    }

    public Boolean getNewsletter() {
        return newsletter;
    }

    public void setNewsletter(Boolean newsletter) {
        this.newsletter = newsletter;
    }

    public Boolean getMagazine() {
        return magazine;
    }

    public void setMagazine(Boolean magazine) {
        this.magazine = magazine;
    }

    public Boolean getEvents() {
        return events;
    }

    public void setEvents(Boolean events) {
        this.events = events;
    }

    public Boolean getTerms() {
        return terms;
    }

    public void setTerms(Boolean terms) {
        this.terms = terms;
    }

    public Boolean getWhatsapp() {
        return whatsapp;
    }

    public void setWhatsapp(Boolean whatsapp) {
        this.whatsapp = whatsapp;
    }

    public String getWebsiteUrl() {
        return websiteUrl;
    }

    public void setWebsiteUrl(String websiteUrl) {
        this.websiteUrl = websiteUrl;
    }

    public String getFacebookUrl() {
        return facebookUrl;
    }

    public void setFacebookUrl(String facebookUrl) {
        this.facebookUrl = facebookUrl;
    }

    public String getLinkedinUrl() {
        return linkedinUrl;
    }

    public void setLinkedinUrl(String linkedinUrl) {
        this.linkedinUrl = linkedinUrl;
    }

    public String getInstagramUsername() {
        return instagramUsername;
    }

    public void setInstagramUsername(String instagramUsername) {
        this.instagramUsername = instagramUsername;
    }

    public String getTwitterUsername() {
        return twitterUsername;
    }

    public void setTwitterUsername(String twitterUsername) {
        this.twitterUsername = twitterUsername;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public ObjectId getVendorId() {
        return vendorId;
    }

    public void setVendorId(ObjectId vendorId) {
        this.vendorId = vendorId;
    }

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

    public ObjectId getImageId() {
        return imageId;
    }

    public void setImageId(ObjectId imageId) {
        this.imageId = imageId;
    }

    public ObjectId getLogoImageId() {
        return logoImageId;
    }

    public void setLogoImageId(ObjectId logoImageId) {
        this.logoImageId = logoImageId;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }

    public String getMainContact() {
        return mainContact;
    }

    public void setMainContact(String mainContact) {
        this.mainContact = mainContact;
    }
}
