package com.agora.pojo;

import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class EventReport extends Pojo {
    // management
    private ObjectId userId;
    private ObjectId eventId;
    private String why;
    private String message;
    private Boolean cancelled;

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

    public ObjectId getEventId() {
        return eventId;
    }

    public void setEventId(ObjectId eventId) {
        this.eventId = eventId;
    }

    public String getWhy() {
        return why;
    }

    public void setWhy(String why) {
        this.why = why;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(<PERSON><PERSON><PERSON> cancelled) {
        this.cancelled = cancelled;
    }
    
}
