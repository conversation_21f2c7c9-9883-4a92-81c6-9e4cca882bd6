package com.agora.pojo.types;

/**
 *
 * <AUTHOR>
 */
public enum EventStatusType {
    draft                   ("Bozza"),
    completed               ("Da approvare"),
    published               ("Pubblicato"),
    refused                 ("Rifiutato"),
    annulled                ("<PERSON>ull<PERSON>"),
    ;
    private final String description;
    
    private EventStatusType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }    
}
