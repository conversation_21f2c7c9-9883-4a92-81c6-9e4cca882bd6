package com.agora.pojo;

import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class EventFollower extends Pojo {

    // management
    private ObjectId userId;
    private ObjectId eventId;
    private Boolean cancelled;

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

    public ObjectId getEventId() {
        return eventId;
    }

    public void setEventId(ObjectId eventId) {
        this.eventId = eventId;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(<PERSON>ole<PERSON> cancelled) {
        this.cancelled = cancelled;
    }

}
