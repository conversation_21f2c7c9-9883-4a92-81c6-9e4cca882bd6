package com.agora.pojo;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class DocumentDescriptor extends Pojo {

    private String filename;
    private String filePath;
    private Map<String, String> metadata;
    private Integer width, height;

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public Map<String, String> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, String> metadata) {
        this.metadata = metadata;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }
    
    public boolean isVertical() {
        if (width != null && height != null) {
            return height > width;
        } else {
            // se non ho le dimensioni la trattiamo come orizzontale
            return false;
        }
    }
}