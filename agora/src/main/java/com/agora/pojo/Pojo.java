package com.agora.pojo;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import org.bson.types.ObjectId;


/**
 *
 * <AUTHOR>
 */
public class Pojo {
    
    // oid
    @JsonProperty("_id")
    private ObjectId id;
    
    // tracking
    private Date creation;
    private Date lastUpdate;
    
    public ObjectId getId() {
        return id;
    }

    public void setId(ObjectId id) {
        this.id = id;
    }

    public Date getCreation() {
        return creation;
    }

    public void setCreation(Date creation) {
        this.creation = creation;
    }
    
    public Date getLastUpdate() {
        return lastUpdate;
    }

    public void setLastUpdate(Date lastUpdate) {
        this.lastUpdate = lastUpdate;
    }

}
