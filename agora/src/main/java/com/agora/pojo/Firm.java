package com.agora.pojo;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class Firm extends Pojo {

    // contacts
    private String siteEmail;                           // customer care su sito
    private String shopEmail;                           // customer care su shop

    // notifications
    private String shopNotification;                    // notifiche dallo shop all'ufficio ordini: never, always
    private String shopNotificationEmail;               // email dell'ufficio ordini (e-commerce)

    private String vendorPortalNotification;            // notifiche dal portale agenti all'ufficio ordini: never, always
    private String vendorPortalNotificationEmail;       // email dell'ufficio ordini (portale agenti)

    private String customerNotification;                // notifiche dal b.e. all'acquirente: never, always
    private String vendorNotification;                  // notifiche dal b.e. al venditore: never, always

    private Boolean mailNotificationEnabled;                  // notifiche mail

    // mail notification scheduling
    private Date lastDailyNotificationSent;            // ultima notifica giornaliera inviata
    private Date lastWeeklyNotificationSent;           // ultima notifica settimanale inviata

    // api
    private String apikey;                              // apikey
    private String secondApikey;                        // apikey

    // facebook
    private Boolean facebookEnableCrashReporter;
    private Boolean facebookEnableDebug;
    private Boolean facebookEnableEventDebug;
    private Boolean facebookEnableEventBriefDebug;
    private Boolean facebookEnableLocalEvents;

    // content display settings
    private Integer descriptionTruncateLength;          // character limit for description truncation
    
        
    public String getSiteEmail() {
        return siteEmail;
    }

    public void setSiteEmail(String siteEmail) {
        this.siteEmail = siteEmail;
    }

    public String getShopEmail() {
        return shopEmail;
    }

    public void setShopEmail(String shopEmail) {
        this.shopEmail = shopEmail;
    }

    public String getShopNotification() {
        return shopNotification;
    }

    public void setShopNotification(String shopNotification) {
        this.shopNotification = shopNotification;
    }

    public String getShopNotificationEmail() {
        return shopNotificationEmail;
    }

    public void setShopNotificationEmail(String shopNotificationEmail) {
        this.shopNotificationEmail = shopNotificationEmail;
    }

    public String getVendorPortalNotification() {
        return vendorPortalNotification;
    }

    public void setVendorPortalNotification(String vendorPortalNotification) {
        this.vendorPortalNotification = vendorPortalNotification;
    }

    public String getVendorPortalNotificationEmail() {
        return vendorPortalNotificationEmail;
    }

    public void setVendorPortalNotificationEmail(String vendorPortalNotificationEmail) {
        this.vendorPortalNotificationEmail = vendorPortalNotificationEmail;
    }

    public String getCustomerNotification() {
        return customerNotification;
    }

    public void setCustomerNotification(String customerNotification) {
        this.customerNotification = customerNotification;
    }

    public String getVendorNotification() {
        return vendorNotification;
    }

    public void setVendorNotification(String vendorNotification) {
        this.vendorNotification = vendorNotification;
    }

    public Boolean getMailNotificationEnabled() {
        return mailNotificationEnabled;
    }

    public void setMailNotificationEnabled(Boolean mailNotificationEnabled) {
        this.mailNotificationEnabled = mailNotificationEnabled;
    }

    public Date getLastDailyNotificationSent() {
        return lastDailyNotificationSent;
    }

    public void setLastDailyNotificationSent(Date lastDailyNotificationSent) {
        this.lastDailyNotificationSent = lastDailyNotificationSent;
    }

    public Date getLastWeeklyNotificationSent() {
        return lastWeeklyNotificationSent;
    }

    public void setLastWeeklyNotificationSent(Date lastWeeklyNotificationSent) {
        this.lastWeeklyNotificationSent = lastWeeklyNotificationSent;
    }

    public String getApikey() {
        return apikey;
    }

    public void setApikey(String apikey) {
        this.apikey = apikey;
    }

    public String getSecondApikey() {
        return secondApikey;
    }

    public void setSecondApikey(String secondApikey) {
        this.secondApikey = secondApikey;
    }

    public Boolean getFacebookEnableCrashReporter() {
        return facebookEnableCrashReporter;
    }

    public void setFacebookEnableCrashReporter(Boolean facebookEnableCrashReporter) {
        this.facebookEnableCrashReporter = facebookEnableCrashReporter;
    }

    public Boolean getFacebookEnableDebug() {
        return facebookEnableDebug;
    }

    public void setFacebookEnableDebug(Boolean facebookEnableDebug) {
        this.facebookEnableDebug = facebookEnableDebug;
    }

    public Boolean getFacebookEnableEventDebug() {
        return facebookEnableEventDebug;
    }

    public void setFacebookEnableEventDebug(Boolean facebookEnableEventDebug) {
        this.facebookEnableEventDebug = facebookEnableEventDebug;
    }

    public Boolean getFacebookEnableEventBriefDebug() {
        return facebookEnableEventBriefDebug;
    }

    public void setFacebookEnableEventBriefDebug(Boolean facebookEnableEventBriefDebug) {
        this.facebookEnableEventBriefDebug = facebookEnableEventBriefDebug;
    }

    public Boolean getFacebookEnableLocalEvents() {
        return facebookEnableLocalEvents;
    }

    public void setFacebookEnableLocalEvents(Boolean facebookEnableLocalEvents) {
        this.facebookEnableLocalEvents = facebookEnableLocalEvents;
    }

    public Integer getDescriptionTruncateLength() {
        return descriptionTruncateLength;
    }

    public void setDescriptionTruncateLength(Integer descriptionTruncateLength) {
        this.descriptionTruncateLength = descriptionTruncateLength;
    }

}
