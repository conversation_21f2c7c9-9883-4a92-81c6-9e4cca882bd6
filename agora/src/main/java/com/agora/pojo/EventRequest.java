package com.agora.pojo;

import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class EventRequest extends Pojo {
    // management
    private ObjectId userId;
    private ObjectId pageId;
    private String location;
    private String message;
    private Boolean cancelled;

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

    public ObjectId getPageId() {
        return pageId;
    }

    public void setPageId(ObjectId pageId) {
        this.pageId = pageId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(<PERSON>ole<PERSON> cancelled) {
        this.cancelled = cancelled;
    }
    
}
