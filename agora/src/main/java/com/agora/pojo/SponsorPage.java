package com.agora.pojo;

import org.bson.types.ObjectId;
import java.util.Date;

/**
 * SponsorPage model for sponsored pages functionality
 * 
 * <AUTHOR> Agent
 */
public class SponsorPage extends Pojo {
    
    private ObjectId pageId;            // Reference to the sponsored page
    private Date expirationDate;        // When the sponsorship expires
    private Integer sort;               // Sort order for display
    private Boolean cancelled;          // Soft delete flag
    
    public ObjectId getPageId() {
        return pageId;
    }
    
    public void setPageId(ObjectId pageId) {
        this.pageId = pageId;
    }
    
    public Date getExpirationDate() {
        return expirationDate;
    }
    
    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }
    
    public Integer getSort() {
        return sort;
    }
    
    public void setSort(Integer sort) {
        this.sort = sort;
    }
    
    public Boolean getCancelled() {
        return cancelled;
    }
    
    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }
}
