package com.agora.pojo;

import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class HomeSlider extends Pojo {
    
    // italian slide
    private String linkVimeo;
    private String title;    
    private String subtitle;    
    private String ctaCaption;
    private String link;      
    
    // english slide
    private String titleEnglish;
    private String subtitleEnglish;
    private String ctaCaptionEnglish;
    private String linkEnglish;

    
    private ObjectId imageId;

    private Boolean cancelled;

    
    public ObjectId getImageId() {
        return imageId;
    }

    public String getLinkVimeo() {
        return linkVimeo;
    }

    public void setLinkVimeo(String linkVimeo) {
        this.linkVimeo = linkVimeo;
    }

    public String getCtaCaption() {
        return ctaCaption;
    }

    public void setCtaCaption(String ctaCaption) {
        this.ctaCaption = ctaCaption;
    }

    public String getCtaCaptionEnglish() {
        return ctaCaptionEnglish;
    }

    public void setCtaCaptionEnglish(String ctaCaptionEnglish) {
        this.ctaCaptionEnglish = ctaCaptionEnglish;
    }

    public String getLinkEnglish() {
        return linkEnglish;
    }

    public void setLinkEnglish(String linkEnglish) {
        this.linkEnglish = linkEnglish;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleEnglish() {
        return titleEnglish;
    }

    public void setTitleEnglish(String titleEnglish) {
        this.titleEnglish = titleEnglish;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getSubtitleEnglish() {
        return subtitleEnglish;
    }

    public void setSubtitleEnglish(String subtitleEnglish) {
        this.subtitleEnglish = subtitleEnglish;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public void setImageId(ObjectId imageId) {
        this.imageId = imageId;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }

}
