package com.agora.pojo;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class Apilog extends Pojo {
    
    private String name;
    private String method;
    private Map params;
    private String body;
    private String out;
    private Long millies;
    private Boolean done;
    private String message;
    private Integer count;
    private Integer kos;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public Map getParams() {
        return params;
    }

    public void setParams(Map params) {
        this.params = params;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getOut() {
        return out;
    }

    public void setOut(String out) {
        this.out = out;
    }

    public Long getMillies() {
        return millies;
    }

    public void setMillies(Long millies) {
        this.millies = millies;
    }

    public Boolean getDone() {
        return done;
    }

    public void setDone(Boolean done) {
        this.done = done;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Integer getKos() {
        return kos;
    }

    public void setKos(Integer kos) {
        this.kos = kos;
    }

}
