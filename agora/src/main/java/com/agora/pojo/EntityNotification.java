package com.agora.pojo;

import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class EntityNotification extends Pojo {
    private ObjectId eventId;
    private String type;
    private Boolean isRead;
    private Date readDate;
    private ObjectId userId;
    private Date date;
    private String lat;
    private String lng;
    private Boolean mailActive;
    private Boolean isSentMail;
    private Date sendDate;
    private Boolean cancelled;
    private List<ObjectId> validPageIds;    //campo di appoggio per query, pagine che io segui tra le n coinvolte
    
    public ObjectId getEventId() {
        return eventId;
    }

    public void setEventId(ObjectId eventId) {
        this.eventId = eventId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getIsRead() {
        return isRead;
    }

    public void setIsRead(Boolean isRead) {
        this.isRead = isRead;
    }

    public Date getReadDate() {
        return readDate;
    }

    public void setReadDate(Date readDate) {
        this.readDate = readDate;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public Boolean getMailActive() {
        return mailActive;
    }

    public void setMailActive(Boolean mailActive) {
        this.mailActive = mailActive;
    }

    public Boolean getIsSentMail() {
        return isSentMail;
    }

    public void setIsSentMail(Boolean isSentMail) {
        this.isSentMail = isSentMail;
    }

    public Date getSendDate() {
        return sendDate;
    }

    public void setSendDate(Date sendDate) {
        this.sendDate = sendDate;
    }

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }

    public List<ObjectId> getValidPageIds() {
        return validPageIds;
    }

    public void setValidPageIds(List<ObjectId> validPageIds) {
        this.validPageIds = validPageIds;
    }

}
