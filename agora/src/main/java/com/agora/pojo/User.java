package com.agora.pojo;

import java.util.Date;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class User extends Pojo {

    // basic info
    private String email;
    
    // profile
    private String name;
    private String username;
    private String password;
    private String profileType;         // unconfirmed, standard, vendor, head, admin, system
    
    // cashing
    private String cashingType;         // null o "cash"
    private String cashingKey;          // chiave per identificare il collegamento con la cassa
    private String cashingComNumber;    // COM1, COM2, COM3, COM4
    
    // registration
    private Date registrationSendDate;
    private Date registrationDate;
    private String registrationToken;
    private Boolean registered;
    private Boolean facebookLink;
    
    // password recovery
    private Date recoverySendDate;
    private Date recoveryDate;
    private String recoveryToken;
    private Boolean recovered;
    private Boolean smsVerified;
    
    // image
    private ObjectId imageId;

    private Boolean cancelled;
    
    private String keyword;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getProfileType() {
        return profileType;
    }

    public void setProfileType(String profileType) {
        this.profileType = profileType;
    }

    public String getCashingType() {
        return cashingType;
    }

    public void setCashingType(String cashingType) {
        this.cashingType = cashingType;
    }

    public String getCashingKey() {
        return cashingKey;
    }

    public void setCashingKey(String cashingKey) {
        this.cashingKey = cashingKey;
    }

    public String getCashingComNumber() {
        return cashingComNumber;
    }

    public void setCashingComNumber(String cashingComNumber) {
        this.cashingComNumber = cashingComNumber;
    }

    public Date getRegistrationSendDate() {
        return registrationSendDate;
    }

    public void setRegistrationSendDate(Date registrationSendDate) {
        this.registrationSendDate = registrationSendDate;
    }

    public Date getRegistrationDate() {
        return registrationDate;
    }

    public void setRegistrationDate(Date registrationDate) {
        this.registrationDate = registrationDate;
    }

    public String getRegistrationToken() {
        return registrationToken;
    }

    public void setRegistrationToken(String registrationToken) {
        this.registrationToken = registrationToken;
    }

    public Boolean getRegistered() {
        return registered;
    }

    public void setRegistered(Boolean registered) {
        this.registered = registered;
    }

    public Boolean getFacebookLink() {
        return facebookLink;
    }

    public void setFacebookLink(Boolean facebookLink) {
        this.facebookLink = facebookLink;
    }

    public Date getRecoverySendDate() {
        return recoverySendDate;
    }

    public void setRecoverySendDate(Date recoverySendDate) {
        this.recoverySendDate = recoverySendDate;
    }

    public Date getRecoveryDate() {
        return recoveryDate;
    }

    public void setRecoveryDate(Date recoveryDate) {
        this.recoveryDate = recoveryDate;
    }

    public String getRecoveryToken() {
        return recoveryToken;
    }

    public void setRecoveryToken(String recoveryToken) {
        this.recoveryToken = recoveryToken;
    }

    public Boolean getRecovered() {
        return recovered;
    }

    public void setRecovered(Boolean recovered) {
        this.recovered = recovered;
    }

    public Boolean getSmsVerified() {
        return smsVerified;
    }

    public void setSmsVerified(Boolean smsVerified) {
        this.smsVerified = smsVerified;
    }

    public ObjectId getImageId() {
        return imageId;
    }

    public void setImageId(ObjectId imageId) {
        this.imageId = imageId;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
        
}
