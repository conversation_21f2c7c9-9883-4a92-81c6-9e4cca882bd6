package com.agora.pojo;

/**
 *
 * <AUTHOR>
 */
public class Location {

    // this class wraps original mongodb Location class due to theese problems:
    // - we cannot create a spacial index where both coordinates and position are present
    // - we cannot easily deserialize to object due to missing constructor with empty parameters
    // - we cannot easily serialize from string 'cause "Location" enumeration is streamed as "POINT" due to its enumeration name
    
    private String type;
    private Double[] coordinates;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Double[] getCoordinates() {
        return coordinates;
    }

    public void setCoordinates(Double[] coordinates) {
        this.coordinates = coordinates;
    }
    
}
