package com.agora.category;

import com.github.slugify.Slugify;
import com.agora.core.Defaults;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.AreaDao;
import com.agora.dao.CategoryDao;
import com.agora.pojo.Area;
import com.agora.pojo.Category;
import com.agora.pojo.User;
import com.agora.pojo.types.ProfileType;
import com.agora.support.image.slim.SlimImage;
import com.agora.util.ParamUtils;
import com.agora.util.PojoUtils;
import com.agora.util.RouteUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class CategoryController {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(CategoryController.class.getName());
    
    public static TemplateViewRoute categories = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        attributes.put("user", user);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        List<Category> categoryList = CategoryDao.loadCategoryList();
        attributes.put("categoryList", categoryList);        
        
        return Manager.render(Templates.CATEGORIES, attributes, RouteUtils.pathType(request));
    };
    
    public static TemplateViewRoute category_edit = (Request request, Response response) -> {
        
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        Category category;
        if (oid != null) {
            category = CategoryDao.loadCategory(oid);
        } else {
            category = new Category();
        }
        attributes.put("category", category);
        
        List<Area> areaList = AreaDao.loadAreaList();
        attributes.put("areaList", areaList);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.CATEGORY_EDIT, attributes, RouteUtils.pathType(request));
    };
    
    public static Route category_edit_save = (Request request, Response response) -> {
        // params
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        if (oid != null) {
            Category categoryOld = CategoryDao.loadCategory(oid);
            Category category = PojoUtils.mergeFromRequest(request, categoryOld);

            Slugify slg = new Slugify();
            String identifier = category.getTitle();
            category.setIdentifier(slg.slugify(identifier));                

            String identifierEnglish = category.getTitleEnglish();
            category.setIdentifierEnglish(slg.slugify(identifierEnglish));

            if (isValidCategory(category)) {
                CategoryDao.updateCategory(category);

                // image
                String slim = request.queryParams("uploaded-files");
                if (StringUtils.isNotBlank(slim)) {
                    SlimImage uploaded = null;
                    try {
                        uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    if (uploaded != null) {
                        CategoryDao.updateCategoryImage(user.getUsername(), oid, uploaded);
                    } else {
                        CategoryDao.removeCategoryImage(oid);
                    }
                }
                    
                // navigation
                    response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.CATEGORIES));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.CATEGORY_EDIT) + "?oid=" + category.getId());
            }
        } else {
            // params
            Category category = PojoUtils.createFromRequest(request, Category.class);
            
            Slugify slg = new Slugify();
            String identifier = category.getTitle();
            category.setIdentifier(slg.slugify(identifier));                

            String identifierEnglish = category.getTitleEnglish();
            category.setIdentifierEnglish(slg.slugify(identifierEnglish));

            if (isValidCategory(category)) {
                
                ObjectId categoryId = CategoryDao.insertCategory(category);

                // image
                String slim = request.queryParams("uploaded-files");
                if (StringUtils.isNotBlank(slim)) {
                    SlimImage uploaded = null;
                    try {
                        uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    if (uploaded != null) {
                        CategoryDao.updateCategoryImage(user.getUsername(), categoryId, uploaded);
                    }
                }
                
                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.CATEGORIES));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.CATEGORY_EDIT));
            }
        }        

        return null;
    };  
    
    public static Route category_remove = (Request request, Response response) -> {
        
        ObjectId categoryId = ParamUtils.toObjectId(request.queryParams("categoryId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        if (categoryId != null) {
            // params
            Category category = CategoryDao.loadCategory(categoryId);
            if (category != null) {
                CategoryDao.updateCategoryCancelled(categoryId, true);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };      
    
    private static boolean isValidCategory(Category entity) {
        boolean valid = (entity != null) &&
                StringUtils.isNotBlank(entity.getTitle()) &&
                StringUtils.isNotBlank(entity.getCode()) &&
                StringUtils.isNotBlank(entity.getArea()) &&
                !exists(entity)
                ;
        
        if (!valid) {
            if (entity != null) {
                LOGGER.warn(
                        "category validation problem:\n" +
                        "- title " + entity.getTitle() + "\n" +
                        "- code " + entity.getCode()+ "\n" +
                        "- area " + entity.getArea()+ "\n"
                );
            } else {
                LOGGER.warn(
                        "category validation problem:\n" +
                        "- empty"
                );
            }
        }
        
        return valid;
    }   

    private static boolean exists(Category entity) {
        boolean exists = false;
        if (StringUtils.isNotBlank(entity.getCode()) && StringUtils.isNotBlank(entity.getArea()) && StringUtils.isNotBlank(entity.getIdentifier())) {
            Category category = null;
            try {
                category = CategoryDao.loadCategoryByCode(entity.getCode());
                if (category == null) {
                    category = CategoryDao.loadCategoryByIdentifier(entity.getArea(), entity.getIdentifier());
                } else {
                    if (StringUtils.equals(category.getId().toString(), entity.getId().toString())) {
                        category = null;
                        category = CategoryDao.loadCategoryByIdentifier(entity.getArea(), entity.getIdentifier());
                    }
                }
                if (category != null) {
                    if (StringUtils.equals(category.getId().toString(), entity.getId().toString())) {
                        category = null;
                    }
                }
                
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            exists = (category != null);
        }
        return exists;
    }    
}
