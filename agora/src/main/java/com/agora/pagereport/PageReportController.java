package com.agora.pagereport;

import com.agora.commons.EntityCommons;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.PageReportDao;
import com.agora.pojo.PageReport;
import com.agora.pojo.User;
import com.agora.pojo.types.ProfileType;
import com.agora.util.RouteUtils;
import com.agora.util.TimeUtils;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */

public class PageReportController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PageReportController.class.getName());
    
    public static TemplateViewRoute be_pagereports = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
       
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME);
            return Manager.renderEmpty();
        }

        // avatar
        ObjectId entityImageId = EntityCommons.imageIdFromUser(user);
        attributes.put("entityImageId", entityImageId);

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        // date filter
        Date startDate = TimeUtils.beginOfMonth(TimeUtils.now());
        Date endDate = TimeUtils.today();
        
        if (StringUtils.isNotBlank(request.queryParams("startDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("startDate"));
            if (tmp != null) {
                startDate = tmp;
            }
        }
        if (StringUtils.isNotBlank(request.queryParams("endDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("endDate"));
            if (tmp != null) {
                endDate = tmp;
            }
        }
        
        attributes.put("startDate", startDate);
        attributes.put("endDate", endDate);
        
        // profile
        String profile = request.queryParams("profile");
        attributes.put("profile", profile);
        
        // list
        List<PageReport> pagereportList = PageReportDao.loadPageReportListByDateRange(startDate, endDate);
        attributes.put("pagereportList", pagereportList);

        return Manager.render(Templates.BE_PAGE_REPORTS, attributes, RouteUtils.pathType(request));
    };
    
}
