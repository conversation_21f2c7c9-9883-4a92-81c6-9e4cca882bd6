package com.agora.sponsorevent;

import com.agora.commons.SponsorEventCommons;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;
import com.agora.dao.EventDao;
import com.agora.dao.SponsorEventDao;
import com.agora.pojo.Event;
import com.agora.pojo.SponsorEvent;
import com.agora.pojo.User;
import com.agora.util.ParamUtils;
import com.agora.util.PojoUtils;
import com.agora.util.RouteUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;

/**
 * Controller for SponsorEvent functionality
 * 
 * <AUTHOR> Agent
 */
public class SponsorEventController {

    public static TemplateViewRoute sponsor_event_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }

        // check permissions
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), "system")
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), "admin")) {
            response.redirect(RouteUtils.contextPath(request) + Paths.DASHBOARD);
            return Manager.renderEmpty();
        }

        attributes.put("user", user);

        // remove old sponsor events
        SponsorEventDao.removeOldSponsorEvents();

        List<SponsorEventCommons.SponsorEventEntry> sponsorEventEntries = SponsorEventCommons.loadSponsorEventEntries();
        attributes.put("sponsorEventEntries", sponsorEventEntries);

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.SPONSOR_EVENT_COLLECTION, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute sponsor_event_edit = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }

        // check permissions
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), "system")
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), "admin")) {
            response.redirect(RouteUtils.contextPath(request) + Paths.DASHBOARD);
            return Manager.renderEmpty();
        }

        attributes.put("user", user);

        ObjectId sponsorEventId = ParamUtils.toObjectId(request.queryParams("sponsorEventId"));
        SponsorEvent sponsorEvent;
        if (sponsorEventId != null) {
            sponsorEvent = SponsorEventDao.loadSponsorEvent(sponsorEventId);
        } else {
            sponsorEvent = new SponsorEvent();
        }
        attributes.put("sponsorEvent", sponsorEvent);

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.SPONSOR_EVENT_EDIT, attributes, RouteUtils.pathType(request));
    };

    public static Route sponsor_event_edit_save = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }

        // check permissions
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), "system")
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), "admin")) {
            response.redirect(RouteUtils.contextPath(request) + Paths.DASHBOARD);
            return null;
        }

        // params
        Map<String, String> params = PojoUtils.paramsFromRequest(request);
        ObjectId sponsorEventId = null;
        if (request.queryParams("sponsorEventId") != null) {
            sponsorEventId = ParamUtils.toObjectId(request.queryParams("sponsorEventId"));
        }

        SponsorEvent sponsorEvent;
        if (sponsorEventId != null) {
            sponsorEvent = SponsorEventDao.loadSponsorEvent(sponsorEventId);
        } else {
            sponsorEvent = new SponsorEvent();
        }

        // merge params
        sponsorEvent = PojoUtils.mergeFromParams(params, sponsorEvent);

        try {
            if (sponsorEventId != null) {
                SponsorEventDao.updateSponsorEvent(sponsorEvent);
            } else {
                SponsorEventDao.insertSponsorEvent(sponsorEvent);
            }
            response.redirect(RouteUtils.contextPath(request) + Paths.SPONSOR_EVENT_COLLECTION + "/ok");
        } catch (Exception ex) {
            response.redirect(RouteUtils.contextPath(request) + Paths.SPONSOR_EVENT_EDIT + "/error");
        }

        return null;
    };

    public static Route sponsor_event_remove = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }

        // check permissions
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), "system")
                && !StringUtils.equalsIgnoreCase(user.getProfileType(), "admin")) {
            response.redirect(RouteUtils.contextPath(request) + Paths.DASHBOARD);
            return null;
        }

        ObjectId sponsorEventId = ParamUtils.toObjectId(request.queryParams("sponsorEventId"));
        if (sponsorEventId != null) {
            try {
                SponsorEventDao.removeSponsorEvent(sponsorEventId);
                response.redirect(RouteUtils.contextPath(request) + Paths.SPONSOR_EVENT_COLLECTION + "/ok");
            } catch (Exception ex) {
                response.redirect(RouteUtils.contextPath(request) + Paths.SPONSOR_EVENT_COLLECTION + "/error");
            }
        }

        return null;
    };
}
