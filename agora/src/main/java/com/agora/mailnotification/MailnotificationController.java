package com.agora.mailnotification;

import com.agora.commons.EntityCommons;
import com.agora.commons.MailnotificationCommons;
import com.agora.commons.NotificationCommons;
import com.agora.core.Defaults;
import com.agora.core.Manager;
import com.agora.core.Paths;
import com.agora.core.Templates;

import com.agora.dao.FirmDao;
import com.agora.dao.MailnotificationDao;
import com.agora.dao.UserDao;

import com.agora.pojo.Firm;
import com.agora.pojo.Mailnotification;
import com.agora.pojo.MailnotificationInOut;
import com.agora.pojo.User;
import com.agora.pojo.types.MailnotificationStatusType;
import com.agora.pojo.types.ProfileType;

import com.agora.util.ParamUtils;
import com.agora.util.RouteUtils;
import com.agora.util.TimeUtils;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class MailnotificationController {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(MailnotificationController.class.getName());

    private static final String  APIKEY_USER           = "<EMAIL>";
 
    public static TemplateViewRoute mailnotifications = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.LOGIN, StringUtils.defaultIfBlank(language, Defaults.LANGUAGE)));
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, StringUtils.defaultIfBlank(language, Defaults.LANGUAGE)));
            return Manager.renderEmpty();
        }

        // avatar
        ObjectId entityImageId = EntityCommons.imageIdFromUser(user);
        attributes.put("entityImageId", entityImageId);

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        // date filter
        Date startDate = TimeUtils.yesterday();
        Date endDate = TimeUtils.today();
        
        if (StringUtils.isNotBlank(request.queryParams("startDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("startDate"));
            if (tmp != null) {
                startDate = tmp;
            }
        }
        if (StringUtils.isNotBlank(request.queryParams("endDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("endDate"));
            if (tmp != null) {
                endDate = tmp;
            }
        }
        
        attributes.put("startDate", startDate);
        attributes.put("endDate", endDate);
        
        List<Mailnotification> mailnotificationList = MailnotificationDao.loadMailnotificationListByDateRange(startDate, endDate);
        attributes.put("mailnotificationList", mailnotificationList);

        Mailnotification mailnotification = MailnotificationDao.loadProcessingMailnotification();
        attributes.put("mailnotification", mailnotification);
        
        Firm firm = FirmDao.loadFirm();
        attributes.put("firm", firm);
        
        return Manager.render(Templates.MAILNOTIFICATIONS, attributes, RouteUtils.pathType(request));
    };
    
    public static Route mailnotifications_data_send = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            String apikey = request.queryParams("apikey");
            if (StringUtils.isNotBlank(apikey)) {
                Firm firm = null;
                try {
                    firm = FirmDao.loadFirm();
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if (firm != null) {
                    if (StringUtils.isNotBlank(firm.getApikey()) && (StringUtils.equalsIgnoreCase(apikey, firm.getApikey()) || StringUtils.equalsIgnoreCase(apikey, firm.getSecondApikey()))) {
                        try {
                            user = UserDao.loadUserByUsername(APIKEY_USER);
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                    }
                }
            }
        }
        
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        // not enabled
        Firm firm = null;
        try {
            firm = FirmDao.loadFirm();
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        if (firm == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "missing configuration");
        }
        if (BooleanUtils.isNotTrue(firm.getMailNotificationEnabled())) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "not enabled");
        }
        
        // already processing
        if (MailnotificationCommons.alignmentProcessing()) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "already processing");
        }
        
        // operation - start
        if (!MailnotificationCommons.alignmentStart(MailnotificationCommons.OperationType.sendnotification, user.getId())) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "cannot start");
        }
        
        String frequency = request.queryParams("frequency");
        
        // process
        if (MailnotificationCommons.processMailNotifications(request, user.getId(), user.getUsername(), frequency)) {

            // progress - finish
            MailnotificationCommons.alignmentFinish();

        } else {

            // progress - finish
            MailnotificationCommons.alignmentFinish(MailnotificationStatusType.error);
        }

        return "ok";
    };

    public static Route mailnotification_status = (Request request, Response response) -> {

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            String apikey = request.queryParams("apikey");
            if (StringUtils.isNotBlank(apikey)) {
                Firm firm = null;
                try {
                    firm = FirmDao.loadFirm();
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if (firm != null) {
                    if (StringUtils.isNotBlank(firm.getApikey()) && (StringUtils.equalsIgnoreCase(apikey, firm.getApikey()) || StringUtils.equalsIgnoreCase(apikey, firm.getSecondApikey()))) {
                        try {
                            user = UserDao.loadUserByUsername(APIKEY_USER);
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                    }
                }
            }
        }
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        boolean finish = BooleanUtils.toBoolean(request.queryParams("finish"));
        
        if (finish) {
            MailnotificationCommons.alignmentFinish(MailnotificationStatusType.abort);
        }
        
        Mailnotification mailnotification = null;
        try {
            mailnotification = MailnotificationDao.loadProcessingMailnotification();
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        
        return mailnotification;
    };    

    public static TemplateViewRoute mailnotification_view = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // language
        String language = RouteUtils.language(request);
        attributes.put("language", language);
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.LOGIN, StringUtils.defaultIfBlank(language, Defaults.LANGUAGE)));
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.system.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, StringUtils.defaultIfBlank(language, Defaults.LANGUAGE)));
            return Manager.renderEmpty();
        }
        
        // avatar
        ObjectId entityImageId = EntityCommons.imageIdFromUser(user);
        attributes.put("entityImageId", entityImageId);

        // localized versions of current path
        attributes.put("paths", Paths.localizedPathsMap(RouteUtils.unlanguage(request.servletPath(), language)));
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // zucchetti
        ObjectId mailnotificationId = ParamUtils.toObjectId(request.queryParams("mailnotificationId"));
        if (mailnotificationId == null) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, StringUtils.defaultIfBlank(language, Defaults.LANGUAGE)));
            return Manager.renderEmpty();
        }

        Mailnotification mailnotification = MailnotificationDao.loadMailnotification(mailnotificationId);
        attributes.put("mailnotification", mailnotification);

        return Manager.render(Templates.MAILNOTIFICATION_VIEW, attributes, RouteUtils.pathType(request));
    };
    

    
}
