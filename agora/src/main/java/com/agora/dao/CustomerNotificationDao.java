package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.CustomerNotification;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.exists;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class CustomerNotificationDao {

    public static CustomerNotification loadCustomerNotification(ObjectId customerNotificationId) throws Exception {
        if (customerNotificationId == null) {
            throw new InvalidParameterException("empty customerNotificationId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer_notification");
        Document doc = collection.find(eq("_id", customerNotificationId)).first();
        return Manager.fromDocument(doc, CustomerNotification.class);
    }
    
    public static long loadCustomerNotificationCount(ObjectId customerId) throws Exception {
        if (customerId == null) {
            throw new InvalidParameterException("empty customerId");
        }

        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer_notification");
        long count = collection.count(
                and(
                        ne("cancelled", true),
                        eq("customerId", customerId)
                )
        );
        
        return count;
    }

    public static List<CustomerNotification> loadCustomerNotificationList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer_notification");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true)
                        )
                )
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, CustomerNotification.class);
    }
    
    public static List<CustomerNotification> loadCustomerNotificationActiveList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer_notification");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true),
                                ne("emailActive", true)
                        )
                )
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, CustomerNotification.class);
    }
    
    public static List<CustomerNotification> loadCustomerNotificationActiveListByFrequency(String frequency) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer_notification");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true),
                                eq("emailActive", true),
                                eq("frequency", frequency),
                                exists("lat"),
                                exists("lng")
                        )
                )
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, CustomerNotification.class);
    }
    
    public static List<CustomerNotification> loadCustomerNotificationListByCustomerId(ObjectId customerId) throws Exception {
        if (customerId == null) {
            throw new InvalidParameterException("empty customerId");
        }
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer_notification");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true),
                                eq("customerId", customerId)
                        )
                )
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, CustomerNotification.class);
    }
    
    public static List<CustomerNotification> loadCustomerNotificationListByUserId(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer_notification");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true),
                                eq("userId", userId)
                        )
                )
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, CustomerNotification.class);
    }
    
    public static Boolean existCustomerNotificationByUserId(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer_notification");
        Document firstDoc = collection
                .find(
                        and(
                                ne("cancelled", true),
                                eq("userId", userId)
                        )
                )
                .first();
        return firstDoc != null;
    }    
    
    public static ObjectId insertCustomerNotification(CustomerNotification customerNotification) throws Exception {
        if (customerNotification == null) {
            throw new InvalidParameterException("empty customerNotification");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        customerNotification.setCreation(now);
        customerNotification.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer_notification");
        Document doc = Manager.toDocument(customerNotification);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateCustomerNotification(CustomerNotification customerNotification) throws Exception {
        if (customerNotification == null) {
            throw new InvalidParameterException("empty customerNotification");
        }

        // defaults
        Date now = new Date();
        
        // internals
        customerNotification.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer_notification");
        collection.replaceOne(
                new Document("_id", customerNotification.getId()),
                Manager.toDocument(customerNotification)
        );
        
    }

    public static void updateCustomerNotificationActive(ObjectId customerNotificationId, boolean active) throws Exception {
        if (customerNotificationId == null) {
            throw new InvalidParameterException("empty customerNotificationId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        CustomerNotification customerNotification = loadCustomerNotification(customerNotificationId);
        customerNotification.setActive(active);
        customerNotification.setEmailActive(active);
        
        // internals
        customerNotification.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer_notification");
        collection.replaceOne(
                new Document("_id", customerNotification.getId()),
                Manager.toDocument(customerNotification)
        );
        
    }

    public static void updateCustomerNotificationCancelled(ObjectId customerNotificationId, boolean cancelled) throws Exception {
        if (customerNotificationId == null) {
            throw new InvalidParameterException("empty customerNotificationId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        CustomerNotification customerNotification = loadCustomerNotification(customerNotificationId);
        customerNotification.setCancelled(cancelled);
        
        // internals
        customerNotification.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer_notification");
        collection.replaceOne(
                new Document("_id", customerNotification.getId()),
                Manager.toDocument(customerNotification)
        );
        
    }
    
}
