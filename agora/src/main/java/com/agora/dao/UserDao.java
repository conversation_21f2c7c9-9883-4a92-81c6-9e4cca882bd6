package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.User;
import com.agora.pojo.types.ImageType;
import com.agora.pojo.types.ProfileType;
import com.agora.support.image.slim.SlimImage;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;

import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;

import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 * <AUTHOR>
 */
public class UserDao {

    public static List<User> loadUserList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        FindIterable<Document> list = collection
                .find(ne("cancelled", true))
                .sort(orderBy(ascending("lastname")));
        return Manager.fromDocumentList(list, User.class);
    }

    public static List<User> loadUserList(List<ObjectId> userIds) throws Exception {
        if (userIds == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        FindIterable<Document> list = collection.find(in("_id", userIds));
        return Manager.fromDocumentList(list, User.class);
    }

    public static User loadUser(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        Document doc = collection.find(eq("_id", userId)).first();

        User user = null;
        if (doc != null) {
            user = Manager.fromDocument(doc, User.class);
        }
        return user;
    }

    public static User loadUserByUsername(String username) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        Document doc = collection.find(and(
                ne("cancelled", true),
                regex("username", Pattern.compile("(\\s|^)" + username + "(\\s|$)", Pattern.CASE_INSENSITIVE))
        )).first();

        User user = null;
        if (doc != null) {
            user = Manager.fromDocument(doc, User.class);
        }
        return user;
    }

    public static User loadUserByRegistrationToken(String registrationToken) throws Exception {
        if (StringUtils.isBlank(registrationToken)) {
            throw new InvalidParameterException("empty registrationToken");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        Document doc = collection.find(and(
                ne("cancelled", true),
                eq("registrationToken", registrationToken)
        )).first();

        User user = null;
        if (doc != null) {
            user = Manager.fromDocument(doc, User.class);
        }
        return user;
    }

    public static User loadUserByEmail(String email) throws Exception {
        if (StringUtils.isBlank(email)) {
            throw new InvalidParameterException("empty email");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        Document doc = collection
                .find(and(
                        ne("cancelled", true),
                        regex("email", Pattern.compile("(\\s|^)" + email + "(\\s|$)", Pattern.CASE_INSENSITIVE))
                )).first();

        User user = null;
        if (doc != null) {
            user = Manager.fromDocument(doc, User.class);
        }
        return user;
    }

    public static User loadUserByCashingKey(String cashingKey) throws Exception {
        if (StringUtils.isBlank(cashingKey)) {
            throw new InvalidParameterException("empty cashingKey");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        Document doc = collection.find(and(
                ne("cancelled", true),
                regex("cashingKey", Pattern.compile("(\\s|^)" + cashingKey + "(\\s|$)", Pattern.CASE_INSENSITIVE))
        )).first();

        User user = null;
        if (doc != null) {
            user = Manager.fromDocument(doc, User.class);
        }
        return user;
    }

    public static List<User> loadUserListBy(String name, int skip, int limit) throws Exception {
        if (name == null) {
            throw new InvalidParameterException("empty name");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        List<User> result = new ArrayList<>();

        FindIterable<Document> documents = collection.find(
                        and(
                                ne("cancelled", true),
                                or(
                                        regex("name", Pattern.compile(name, Pattern.CASE_INSENSITIVE)),
                                        regex("email", Pattern.compile(name, Pattern.CASE_INSENSITIVE))
                                )
                        )).skip(skip)
                .limit(limit);

        for (Document doc : documents) {
            result.add(Manager.fromDocument(doc, User.class));
        }

        return result;
    }

    public static ObjectId insertUser(User user) {

        if (user == null) {
            throw new InvalidParameterException("empty user");
        }
        if (StringUtils.isBlank(user.getEmail())) {
            throw new InvalidParameterException("empty email");
        }
        if (StringUtils.isBlank(user.getPassword())) {
            throw new InvalidParameterException("empty password");
        }
        if (StringUtils.isBlank(user.getProfileType())) {
            throw new InvalidParameterException("empty profileType");
        }

        // defaults
        Date now = new Date();

        user.setRegistrationSendDate(now);
        user.setRegistrationToken(UUID.randomUUID().toString());

        // internals
        user.setCreation(now);
        user.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        Document doc = Manager.toDocument(user);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateUser(User user) throws Exception {
        if (user == null) {
            throw new InvalidParameterException("empty user");
        }

        // defaults
        Date now = new Date();

        // internals
        user.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        collection.replaceOne(
                new Document("_id", user.getId()),
                Manager.toDocument(user)
        );

    }

    public static void updateUserRegistration(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        // defaults
        Date now = new Date();

        // update
        User user = loadUser(userId);

        // update only when necessary
        if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.unconfirmed.toString())) {
            user.setRegistered(true);
            user.setRegistrationDate(now);
            user.setProfileType(ProfileType.customer.toString());
        }

        // internals
        user.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        collection.replaceOne(
                new Document("_id", user.getId()),
                Manager.toDocument(user)
        );

    }

    public static void updateUserRecovery(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        // defaults
        Date now = new Date();

        // update
        User user = loadUser(userId);
        user.setRecoverySendDate(now);
        user.setRecoveryDate(null);
        user.setRecoveryToken(UUID.randomUUID().toString());
        user.setRecovered(null);
        user.setPassword(UUID.randomUUID().toString());

        // internals
        user.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        collection.replaceOne(
                new Document("_id", user.getId()),
                Manager.toDocument(user)
        );

    }

    public static void removeUserRecovery(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        // defaults
        Date now = new Date();

        // update
        User user = loadUser(userId);
        user.setRecoveryDate(now);
        user.setRecovered(true);

        // internals
        user.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        collection.replaceOne(
                new Document("_id", user.getId()),
                Manager.toDocument(user)
        );

    }

    public static void updateUserPassword(ObjectId userId, String password) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        if (StringUtils.isBlank(password)) {
            throw new InvalidParameterException("empty password");
        }

        // defaults
        Date now = new Date();

        // update
        User user = loadUser(userId);
        user.setPassword(password);

        // internals
        user.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        collection.replaceOne(
                new Document("_id", user.getId()),
                Manager.toDocument(user)
        );

    }

    //    public static void updateUserImage(String username, ObjectId userId, SlimImage image) throws Exception {
//        if (StringUtils.isBlank(username)) {
//            throw new InvalidParameterException("empty username");
//        }
//        if (userId == null) {
//            throw new InvalidParameterException("empty userId");
//        }
//        if (image == null) {
//            throw new InvalidParameterException("empty image");
//        }
//        if (image.isEmpty()) {
//            throw new InvalidParameterException("empty image bytes");
//        }
//
//        // defaults
//        Date now = new Date();
//        String filename = ImageDao.composeFilename(ImageType.user, username, image.getExtension());
//        String type = image.getType();
//
//        // save image
//        ObjectId imageId = ImageDao.insertImage(filename,
//                type,
//                image.getBytes()
//        );
//
//        // update imageId
//        User user = loadUser(userId);
//        user.setImageId(imageId);
//
//        // internals
//        user.setLastUpdate(now);
//
//        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
//        collection.replaceOne(
//                new Document("_id", user.getId()),
//                Manager.toDocument(user)
//        );
//
//    }
//
//    public static void removeUserImage(ObjectId userId) throws Exception {
//        if (userId == null) {
//            throw new InvalidParameterException("empty userId");
//        }
//
//        // defaults
//        Date now = new Date();
//
//        // update
//        User user = loadUser(userId);
//        user.setImageId(null);
//
//        // internals
//        user.setLastUpdate(now);
//
//        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
//        collection.replaceOne(
//                new Document("_id", user.getId()),
//                Manager.toDocument(user)
//        );
//
//    }
    public static void updateUserImage(String username, ObjectId userId, SlimImage image) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        if (image == null) {
            throw new InvalidParameterException("empty image");
        }
        if (image.isEmpty()) {
            throw new InvalidParameterException("empty image bytes");
        }

        // defaults
        Date now = new Date();
        String filename = ImageDao.composeFilename(ImageType.user, username, image.getExtension());
        String type = image.getType();

        // save image
        ObjectId imageId = ImageDao.insertImage(filename,
                type,
                image.getBytes()
        );

        // update imageId
        User user = loadUser(userId);
        user.setImageId(imageId);

        // internals
        user.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        collection.replaceOne(
                new Document("_id", user.getId()),
                Manager.toDocument(user)
        );

    }

    public static void removeUserImage(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        // defaults
        Date now = new Date();

        // update
        User user = loadUser(userId);
        user.setImageId(null);

        // internals
        user.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        collection.replaceOne(
                new Document("_id", user.getId()),
                Manager.toDocument(user)
        );

    }
}
