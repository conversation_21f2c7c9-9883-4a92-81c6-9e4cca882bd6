package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.Subcategory;
import com.agora.pojo.types.ImageType;
import com.agora.support.image.slim.SlimImage;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Filters.regex;
import static com.mongodb.client.model.Sorts.ascending;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import static com.mongodb.client.model.Sorts.orderBy;
import java.util.regex.Pattern;
import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class SubcategoryDao {
    
    public static Subcategory loadSubcategory(ObjectId subcategoryId) throws Exception {
        if (subcategoryId == null) {
            throw new InvalidParameterException("empty subcategoryId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("subcategory");
        Document doc = collection.find(eq("_id", subcategoryId)).first();
        return Manager.fromDocument(doc, Subcategory.class);
    }    
    
    public static List<Subcategory> loadSubcategoryList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("subcategory");
        FindIterable<Document> list = collection
                .find(ne("cancelled", true))
                .sort(orderBy(ascending("area"), ascending("category"), ascending("sorting"), ascending("code")));
        return Manager.fromDocumentList(list, Subcategory.class);
    }
    
    public static List<Subcategory> loadSubcategoryListBy(String area, String category) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("subcategory");
        FindIterable<Document> list = collection
                .find(and(
                        eq("area", area),
                        eq("category", category),
                        ne("cancelled", true)
                ))
                .sort(orderBy(ascending("area"), ascending("category"), ascending("sorting"), ascending("code")));
        return Manager.fromDocumentList(list, Subcategory.class);
    }
    
    public static List<Subcategory> loadSubcategoryListBy(String area, String category, String title, int skip, int limit) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("subcategory");
        
        List<Bson> filters = new ArrayList<>();
        
        filters.add(eq("area", area));
        filters.add(eq("category", category));
        if (StringUtils.isNotBlank(title)) {
            filters.add(regex("title", Pattern.compile("^"+ title, Pattern.CASE_INSENSITIVE + Pattern.UNICODE_CASE)));
        }
        
        Bson sort = orderBy(ascending("title"));
        
        FindIterable<Document> list = collection
                    .find(and(filters))
                    .skip(skip)
                    .limit(limit > 0 ? limit : 0)
                    .sort(sort);
        return Manager.fromDocumentList(list, Subcategory.class);
    }
    
    public static Subcategory loadSubcategoryByIdentifier(String areaCode, String categoryCode, String identifier) throws Exception {
        if (StringUtils.isBlank(areaCode)) {
            throw new InvalidParameterException("empty areaCode");
        }
        if (StringUtils.isBlank(categoryCode)) {
            throw new InvalidParameterException("empty categoryCode");
        }
        if (identifier == null) {
            throw new InvalidParameterException("empty identifier");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("subcategory");
        Document doc = collection.find(
                and(
                        eq("area", areaCode),
                        eq("category", categoryCode),
                        eq("identifier", identifier)
                )
        ).first();
        return Manager.fromDocument(doc, Subcategory.class);
    }
    
    public static Subcategory loadSubcategoryByIdentifierEnglish(String areaCode, String categoryCode, String identifierEnglish) throws Exception {
        if (StringUtils.isBlank(areaCode)) {
            throw new InvalidParameterException("empty areaCode");
        }
        if (StringUtils.isBlank(categoryCode)) {
            throw new InvalidParameterException("empty categoryCode");
        }
        if (identifierEnglish == null) {
            throw new InvalidParameterException("empty identifierEnglish");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("subcategory");
        Document doc = collection.find(
                and(
                        eq("area", areaCode),
                        eq("category", categoryCode),
                        eq("identifierEnglish", identifierEnglish)
                )
        ).first();
        return Manager.fromDocument(doc, Subcategory.class);
    }
    
    public static Subcategory loadSubcategoryByCode(String code) throws Exception {
        if (code == null) {
            throw new InvalidParameterException("empty code");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("subcategory");
        Document doc = collection.find(eq("code", code)).first();
        return Manager.fromDocument(doc, Subcategory.class);
    }

    public static Subcategory loadSubcategoryByAreaCategoryAndCode(String area, String category, String code) throws Exception {
        if (area == null) {
            throw new InvalidParameterException("empty area");
        }
        if (category == null) {
            throw new InvalidParameterException("empty category");
        }
        if (code == null) {
            throw new InvalidParameterException("empty code");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("subcategory");
        Document doc = collection.find(and(eq("area", area), eq("category", category), eq("code", code))).first();
        return Manager.fromDocument(doc, Subcategory.class);
    }

    public static Subcategory loadSubcategoryByTitle(String title) throws Exception {
        if (title == null) {
            throw new InvalidParameterException("empty title");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("subcategory");
        Document doc = collection.find(eq("title", title)).first();
        return Manager.fromDocument(doc, Subcategory.class);
    }
    
    public static Subcategory loadSubcategoryByTitleEnglish(String titleEnglish) throws Exception {
        if (titleEnglish == null) {
            throw new InvalidParameterException("empty titleEnglish");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("subcategory");
        Document doc = collection.find(eq("titleEnglish", titleEnglish)).first();
        return Manager.fromDocument(doc, Subcategory.class);
    }
    
    public static ObjectId insertSubcategory(Subcategory subcategory) throws Exception {
        if (subcategory == null) {
            throw new InvalidParameterException("empty subcategory");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        subcategory.setCreation(now);
        subcategory.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("subcategory");
        Document doc = Manager.toDocument(subcategory);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }    
    
    public static void updateSubcategory(Subcategory subcategory) throws Exception {
        if (subcategory == null) {
            throw new InvalidParameterException("empty subcategory");
        }

        // defaults
        Date now = new Date();
        
        // internals
        subcategory.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("subcategory");
        collection.replaceOne(
                new Document("_id", subcategory.getId()),
                Manager.toDocument(subcategory)
        );
        
    }    
    
    public static void updateSubcategoryCancelled(ObjectId subcategoryId, boolean cancelled) throws Exception {
        if (subcategoryId == null) {
            throw new InvalidParameterException("empty subcategoryId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Subcategory subcategory = loadSubcategory(subcategoryId);
        subcategory.setCancelled(cancelled);
        
        // internals
        subcategory.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("subcategory");
        collection.replaceOne(
                new Document("_id", subcategory.getId()),
                Manager.toDocument(subcategory)
        );
        
    }
    
    public static void updateSubcategoryImage(String username, ObjectId subcategoryId, SlimImage image) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        if (subcategoryId == null) {
            throw new InvalidParameterException("empty subcategoryId");
        }
        if (image == null) {
            throw new InvalidParameterException("empty image");
        }
        if (image.isEmpty()) {
            throw new InvalidParameterException("empty image bytes");
        }
        
        // defaults
        Date now = new Date();
        String filename = ImageDao.composeFilename(ImageType.subcategory, username, image.getExtension());
        String type = image.getType();
        
        // save image
        ObjectId imageId = ImageDao.insertImage(filename, 
                type, 
                image.getBytes()
        );
        
        // update imageId
        Subcategory subcategory = loadSubcategory(subcategoryId);
        if (subcategory.getImageIds() == null) {
            subcategory.setImageIds(new ArrayList<>());
        }
        if (subcategory.getImageIds().isEmpty()) {
            subcategory.getImageIds().add(new ObjectId());
        }
        subcategory.getImageIds().set(0, imageId);

        // internals
        subcategory.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("subcategory");
        collection.replaceOne(
                new Document("_id", subcategory.getId()),
                Manager.toDocument(subcategory)
        );
        
    }
    
    public static void removeSubcategoryImage(ObjectId subcategoryId) throws Exception {
        if (subcategoryId == null) {
            throw new InvalidParameterException("empty subcategoryId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Subcategory subcategory = loadSubcategory(subcategoryId);
        subcategory.setImageIds(null);
        
        // internals
        subcategory.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("subcategory");
        collection.replaceOne(
                new Document("_id", subcategory.getId()),
                Manager.toDocument(subcategory)
        );
        
    }
    
    
}
