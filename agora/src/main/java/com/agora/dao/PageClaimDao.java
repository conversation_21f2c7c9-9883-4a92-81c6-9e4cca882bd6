package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.PageClaim;
import com.agora.util.MongoUtils;
import com.agora.util.TimeUtils;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.descending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class PageClaimDao {
    
    public static PageClaim loadPageClaim(ObjectId pageClaimId) throws Exception {
        if (pageClaimId == null) {
            throw new InvalidParameterException("empty pageClaimId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("pageclaim");
        Document doc = collection.find(eq("_id", pageClaimId)).first();
        return Manager.fromDocument(doc, PageClaim.class);
    }    
    
    public static List<PageClaim> loadPageClaimList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("pageclaim");
        FindIterable<Document> list = collection
                .find(ne("cancelled", true))
                .sort(orderBy(descending("creation")));
        return Manager.fromDocumentList(list, PageClaim.class);
    }
    
    

    public static List<PageClaim> loadPageClaimListByDateRange(Date from, Date to) throws Exception {
        List<Bson> filters = new ArrayList<>();
        
        if (from != null) {
            from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
            filters.add(gte("creation", from));
        }
        if (to != null) {
            to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));
            filters.add(lte("creation", to));
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("pageclaim");
        FindIterable<Document> list = collection
                .find(and(filters))
                .sort(orderBy(descending("creation")));
        return Manager.fromDocumentList(list, PageClaim.class);
    }
        
    
    public static ObjectId insertPageClaim(PageClaim pageClaim) throws Exception {
        if (pageClaim == null) {
            throw new InvalidParameterException("empty pageclaim");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        pageClaim.setCreation(now);
        pageClaim.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("pageclaim");
        Document doc = Manager.toDocument(pageClaim);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }    
    
}
