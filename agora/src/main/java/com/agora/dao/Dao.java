package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.Pojo;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.eq;
import java.security.InvalidParameterException;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Dao {
    
    public static Object load(String table, ObjectId id) throws Exception {
        if (StringUtils.isBlank(table)) {
            throw new InvalidParameterException("empty table");
        }
        if (id == null) {
            throw new InvalidParameterException("empty id for table " + table);
        }

        final String packageName = StringUtils.substringBeforeLast((new Pojo()).getClass().getCanonicalName(), ".");
        String className = packageName + "." + StringUtils.capitalize(table);
        if (StringUtils.equals(table, "documentdescriptor")) {
            className = packageName + ".DocumentDescriptor";
        }
        
        Class clss = null;
        try {
            clss = Class.forName(className);
        } catch (Exception ignored) {
            // ignored
        }
        if (clss == null) {
            throw new InvalidParameterException("missing class " + className + " for table " + table);
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection(StringUtils.lowerCase(table));
        Document doc = collection.find(eq("_id", id)).first();
        
        Object obj = null;
        if (doc != null) {
            obj = Manager.fromDocument(doc, clss);
        }
        return obj;
    }
    
}
