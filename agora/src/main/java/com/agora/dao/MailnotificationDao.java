package com.agora.dao;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Sorts.descending;
import static com.mongodb.client.model.Sorts.orderBy;
import com.agora.core.Manager;
import com.agora.pojo.Mailnotification;
import com.agora.pojo.types.MailnotificationStatusType;
import com.agora.util.MongoUtils;
import com.agora.util.TimeUtils;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class MailnotificationDao {

    public static List<Mailnotification> loadMailnotificationListByDateRange(Date from, Date to) throws Exception {
        if (from == null) {
            throw new InvalidParameterException("empty from");
        }
        if (to == null) {
            throw new InvalidParameterException("empty to");
        }
        
        from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
        to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("mailnotification");
        FindIterable<Document> list = collection
                .find(and(
                        gte("creation", from),
                        lte("creation", to)))
                .sort(orderBy(descending("creation")));
        return Manager.fromDocumentList(list, Mailnotification.class);
    }
    
    public static Mailnotification loadMailnotification(ObjectId mailnotificationId) throws Exception {
        if (mailnotificationId == null) {
            throw new InvalidParameterException("empty mailnotificationId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("mailnotification");
        Document doc = collection.find(eq("_id", mailnotificationId)).first();
        return Manager.fromDocument(doc, Mailnotification.class);
    }
    
    public static Mailnotification loadProcessingMailnotification() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("mailnotification");
        Document doc = collection.find(eq("status", MailnotificationStatusType.processing.toString())).first();
        return Manager.fromDocument(doc, Mailnotification.class);
    }
    
    public static ObjectId insertMailnotification(Mailnotification mailnotification) throws Exception {
        if (mailnotification == null) {
            throw new InvalidParameterException("empty mailnotification");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        mailnotification.setCreation(now);
        mailnotification.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("mailnotification");
        Document doc = Manager.toDocument(mailnotification);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateMailnotification(Mailnotification mailnotification) throws Exception {
        if (mailnotification == null) {
            throw new InvalidParameterException("empty mailnotification");
        }

        // defaults
        Date now = new Date();
        
        // internals
        mailnotification.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("mailnotification");
        collection.replaceOne(
                new Document("_id", mailnotification.getId()),
                Manager.toDocument(mailnotification)
        );
        
    }

}
