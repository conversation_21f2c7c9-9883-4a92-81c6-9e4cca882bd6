package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.EventRequest;
import com.agora.util.MongoUtils;
import com.agora.util.TimeUtils;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.descending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class EventRequestDao {
 
    public static List<EventRequest> loadEventRequestListByDateRange(Date from, Date to) throws Exception {
        List<Bson> filters = new ArrayList<>();
        
        filters.add(ne("cancelled", true));
        if (from != null) {
            from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
            filters.add(gte("creation", from));
        }
        if (to != null) {
            to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));
            filters.add(lte("creation", to));
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eventrequest");
        FindIterable<Document> list = collection
                .find(and(filters))
                .sort(orderBy(descending("creation")));
        return Manager.fromDocumentList(list, EventRequest.class);
    }
    
    public static EventRequest loadEventRequest(ObjectId eventrequestId) throws Exception {
        if (eventrequestId == null) {
            throw new InvalidParameterException("empty eventrequestId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eventrequest");
        Document doc = collection.find(eq("_id", eventrequestId)).first();
        return Manager.fromDocument(doc, EventRequest.class);
    }
    
    public static EventRequest loadEventRequestByPageIdAndUserId(ObjectId pageId, ObjectId userId) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eventrequest");
        Document doc = collection.find(and(eq("pageId", pageId), eq("userId", userId), ne("cancelled", true))).first();
        return Manager.fromDocument(doc, EventRequest.class);
    }
    
    public static ObjectId insertEventRequest(EventRequest eventrequest) throws Exception {
        if (eventrequest == null) {
            throw new InvalidParameterException("empty eventrequest");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        eventrequest.setCreation(now);
        eventrequest.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eventrequest");
        Document doc = Manager.toDocument(eventrequest);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateEventRequest(EventRequest eventrequest) throws Exception {
        if (eventrequest == null) {
            throw new InvalidParameterException("empty eventrequest");
        }

        // defaults
        Date now = new Date();
        
        // internals
        eventrequest.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eventrequest");
        collection.replaceOne(
                new Document("_id", eventrequest.getId()),
                Manager.toDocument(eventrequest)
        );
        
    }
        
}
