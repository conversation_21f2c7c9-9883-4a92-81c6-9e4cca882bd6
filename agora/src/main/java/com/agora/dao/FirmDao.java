package com.agora.dao;

import com.mongodb.client.MongoCollection;
import com.agora.core.Manager;
import com.agora.pojo.Firm;
import java.security.InvalidParameterException;
import java.util.Date;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class FirmDao {
    
    public static Firm loadFirm() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("firm");
        Document doc = collection.find().first();
        
        Firm firm = null;
        if (doc != null) {
            firm = Manager.fromDocument(doc, Firm.class);
        }
        return firm;
    }
    
    public static ObjectId insertFirm(Firm firm) throws Exception {
        if (firm == null) {
            throw new InvalidParameterException("empty firm");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        firm.setCreation(now);
        firm.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("firm");
        Document doc = Manager.toDocument(firm);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }        

    public static void updateFirm(Firm firm) throws Exception {
        if (firm == null) {
            throw new InvalidParameterException("empty firm");
        }

        // defaults
        Date now = new Date();
        
        // internals
        firm.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("firm");
        collection.replaceOne(
                new Document("_id", firm.getId()),
                Manager.toDocument(firm)
        );

    }
    
}
