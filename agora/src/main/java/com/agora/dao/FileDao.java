package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.DocumentDescriptor;
import com.agora.pojo.types.FileType;
import com.agora.support.file.Filex;
import com.agora.util.TimeUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.gridfs.GridFSBucket;
import com.mongodb.client.gridfs.GridFSBuckets;
import com.mongodb.client.gridfs.GridFSDownloadStream;
import com.mongodb.client.gridfs.GridFSFindIterable;
import com.mongodb.client.gridfs.model.GridFSUploadOptions;
import static com.mongodb.client.model.Filters.eq;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class FileDao {
  
    public static String composeFilename(FileType type, String extension) throws Exception {
        if (type == null) {
            throw new InvalidParameterException("empty type");
        }
        // extension is optional
        
        // defaults
        Date now = new Date();
        if (StringUtils.isNotBlank(extension)) {
            extension = "." + extension;
        }
        
        return type + "-" + TimeUtils.toString(now, "yyyyMMdd") + "-" + UUID.randomUUID().toString() + (StringUtils.isNotBlank(extension) ? extension : "");
    }
    
    public static String composeFilenameV2(FileType type, String filename, String extension) throws Exception {
        if (type == null) {
            throw new InvalidParameterException("empty type");
        }
        // extension is optional
        
        // defaults
        Date now = new Date();
        if (StringUtils.isNotBlank(extension)) {
            extension = "." + extension;
        }
        
        return type + "-" + sanitizeFilename(filename) + "-" + TimeUtils.toString(now, "yyyyMMdd-hhmmssSSS") + (StringUtils.isNotBlank(extension) ? extension : "");
    }

    public static Filex loadFile(ObjectId oid) throws Exception {
        if (oid == null) {
            throw new InvalidParameterException("empty oid");
        }
        
        String filename;
        String originalFilename;
        String contentType;
        int length;
        byte[] bytes;
        
        GridFSBucket bucket = GridFSBuckets.create(Manager.mongoDatabase, "file");
        GridFSFindIterable iterable = bucket.find(eq("_id", oid));
        if (iterable.first() != null) {
            try (GridFSDownloadStream stream = bucket.openDownloadStream(oid)) {
                filename = stream.getGridFSFile().getFilename();
                contentType = stream.getGridFSFile().getMetadata().getString("contentType");
                originalFilename = stream.getGridFSFile().getMetadata().getString("originalFilename");
                length = (int) stream.getGridFSFile().getLength();
            }
            try (ByteArrayOutputStream out = new ByteArrayOutputStream(length)) {
                bucket.downloadToStream(oid, out);
                bytes = out.toByteArray();
            }
            return new Filex(filename, originalFilename, contentType, bytes, null);

        } else {
            return null;
        }
        
    }
    
    public static Filex loadFileV2(ObjectId oid) throws Exception {
        if (oid == null) {
            throw new InvalidParameterException("empty oid");
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("documentdescriptor");
        Document doc = collection.find(eq("_id", oid)).first();
        DocumentDescriptor document = Manager.fromDocument(doc, DocumentDescriptor.class);
        File tmpFile = new File(document.getFilePath());
        
        return new Filex(document.getFilename(), document.getMetadata().get("originalFilename"), document.getMetadata().get("contentType"), FileUtils.readFileToByteArray(tmpFile), document.getFilePath());
    }
    
    public static ObjectId insertFile(String filename, String originalFilename, String type, byte[] bytes) throws Exception {
        if (StringUtils.isBlank(filename)) {
            throw new InvalidParameterException("empty filename");
        }
        if (StringUtils.isBlank(originalFilename)) {
            throw new InvalidParameterException("empty originalFilename");
        }
        if (StringUtils.isBlank(type)) {
            throw new InvalidParameterException("empty type");
        }
        if (bytes == null) {
            throw new InvalidParameterException("empty bytes");
        }
        
        GridFSBucket bucket = GridFSBuckets.create(Manager.mongoDatabase, "file");
        
        Document metadata = new Document();
        metadata.put("contentType", type);
        metadata.put("originalFilename", originalFilename);
        
        GridFSUploadOptions options = new GridFSUploadOptions()
                .metadata(metadata)
                ;
        ByteArrayInputStream data = new ByteArrayInputStream(bytes);
        ObjectId oid = bucket.uploadFromStream(filename, data, options);
        IOUtils.closeQuietly(data);
        
        return oid;
    }
    
    public static ObjectId insertFileV2(File file, String originalFilename, String type) throws Exception {
        if (file == null) {
            throw new InvalidParameterException("empty file");
        }
        if (StringUtils.isBlank(originalFilename)) {
            throw new InvalidParameterException("empty originalFilename");
        }
        if (StringUtils.isBlank(type)) {
            throw new InvalidParameterException("empty type");
        }
        
        Map<String, String> metadata = new HashMap<>();
        metadata.put("contentType", type);
        metadata.put("originalFilename", originalFilename);
        
        Date now = new Date();
        DocumentDescriptor document = new DocumentDescriptor();
        document.setFilename(file.getName());
        document.setFilePath(file.getPath());
        document.setMetadata(metadata);
        document.setCreation(now);
        document.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("documentdescriptor");
        Document doc = Manager.toDocument(document);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void deleteVideo(ObjectId oid) {
        if (oid == null) {
            throw new InvalidParameterException("empty oid");
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("documentdescriptor");
        Document doc = collection.find(eq("_id", oid)).first();
        DocumentDescriptor document = Manager.fromDocument(doc, DocumentDescriptor.class);
        if (document != null) {
            File tmpFile = new File(document.getFilePath());
            if (tmpFile.exists()) {
                tmpFile.delete();
            }
            collection.deleteOne(eq("_id", oid));
        }
    }
        
    ////////////
    // internals
    
    private static String sanitizeFilename(String name) {
        return StringUtils.replace(StringUtils.replacePattern(name, "[\\\\/:*?\"<>|]", ""), " ", "_");
    }
    
}
