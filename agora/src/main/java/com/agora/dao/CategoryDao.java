package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.Category;
import com.agora.pojo.types.ImageType;
import com.agora.support.image.slim.SlimImage;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class CategoryDao {
    
    public static Category loadCategory(ObjectId categoryId) throws Exception {
        if (categoryId == null) {
            throw new InvalidParameterException("empty categoryId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("category");
        Document doc = collection.find(eq("_id", categoryId)).first();
        return Manager.fromDocument(doc, Category.class);
    }    
    
    public static List<Category> loadCategoryList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("category");
        FindIterable<Document> list = collection
                .find(ne("cancelled", true))
                .sort(orderBy(ascending("area"), ascending("sorting"), ascending("code")));
        return Manager.fromDocumentList(list, Category.class);
    }
    
    public static List<Category> loadCategoryListBy(String area) throws Exception {
        return loadCategoryListBy(area, 0);
    }
    
    public static List<Category> loadCategoryListBy(String area, int limit) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("category");
        FindIterable<Document> list = collection
                .find(and(
                        eq("area", area),
                        ne("cancelled", true)
                ))
                .limit(limit > 0 ? limit : 0)
                .sort(orderBy(ascending("area"), ascending("sorting"), ascending("code")));
        return Manager.fromDocumentList(list, Category.class);
    }
    
    public static Category loadCategoryByIdentifier(String areaCode, String identifier) throws Exception {
        if (StringUtils.isBlank(areaCode)) {
            throw new InvalidParameterException("empty areaCode");
        }
        if (identifier == null) {
            throw new InvalidParameterException("empty identifier");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("category");
        Document doc = collection.find(
                and(
                        eq("area", areaCode),
                        eq("identifier", identifier)
                        )
        ).first();
        return Manager.fromDocument(doc, Category.class);
    }
    
    public static Category loadCategoryByIdentifierEnglish(String areaCode, String identifierEnglish) throws Exception {
        if (StringUtils.isBlank(areaCode)) {
            throw new InvalidParameterException("empty areaCode");
        }
        if (identifierEnglish == null) {
            throw new InvalidParameterException("empty identifierEnglish");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("category");
        Document doc = collection.find(
                and(
                        eq("area", areaCode),
                        eq("identifierEnglish", identifierEnglish)
                        )
        ).first();
        return Manager.fromDocument(doc, Category.class);
    }
    
    public static Category loadCategoryByCode(String code) throws Exception {
        if (code == null) {
            throw new InvalidParameterException("empty code");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("category");
        Document doc = collection.find(eq("code", code)).first();
        return Manager.fromDocument(doc, Category.class);
    }
    
    public static Category loadCategoryByTitle(String title) throws Exception {
        if (title == null) {
            throw new InvalidParameterException("empty title");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("category");
        Document doc = collection.find(eq("title", title)).first();
        return Manager.fromDocument(doc, Category.class);
    }
    
    public static Category loadCategoryByTitleEnglish(String titleEnglish) throws Exception {
        if (titleEnglish == null) {
            throw new InvalidParameterException("empty titleEnglish");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("category");
        Document doc = collection.find(eq("titleEnglish", titleEnglish)).first();
        return Manager.fromDocument(doc, Category.class);
    }
    
    public static ObjectId insertCategory(Category category) throws Exception {
        if (category == null) {
            throw new InvalidParameterException("empty category");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        category.setCreation(now);
        category.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("category");
        Document doc = Manager.toDocument(category);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }    
    
    public static void updateCategory(Category category) throws Exception {
        if (category == null) {
            throw new InvalidParameterException("empty category");
        }

        // defaults
        Date now = new Date();
        
        // internals
        category.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("category");
        collection.replaceOne(
                new Document("_id", category.getId()),
                Manager.toDocument(category)
        );
        
    }    
    
    public static void updateCategoryCancelled(ObjectId categoryId, boolean cancelled) throws Exception {
        if (categoryId == null) {
            throw new InvalidParameterException("empty categoryId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Category category = loadCategory(categoryId);
        category.setCancelled(cancelled);
        
        // internals
        category.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("category");
        collection.replaceOne(
                new Document("_id", category.getId()),
                Manager.toDocument(category)
        );
        
    }
    
    public static void updateCategoryImage(String username, ObjectId categoryId, SlimImage image) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        if (categoryId == null) {
            throw new InvalidParameterException("empty categoryId");
        }
        if (image == null) {
            throw new InvalidParameterException("empty image");
        }
        if (image.isEmpty()) {
            throw new InvalidParameterException("empty image bytes");
        }
        
        // defaults
        Date now = new Date();
        String filename = ImageDao.composeFilename(ImageType.category, username, image.getExtension());
        String type = image.getType();
        
        // save image
        ObjectId imageId = ImageDao.insertImage(filename, 
                type, 
                image.getBytes()
        );
        
        // update imageId
        Category category = loadCategory(categoryId);
        if (category.getImageIds() == null) {
            category.setImageIds(new ArrayList<>());
        }
        if (category.getImageIds().isEmpty()) {
            category.getImageIds().add(new ObjectId());
        }
        category.getImageIds().set(0, imageId);

        // internals
        category.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("category");
        collection.replaceOne(
                new Document("_id", category.getId()),
                Manager.toDocument(category)
        );
        
    }
    
    public static void removeCategoryImage(ObjectId categoryId) throws Exception {
        if (categoryId == null) {
            throw new InvalidParameterException("empty categoryId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Category category = loadCategory(categoryId);
        category.setImageIds(null);
        
        // internals
        category.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("category");
        collection.replaceOne(
                new Document("_id", category.getId()),
                Manager.toDocument(category)
        );
        
    }
    
    
}
