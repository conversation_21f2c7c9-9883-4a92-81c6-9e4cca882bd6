package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.City;
import com.agora.support.CityEntry;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Accumulators.min;
import static com.mongodb.client.model.Accumulators.sum;
import static com.mongodb.client.model.Aggregates.group;
import static com.mongodb.client.model.Aggregates.match;
import static com.mongodb.client.model.Aggregates.project;
import static com.mongodb.client.model.Aggregates.sort;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Filters.regex;
import static com.mongodb.client.model.Projections.excludeId;
import static com.mongodb.client.model.Projections.fields;
import static com.mongodb.client.model.Projections.include;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class CityDao {

    public static City loadCity(ObjectId cityId) throws Exception {
        if (cityId == null) {
            throw new InvalidParameterException("empty cityId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("city");
        Document doc = collection.find(eq("_id", cityId)).first();
        return Manager.fromDocument(doc, City.class);
    }

    public static City loadCityByCity(String city) throws Exception {
        return loadCityByCity(city, true);
    }

    public static City loadCityByCity(String city, boolean caseSensitive) throws Exception {
        if (city == null) {
            throw new InvalidParameterException("empty city");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("city");

        List<Bson> filters = new ArrayList<>();

        if (caseSensitive) {
            filters.add(eq("city", city));
        } else {
            filters.add(regex("city", Pattern.compile("^" + city + "$", Pattern.CASE_INSENSITIVE)));
        }

        Document doc = collection
                .find(and(filters))
                .first();
        return Manager.fromDocument(doc, City.class);
    }

    public static City loadCityByIdentifier(String identifier) throws Exception {
        if (identifier == null) {
            throw new InvalidParameterException("empty identifier");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("city");
        Document doc = collection.find(eq("identifier", identifier)).first();
        return Manager.fromDocument(doc, City.class);
    }

    public static List<City> loadCityList() throws Exception {
        return loadCityListBy(null, null, 0, 0);
    }

    public static List<City> loadCityListBy(String city, String postalCode, int skip, int limit) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("city");

        List<Bson> filters = new ArrayList<>();

        if (StringUtils.isNotBlank(city)) {
            filters.add(regex("city", Pattern.compile("^" + city, Pattern.CASE_INSENSITIVE + Pattern.UNICODE_CASE)));
        }
        if (StringUtils.isNotBlank(postalCode)) {
            filters.add(regex("postalCode", Pattern.compile("^" + postalCode, Pattern.CASE_INSENSITIVE + Pattern.UNICODE_CASE)));
        }

        FindIterable<Document> list = collection
                .find(and(filters))
                .sort(orderBy(ascending("city")))
                .skip(skip)
                .limit(limit > 0 ? limit : 0);
        return Manager.fromDocumentList(list, City.class);
    }

    public static List<CityEntry> loadCityEntryByPattern(String city) throws Exception {
        List<Bson> filters = new ArrayList<>();

        filters.add(ne("city", ""));
        filters.add(ne("city", null));
        if (StringUtils.isNotBlank(city)) {
            filters.add(regex("city", Pattern.compile("^" + city, Pattern.CASE_INSENSITIVE + Pattern.UNICODE_CASE)));
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event");
        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                        match(and(filters)),
                        group("$city", min("city", "$city"), sum("count", 1)),
                        project(fields(excludeId(), include("city"))),
                        sort(orderBy(ascending("city")))
                ));
        return Manager.fromAggregateList(list, CityEntry.class);
    }

}
