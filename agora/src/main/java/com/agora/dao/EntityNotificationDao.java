package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.EntityNotification;
import com.agora.util.MongoUtils;
import com.agora.util.TimeUtils;
import com.mongodb.Block;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Accumulators;
import com.mongodb.client.model.Aggregates;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.exists;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Filters.ne;
import com.mongodb.client.model.Sorts;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.descending;
import static com.mongodb.client.model.Sorts.orderBy;
import com.mongodb.client.model.Updates;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class EntityNotificationDao {

    public static EntityNotification loadEntityNotification(ObjectId entityNotificationId) throws Exception {
        if (entityNotificationId == null) {
            throw new InvalidParameterException("empty entityNotificationId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("entity_notification");
        Document doc = collection.find(eq("_id", entityNotificationId)).first();
        return Manager.fromDocument(doc, EntityNotification.class);
    }

    public static Map<ObjectId, List<ObjectId>> loadUserIdPageIdMap(List<ObjectId> pageIds) throws Exception {
        if (pageIds == null || pageIds.isEmpty()) {
            throw new InvalidParameterException("empty pageIds");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_follower");

        Document query = new Document("pageId", new Document("$in", pageIds))
                .append("cancelled", new Document("$ne", true));

        List<Document> results = collection.find(query).into(new ArrayList<>());

        Map<ObjectId, List<ObjectId>> userIdPageIdMap = new HashMap<>();

        for (Document result : results) {
            ObjectId userId = result.getObjectId("userId");
            ObjectId pageId = result.getObjectId("pageId");

            // Aggiungi il pageId alla lista dell'utente nella mappa
            userIdPageIdMap.computeIfAbsent(userId, k -> new ArrayList<>()).add(pageId);
        }

        return userIdPageIdMap;
    }
            
    public static long countEntityNotificationByUserId(ObjectId userId, Boolean onlyNew) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        
        List<Bson> filters = new ArrayList<>();
        filters.add(ne("cancelled", true));
        filters.add(eq("userId", userId));
        if (BooleanUtils.isTrue(onlyNew)) {
            filters.add(ne("isRead", true));
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("entity_notification");
        long count = collection.count(
                and(filters)
        );
        
        return count;
    }

    public static List<EntityNotification> loadEntityNotificationList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("entity_notification");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true)
                        )
                )
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, EntityNotification.class);
    }
    
    public static List<EntityNotification> loadDistinctEntityNotificationListByUserId(ObjectId userId, int skip, int limit) throws Exception {
        
        if (userId == null) {
            throw new InvalidParameterException("User ID cannot be null");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("entity_notification");

        List<Bson> aggregationPipeline = Arrays.asList(
            Aggregates.match(and(
                eq("userId", userId),
                ne("cancelled", true),
                ne("cancelled", true),
                ne("isRead", true)
            )),
            Aggregates.sort(Sorts.descending("creation")),
            Aggregates.group(
                "$eventId", 
                Accumulators.first("notification", "$$ROOT")
            ), Aggregates.sort(Sorts.descending("notification.creation")),
            Aggregates.skip(skip > 0 ? skip : 0),
            Aggregates.limit(limit > 0 ? limit : 0)
        );

        AggregateIterable<Document> aggregatedResults = collection.aggregate(aggregationPipeline);

        List<EntityNotification> notifications = new ArrayList<>();
        aggregatedResults.forEach((Block<? super Document>) doc -> {
            Document notificationDoc = (Document) doc.get("notification");
            notifications.add(Manager.fromDocument(notificationDoc, EntityNotification.class));
        });

        return notifications;
    }
    
    public static List<EntityNotification> loadEntityNotificationListByUserId(ObjectId userId, int skip, int limit) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("entity_notification");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true),
                                eq("userId", userId)
                        )
                )
                .skip(skip > 0 ? skip : 0)
                .limit(limit > 0 ? limit : 0)
                .sort(orderBy(descending("creation")));

        return Manager.fromDocumentList(list, EntityNotification.class);
    }
    
    public static List<EntityNotification> loadEntityNotificationListByUserIdAndDateRangeForNotify(ObjectId userId, Date from, Date to) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        if (from == null) {
            throw new InvalidParameterException("empty from");
        }
        if (to == null) {
            throw new InvalidParameterException("empty to");
        }
        
        from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
        to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("entity_notification");
        FindIterable<Document> list = collection
            .find(
                and(
                    ne("cancelled", true),
                    eq("userId", userId),
                    gte("date", from),
                    lte("date", to),
                    eq("mailActive", true),
                    ne("isSentMail", true),
                    exists("lat"),
                    exists("lng")
                    
                )
            )
            .sort(orderBy(descending("creation")));

        return Manager.fromDocumentList(list, EntityNotification.class);
    }
    
    public static ObjectId insertEntityNotification(EntityNotification entityNotification) throws Exception {
        if (entityNotification == null) {
            throw new InvalidParameterException("empty entityNotification");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        entityNotification.setCreation(now);
        entityNotification.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("entity_notification");
        Document doc = Manager.toDocument(entityNotification);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void insertEntityNotificationList(List<EntityNotification> entityNotificationList) throws Exception {
        if (entityNotificationList == null) {
            throw new InvalidParameterException("empty entityNotificationList");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("entity_notification");
        List<Document> result = new ArrayList<>();
        entityNotificationList.stream().forEach((object) -> {
            result.add(Manager.toDocument(object));
        });
        collection.insertMany(result);
    }

    public static void updateEntityNotification(EntityNotification entityNotification) throws Exception {
        if (entityNotification == null) {
            throw new InvalidParameterException("empty entityNotification");
        }

        // defaults
        Date now = new Date();
        
        // internals
        entityNotification.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("entity_notification");
        collection.replaceOne(
                new Document("_id", entityNotification.getId()),
                Manager.toDocument(entityNotification)
        );
        
    }
    
    public static void updateEntityNotificationsSent(List<ObjectId> enetiyNotifcationIds) throws Exception {
        if (enetiyNotifcationIds == null || enetiyNotifcationIds.isEmpty()) {
            throw new InvalidParameterException("empty entityNotification");
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("entity_notification");

        // defaults
        Date now = new Date();
        
        // Crea un filtro per selezionare i documenti da aggiornare
        Document filter = new Document("_id", new Document("$in", enetiyNotifcationIds));

        // Crea l'aggiornamento per impostare isSentMail a true
        Document update = new Document("$set", new Document()
            .append("isSentMail", true)
            .append("lastUpdate", now)
            .append("sendDate", now));
        
        // Esegui l'aggiornamento multiplo
        collection.updateMany(filter, update);

        
    }

    public static void updatePageFollowerListCancelledByEntityIdAndUserId(ObjectId entityId, ObjectId customerId, boolean cancelled) throws Exception {
        if (entityId == null) {
            throw new InvalidParameterException("empty entityId");
        }
        
        Bson query = and(eq("entityId", entityId),eq("customerId", customerId));
        Bson updates = Updates.combine(
        Updates.set("cancelled", cancelled),
        Updates.set("lastUpdate", new Date()));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_follower");
        collection.updateMany(query, updates);
        
    }

    public static void updateEntityNotificationAllRead(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        // defaults
        Date now = new Date();
        
        Bson query = and(eq("userId", userId), ne("cancelled", true), ne("isRead", true));
        Bson updates = Updates.combine(
        Updates.set("isRead", true),
        Updates.set("read", now),
        Updates.set("lastUpdate", new Date()));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("entity_notification");
        collection.updateMany(query, updates);

    }    
    
    public static void updateEntityNotificationRead(ObjectId entityNotificationId) throws Exception {
        if (entityNotificationId == null) {
            throw new InvalidParameterException("empty entityNotificationId");
        }

        // defaults
        Date now = new Date();

        // update
        EntityNotification entityNotification = loadEntityNotification(entityNotificationId);
        entityNotification.setIsRead(true);
        entityNotification.setReadDate(now);

        // internals
        entityNotification.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("entity_notification");
        collection.replaceOne(
                new Document("_id", entityNotification.getId()),
                Manager.toDocument(entityNotification)
        );

    }
        
}
