package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.Page;
import com.agora.pojo.PageNotification;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import com.mongodb.client.model.Updates;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class PageNotificationDao {

    public static PageNotification loadPageNotification(ObjectId pageFollowerId) throws Exception {
        if (pageFollowerId == null) {
            throw new InvalidParameterException("empty pageFollowerId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_notification");
        Document doc = collection.find(eq("_id", pageFollowerId)).first();
        return Manager.fromDocument(doc, PageNotification.class);
    }
    
    public static long loadPageNotificationCount(ObjectId pageId) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }

        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_notification");
        long count = collection.count(
                and(
                        ne("cancelled", true),
                        eq("pageId", pageId)
                )
        );

        Page page = PageDao.loadPage(pageId);
        if (page != null) {
            if (page.getFollowers() != null) {
                count += page.getFollowers();
            }
        }
        
        return count;
    }
    
    public static long loadPageFollowedCount(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_notification");
        long count = collection.count(
                and(
                        ne("cancelled", true),
                        eq("userId", userId)
                )
        );
        
        return count;
    }

    public static PageNotification loadPageNotificationByUserAndPage(ObjectId userId, ObjectId pageId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_notification");
        Document doc = collection.find(
                and(
                        ne("cancelled", true),
                        eq("userId", userId),
                        eq("pageId", pageId)
                )
        ).first();
        return Manager.fromDocument(doc, PageNotification.class);
    }

    public static boolean hasPageNotificationByUserAndPage(ObjectId userId, List<ObjectId> pageIds) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        if (pageIds == null || pageIds.isEmpty()) {
            throw new InvalidParameterException("empty pageIds");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_notification");
        long count = collection.countDocuments(
            and(
                    ne("cancelled", true),
                    eq("userId", userId),
                    in("pageId", pageIds)
            )
        );
        return count > 0;
    }

    public static List<PageNotification> loadPageNotificationList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_notification");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true)
                        )
                )
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, PageNotification.class);
    }

    public static List<PageNotification> loadPageNotificationListByPage(ObjectId pageId) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_notification");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true),
                                eq("pageId", pageId)
                        )
                )
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, PageNotification.class);
    }
    
    public static List<PageNotification> loadPageNotificationListByUser(ObjectId userId) throws Exception {
        return loadPageNotificationListByUser(userId, 0, 0);
    }

    public static List<PageNotification> loadPageNotificationListByUser(ObjectId userId, int skip, int limit) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_notification");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true),
                                eq("userId", userId)
                        )
                )
                .sort(orderBy(ascending("creation")))
                .skip(skip)
                .limit(limit);
        return Manager.fromDocumentList(list, PageNotification.class);
    }

    public static ObjectId insertPageNotification(PageNotification pageFollower) throws Exception {
        if (pageFollower == null) {
            throw new InvalidParameterException("empty pageFollower");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        pageFollower.setCreation(now);
        pageFollower.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_notification");
        Document doc = Manager.toDocument(pageFollower);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updatePageNotification(PageNotification pageFollower) throws Exception {
        if (pageFollower == null) {
            throw new InvalidParameterException("empty pageFollower");
        }

        // defaults
        Date now = new Date();
        
        // internals
        pageFollower.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_notification");
        collection.replaceOne(
                new Document("_id", pageFollower.getId()),
                Manager.toDocument(pageFollower)
        );
        
    }

    public static void updatePageNotificationCancelled(ObjectId pageFollowerId, boolean cancelled) throws Exception {
        if (pageFollowerId == null) {
            throw new InvalidParameterException("empty pageFollowerId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        PageNotification pageFollower = loadPageNotification(pageFollowerId);
        pageFollower.setCancelled(cancelled);
        
        // internals
        pageFollower.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_notification");
        collection.replaceOne(
                new Document("_id", pageFollower.getId()),
                Manager.toDocument(pageFollower)
        );
        
    }
    
    public static void updatePageNotificationListCancelledByPageId(ObjectId pageId, boolean cancelled) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }
        
        Bson query = eq("pageId", pageId);
        Bson updates = Updates.combine(
        Updates.set("cancelled", cancelled),
        Updates.set("lastUpdate", new Date()));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_notification");
        collection.updateMany(query, updates);
        
    }
    
}
