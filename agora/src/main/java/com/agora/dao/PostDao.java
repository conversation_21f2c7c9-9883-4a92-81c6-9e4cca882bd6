package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.Post;
import com.agora.pojo.types.ImageType;
import com.agora.support.image.slim.SlimImage;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Accumulators.min;
import static com.mongodb.client.model.Accumulators.sum;
import static com.mongodb.client.model.Aggregates.group;
import static com.mongodb.client.model.Aggregates.match;
import static com.mongodb.client.model.Aggregates.project;
import static com.mongodb.client.model.Aggregates.sort;
import static com.mongodb.client.model.Aggregates.unwind;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Filters.nin;
import static com.mongodb.client.model.Filters.text;
import static com.mongodb.client.model.Projections.excludeId;
import static com.mongodb.client.model.Projections.fields;
import static com.mongodb.client.model.Projections.include;
import com.mongodb.client.model.Sorts;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.descending;
import static com.mongodb.client.model.Sorts.orderBy;
import com.agora.util.MongoUtils;
import com.agora.util.TimeUtils;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class PostDao {

    public static Post loadPost(ObjectId postId) throws Exception {
        if (postId == null) {
            throw new InvalidParameterException("empty postId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        Document doc = collection.find(eq("_id", postId)).first();
        return Manager.fromDocument(doc, Post.class);
    }

    public static List<Post> loadPostList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        FindIterable<Document> list = collection
                .find(ne("cancelled", true))
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, Post.class);
    }
    public static List<Post> loadSponsoredPostList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        FindIterable<Document> list = collection
                .find(and(ne("cancelled", true), eq("sponsored", true), eq("published", true)))
                .sort(orderBy(descending("creation")))
                .limit(3);
        return Manager.fromDocumentList(list, Post.class);
    }
    public static List<Post> loadEditorChoicePostList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        FindIterable<Document> list = collection
                .find(and(ne("cancelled", true), eq("editorChoice", true), eq("published", true)))
                .sort(orderBy(descending("creation")))
                .limit(9);
        return Manager.fromDocumentList(list, Post.class);
    }

    public static long loadPostCountByLanguage(String language) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        long count = collection.count(
                and(
                    eq("language", language),
                    ne("cancelled", true))
        );

        return count;
    }
    
    public static long loadPostCountBy(String[] categories, String text, String tag) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        
        List<Bson> filters = new ArrayList<>();
        
        filters.add(ne("cancelled", true));
        if ((categories != null) &&
                (categories.length > 0)) {
            filters.add(in("category", categories));
        }
        
        if (StringUtils.isNotBlank(tag)) {
            filters.add(eq("tags", tag));
        }
        
        filters.add(eq("published", true));

        if (StringUtils.isNotBlank(text)) {
            // based on text index "post_text_it"
            filters.add(text(text));

        }        
        
        long count = collection.count(
                and(and(filters))
        );

        return count;
    }

    public static List<Post> loadPostListBy(String[] categories, String text, String tag, int skip, int limit) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");

        List<Bson> filters = new ArrayList<>();

        filters.add(ne("cancelled", true));
        if ((categories != null) &&
                (categories.length > 0)) {
            filters.add(in("category", categories));
        }
        
        if (StringUtils.isNotBlank(tag)) {
            filters.add(eq("tags", tag));
        }
        
        filters.add(eq("published", true));

        if (StringUtils.isNotBlank(text)) {
            // based on text index "post_text_it"
            filters.add(text(text));

        }
        
        Bson sort = orderBy(descending("creation"));
        if (StringUtils.isNotBlank(text)) {
            sort = Sorts.metaTextScore("score");
        }
        
        FindIterable<Document> list;

        list = collection
                .find(and(filters))
                .skip(skip)
                .limit(limit > 0 ? limit : 0)
                .sort(sort);

        return Manager.fromDocumentList(list, Post.class);
    }
    
    public static List<Post> loadPostListWhereNotInCategories(String[] categories, int limit) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");

        List<Bson> filters = new ArrayList<>();

        filters.add(ne("cancelled", true));
        if ((categories != null) &&
                (categories.length > 0)) {
            filters.add(nin("category", categories));
        }
        
        FindIterable<Document> list;

        list = collection
                .find(and(filters))
                .limit(limit > 0 ? limit : 0);

        return Manager.fromDocumentList(list, Post.class);
    }

    
    public static List<Post> loadPostListByDateRange(Date from, Date to) throws Exception {
        if (from == null) {
            throw new InvalidParameterException("empty from");
        }
        if (to == null) {
            throw new InvalidParameterException("empty to");
        }
        
        from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
        to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        FindIterable<Document> list = collection.find(and(
                ne("cancelled", true),
                gte("creation", from),
                lte("creation", to))
        );
        
        return Manager.fromDocumentList(list, Post.class);
    }
    
    public static List<Post> loadSmilarPost(Post post) throws Exception {
        
        if (post == null) {
            throw new InvalidParameterException("empty post");
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        FindIterable<Document> list = collection.find(and(
                ne("cancelled", true),
                ne("_id", post.getId()),
                eq("category", post.getCategory())
        ))
        .sort(orderBy(descending("creation")))
        .limit(5);
        
        return Manager.fromDocumentList(list, Post.class);
    }
    
    public static List<Post> loadPostListByDateRangeAndUserId(Date from, Date to, ObjectId userId) throws Exception {
        if (from == null) {
            throw new InvalidParameterException("empty from");
        }
        if (to == null) {
            throw new InvalidParameterException("empty to");
        }
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        
        from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
        to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        FindIterable<Document> list = collection.find(and(
                eq("userId", userId),
                ne("cancelled", true),
                gte("creation", from),
                lte("creation", to))
        );
        
        return Manager.fromDocumentList(list, Post.class);
    }
    
    public static Post loadPostByIdentifier(String identifier) throws Exception {
        if (identifier == null) {
            throw new InvalidParameterException("empty identifier");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        Document doc = collection.find(eq("identifier", identifier)).first();
        return Manager.fromDocument(doc, Post.class);
    }

    public static ObjectId insertPost(Post post) throws Exception {
        if (post == null) {
            throw new InvalidParameterException("empty post");
        }

        // defaults
        Date now = new Date();

        // internals
        post.setCreation(now);
        post.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        Document doc = Manager.toDocument(post);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updatePost(Post post) throws Exception {
        if (post == null) {
            throw new InvalidParameterException("empty post");
        }

        // defaults
        Date now = new Date();

        // internals
        post.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        collection.replaceOne(
                new Document("_id", post.getId()),
                Manager.toDocument(post)
        );
    }

    public static void updatePostCancelled(ObjectId postId, boolean cancelled) throws Exception {
        if (postId == null) {
            throw new InvalidParameterException("empty postId");
        }

        // defaults
        Date now = new Date();

        // update
        Post post = loadPost(postId);
        post.setCancelled(cancelled);

        // internals
        post.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        collection.replaceOne(
                new Document("_id", post.getId()),
                Manager.toDocument(post)
        );

    }

    public static void updatePostImages(String username, ObjectId postId, List<SlimImage> images) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        if (postId == null) {
            throw new InvalidParameterException("empty postId");
        }
        if (images == null) {
            throw new InvalidParameterException("empty images");
        }
        if (images.isEmpty()) {
            throw new InvalidParameterException("empty image");
        }
        for (SlimImage image : images) {
            if (image.isEmpty()) {
                throw new InvalidParameterException("empty image bytes");
            }
        }

        // defaults
        Date now = new Date();

        List<ObjectId> imageIds = new ArrayList<>();
        for (SlimImage image : images) {
            String filename = ImageDao.composeFilename(ImageType.post, username, image.getExtension());
            String type = image.getType();

            // save image
            ObjectId imageId = ImageDao.insertImage(filename,
                    type,
                    image.getBytes()
            );

            if (imageId != null) {
                imageIds.add(imageId);
            }
        }
        if (imageIds.isEmpty()) {
            imageIds = null;
        }

        // update imageId
        Post post = loadPost(postId);
        post.setImageIds(imageIds);

        // internals
        post.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        collection.replaceOne(
                new Document("_id", post.getId()),
                Manager.toDocument(post)
        );

    }

    public static void removePostImages(ObjectId postId) throws Exception {
        if (postId == null) {
            throw new InvalidParameterException("empty postId");
        }

        // defaults
        Date now = new Date();

        // update
        Post post = loadPost(postId);
        post.setImageIds(null);

        // internals
        post.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        collection.replaceOne(
                new Document("_id", post.getId()),
                Manager.toDocument(post)
        );

    }

    public static List<String> loadPostCategoryList(String language) throws Exception {
        /*
            db.getCollection('post').aggregate(
                [
                    {
                        $group: {
                            _id : '$category',
                            category: { $min: '$category'},
                            count: { $sum: 1 }
                        }
                    },
                    { $project: { _id: 0, category: 1}},
                    { $sort : { category : 1 } }
                ]
            )
        */
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");

        List<Bson> filters = new ArrayList<>();

        filters.add(ne("cancelled", true));

        if (StringUtils.isNotBlank(language)) {
            filters.add(eq("language", language));
        }


        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                    match(and(filters)),
                    group("$category", min("category", "$category"), sum("count", 1)),
                    project(fields(excludeId(), include("category"))),
                    sort(orderBy(ascending("category")))
                ));

        List<String> items = new ArrayList<>();
        if (list != null) {
            for (Document document : list) {
                String value = document.getString("category");
                items.add(value);
            }
        }

        return !items.isEmpty() ? items : null;
    }

    public static List<String> loadPostTagList() throws Exception {
        /*
            db.getCollection('post').aggregate(
                [
                    {
                        $group: {
                            _id : '$tags',
                            category: { $min: '$tags'},
                            count: { $sum: 1 }
                        }
                    },
                    { $project: { _id: 0, tags: 1}},
                    { $sort : { tags : 1 } }
                ]
            )
        */
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");

        List<Bson> filters = new ArrayList<>();

        filters.add(ne("cancelled", true));

        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                    match(and(filters)),
                    unwind("$tags"),
                    group("$tags", sum("count", 1)),
                    project(fields(include("tags"))),
                    sort(orderBy(ascending("_id")))
                ));

        List<String> items = new ArrayList<>();
        if (list != null) {
            for (Document document : list) {
                String value = document.getString("_id");
                items.add(value);
            }
        }
        return !items.isEmpty() ? items : null;
    }

    public static List<String> loadPostTagListBy(String[] categories, String text) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");

        List<Bson> filters = new ArrayList<>();

        filters.add(ne("cancelled", true));
        if ((categories != null) &&
                (categories.length > 0)) {
            filters.add(in("category", categories));
        }

        filters.add(eq("published", true));

        if (StringUtils.isNotBlank(text)) {
            // based on text index "post_text_it"
            filters.add(text(text));

        }
        
        Bson sort = orderBy(descending("creation"));
        if (StringUtils.isNotBlank(text)) {
            sort = Sorts.metaTextScore("score");
        }

        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                    match(and(filters)),
                    unwind("$tags"),
                    group("$tags", sum("count", 1)),
                    project(fields(include("tags"))),
                    sort(orderBy(ascending("_id")))
                ));

        List<String> items = new ArrayList<>();
        if (list != null) {
            for (Document document : list) {
                String value = document.getString("_id");
                items.add(value);
            }
        }
        return !items.isEmpty() ? items : null;
    }
}
