package com.agora.dao;

import com.agora.pojo.Page;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import com.agora.core.Manager;
import com.agora.pojo.PageFollower;
import com.mongodb.client.model.Projections;
import com.mongodb.client.model.Updates;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class PageFollowerDao {

    public static PageFollower loadPageFollower(ObjectId pageFollowerId) throws Exception {
        if (pageFollowerId == null) {
            throw new InvalidParameterException("empty pageFollowerId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_follower");
        Document doc = collection.find(eq("_id", pageFollowerId)).first();
        return Manager.fromDocument(doc, PageFollower.class);
    }

    public static long loadPageFollowerCount(ObjectId pageId) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_follower");
        long count = collection.count(
                and(
                        ne("cancelled", true),
                        eq("pageId", pageId)
                )
        );

        Page page = PageDao.loadPage(pageId);
        if (page != null) {
            if (page.getFollowers() != null) {
                count += page.getFollowers();
            }
        }

        return count;
    }

    public static long loadPageFollowedCount(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_follower");
        long count = collection.count(
                and(
                        ne("cancelled", true),
                        eq("userId", userId)
                )
        );

        return count;
    }

    public static PageFollower loadPageFollowerByUserAndPage(ObjectId userId, ObjectId pageId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_follower");
        Document doc = collection.find(
                and(
                        ne("cancelled", true),
                        eq("userId", userId),
                        eq("pageId", pageId)
                )
        ).first();
        return Manager.fromDocument(doc, PageFollower.class);
    }

    public static List<PageFollower> loadPageFollowerList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_follower");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true)
                        )
                )
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, PageFollower.class);
    }

    public static List<PageFollower> loadPageFollowerListByUser(ObjectId userId) throws Exception {
        return loadPageFollowerListByUser(userId, 0, 0);
    }

    public static List<PageFollower> loadPageFollowerListByUser(ObjectId userId, int skip, int limit) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_follower");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true),
                                eq("userId", userId)
                        )
                )
                .sort(orderBy(ascending("creation")))
                .skip(skip)
                .limit(limit);
        return Manager.fromDocumentList(list, PageFollower.class);
    }

    public static List<ObjectId> loadPageFollowedIdListByUser(ObjectId userId) throws Exception {
        return loadPageFollowedIdListByUser(userId, 0, 0);
    }

    public static List<ObjectId> loadPageFollowedIdListByUser(ObjectId userId, int skip, int limit) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_follower");

        return collection
                .find(
                        and(
                                ne("cancelled", true),
                                eq("userId", userId)
                        )
                )
                .projection(Projections.include("pageId"))
                .skip(skip)
                .limit(limit)
                .map(doc -> doc.getObjectId("pageId")) // Extract "_id" from each document
                .into(new ArrayList<>());
    }

    public static List<PageFollower> loadPageFollowerListByPage(ObjectId pageId) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_follower");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true),
                                eq("pageId", pageId)
                        )
                )
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, PageFollower.class);
    }

    public static ObjectId insertPageFollower(PageFollower pageFollower) throws Exception {
        if (pageFollower == null) {
            throw new InvalidParameterException("empty pageFollower");
        }

        // defaults
        Date now = new Date();

        // internals
        pageFollower.setCreation(now);
        pageFollower.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_follower");
        Document doc = Manager.toDocument(pageFollower);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updatePageFollower(PageFollower pageFollower) throws Exception {
        if (pageFollower == null) {
            throw new InvalidParameterException("empty pageFollower");
        }

        // defaults
        Date now = new Date();

        // internals
        pageFollower.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_follower");
        collection.replaceOne(
                new Document("_id", pageFollower.getId()),
                Manager.toDocument(pageFollower)
        );

    }

    public static void updatePageFollowerCancelled(ObjectId pageFollowerId, boolean cancelled) throws Exception {
        if (pageFollowerId == null) {
            throw new InvalidParameterException("empty pageFollowerId");
        }

        // defaults
        Date now = new Date();

        // update
        PageFollower pageFollower = loadPageFollower(pageFollowerId);
        pageFollower.setCancelled(cancelled);

        // internals
        pageFollower.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_follower");
        collection.replaceOne(
                new Document("_id", pageFollower.getId()),
                Manager.toDocument(pageFollower)
        );

    }

    public static void updatePageFollowerListCancelledByPageId(ObjectId pageId, boolean cancelled) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }

        Bson query = eq("pageId", pageId);
        Bson updates = Updates.combine(
                Updates.set("cancelled", cancelled),
                Updates.set("lastUpdate", new Date()));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("page_follower");
        collection.updateMany(query, updates);

    }

}
