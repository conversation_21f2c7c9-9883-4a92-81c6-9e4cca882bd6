package com.agora.dao;

import com.github.slugify.Slugify;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import com.agora.core.Manager;
import com.agora.pojo.StableImage;
import com.agora.pojo.types.ImageType;
import com.agora.pojo.types.StableImageType;
import com.agora.util.RouteUtils;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.List;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tika.Tika;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class StableImageDao {

    public static StableImage loadStableImage(ObjectId stableImageId) throws Exception {
        if (stableImageId == null) {
            throw new InvalidParameterException("empty stableImageId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("stable_image");
        Document doc = collection.find(eq("_id", stableImageId)).first();
        return Manager.fromDocument(doc, StableImage.class);
    }

    public static List<StableImage> loadStableImageList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("stable_image");
        FindIterable<Document> list = collection
                .find(ne("cancelled", true))
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, StableImage.class);
    }
    
    public static StableImage loadStableImageByIdentifier(String identifier) throws Exception {
        if (identifier == null) {
            throw new InvalidParameterException("empty identifier");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("stable_image");
        Document doc = collection.find(eq("identifier", identifier)).first();
        return Manager.fromDocument(doc, StableImage.class);
    }
    
//    public static ObjectId insertStableImage(StableImage stableImage) throws Exception {
//        if (stableImage == null) {
//            throw new InvalidParameterException("empty stableImage");
//        }
//
//        // defaults
//        Date now = new Date();
//
//        // internals
//        stableImage.setCreation(now);
//        stableImage.setLastUpdate(now);
//
//        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("stable_image");
//        Document doc = Manager.toDocument(stableImage);
//        collection.insertOne(doc);
//        return doc.get("_id", ObjectId.class);
//    }

    public static ObjectId insertStableImage(StableImageType type, String username, String originalFilename, byte[] bytes) throws Exception {
        if (type == null) {
            throw new InvalidParameterException("empty bytes");
        }
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        if (StringUtils.isBlank(originalFilename)) {
            throw new InvalidParameterException("empty originalFilename");
        }
        if (bytes == null) {
            throw new InvalidParameterException("empty bytes");
        }
        if (bytes.length == 0) {
            throw new InvalidParameterException("empty bytes");
        }
        
        // mimetype
        Tika tika = new Tika();
        String mimetype = tika.detect(bytes, FilenameUtils.getName(originalFilename));
        
        // filename (internal)
        String filename = ImageDao.composeFilename(ImageType.post, username, FilenameUtils.getExtension(originalFilename));

        // save image
        ObjectId imageId = ImageDao.insertImage(filename,
                mimetype,
                bytes
        );

        // identifier
        Slugify slg = new Slugify();
        String identifier = originalFilename + "-" + RouteUtils.generateIdentifier();
        identifier = slg.slugify(identifier);
        
        // post image
        StableImage stableImage = new StableImage();

        // defaults
        Date now = new Date();
        
        // internals
        stableImage.setCreation(now);
        stableImage.setLastUpdate(now);
        
        stableImage.setType(type.toString());
        stableImage.setFilename(filename);
        stableImage.setOriginalFilename(originalFilename);
        stableImage.setIdentifier(identifier);
        stableImage.setContentType(mimetype);
        stableImage.setImageId(imageId);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("stable_image");
        Document doc = Manager.toDocument(stableImage);
        collection.insertOne(doc);
        return imageId;
    }
    
    public static void updateStableImage(StableImage stableImage) throws Exception {
        if (stableImage == null) {
            throw new InvalidParameterException("empty stableImage");
        }

        // defaults
        Date now = new Date();

        // internals
        stableImage.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("stable_image");
        collection.replaceOne(
                new Document("_id", stableImage.getId()),
                Manager.toDocument(stableImage)
        );
    }

    public static void updateStableImageCancelled(ObjectId stableImageId, boolean cancelled) throws Exception {
        if (stableImageId == null) {
            throw new InvalidParameterException("empty stableImageId");
        }

        // defaults
        Date now = new Date();

        // update
        StableImage stableImage = loadStableImage(stableImageId);
        stableImage.setCancelled(cancelled);

        // internals
        stableImage.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("stable_image");
        collection.replaceOne(
                new Document("_id", stableImage.getId()),
                Manager.toDocument(stableImage)
        );

    }

}
