package com.agora.dao;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import com.agora.core.Manager;
import com.agora.pojo.Label;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class LabelDao {

    public static List<Label> loadLabelList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("label");
        FindIterable<Document> list = collection
                .find()
                .sort(orderBy(ascending("key")));
        return Manager.fromDocumentList(list, Label.class);
    }

    public static ObjectId insertLabel(Label label) throws Exception {
        if (label == null) {
            throw new InvalidParameterException("empty label");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        label.setCreation(now);
        label.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("label");
        Document doc = Manager.toDocument(label);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }    
    
    public static void removeLabels() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("label");
        collection.drop();
    }
    
}
