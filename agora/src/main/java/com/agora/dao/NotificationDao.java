package com.agora.dao;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.descending;
import static com.mongodb.client.model.Sorts.orderBy;
import com.agora.core.Manager;
import com.agora.pojo.Notification;
import static com.mongodb.client.model.Filters.and;
import com.mongodb.client.model.Updates;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class NotificationDao {

    public static Notification loadNotification(ObjectId notificationId) throws Exception {
        if (notificationId == null) {
            throw new InvalidParameterException("empty notificationId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("notification");
        Document doc = collection.find(eq("_id", notificationId)).first();
        return Manager.fromDocument(doc, Notification.class);
    }

    public static List<Notification> loadNotificationList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("notification");
        FindIterable<Document> list = collection
                .find(ne("cancelled", true))
                .sort(orderBy(descending("startDate")));
        return Manager.fromDocumentList(list, Notification.class);
    }

    public static ObjectId insertNotification(Notification notification) throws Exception {
        if (notification == null) {
            throw new InvalidParameterException("empty notification");
        }

        // defaults
        Date now = new Date();

        // internals
        notification.setCreation(now);
        notification.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("notification");
        Document doc = Manager.toDocument(notification);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateNotification(Notification notification) throws Exception {
        if (notification == null) {
            throw new InvalidParameterException("empty notification");
        }

        // defaults
        Date now = new Date();

        // internals
        notification.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("notification");
        collection.replaceOne(
                new Document("_id", notification.getId()),
                Manager.toDocument(notification)
        );
    }
    
}
