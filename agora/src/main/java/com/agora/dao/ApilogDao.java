package com.agora.dao;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Sorts.descending;
import static com.mongodb.client.model.Sorts.orderBy;
import com.agora.core.Manager;
import com.agora.pojo.Apilog;
import com.agora.util.MongoUtils;
import com.agora.util.TimeUtils;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class ApilogDao {

    public static List<Apilog> loadApilogListByDateRange(Date from, Date to) throws Exception {
        if (from == null) {
            throw new InvalidParameterException("empty from");
        }
        if (to == null) {
            throw new InvalidParameterException("empty to");
        }
        
        from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
        to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("apilog");
        FindIterable<Document> list = collection
                .find(and(
                        gte("creation", from),
                        lte("creation", to)))
                .sort(orderBy(descending("creation")));
        return Manager.fromDocumentList(list, Apilog.class);
    }
    
    public static Apilog loadApilog(ObjectId apilogId) throws Exception {
        if (apilogId == null) {
            throw new InvalidParameterException("empty apilogId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("apilog");
        Document doc = collection.find(eq("_id", apilogId)).first();
        return Manager.fromDocument(doc, Apilog.class);
    }
    
    public static ObjectId insertApilog(Apilog apilog) throws Exception {
        if (apilog == null) {
            throw new InvalidParameterException("empty apilog");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        apilog.setCreation(now);
        apilog.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("apilog");
        Document doc = Manager.toDocument(apilog);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateApilog(Apilog apilog) throws Exception {
        if (apilog == null) {
            throw new InvalidParameterException("empty apilog");
        }

        // defaults
        Date now = new Date();
        
        // internals
        apilog.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("apilog");
        collection.replaceOne(
                new Document("_id", apilog.getId()),
                Manager.toDocument(apilog)
        );
        
    }

}
