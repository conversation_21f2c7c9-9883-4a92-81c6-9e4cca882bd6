package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.Province;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.regex;
import java.security.InvalidParameterException;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.util.ArrayList;
import java.util.regex.Pattern;
import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class ProvinceDao {

    public static List<Province> loadProvinceList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("province");
        FindIterable<Document> list = collection
                .find()
                .sort(orderBy(ascending("description")));
        return Manager.fromDocumentList(list, Province.class);
    }
    
    public static Province loadProvince(String code) throws Exception {
        if (StringUtils.isBlank(code)) {
            throw new InvalidParameterException("empty code");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("province");
        Document doc = collection.find(regex("code", Pattern.compile(code, Pattern.CASE_INSENSITIVE + Pattern.UNICODE_CASE))).first();
        return Manager.fromDocument(doc, Province.class);
    }

    public static Province loadProvinceByDescription(String description) throws Exception {
        if (description == null) {
            throw new InvalidParameterException("empty description");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("province");
        
        List<Bson> filters = new ArrayList<>();
        
        if (StringUtils.isNotBlank(description)) {
            filters.add(regex("description", Pattern.compile(description, Pattern.CASE_INSENSITIVE + Pattern.UNICODE_CASE)));
        }
                
        Document doc = collection.find(and(filters)).first();
        return Manager.fromDocument(doc, Province.class);
    }
    
    public static List<Province> loadProvinceListBy(String province, int skip, int limit) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("province");
        
        List<Bson> filters = new ArrayList<>();
        
        if (StringUtils.isNotBlank(province)) {
            filters.add(regex("description", Pattern.compile("^"+ province, Pattern.CASE_INSENSITIVE + Pattern.UNICODE_CASE)));
        }
        
        FindIterable<Document> list = collection
                .find(and(filters))
                .sort(orderBy(ascending("description")))
                .skip(skip)
                .limit(limit > 0 ? limit : 0);
        return Manager.fromDocumentList(list, Province.class);
    }   
    
}
