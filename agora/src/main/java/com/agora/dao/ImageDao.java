package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.DocumentDescriptor;
import com.agora.pojo.types.ImageType;
import com.agora.support.image.Image;
import com.agora.util.TimeUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.gridfs.GridFSBucket;
import com.mongodb.client.gridfs.GridFSBuckets;
import com.mongodb.client.gridfs.GridFSDownloadStream;
import com.mongodb.client.gridfs.GridFSFindIterable;
import com.mongodb.client.gridfs.model.GridFSFile;
import com.mongodb.client.gridfs.model.GridFSUploadOptions;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.regex;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class ImageDao {

    public static String composeFilename(ImageType type, String username, String extension) throws Exception {
        if (type == null) {
            throw new InvalidParameterException("empty type");
        }
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        // extension is optional

        // defaults
        Date now = new Date();
        if (StringUtils.isNotBlank(extension)) {
            extension = "." + extension;
        }

        return type + "-" + username + "-" + TimeUtils.toString(now, "yyyyMMdd") + "-" + UUID.randomUUID().toString() + (StringUtils.isNotBlank(extension) ? extension : "");
    }

    public static String composeFilenameV2(ImageType type, String filename, String extension) {
        if (type == null) {
            throw new InvalidParameterException("empty type");
        }
        // extension is optional

        // defaults
        Date now = new Date();
        if (StringUtils.isNotBlank(extension)) {
            extension = "." + extension;
        }
        return type + "-" + sanitizeFilename(filename) + "-" + TimeUtils.toString(now, "yyyyMMdd-hhmmssSSS") + (StringUtils.isNotBlank(extension) ? extension : "");
    }

    public static List<Image> loadImageList() throws Exception {
        String name;
        String contentType;
        int length;
        byte[] bytes;

        List<Image> imageList = new ArrayList<>();
        GridFSBucket bucket = GridFSBuckets.create(Manager.mongoDatabase, "image");
        GridFSFindIterable allImages = bucket.find();
        try ( MongoCursor<GridFSFile> iterator = allImages.iterator()) {
            while (iterator.hasNext()) {
                GridFSFile next = iterator.next();
                try ( GridFSDownloadStream stream = bucket.openDownloadStream(next.getObjectId())) {
                    name = stream.getGridFSFile().getFilename();
                    contentType = stream.getGridFSFile().getMetadata().getString("contentType");
                    length = (int) stream.getGridFSFile().getLength();
                }
                try ( ByteArrayOutputStream out = new ByteArrayOutputStream(length)) {
                    bucket.downloadToStream(next.getObjectId(), out);
                    bytes = out.toByteArray();
                    imageList.add(new Image(name, contentType, bytes));
                }
            }
        }

        return imageList;
    }

    public static List<ObjectId> loadProductImageObjectIdList() throws Exception {
        List<ObjectId> imageList = new ArrayList<>();
        GridFSBucket bucket = GridFSBuckets.create(Manager.mongoDatabase, "image");
        GridFSFindIterable allImages = bucket.find(regex("filename", Pattern.compile("^product-", Pattern.CASE_INSENSITIVE + Pattern.UNICODE_CASE)));
        try ( MongoCursor<GridFSFile> iterator = allImages.iterator()) {
            while (iterator.hasNext()) {
                GridFSFile next = iterator.next();
                imageList.add(next.getObjectId());
            }
        }

        return imageList;
    }

    public static Image loadImage(ObjectId oid) throws Exception {
        if (oid == null) {
            throw new InvalidParameterException("empty oid");
        }

        String name;
        String contentType;
        int length;
        byte[] bytes;

        GridFSBucket bucket = GridFSBuckets.create(Manager.mongoDatabase, "image");
        GridFSFindIterable iterable = bucket.find(eq("_id", oid));
        if (iterable.first() != null) {
            try ( GridFSDownloadStream stream = bucket.openDownloadStream(oid)) {
                name = stream.getGridFSFile().getFilename();
                contentType = stream.getGridFSFile().getMetadata().getString("contentType");
                length = (int) stream.getGridFSFile().getLength();
            }
            try ( ByteArrayOutputStream out = new ByteArrayOutputStream(length)) {
                bucket.downloadToStream(oid, out);
                bytes = out.toByteArray();
            }
            return new Image(name, contentType, bytes);
        } else {
            return null;
        }

    }

    public static Image loadImageV2(ObjectId oid) throws Exception {
        if (oid == null) {
            throw new InvalidParameterException("empty oid");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("documentdescriptor");
        Document doc = collection.find(eq("_id", oid)).first();
        DocumentDescriptor document = Manager.fromDocument(doc, DocumentDescriptor.class);
        if (document != null) {
            File tmpFile = new File(document.getFilePath());
            return new Image(document.getFilename(), document.getMetadata().get("originalFilename"), document.getMetadata().get("contentType"), FileUtils.readFileToByteArray(tmpFile), document.getWidth(), document.getHeight());
        } else {
            return null;
        }
    }

    public static Image loadThumbnail(String filename) throws Exception {
        if (StringUtils.isBlank(filename)) {
            throw new InvalidParameterException("empty filename");
        }

        String name;
        String contentType;
        int length;
        byte[] bytes;

        GridFSBucket bucket = GridFSBuckets.create(Manager.mongoDatabase, "thumbnail");
        try ( GridFSDownloadStream stream = bucket.openDownloadStream(filename)) {
            name = stream.getGridFSFile().getFilename();
            contentType = stream.getGridFSFile().getMetadata().getString("contentType");
            length = (int) stream.getGridFSFile().getLength();
        }
        try ( ByteArrayOutputStream out = new ByteArrayOutputStream(length)) {
            bucket.downloadToStream(filename, out);
            bytes = out.toByteArray();
        }

        return new Image(name, contentType, bytes);
    }

    public static ObjectId insertImage(String filename, String type, byte[] bytes) throws Exception {
        if (StringUtils.isBlank(filename)) {
            throw new InvalidParameterException("empty filename");
        }
        if (StringUtils.isBlank(type)) {
            throw new InvalidParameterException("empty type");
        }
        if (bytes == null) {
            throw new InvalidParameterException("empty bytes");
        }

        GridFSBucket bucket = GridFSBuckets.create(Manager.mongoDatabase, "image");
        GridFSUploadOptions options = new GridFSUploadOptions()
                .metadata(new Document("contentType", type));
        ByteArrayInputStream data = new ByteArrayInputStream(bytes);
        ObjectId oid = bucket.uploadFromStream(filename, data, options);
        IOUtils.closeQuietly(data);

        return oid;
    }

    public static ObjectId insertImageV2(File file, String originalFilename, String type, Integer width, Integer height) throws Exception {
        if (file == null) {
            throw new InvalidParameterException("empty file");
        }
        if (StringUtils.isBlank(originalFilename)) {
            throw new InvalidParameterException("empty originalFilename");
        }
        if (StringUtils.isBlank(type)) {
            throw new InvalidParameterException("empty type");
        }

        Map<String, String> metadata = new HashMap<>();
        metadata.put("contentType", type);
        metadata.put("originalFilename", originalFilename);

        Date now = new Date();
        DocumentDescriptor document = new DocumentDescriptor();
        document.setFilename(file.getName());
        document.setFilePath(file.getPath());
        document.setMetadata(metadata);
        document.setWidth(width);
        document.setHeight(height);
        document.setCreation(now);
        document.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("documentdescriptor");
        Document doc = Manager.toDocument(document);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static ObjectId insertThumbnail(String filename, String type, byte[] bytes) throws Exception {
        if (StringUtils.isBlank(filename)) {
            throw new InvalidParameterException("empty filename");
        }
        if (StringUtils.isBlank(type)) {
            throw new InvalidParameterException("empty type");
        }
        if (bytes == null) {
            throw new InvalidParameterException("empty bytes");
        }

        GridFSBucket bucket = GridFSBuckets.create(Manager.mongoDatabase, "thumbnail");
        GridFSUploadOptions options = new GridFSUploadOptions()
                .metadata(new Document("contentType", type));
        ByteArrayInputStream data = new ByteArrayInputStream(bytes);
        ObjectId oid = bucket.uploadFromStream(filename, data, options);
        IOUtils.closeQuietly(data);

        return oid;
    }

    public static void deleteImage(ObjectId oid) {
        if (oid == null) {
            throw new InvalidParameterException("empty oid");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("documentdescriptor");
        Document doc = collection.find(eq("_id", oid)).first();
        DocumentDescriptor document = Manager.fromDocument(doc, DocumentDescriptor.class);
        if (document != null) {
            File tmpFile = new File(document.getFilePath());
            if (tmpFile.exists()) {
                tmpFile.delete();
            }
            collection.deleteOne(eq("_id", oid));
        }
    }

    ////////////
    // internals
    private static String sanitizeFilename(String name) {
        return StringUtils.replace(StringUtils.replacePattern(name, "[\\\\/:*?\"<>|]", ""), " ", "_");
    }

}
