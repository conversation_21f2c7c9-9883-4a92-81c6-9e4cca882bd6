package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.Country;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.regex;
import static com.mongodb.client.model.Sorts.ascending;
import java.security.InvalidParameterException;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import static com.mongodb.client.model.Sorts.orderBy;
import java.util.regex.Pattern;
import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class CountryDao {

    public static List<Country> loadCountryList(String language) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("country");
        
        Bson sort = orderBy(ascending("sorting"), ascending("description"));
        if (StringUtils.equalsIgnoreCase(language, "en")) {
            sort = orderBy(ascending("sorting"), ascending("descriptionEnglish"));
        }
        
        FindIterable<Document> list = collection
                .find()
                .sort(sort);
        return Manager.fromDocumentList(list, Country.class);
    }
    
    public static Country loadCountry(String code) throws Exception {
        if (StringUtils.isBlank(code)) {
            throw new InvalidParameterException("empty code");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("country");
        Document doc = collection.find(regex("code", Pattern.compile(code, Pattern.CASE_INSENSITIVE + Pattern.UNICODE_CASE))).first();
        return Manager.fromDocument(doc, Country.class);
    }

    public static Country loadCountryByDescription(String description) throws Exception {
        if (description == null) {
            throw new InvalidParameterException("empty description");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("country");
        Document doc = collection.find(eq("description", description)).first();
        return Manager.fromDocument(doc, Country.class);
    }

}
