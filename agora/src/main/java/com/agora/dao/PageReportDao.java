package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.PageReport;
import com.agora.util.MongoUtils;
import com.agora.util.TimeUtils;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Sorts.descending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class PageReportDao {
 
    public static List<PageReport> loadPageReportListByDateRange(Date from, Date to) throws Exception {
        List<Bson> filters = new ArrayList<>();
        
        if (from != null) {
            from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
            filters.add(gte("creation", from));
        }
        if (to != null) {
            to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));
            filters.add(lte("creation", to));
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("pagereport");
        FindIterable<Document> list = collection
                .find(and(filters))
                .sort(orderBy(descending("creation")));
        return Manager.fromDocumentList(list, PageReport.class);
    }
    
    public static PageReport loadPageReport(ObjectId pagereportId) throws Exception {
        if (pagereportId == null) {
            throw new InvalidParameterException("empty pagereportId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("pagereport");
        Document doc = collection.find(eq("_id", pagereportId)).first();
        return Manager.fromDocument(doc, PageReport.class);
    }
    
    public static PageReport loadPageReportByPageIdAndUserId(ObjectId pageId, ObjectId userId) throws Exception {
        if (pageId == null) {
            throw new InvalidParameterException("empty pageId");
        }
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("pagereport");
        Document doc = collection.find(and(eq("pageId", pageId), eq("userId", userId))).first();
        return Manager.fromDocument(doc, PageReport.class);
    }
    
    public static ObjectId insertPageReport(PageReport pagereport) throws Exception {
        if (pagereport == null) {
            throw new InvalidParameterException("empty pagereport");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        pagereport.setCreation(now);
        pagereport.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("pagereport");
        Document doc = Manager.toDocument(pagereport);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updatePageReport(PageReport pagereport) throws Exception {
        if (pagereport == null) {
            throw new InvalidParameterException("empty pagereport");
        }

        // defaults
        Date now = new Date();
        
        // internals
        pagereport.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("pagereport");
        collection.replaceOne(
                new Document("_id", pagereport.getId()),
                Manager.toDocument(pagereport)
        );
        
    }
        
}
