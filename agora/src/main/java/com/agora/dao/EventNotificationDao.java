package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.EventNotification;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Aggregates;
import static com.mongodb.client.model.Aggregates.sample;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import com.mongodb.client.model.Updates;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class EventNotificationDao {

    public static EventNotification loadEventNotification(ObjectId eventFollowerId) throws Exception {
        if (eventFollowerId == null) {
            throw new InvalidParameterException("empty eventFollowerId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_notification");
        Document doc = collection.find(eq("_id", eventFollowerId)).first();
        return Manager.fromDocument(doc, EventNotification.class);
    }
    
    public static long loadEventNotificationCount(ObjectId eventId) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }

        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_notification");
        long count = collection.count(
                and(
                        ne("cancelled", true),
                        eq("eventId", eventId)
                )
        );
        
        return count;
    }
    
    public static List<EventNotification> loadEventNotificationExcludeOne(ObjectId eventId, int limit) throws Exception {
        return loadEventNotificationExcludeOne(eventId, null, limit);
    }
    
    public static List<EventNotification> loadEventNotificationExcludeOne(ObjectId eventId, ObjectId userId, int limit) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }

        List<Bson> filters = new ArrayList<>();
        
        filters.add(ne("cancelled", true));
        filters.add(eq("eventId", eventId));
        
        if (userId != null) {
            filters.add(eq("userId", userId));
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_notification");
        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                        Aggregates.match(and(filters)),
                        sample(limit)
                ));
        
        return Manager.fromAggregateList(list, EventNotification.class);
    }

    public static EventNotification loadEventNotificationByUserAndEvent(ObjectId userId, ObjectId eventId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_notification");
        Document doc = collection.find(
                and(
                        ne("cancelled", true),
                        eq("userId", userId),
                        eq("eventId", eventId)
                )
        ).first();
        return Manager.fromDocument(doc, EventNotification.class);
    }
    
    public static long loadEventNotificationCountByUserAndEvent(ObjectId userId, ObjectId eventId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_notification");
        long count = collection.count(
            and(
                    ne("cancelled", true),
                    eq("userId", userId),
                    eq("eventId", eventId)
            )
        );

        return count;
    }


    public static List<EventNotification> loadEventNotificationList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_notification");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true)
                        )
                )
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, EventNotification.class);
    }
    
    public static List<EventNotification> loadEventNotificationListByUser(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_notification");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true),
                                eq("userId", userId)
                        )
                )
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, EventNotification.class);
    }
    
    public static List<EventNotification> loadEventNotificationListByEvent(ObjectId eventId) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_notification");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true),
                                eq("eventId", eventId)
                        )
                )
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, EventNotification.class);
    }

    public static ObjectId insertEventNotification(EventNotification eventFollower) throws Exception {
        if (eventFollower == null) {
            throw new InvalidParameterException("empty eventFollower");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        eventFollower.setCreation(now);
        eventFollower.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_notification");
        Document doc = Manager.toDocument(eventFollower);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateEventNotification(EventNotification eventFollower) throws Exception {
        if (eventFollower == null) {
            throw new InvalidParameterException("empty eventFollower");
        }

        // defaults
        Date now = new Date();
        
        // internals
        eventFollower.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_notification");
        collection.replaceOne(
                new Document("_id", eventFollower.getId()),
                Manager.toDocument(eventFollower)
        );
        
    }

    public static void updateEventNotificationCancelled(ObjectId eventFollowerId, boolean cancelled) throws Exception {
        if (eventFollowerId == null) {
            throw new InvalidParameterException("empty eventFollowerId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        EventNotification eventFollower = loadEventNotification(eventFollowerId);
        eventFollower.setCancelled(cancelled);
        
        // internals
        eventFollower.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_notification");
        collection.replaceOne(
                new Document("_id", eventFollower.getId()),
                Manager.toDocument(eventFollower)
        );
        
    }

    public static void updateEventNotificationListCancelledByEventId(ObjectId eventId, boolean cancelled) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }
        
        Bson query = eq("eventId", eventId);
        Bson updates = Updates.combine(
        Updates.set("cancelled", cancelled),
        Updates.set("lastUpdate", new Date()));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_notification");
        collection.updateMany(query, updates);
        
    }
    
}
