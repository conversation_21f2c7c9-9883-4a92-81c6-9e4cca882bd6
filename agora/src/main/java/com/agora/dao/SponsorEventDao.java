package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.SponsorEvent;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;

import static com.mongodb.client.model.Filters.*;
import static com.mongodb.client.model.Sorts.*;

import java.security.InvalidParameterException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.time.DateUtils;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 * DAO for SponsorEvent operations
 * 
 * <AUTHOR> Agent
 */
public class SponsorEventDao {

    public static SponsorEvent loadSponsorEvent(ObjectId sponsorEventId) throws Exception {
        if (sponsorEventId == null) {
            throw new InvalidParameterException("empty sponsorEventId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("sponsor_event");
        Document doc = collection.find(eq("_id", sponsorEventId)).first();
        return Manager.fromDocument(doc, SponsorEvent.class);
    }

    public static List<SponsorEvent> loadSponsorEventList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("sponsor_event");
        FindIterable<Document> list = collection
                .find(ne("cancelled", true))
                .sort(orderBy(ascending("sort"), ascending("creation")));
        return Manager.fromDocumentList(list, SponsorEvent.class);
    }

    public static List<SponsorEvent> loadActiveSponsorEventList() throws Exception {
        Date now = new Date();
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("sponsor_event");
        FindIterable<Document> list = collection
                .find(new Document("cancelled", new Document("$ne", true))
                        .append("expirationDate", new Document("$gte", now)))
                .sort(orderBy(descending("sort"), ascending("creation")));
        return Manager.fromDocumentList(list, SponsorEvent.class);
    }

    public static ObjectId insertSponsorEvent(SponsorEvent sponsorEvent) throws Exception {
        if (sponsorEvent == null) {
            throw new InvalidParameterException("empty sponsorEvent");
        }

        // defaults
        Date now = new Date();

        // internals
        sponsorEvent.setCreation(now);
        sponsorEvent.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("sponsor_event");
        Document doc = Manager.toDocument(sponsorEvent);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateSponsorEvent(SponsorEvent sponsorEvent) throws Exception {
        if (sponsorEvent == null) {
            throw new InvalidParameterException("empty sponsorEvent");
        }

        // defaults
        Date now = new Date();

        // internals
        sponsorEvent.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("sponsor_event");
        collection.replaceOne(
                new Document("_id", sponsorEvent.getId()),
                Manager.toDocument(sponsorEvent)
        );
    }

    public static void removeSponsorEvent(ObjectId sponsorEventId) throws Exception {
        if (sponsorEventId == null) {
            throw new InvalidParameterException("empty sponsorEventId");
        }

        SponsorEvent sponsorEvent = loadSponsorEvent(sponsorEventId);
        if (sponsorEvent != null) {
            sponsorEvent.setCancelled(true);
            updateSponsorEvent(sponsorEvent);
        }
    }

    // function to delete sponsor events older more than 5 days
    public static void removeOldSponsorEvents() throws Exception {
        Date now = new Date();
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("sponsor_event");
        FindIterable<Document> list = collection
                .find(lt("expirationDate", DateUtils.addDays(now, -5)));
        List<SponsorEvent> sponsorEvents = Manager.fromDocumentList(list, SponsorEvent.class);

        if (sponsorEvents == null) {
            return;
        }
        for (SponsorEvent sponsorEvent : sponsorEvents) {
            sponsorEvent.setCancelled(true);
            updateSponsorEvent(sponsorEvent);
        }
    }
}
