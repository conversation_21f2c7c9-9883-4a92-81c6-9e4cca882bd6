package com.agora.dao;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Filters.regex;
import com.agora.core.Manager;
import com.agora.pojo.Vendor;
import com.agora.pojo.types.ImageType;
import com.agora.support.image.slim.SlimImage;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.util.regex.Pattern;

/**
 *
 * <AUTHOR>
 */
public class VendorDao {

    public static Vendor loadVendor(ObjectId vendorId) throws Exception {
        if (vendorId == null) {
            throw new InvalidParameterException("empty vendorId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("vendor");
        Document doc = collection.find(eq("_id", vendorId)).first();
        return Manager.fromDocument(doc, Vendor.class);
    }
    
    public static Vendor loadVendorByEmail(String email) throws Exception {
        if (email == null) {
            throw new InvalidParameterException("empty email");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("vendor");
        Document doc = collection
                .find(and(
                        ne("cancelled", true),
                        regex("email", Pattern.compile("(\\s|^)"+ email +"(\\s|$)", Pattern.CASE_INSENSITIVE))
                ))
                .first();
        return Manager.fromDocument(doc, Vendor.class);
    }
    
    public static Vendor loadVendorByUserId(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("vendor");
        Document doc = collection.find(eq("userId", userId)).first();
        return Manager.fromDocument(doc, Vendor.class);
    }
    
    public static List<Vendor> loadVendorList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("vendor");
        FindIterable<Document> list = collection
                .find(and(
                        ne("email", "<EMAIL>"),
                        ne("cancelled", true)))
                .sort(orderBy(ascending("lastname")));
        return Manager.fromDocumentList(list, Vendor.class);
    }
    
    public static ObjectId insertVendor(Vendor vendor) throws Exception {
        if (vendor == null) {
            throw new InvalidParameterException("empty vendor");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        vendor.setCreation(now);
        vendor.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("vendor");
        Document doc = Manager.toDocument(vendor);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateVendor(Vendor vendor) throws Exception {
        if (vendor == null) {
            throw new InvalidParameterException("empty vendor");
        }

        // defaults
        Date now = new Date();
        
        // internals
        vendor.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("vendor");
        collection.replaceOne(
                new Document("_id", vendor.getId()),
                Manager.toDocument(vendor)
        );
        
    }

    public static void updateVendorCancelled(ObjectId vendorId, boolean cancelled) throws Exception {
        if (vendorId == null) {
            throw new InvalidParameterException("empty vendorId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Vendor vendor = loadVendor(vendorId);
        vendor.setCancelled(cancelled);
        
        // internals
        vendor.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("vendor");
        collection.replaceOne(
                new Document("_id", vendor.getId()),
                Manager.toDocument(vendor)
        );
        
    }
    
    public static void updateVendorImage(String username, ObjectId vendorId, SlimImage image) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        if (vendorId == null) {
            throw new InvalidParameterException("empty vendorId");
        }
        if (image == null) {
            throw new InvalidParameterException("empty image");
        }
        if (image.isEmpty()) {
            throw new InvalidParameterException("empty image bytes");
        }
        
        // defaults
        Date now = new Date();
        String filename = ImageDao.composeFilename(ImageType.vendor, username, image.getExtension());
        String type = image.getType();
        
        // save image
        ObjectId imageId = ImageDao.insertImage(filename, 
                type, 
                image.getBytes()
        );
        
        // update imageId
        Vendor vendor = loadVendor(vendorId);
        vendor.setImageId(imageId);
        
        // internals
        vendor.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("vendor");
        collection.replaceOne(
                new Document("_id", vendor.getId()),
                Manager.toDocument(vendor)
        );
        
    }
    
    public static void removeVendorImage(ObjectId vendorId) throws Exception {
        if (vendorId == null) {
            throw new InvalidParameterException("empty vendorId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Vendor vendor = loadVendor(vendorId);
        vendor.setImageId(null);
        
        // internals
        vendor.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("vendor");
        collection.replaceOne(
                new Document("_id", vendor.getId()),
                Manager.toDocument(vendor)
        );
        
    }
    
}
