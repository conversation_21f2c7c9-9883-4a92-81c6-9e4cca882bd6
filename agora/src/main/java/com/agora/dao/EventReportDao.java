package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.EventReport;
import com.agora.util.MongoUtils;
import com.agora.util.TimeUtils;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Sorts.descending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class EventReportDao {
 
    public static List<EventReport> loadEventReportListByDateRange(Date from, Date to) throws Exception {
        List<Bson> filters = new ArrayList<>();
        
        if (from != null) {
            from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
            filters.add(gte("creation", from));
        }
        if (to != null) {
            to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));
            filters.add(lte("creation", to));
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eventreport");
        FindIterable<Document> list = collection
                .find(and(filters))
                .sort(orderBy(descending("creation")));
        return Manager.fromDocumentList(list, EventReport.class);
    }
    
    public static EventReport loadEventReport(ObjectId eventreportId) throws Exception {
        if (eventreportId == null) {
            throw new InvalidParameterException("empty eventreportId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eventreport");
        Document doc = collection.find(eq("_id", eventreportId)).first();
        return Manager.fromDocument(doc, EventReport.class);
    }
    
    public static EventReport loadEventReportByEventIdAndUserId(ObjectId eventId, ObjectId userId) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eventreport");
        Document doc = collection.find(and(eq("eventId", eventId), eq("userId", userId))).first();
        return Manager.fromDocument(doc, EventReport.class);
    }
    
    public static ObjectId insertEventReport(EventReport eventreport) throws Exception {
        if (eventreport == null) {
            throw new InvalidParameterException("empty eventreport");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        eventreport.setCreation(now);
        eventreport.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eventreport");
        Document doc = Manager.toDocument(eventreport);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateEventReport(EventReport eventreport) throws Exception {
        if (eventreport == null) {
            throw new InvalidParameterException("empty eventreport");
        }

        // defaults
        Date now = new Date();
        
        // internals
        eventreport.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eventreport");
        collection.replaceOne(
                new Document("_id", eventreport.getId()),
                Manager.toDocument(eventreport)
        );
        
    }
        
}
