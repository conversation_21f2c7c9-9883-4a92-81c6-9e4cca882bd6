package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.Area;
import com.agora.pojo.types.ImageType;
import com.agora.support.image.slim.SlimImage;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class AreaDao {
    
    public static Area loadArea(ObjectId areaId) throws Exception {
        if (areaId == null) {
            throw new InvalidParameterException("empty areaId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("area");
        Document doc = collection.find(eq("_id", areaId)).first();
        return Manager.fromDocument(doc, Area.class);
    }    
    
    public static List<Area> loadAreaList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("area");
        FindIterable<Document> list = collection
                .find(ne("cancelled", true))
                .sort(orderBy(ascending("sorting"), ascending("code")));
        return Manager.fromDocumentList(list, Area.class);
    }
    
    public static Area loadAreaByIdentifier(String identifier) throws Exception {
        if (identifier == null) {
            throw new InvalidParameterException("empty identifier");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("area");
        Document doc = collection.find(eq("identifier", identifier)).first();
        return Manager.fromDocument(doc, Area.class);
    }
    
    public static Area loadAreaByIdentifierEnglish(String identifierEnglish) throws Exception {
        if (identifierEnglish == null) {
            throw new InvalidParameterException("empty identifierEnglish");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("area");
        Document doc = collection.find(eq("identifierEnglish", identifierEnglish)).first();
        return Manager.fromDocument(doc, Area.class);
    }
    
    public static Area loadAreaByCode(String code) throws Exception {
        if (code == null) {
            throw new InvalidParameterException("empty code");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("area");
        Document doc = collection.find(eq("code", code)).first();
        return Manager.fromDocument(doc, Area.class);
    }

    public static Area loadAreaByTitle(String title) throws Exception {
        if (title == null) {
            throw new InvalidParameterException("empty title");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("area");
        Document doc = collection.find(eq("title", title)).first();
        return Manager.fromDocument(doc, Area.class);
    }
    
    public static Area loadAreaByTitleEnglish(String titleEnglish) throws Exception {
        if (titleEnglish == null) {
            throw new InvalidParameterException("empty titleEnglish");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("area");
        Document doc = collection.find(eq("titleEnglish", titleEnglish)).first();
        return Manager.fromDocument(doc, Area.class);
    }
    
    public static ObjectId insertArea(Area area) throws Exception {
        if (area == null) {
            throw new InvalidParameterException("empty area");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        area.setCreation(now);
        area.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("area");
        Document doc = Manager.toDocument(area);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }    
    
    public static void updateArea(Area area) throws Exception {
        if (area == null) {
            throw new InvalidParameterException("empty area");
        }

        // defaults
        Date now = new Date();
        
        // internals
        area.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("area");
        collection.replaceOne(
                new Document("_id", area.getId()),
                Manager.toDocument(area)
        );
        
    }    
    
    public static void updateAreaCancelled(ObjectId areaId, boolean cancelled) throws Exception {
        if (areaId == null) {
            throw new InvalidParameterException("empty areaId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Area area = loadArea(areaId);
        area.setCancelled(cancelled);
        
        // internals
        area.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("area");
        collection.replaceOne(
                new Document("_id", area.getId()),
                Manager.toDocument(area)
        );
        
    }
    
    public static void updateAreaImage(String username, ObjectId areaId, SlimImage image) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        if (areaId == null) {
            throw new InvalidParameterException("empty areaId");
        }
        if (image == null) {
            throw new InvalidParameterException("empty image");
        }
        if (image.isEmpty()) {
            throw new InvalidParameterException("empty image bytes");
        }
        
        // defaults
        Date now = new Date();
        String filename = ImageDao.composeFilename(ImageType.area, username, image.getExtension());
        String type = image.getType();
        
        // save image
        ObjectId imageId = ImageDao.insertImage(filename, 
                type, 
                image.getBytes()
        );
        
        // update imageId
        Area area = loadArea(areaId);
        if (area.getImageIds() == null) {
            area.setImageIds(new ArrayList<>());
        }
        if (area.getImageIds().isEmpty()) {
            area.getImageIds().add(new ObjectId());
        }
        area.getImageIds().set(0, imageId);

        // internals
        area.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("area");
        collection.replaceOne(
                new Document("_id", area.getId()),
                Manager.toDocument(area)
        );
        
    }
    
    public static void removeAreaImage(ObjectId areaId) throws Exception {
        if (areaId == null) {
            throw new InvalidParameterException("empty areaId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Area area = loadArea(areaId);
        area.setImageIds(null);
        
        // internals
        area.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("area");
        collection.replaceOne(
                new Document("_id", area.getId()),
                Manager.toDocument(area)
        );
        
    }
    
}
