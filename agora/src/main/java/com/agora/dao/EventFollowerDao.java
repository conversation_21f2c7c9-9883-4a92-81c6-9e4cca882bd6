package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.Event;
import com.agora.pojo.EventFollower;
import com.agora.pojo.Page;
import com.agora.util.TimeUtils;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Aggregates;
import static com.mongodb.client.model.Aggregates.sample;
import com.mongodb.client.model.Filters;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Filters.ne;
import com.mongodb.client.model.Sorts;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.descending;
import static com.mongodb.client.model.Sorts.orderBy;
import com.mongodb.client.model.Updates;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Arrays;
import static java.util.Collections.list;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class EventFollowerDao {

    public static EventFollower loadEventFollower(ObjectId eventFollowerId) throws Exception {
        if (eventFollowerId == null) {
            throw new InvalidParameterException("empty eventFollowerId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_follower");
        Document doc = collection.find(eq("_id", eventFollowerId)).first();
        return Manager.fromDocument(doc, EventFollower.class);
    }
    
    public static long loadEventFollowerCount(ObjectId eventId) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }

        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_follower");
        long count = collection.count(
                and(
                        ne("cancelled", true),
                        eq("eventId", eventId)
                )
        );

        Event event = EventDao.loadEvent(eventId);
        if (event != null) {
            if (event.getFollowers() != null) {
                count += event.getFollowers();
            }
        }
        
        return count;
    }
    
    public static List<EventFollower> loadEventFollowerExcludeOne(ObjectId eventId, int limit) throws Exception {
        return loadEventFollowerExcludeOne(eventId, null, limit);
    }
    
    public static List<EventFollower> loadEventFollowerExcludeOne(ObjectId eventId, ObjectId userId, int limit) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }

        List<Bson> filters = new ArrayList<>();
        
        filters.add(ne("cancelled", true));
        filters.add(eq("eventId", eventId));
        
        if (userId != null) {
            filters.add(eq("userId", userId));
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_follower");
        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                        Aggregates.match(and(filters)),
                        sample(limit)
                ));
        
        return Manager.fromAggregateList(list, EventFollower.class);
    }

    public static EventFollower loadEventFollowerByUserAndEvent(ObjectId userId, ObjectId eventId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_follower");
        Document doc = collection.find(
                and(
                        ne("cancelled", true),
                        eq("userId", userId),
                        eq("eventId", eventId)
                )
        ).first();
        return Manager.fromDocument(doc, EventFollower.class);
    }
    
    public static long loadEventFollowerCountByUserAndEvent(ObjectId userId, ObjectId eventId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_follower");
        long count = collection.count(
            and(
                    ne("cancelled", true),
                    eq("userId", userId),
                    eq("eventId", eventId)
            )
        );

        return count;
    }


    public static List<EventFollower> loadEventFollowerList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_follower");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true)
                        )
                )
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, EventFollower.class);
    }
    
    public static List<EventFollower> loadEventFollowerListByUser(ObjectId userId) throws Exception {
        return loadEventFollowerListByUser(userId, 0, 0);
    }
    
    public static List<EventFollower> loadEventFollowerListByUser(ObjectId userId, int skip, int limit) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_follower");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true),
                                eq("userId", userId)
                        )
                )
                .sort(orderBy(ascending("creation")))
                .skip(skip)
                .limit(limit);
        return Manager.fromDocumentList(list, EventFollower.class);
    }
    
    public static List<EventFollower> loadEventFollowerFutureListByUser(ObjectId userId, int skip, int limit) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
                        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_follower");
        
        List<Bson> pipeline = Arrays.asList(
                Aggregates.lookup("event", "eventId", "_id", "eventInfo"),

                Aggregates.match(
                        Filters.and(
                                Filters.gte("eventInfo.startDate", TimeUtils.today()),
                                Filters.ne("cancelled", true),
                                Filters.eq("userId", userId)
                        )
                ),

                Aggregates.sort(Sorts.ascending("eventInfo.startDate")),
                Aggregates.skip(skip),
                Aggregates.limit(limit)
        );

        AggregateIterable<Document> resultDocs = collection.aggregate(pipeline);

        return Manager.fromAggregateList(resultDocs, EventFollower.class);
    }
    
    public static List<EventFollower> loadEventFollowerPastListByUser(ObjectId userId, int skip, int limit) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
               
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_follower");
        
        List<Bson> pipeline = Arrays.asList(
                Aggregates.lookup("event", "eventId", "_id", "eventInfo"),

                Aggregates.match(
                        Filters.and(
                                Filters.lte("eventInfo.startDate", TimeUtils.today()),
                                Filters.ne("cancelled", true),
                                Filters.eq("userId", userId)
                        )
                ),

                Aggregates.sort(Sorts.descending("eventInfo.startDate")),
                Aggregates.skip(skip),
                Aggregates.limit(limit)
        );

        AggregateIterable<Document> resultDocs = collection.aggregate(pipeline);

        return Manager.fromAggregateList(resultDocs, EventFollower.class);
    }
    
    public static List<EventFollower> loadEventFollowerListByEvent(ObjectId eventId) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_follower");
        FindIterable<Document> list = collection
                .find(
                        and(
                                ne("cancelled", true),
                                eq("eventId", eventId)
                        )
                )
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, EventFollower.class);
    }

    public static ObjectId insertEventFollower(EventFollower eventFollower) throws Exception {
        if (eventFollower == null) {
            throw new InvalidParameterException("empty eventFollower");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        eventFollower.setCreation(now);
        eventFollower.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_follower");
        Document doc = Manager.toDocument(eventFollower);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateEventFollower(EventFollower eventFollower) throws Exception {
        if (eventFollower == null) {
            throw new InvalidParameterException("empty eventFollower");
        }

        // defaults
        Date now = new Date();
        
        // internals
        eventFollower.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_follower");
        collection.replaceOne(
                new Document("_id", eventFollower.getId()),
                Manager.toDocument(eventFollower)
        );
        
    }

    public static void updateEventFollowerCancelled(ObjectId eventFollowerId, boolean cancelled) throws Exception {
        if (eventFollowerId == null) {
            throw new InvalidParameterException("empty eventFollowerId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        EventFollower eventFollower = loadEventFollower(eventFollowerId);
        eventFollower.setCancelled(cancelled);
        
        // internals
        eventFollower.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_follower");
        collection.replaceOne(
                new Document("_id", eventFollower.getId()),
                Manager.toDocument(eventFollower)
        );
        
    }

    public static void updateEventFollowerListCancelledByEventId(ObjectId eventId, boolean cancelled) throws Exception {
        if (eventId == null) {
            throw new InvalidParameterException("empty eventId");
        }
        
        Bson query = eq("eventId", eventId);
        Bson updates = Updates.combine(
        Updates.set("cancelled", cancelled),
        Updates.set("lastUpdate", new Date()));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("event_follower");
        collection.updateMany(query, updates);
        
    }
    
}
