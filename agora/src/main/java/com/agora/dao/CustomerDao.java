package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.Customer;
import com.agora.pojo.types.ImageType;
import com.agora.support.CityEntry;
import com.agora.support.image.slim.SlimImage;
import com.agora.util.MongoUtils;
import com.agora.util.TimeUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Accumulators.min;
import static com.mongodb.client.model.Accumulators.sum;
import static com.mongodb.client.model.Aggregates.group;
import static com.mongodb.client.model.Aggregates.match;
import static com.mongodb.client.model.Aggregates.project;
import static com.mongodb.client.model.Aggregates.sort;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Filters.or;
import static com.mongodb.client.model.Filters.regex;
import static com.mongodb.client.model.Projections.excludeId;
import static com.mongodb.client.model.Projections.fields;
import static com.mongodb.client.model.Projections.include;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class CustomerDao {

    public static long loadCustomerCountByDate(Date date) throws Exception {
        if (date == null) {
            throw new InvalidParameterException("empty date");
        }

        Date from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(date));
        Date to = MongoUtils.toComparableDate(TimeUtils.endOfDay(date));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        long count = collection.count(
                and(
                        ne("cancelled", true),
                        gte("sinceDate", from),
                        lte("sinceDate", to))
        );

        return count;
    }

    public static Customer loadCustomer(ObjectId customerId) throws Exception {
        if (customerId == null) {
            throw new InvalidParameterException("empty customerId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        Document doc = collection.find(eq("_id", customerId)).first();
        return Manager.fromDocument(doc, Customer.class);
    }

    public static Customer loadCustomerByUserId(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        Document doc = collection.find(eq("userId", userId)).first();
        return Manager.fromDocument(doc, Customer.class);
    }

    public static List<Customer> loadCustomersByUserId(List<ObjectId> userIds) throws Exception {
        if (userIds == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        FindIterable<Document> list = collection.find(in("userId", userIds));
        return Manager.fromDocumentList(list, Customer.class);
    }

    public static Customer loadCustomerBySubscriptionKey(String subscriptionKey) throws Exception {
        if (StringUtils.isBlank(subscriptionKey)) {
            throw new InvalidParameterException("empty subscriptionKey");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        Document doc = collection.find(eq("subscriptionKey", subscriptionKey)).first();
        return Manager.fromDocument(doc, Customer.class);
    }

    public static Customer loadCustomerBySubscriptionCustomerKey(String subscriptionCustomerKey) throws Exception {
        if (StringUtils.isBlank(subscriptionCustomerKey)) {
            throw new InvalidParameterException("empty subscriptionCustomerKey");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        Document doc = collection.find(eq("subscriptionCustomerKey", subscriptionCustomerKey)).first();
        return Manager.fromDocument(doc, Customer.class);
    }

    public static List<Customer> loadCustomerList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        FindIterable<Document> list = collection
                .find(ne("cancelled", true))
                .sort(orderBy(ascending("lastname")));
        return Manager.fromDocumentList(list, Customer.class);
    }

    //loadOrderListByDateRange
    public static List<Customer> loadCustomerListByDateRange(Date from, Date to) throws Exception {
        if (from == null) {
            throw new InvalidParameterException("empty from");
        }
        if (to == null) {
            throw new InvalidParameterException("empty to");
        }

        from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
        to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        FindIterable<Document> list = collection
                .find(and(
                        ne("cancelled", true),
                        or(eq("sinceDate", null), and(gte("sinceDate", from), lte("sinceDate", to)))))
                .sort(orderBy(ascending("name")));

        return Manager.fromDocumentList(list, Customer.class);
    }

    public static List<Customer> loadCustomerListByVendorId(ObjectId vendorId) throws Exception {
        if (vendorId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        FindIterable<Document> list = collection
                .find(and(
                        eq("vendorId", vendorId),
                        ne("cancelled", true)))
                .sort(orderBy(ascending("lastname")));
        return Manager.fromDocumentList(list, Customer.class);
    }

    public static List<Customer> loadCustomerListByDateRangeVendorId(ObjectId vendorId, Date from, Date to) throws Exception {
        if (vendorId == null) {
            throw new InvalidParameterException("empty userId");
        }
        if (from == null) {
            throw new InvalidParameterException("empty from");
        }
        if (to == null) {
            throw new InvalidParameterException("empty to");
        }

        from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
        to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        FindIterable<Document> list = collection
                .find(and(
                        eq("vendorId", vendorId),
                        ne("cancelled", true),
                        or(eq("sinceDate", null), and(gte("sinceDate", from), lte("sinceDate", to)))))
                .sort(orderBy(ascending("name")));
        return Manager.fromDocumentList(list, Customer.class);
    }

    public static List<Customer> loadCustomerListByEmail(String email) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        FindIterable<Document> list = collection
                .find(and(
                        ne("cancelled", true),
                        regex("email", Pattern.compile("(\\s|^)" + email + "(\\s|$)", Pattern.CASE_INSENSITIVE))
                ))
                .sort(orderBy(ascending("name")));
        return Manager.fromDocumentList(list, Customer.class);
    }

    public static List<Customer> loadCustomerListByEmailPartial(String email) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        FindIterable<Document> list = collection
                .find(and(
                        ne("cancelled", true),
                        regex("email", Pattern.compile("^" + email, Pattern.CASE_INSENSITIVE + Pattern.UNICODE_CASE))
                ))
                .sort(orderBy(ascending("email")));
        return Manager.fromDocumentList(list, Customer.class);
    }

    public static List<Customer> loadCustomerListByVat(String vatNumber) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        FindIterable<Document> list = collection
                .find(and(
                        ne("cancelled", true),
                        regex("vatNumber", Pattern.compile("(\\s|^)" + vatNumber + "(\\s|$)", Pattern.CASE_INSENSITIVE))
                ))
                .sort(orderBy(ascending("name")));
        return Manager.fromDocumentList(list, Customer.class);
    }

    public static List<Customer> loadCustomerListByTin(String tin) throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        FindIterable<Document> list = collection
                .find(and(
                        ne("cancelled", true),
                        regex("tin", Pattern.compile("(\\s|^)" + tin + "(\\s|$)", Pattern.CASE_INSENSITIVE))
                ))
                .sort(orderBy(ascending("name")));
        return Manager.fromDocumentList(list, Customer.class);
    }

    public static ObjectId insertCustomer(Customer customer) throws Exception {
        if (customer == null) {
            throw new InvalidParameterException("empty customer");
        }

        // defaults
        Date now = new Date();

        // internals
        customer.setCreation(now);
        customer.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        Document doc = Manager.toDocument(customer);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateCustomer(Customer customer) throws Exception {
        if (customer == null) {
            throw new InvalidParameterException("empty customer");
        }

        // defaults
        Date now = new Date();

        // internals
        customer.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        collection.replaceOne(
                new Document("_id", customer.getId()),
                Manager.toDocument(customer)
        );

    }

    public static void updateCustomerCancelled(ObjectId customerId, boolean cancelled) throws Exception {
        if (customerId == null) {
            throw new InvalidParameterException("empty customerId");
        }

        // defaults
        Date now = new Date();

        // update
        Customer customer = loadCustomer(customerId);
        customer.setCancelled(cancelled);

        // internals
        customer.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        collection.replaceOne(
                new Document("_id", customer.getId()),
                Manager.toDocument(customer)
        );

    }

    public static void updateCustomerImage(String username, ObjectId customerId, SlimImage image) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        if (customerId == null) {
            throw new InvalidParameterException("empty customerId");
        }
        if (image == null) {
            throw new InvalidParameterException("empty image");
        }
        if (image.isEmpty()) {
            throw new InvalidParameterException("empty image bytes");
        }

        // defaults
        Date now = new Date();
        String filename = ImageDao.composeFilename(ImageType.customer, username, image.getExtension());
        String type = image.getType();

        // save image
        ObjectId imageId = ImageDao.insertImage(filename,
                type,
                image.getBytes()
        );

        // update imageId
        Customer customer = loadCustomer(customerId);
        customer.setImageId(imageId);

        // internals
        customer.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        collection.replaceOne(
                new Document("_id", customer.getId()),
                Manager.toDocument(customer)
        );

    }

    public static void updateCustomerLogoImage(String username, ObjectId customerId, SlimImage image) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        if (customerId == null) {
            throw new InvalidParameterException("empty customerId");
        }
        if (image == null) {
            throw new InvalidParameterException("empty image");
        }
        if (image.isEmpty()) {
            throw new InvalidParameterException("empty image bytes");
        }

        // defaults
        Date now = new Date();
        String filename = ImageDao.composeFilename(ImageType.customerLogo, username, image.getExtension());
        String type = image.getType();

        // save image
        ObjectId imageId = ImageDao.insertImage(filename,
                type,
                image.getBytes()
        );

        // update imageId
        Customer customer = loadCustomer(customerId);
        customer.setLogoImageId(imageId);

        // internals
        customer.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        collection.replaceOne(
                new Document("_id", customer.getId()),
                Manager.toDocument(customer)
        );

    }

    public static void removeCustomerImage(ObjectId customerId) throws Exception {
        if (customerId == null) {
            throw new InvalidParameterException("empty customerId");
        }

        // defaults
        Date now = new Date();

        // update
        Customer customer = loadCustomer(customerId);
        customer.setImageId(null);

        // internals
        customer.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        collection.replaceOne(
                new Document("_id", customer.getId()),
                Manager.toDocument(customer)
        );

    }

    public static void removeCustomerLogoImage(ObjectId customerId) throws Exception {
        if (customerId == null) {
            throw new InvalidParameterException("empty customerId");
        }

        // defaults
        Date now = new Date();

        // update
        Customer customer = loadCustomer(customerId);
        customer.setLogoImageId(null);

        // internals
        customer.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        collection.replaceOne(
                new Document("_id", customer.getId()),
                Manager.toDocument(customer)
        );

    }

    public static List<CityEntry> loadCustomerCityList() throws Exception {
        /*
            db.getCollection('property').aggregate(
                [
                    {
                        $match: {
                            citta: {$ne: '', $ne: null}
                        }
                    },
                    {
                        $group: {
                            _id : '$citta',
                            citta: { $min: '$citta'},
                            count: { $sum: 1 }
                        }
                    },
                    { $project: { _id: 0, citta: 1}},
                    { $sort : { citta : 1 } }
                ]
            )
         */
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                        match(and(
                                ne("city", ""),
                                ne("city", null)
                        )),
                        group("$city", min("city", "$city"), sum("count", 1)),
                        project(fields(excludeId(), include("city"))),
                        sort(orderBy(ascending("city")))
                ));
        return Manager.fromAggregateList(list, CityEntry.class);
    }

    public static List<CityEntry> loadCustomerCityListByVendorId(ObjectId vendorId) throws Exception {
        /*
            db.getCollection('property').aggregate(
                [
                    {
                        $match: {
                            citta: {$ne: '', $ne: null}
                        }
                    },
                    {
                        $group: {
                            _id : '$citta',
                            citta: { $min: '$citta'},
                            count: { $sum: 1 }
                        }
                    },
                    { $project: { _id: 0, citta: 1}},
                    { $sort : { citta : 1 } }
                ]
            )
         */
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");
        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                        match(and(eq("vendorId", vendorId),
                                ne("city", ""),
                                ne("city", null)
                        )),
                        group("$city", min("city", "$city"), sum("count", 1)),
                        project(fields(excludeId(), include("city"))),
                        sort(orderBy(ascending("city")))
                ));
        return Manager.fromAggregateList(list, CityEntry.class);
    }

    public static Date loadCustomerMinDate() {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");

        /*
            db.getCollection('customer').aggregate(
                [
                    {
                        $match:
                            {
                                cancelled: { $ne: true },
                                sinceDate: { $ne: null }
                            }
                    },
                    {
                        $group:
                            {
                                _id: null,
                                minDate: { $min: "$sinceDate" }
                            }
                    },
                    {
                        $project:
                            {
                                _id: 0,
                                minDate: 1
                            }
                    }
                ]
            )
         */
        AggregateIterable<Document> mins = collection
                .aggregate(Arrays.asList(
                        match(and(
                                ne("cancelled", true),
                                ne("sinceDate", null)
                        )),
                        group(null, min("minDate", "$sinceDate")),
                        project(fields(excludeId(), include("minDate")))
                ));

        Date min = null;
        for (Document item : mins) {
            min = item.getDate("minDate");
            break;
        }

        return min;
    }

    public static Map<String, Integer> loadCustomerCountBySinceDate() {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");

        /*
            db.getCollection('customer').aggregate(
                [
                    {
                        $match:
                            {
                                cancelled: { $ne: true },
                                sinceDate: { $ne: null }
                            }
                    },
                    {
                        $group:
                            {
                                _id: { $dateToString: { format: "%Y-%m", date: "$sinceDate" } },
                                count: { $sum: 1 }
                            }
                    },
                    {
                        $sort:
                            {
                                _id: 1
                            }
                    }
                ]
            )
         */
        DBObject yearMonthFormat = new BasicDBObject("format", "%Y-%m");
        yearMonthFormat.put("date", "$sinceDate");
        DBObject yearMonth = new BasicDBObject("$dateToString", yearMonthFormat);

        AggregateIterable<Document> newbies = collection
                .aggregate(Arrays.asList(
                        match(and(
                                ne("cancelled", true),
                                ne("sinceDate", null)
                        )),
                        group(yearMonth, sum("count", 1)),
                        sort(orderBy(ascending("_id")))
                ));

        Map<String, Integer> values = new HashMap<>();
        for (Document newbie : newbies) {
            values.put(newbie.getString("_id"), newbie.getInteger("count", 0));
        }

        return values;
    }

    public static Map<String, Integer> loadCustomerCountByVendorAndSinceDate(ObjectId vendorId) {
        if (vendorId == null) {
            throw new InvalidParameterException("empty vendorId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");

        /*
            db.getCollection('customer').aggregate(
                [
                    {
                        $match:
                            {
                                vendorId:  ObjectId("591d99797d80a2af3623ca39"),
                                cancelled: { $ne: true },
                                sinceDate: { $ne: null }
                            }
                    },
                    {
                        $group:
                            {
                                _id: { $dateToString: { format: "%Y-%m", date: "$sinceDate" } },
                                count: { $sum: 1 }
                            }
                    },
                    {
                        $sort:
                            {
                                _id: 1
                            }
                    }
                ]
            )
         */
        DBObject yearMonthFormat = new BasicDBObject("format", "%Y-%m");
        yearMonthFormat.put("date", "$sinceDate");
        DBObject yearMonth = new BasicDBObject("$dateToString", yearMonthFormat);

        AggregateIterable<Document> newbies = collection
                .aggregate(Arrays.asList(
                        match(and(
                                eq("vendorId", vendorId),
                                ne("cancelled", true),
                                ne("sinceDate", null)
                        )),
                        group(yearMonth, sum("count", 1)),
                        sort(orderBy(ascending("_id")))
                ));

        Map<String, Integer> values = new HashMap<>();
        for (Document newbie : newbies) {
            values.put(newbie.getString("_id"), newbie.getInteger("count", 0));
        }

        return values;
    }

    public static List<String> loadCustomerSectorList() throws Exception {
        /*
            db.getCollection('customer').aggregate(
                [
                    {
                        $match: {
                            $and: [
                                { cancelled: { $ne: true } },
                                { channel: 'B2B'},
                            ]
                        }
                    },
                    {
                        $group: {
                            _id : '$sector',
                            sector: { $min: '$sector'},
                            count: { $sum: 1 }
                        }
                    },
                    { $project: { _id: 0, sector: 1}},
                    { $sort : { sector : 1 } }
                ]
            )
         */
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("customer");

        List<Bson> filters = new ArrayList<>();

        filters.add(ne("cancelled", true));
        filters.add(eq("channel", "B2B"));

        String fld = "sector";

        List<Bson> pipeline = new ArrayList<>();
        pipeline.add(match(and(filters)));
        pipeline.add(group("$" + fld, min(fld, "$" + fld), sum("count", 1)));
        pipeline.add(project(fields(excludeId(), include(fld))));
        pipeline.add(sort(orderBy(ascending(fld))));

        AggregateIterable<Document> list = collection
                .aggregate(pipeline);

        List<String> values = null;
        if (list != null) {
            values = new ArrayList<>();
            for (Document document : list) {
                String value = document.getString(fld);
                if (StringUtils.isNotBlank(value)) {
                    values.add(value);
                }
            }
        }
        return values;
    }

}
