package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.types.PrintType;
import com.agora.support.print.Print;
import com.agora.util.TimeUtils;
import com.mongodb.client.gridfs.GridFSBucket;
import com.mongodb.client.gridfs.GridFSBuckets;
import com.mongodb.client.gridfs.GridFSDownloadStream;
import com.mongodb.client.gridfs.model.GridFSUploadOptions;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.UUID;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class PrintDao {

    public static String composeFilename(PrintType type, String username, String extension) throws Exception {
        if (type == null) {
            throw new InvalidParameterException("empty type");
        }
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        // extension is optional
        
        // defaults
        Date now = new Date();
        if (StringUtils.isNotBlank(extension)) {
            extension = "." + extension;
        }
        
        return type + "-" + username + "-" + TimeUtils.toString(now, "yyyyMMdd") + "-" + UUID.randomUUID().toString() + (StringUtils.isNotBlank(extension) ? extension : "");
    }
    
    public static Print loadPrint(ObjectId oid) throws Exception {
        if (oid == null) {
            throw new InvalidParameterException("empty oid");
        }
        
        String filename;
        String contentType;
        int length;
        byte[] bytes;
        
        GridFSBucket bucket = GridFSBuckets.create(Manager.mongoDatabase, "print");
        try (GridFSDownloadStream stream = bucket.openDownloadStream(oid)) {
            filename = stream.getGridFSFile().getFilename();
            contentType = stream.getGridFSFile().getMetadata().getString("contentType");
            length = (int) stream.getGridFSFile().getLength();
        }
        try (ByteArrayOutputStream out = new ByteArrayOutputStream(length)) {
            bucket.downloadToStream(oid, out);
            bytes = out.toByteArray();
        }
        
        return new Print(filename, contentType, bytes);
    }

    public static ObjectId insertPrint(String filename, String type, byte[] bytes) throws Exception {
        if (StringUtils.isBlank(filename)) {
            throw new InvalidParameterException("empty filename");
        }
        if (bytes == null) {
            throw new InvalidParameterException("empty bytes");
        }
        
        GridFSBucket bucket = GridFSBuckets.create(Manager.mongoDatabase, "print");
        GridFSUploadOptions options = new GridFSUploadOptions()
                .metadata(new Document("contentType", type))
                ;
        ByteArrayInputStream data = new ByteArrayInputStream(bytes);
        ObjectId oid = bucket.uploadFromStream(filename, data, options);
        IOUtils.closeQuietly(data);
        
        return oid;
    }
    
}
