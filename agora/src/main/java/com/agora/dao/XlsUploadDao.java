package com.agora.dao;

import com.agora.core.Manager;
import com.agora.support.xlsupload.XlsUpload;
import com.agora.util.TimeUtils;
import com.mongodb.client.gridfs.GridFSBucket;
import com.mongodb.client.gridfs.GridFSBuckets;
import com.mongodb.client.gridfs.GridFSDownloadStream;
import com.mongodb.client.gridfs.model.GridFSUploadOptions;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class XlsUploadDao {

    public static String composeFilename(String extension) throws Exception {
        // extension is optional
        
        // defaults
        Date now = new Date();
        if (StringUtils.isNotBlank(extension)) {
            extension = "." + extension;
        }
        
        return TimeUtils.toString(now, "yyyyMMdd-HHmmss-SSS") + "-" + UUID.randomUUID().toString() + (StringUtils.isNotBlank(extension) ? extension : "");
    }
    
    public static XlsUpload loadXlsUpload(ObjectId oid) throws Exception {
        if (oid == null) {
            throw new InvalidParameterException("empty oid");
        }
        
        String filename;
        Integer count;
        String errors;
        String infos;
        
        int length;
        byte[] bytes;
        
        GridFSBucket bucket = GridFSBuckets.create(Manager.mongoDatabase, "xlsupload");
        try (GridFSDownloadStream stream = bucket.openDownloadStream(oid)) {
            filename = stream.getGridFSFile().getFilename();
            count = stream.getGridFSFile().getMetadata().getInteger("count");
            errors = stream.getGridFSFile().getMetadata().getString("errors");
            infos = stream.getGridFSFile().getMetadata().getString("infos");
            length = (int) stream.getGridFSFile().getLength();
        }
        try (ByteArrayOutputStream out = new ByteArrayOutputStream(length)) {
            bucket.downloadToStream(oid, out);
            bytes = out.toByteArray();
        }
        
        return new XlsUpload(filename, count, errors, infos, bytes);
    }

    public static ObjectId insertXlsUpload(String filename, Integer count, String errors, String infos, byte[] bytes) throws Exception {
        if (StringUtils.isBlank(filename)) {
            throw new InvalidParameterException("empty filename");
        }
        if (bytes == null) {
            throw new InvalidParameterException("empty bytes");
        }
        
        Map<String, Object> meta = new HashMap<>();
        meta.put("count", count);
        meta.put("errors", errors);
        meta.put("infos", infos);
        GridFSBucket bucket = GridFSBuckets.create(Manager.mongoDatabase, "xlsupload");
        GridFSUploadOptions options = new GridFSUploadOptions()
                .metadata(new Document(meta))
                ;
        ByteArrayInputStream data = new ByteArrayInputStream(bytes);
        ObjectId oid = bucket.uploadFromStream(filename, data, options);
        IOUtils.closeQuietly(data);
        
        return oid;
    }
    
    
}
