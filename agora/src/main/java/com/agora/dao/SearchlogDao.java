package com.agora.dao;

import com.agora.core.Manager;
import com.agora.pojo.Searchlog;
import com.agora.util.MongoUtils;
import com.agora.util.TimeUtils;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Sorts.descending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class SearchlogDao {
 
    public static List<Searchlog> loadSearchlogListByDateRange(Date from, Date to) throws Exception {
        List<Bson> filters = new ArrayList<>();
        
        if (from != null) {
            from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
            filters.add(gte("creation", from));
        }
        if (to != null) {
            to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));
            filters.add(lte("creation", to));
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("searchlog");
        FindIterable<Document> list = collection
                .find(and(filters))
                .sort(orderBy(descending("creation")));
        return Manager.fromDocumentList(list, Searchlog.class);
    }
    
    public static Searchlog loadLastSearchlogByUserIdIpAndToken(ObjectId userId, String ip, String token) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        if (StringUtils.isBlank(ip)) {
            throw new InvalidParameterException("empty ip");
        }
        if (StringUtils.isBlank(token)) {
            throw new InvalidParameterException("empty token");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("searchlog");
        FindIterable<Document> list = collection
                .find(and(
                        eq("userId", userId),
                        eq("ip", ip),
                        eq("token", token)))
                .sort(orderBy(descending("creation")))
                .limit(1);
        List<Searchlog> results = Manager.fromDocumentList(list, Searchlog.class);
        return ((results != null) && (!results.isEmpty())) ? results.get(0) : null;
    }
    
    public static Searchlog loadSearchlogByUserIdIpAndToken(ObjectId userId, String ip, String token) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        if (StringUtils.isBlank(ip)) {
            throw new InvalidParameterException("empty ip");
        }
        if (StringUtils.isBlank(token)) {
            throw new InvalidParameterException("empty token");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("searchlog");
        Document doc = collection.find(and(
                eq("userId", userId),
                eq("ip", ip),
                eq("token", token)
        )).first();
        return Manager.fromDocument(doc, Searchlog.class);
    }
    
    public static Searchlog loadSearchlog(ObjectId searchlogId) throws Exception {
        if (searchlogId == null) {
            throw new InvalidParameterException("empty searchlogId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("searchlog");
        Document doc = collection.find(eq("_id", searchlogId)).first();
        return Manager.fromDocument(doc, Searchlog.class);
    }
    
    public static ObjectId insertSearchlog(Searchlog searchlog) throws Exception {
        if (searchlog == null) {
            throw new InvalidParameterException("empty searchlog");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        searchlog.setCreation(now);
        searchlog.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("searchlog");
        Document doc = Manager.toDocument(searchlog);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateSearchlog(Searchlog searchlog) throws Exception {
        if (searchlog == null) {
            throw new InvalidParameterException("empty searchlog");
        }

        // defaults
        Date now = new Date();
        
        // internals
        searchlog.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("searchlog");
        collection.replaceOne(
                new Document("_id", searchlog.getId()),
                Manager.toDocument(searchlog)
        );
        
    }
        
}
