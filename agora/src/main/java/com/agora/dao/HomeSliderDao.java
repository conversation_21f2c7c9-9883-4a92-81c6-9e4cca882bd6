package com.agora.dao;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import com.agora.core.Manager;
import com.agora.pojo.HomeSlider;
import com.agora.pojo.types.ImageType;
import com.agora.support.image.slim.SlimImage;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class HomeSliderDao {

    public static HomeSlider loadHomeSlider(ObjectId homeSliderId) throws Exception {
        if (homeSliderId == null) {
            throw new InvalidParameterException("empty homeSliderId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("home_slider");
        Document doc = collection.find(eq("_id", homeSliderId)).first();
        return Manager.fromDocument(doc, HomeSlider.class);
    }

    public static List<HomeSlider> loadHomeSliderList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("home_slider");
        FindIterable<Document> list = collection
                .find(ne("cancelled", true))
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, HomeSlider.class);
    }

    public static ObjectId insertHomeSlider(HomeSlider homeSlider) throws Exception {
        if (homeSlider == null) {
            throw new InvalidParameterException("empty homeSlider");
        }

        // defaults
        Date now = new Date();

        // internals
        homeSlider.setCreation(now);
        homeSlider.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("home_slider");
        Document doc = Manager.toDocument(homeSlider);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateHomeSlider(HomeSlider homeSlider) throws Exception {
        if (homeSlider == null) {
            throw new InvalidParameterException("empty homeSlider");
        }

        // defaults
        Date now = new Date();

        // internals
        homeSlider.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("home_slider");
        collection.replaceOne(
                new Document("_id", homeSlider.getId()),
                Manager.toDocument(homeSlider)
        );

    }

    public static void updateHomeSliderCancelled(ObjectId homeSliderId, boolean cancelled) throws Exception {
        if (homeSliderId == null) {
            throw new InvalidParameterException("empty homeSliderId");
        }

        // defaults
        Date now = new Date();

        // update
        HomeSlider homeSlider = loadHomeSlider(homeSliderId);
        homeSlider.setCancelled(cancelled);

        // internals
        homeSlider.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("home_slider");
        collection.replaceOne(
                new Document("_id", homeSlider.getId()),
                Manager.toDocument(homeSlider)
        );

    }

    public static void updateHomeSliderImage(String username, ObjectId homeSliderId, SlimImage image) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        if (homeSliderId == null) {
            throw new InvalidParameterException("empty homeSliderId");
        }
        if (image == null) {
            throw new InvalidParameterException("empty images");
        }
        if (image == null) {
            throw new InvalidParameterException("empty image");
        }
        if (image.isEmpty()) {
            throw new InvalidParameterException("empty image bytes");
        }

        // defaults
        Date now = new Date();
        String filename = ImageDao.composeFilename(ImageType.homeSlider, username, image.getExtension());
        String type = image.getType();

        // save image
        ObjectId imageId = ImageDao.insertImage(filename,
                type,
                image.getBytes()
        );

        // update imageId
        HomeSlider homeSlider = loadHomeSlider(homeSliderId);
        homeSlider.setImageId(imageId);

        // internals
        homeSlider.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("home_slider");
        collection.replaceOne(
                new Document("_id", homeSlider.getId()),
                Manager.toDocument(homeSlider)
        );

    }

    public static void removeHomeSliderImage(ObjectId homeSliderId) throws Exception {
        if (homeSliderId == null) {
            throw new InvalidParameterException("empty homeSliderId");
        }

        // defaults
        Date now = new Date();

        // update
        HomeSlider homeSlider = loadHomeSlider(homeSliderId);
        homeSlider.setImageId(null);

        // internals
        homeSlider.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("home_slider");
        collection.replaceOne(
                new Document("_id", homeSlider.getId()),
                Manager.toDocument(homeSlider)
        );

    }
    
}
