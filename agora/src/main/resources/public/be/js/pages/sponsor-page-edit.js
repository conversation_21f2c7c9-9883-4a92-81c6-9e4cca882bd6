$(function() {

    // Initialize Select2 for page search
    $('#pageId').select2({
        minimumInputLength: 3,
        placeholder: 'Cerca pagina per nome...',
        allowClear: true,
        ajax: {
            url: $('#dataPagesUri').attr('href'),
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    name: params.term,
                    page: params.page || 1
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;

                var results = [];
                if (data) {
                    results = data.map(function(item) {
                        return {
                            id: item[0],
                            text: item[1],
                            avatar: item[2],
                            descr: item[4]
                        };
                    });
                }

                return {
                    results: results,
                    pagination: {
                        more: data && data[0] && data[0][4] ? (params.page * 10) < data[0][4] : false
                    }
                };
            },
            cache: true
        },
        templateResult: formatRepo,
        templateSelection: formatRepoSelection
    });

    function formatRepo(repo) {
        if (repo.loading) {
            return repo.text;
        }

        // Se è un'opzione "Crea"
        if (repo.isCreate || repo.isNew) {
            return $(
                '<div class="select2-result-repository clearfix d-flex align-items-center create-option">' +
                '<div class="select2-result-repository__avatar"><i class="fa-2x bi bi-file-plus"></i></div>' +
                '<div class="select2-result-repository__meta">' +
                '<div class="select2-result-repository__title">Crea "' + repo.text + '"</div>' +
                '</div>' +
                '</div>'
            );
        } else {
            var imgUrl = "https://agor.app/fe/images/avatar/placeholder.jpg";
            if (repo.avatar) {
                imgUrl = $('#imageUri').attr('href') + repo.avatar;
            }

            var $container = $(
                '<div class="select2-result-repository clearfix d-flex align-items-center">' +
                '<div class="select2-result-repository__avatar"><img src="' + imgUrl + '" /></div>' +
                '<div class="select2-result-repository__meta">' +
                '<div class="select2-result-repository__title">' + repo.text + '</div>' +
                "<div class='select2-result-repository__description' style='font-size: small; color: gray;'></div>" +
                '</div>' +
                '</div>'
            );

            if (repo.descr) {
                var shortDesc = repo.descr.length > 50 ? repo.descr.substring(0, 50) + "..." : repo.descr;
                $container.find(".select2-result-repository__description").text(shortDesc);
            }
            return $container;
        }

    }

    function formatRepoSelection(repo) {
        return repo.text;
    }

    // Initialize date picker
    $('.daterange-single').daterangepicker({
        singleDatePicker: true,
        showDropdowns: true,
        locale: {
            format: 'DD/MM/YYYY',
            separator: ' - ',
            applyLabel: 'Applica',
            cancelLabel: 'Annulla',
            fromLabel: 'Da',
            toLabel: 'A',
            customRangeLabel: 'Personalizzato',
            weekLabel: 'S',
            daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve', 'Sa'],
            monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',
                'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
            firstDay: 1
        }
    });

    // Form validation
    $('#sponsor-page-form').validate({
        rules: {
            pageId: {
                required: true
            },
            expirationDate: {
                required: true
            },
            sort: {
                required: false,
                min: 0,
                digits: true
            }
        },
        messages: {
            pageId: {
                required: "Seleziona una pagina"
            },
            expirationDate: {
                required: "Inserisci la data di scadenza",
                date: "Inserisci una data valida"
            },
            sort: {
                min: "Il numero deve essere almeno 0",
                digits: "Inserisci solo numeri"
            }
        }
    });

    // Handle cancel button
    $('.btn-cancel').click(function() {
        window.location.href = $('#sponsorPageCollectionUri').attr('href');
    });

    // Handle delete button
    $('#btn-delete').click(function() {
        if (confirm('Sei sicuro di voler eliminare questa pagina sponsorizzata?')) {
            var removeUri = $('#sponsorPageRemoveUri').attr('href');
            var collectionUri = $('#sponsorPageCollectionUri').attr('href');

            $.ajax({
                url: removeUri,
                type: 'POST',
                success: function(response) {
                    // Redirect to collection page with success message
                    window.location.href = collectionUri + '/ok';
                },
                error: function(xhr, status, error) {
                    // Redirect to collection page with error message
                    window.location.href = collectionUri + '/error';
                }
            });
        }
    });

});
