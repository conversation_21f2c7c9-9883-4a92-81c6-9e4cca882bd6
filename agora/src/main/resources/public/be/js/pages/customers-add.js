$(function() {
    
    $.validator.addMethod('tin', function (value) { 
        if (value !== '' && $("#invoiceCountryCode").children("option:selected").val() === 'IT') {
            if (value.length === 16) {
                return /^(?:(?:[B-DF-HJ-NP-TV-Z]|[AEIOU])[AEIOU][AEIOUX]|[B-DF-HJ-NP-TV-Z]{2}[A-Z]){2}[\dLMNP-V]{2}(?:[A-EHLMPR-T](?:[04LQ][1-9MNP-V]|[1256LMRS][\dLMNP-V])|[DHPS][37PT][0L]|[ACELMRT][37PT][01LM])(?:[A-MZ][1-9MNP-V][\dLMNP-V]{2}|[A-M][0L](?:[1-9MNP-V][\dLMNP-V]|[0L][1-9MNP-V]))[A-Z]$/i.test(value); 
            } else {
                return /^[0-9]{11}$/.test(value); 
            }
        } else {
            return true;
        }
    }, 'Codice fiscale non valido.');

    $.validator.addMethod('fullname', function () { 
        if ($("#lastname").val() || $("#fullname").val()) {
            return true;
        } else {
            return false;
        }
    }, 'Cognome o ragione sociale obbligatorio.');
    
    $.validator.addMethod('contacts', function () { 
        if ($("#email").val() || $("#phoneNumber").val()) {
            return true;
        } else {
            return false;
        }
    }, 'Telefono o email obbligatorio.');

    $.validator.addMethod('einvoice-control', function (value) { 
        if ($("#einvoice").is(':checked')) {
            if ($("#pec").val() || $("#sdiNumber").val()) {
                return true;
            } else {
                return false;
            }
        } else {
            return true;
        }
    }, 'Pec o SDI obbligatori.');

    
    if ($("#countryCode").children("option:selected").val() !== 'IT') {
        $("#provinceCode").attr("required", false);
        $("#provinceCode").attr("disabled", true);
        $('#provinceCode').val('').trigger('change');
    } else {
        $("#provinceCode").attr("required", true);
        $("#provinceCode").attr("disabled", false);
    }
    
    $( "#countryCode" ).change(function() {
        if ($("#countryCode").children("option:selected").val() !== 'IT') {
            $("#provinceCode").attr("required", false);
            $("#provinceCode").attr("disabled", true);
            $('#provinceCode').val('').trigger('change');
        } else {
            $("#provinceCode").attr("required", true);
            $("#provinceCode").attr("disabled", false);
        }
    });
    
//    if ($("#invoiceCountryCode").children("option:selected").val() !== 'IT') {
//        $("#invoiceProvinceCode").attr("required", false);
//        $("#invoiceProvinceCode").attr("disabled", true);
//        $('#invoiceProvinceCode').val('').trigger('change');
//    } else {
//        if ($("#einvoice").is(':checked')) {
//            $("#invoiceProvinceCode").attr("required", true);
//            $("#invoiceProvinceCode").attr("disabled", false);
//        }
//    }
//    
//    $( "#invoiceCountryCode" ).change(function() {
//        if ($("#invoiceCountryCode").children("option:selected").val() !== 'IT') {
//            $("#invoiceProvinceCode").attr("required", false);
//            $("#invoiceProvinceCode").attr("disabled", true);
//            $('#invoiceProvinceCode').val('').trigger('change');
//        } else {
//            if ($("#einvoice").is(':checked')) {
//                $("#invoiceProvinceCode").attr("required", true);
//                $("#invoiceProvinceCode").attr("disabled", false);
//            }
//        }
//    });
    
    // Openings
    $('[format="format-opening"]').formatter({
        pattern: '{{99}}:{{99}}'
    });
    $('[format="format-openings"]').formatter({
        pattern: '{{99}}:{{99}}-{{99}}:{{99}}'
    });
    
    // Switch
    // -------------------------
    if (Array.prototype.forEach) {
        var elems = Array.prototype.slice.call(document.querySelectorAll('.switchery'));
        elems.forEach(function(html) {
            var switchery = new Switchery(html);
        });
    }
    else {
        var elems = document.querySelectorAll('.switchery');

        for (var i = 0; i < elems.length; i++) {
            var switchery = new Switchery(elems[i]);
        }
    }
    
    $(".switchery").each(function() {
        if ($(this).val() === 'true') {
            $(this).attr("checked", "checked");
        }
    });
    
    // Default initialization
    $('.summernote').summernote({
        minHeight: 200,        
        toolbar: [                 
            ['style', ['bold', 'italic', 'underline', 'clear']],                        
            ['para', ['ul', 'ol']],            
            ['insert', ['link']],            
            ['view', ['fullscreen', 'codeview']]
        ],
        disableDragAndDrop: false,
        lang: 'it-IT'        
    });
    
    // Lunghezza campi
    // -------------------------
    $('.maxlength').maxlength({
        alwaysShow: true,
        placement: 'top-right'
    });
    
    // Campo valuta
    // -------------------------
    $('.currency').mask('#.##0,00', {reverse: true});

    // Select with search
    $('.select-search').select2(); 
    
    // Dropdown
    $('.select').select2({
        minimumResultsForSearch: Infinity
    });
    
    $('.select-search-pills').select2({
        language: 'it',
        maximumSelectionLength: 1,
        containerCssClass: 'border-bottom-ddd text-333',
        tags: true
    });    
    
        // Data costituzione
    // -------------------------
    $('.pickadate').pickadate({
        monthsFull: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
        monthsShort: ['Gen', 'Feb', 'Mar', 'Apr', 'Mag', 'Giu', 'Lug', 'Ago', 'Set', 'Ott', 'Nov', 'Dic'],
        weekdaysFull: ['Domenica', 'Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato'],
        weekdaysShort: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve','Sa'],
        selectMonths: true,
        selectYears: true,        
        firstDay: 1,
        disable: [7],
        today: 'Oggi',
        clear: 'Pulisci',
        close: 'Chiudi',
        format: 'dd/mm/yyyy',
        formatSubmit: 'dd/mm/yyyy', //non va https://amsul.ca/pickadate.js/date/#formats_use_hidden_only
        hiddenName: true,
        buttonClear: 'hidden'       
    });
    
    // Initialize validation
    $(".form-horizontal").validate({
        ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
        errorClass: 'validation-error-label',
        successClass: 'validation-valid-label',
        highlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        unhighlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        // Different components require proper error label placement
        errorPlacement: function (error, element) {

            // Styled checkboxes, radios, bootstrap switch
            if (element.parents('div').hasClass("checker") || element.parents('div').hasClass("choice") || element.parent().hasClass('bootstrap-switch-container')) {
                if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                    error.appendTo(element.parent().parent().parent().parent());
                } else {
                    error.appendTo(element.parent().parent().parent().parent().parent());
                }
            }

            // Unstyled checkboxes, radios
            else if (element.parents('div').hasClass('checkbox') || element.parents('div').hasClass('radio')) {
                error.appendTo(element.parent().parent().parent());
            }

            // Input with icons and Select2
            else if (element.parents('div').hasClass('has-feedback') || element.hasClass('select2-hidden-accessible')) {
                error.appendTo(element.parent());
            }

            // Inline checkboxes, radios
            else if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                error.appendTo(element.parent().parent());
            }

            // Input group, styled file input
            else if (element.parent().hasClass('uploader') || element.parents().hasClass('input-group')) {
                error.appendTo(element.parent().parent());
            } else {
                error.insertAfter(element);
            }
        },
        rules: {
            email: {
                email: true
            }
        }
    });
    
    $('.btn-copy-address').click(function () {
        $('#invoiceFullname').val($('#fullname').val());
        $('#invoiceCountryCode').val($('#countryCode').val()).trigger('change');
        $('#invoiceAddress').val($('#address').val());
        $('#invoicePostalCode').val($('#postalCode').val());
        $('#invoiceCity').val($('#city').val());
        $('#invoiceProvinceCode').val($('#provinceCode').val()).trigger('change');
    });
    
    $('.btn-cancel').click(function () {
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
            content: "Le modifiche fatte fin'ora verranno perse.",
            buttons: {
                annulla: {
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                },
                conferma: {
                    btnClass: 'bg-primary',
                    action: function () {
                        var url = $("#customersAddAbortUri").attr("href");
                        window.location.href = url;
                    }
                }
            }
        });
    });
    
});
