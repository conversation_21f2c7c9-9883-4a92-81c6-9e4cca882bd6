$(document).ready(function () {

    registerUpload();

});

function registerUpload() {

    // registering upload submit action
    $("form#form-upload-pages-verify").off("submit");
    $("form#form-upload-pages-verify").submit(function (event) {
        event.preventDefault();

        var formData = new FormData($(this)[0]);
        var url = $(this).attr('action');
        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success:
                    function (returndata) {

                        var uploadId = returndata;
                        $.unblockUI();
                        // refresh panel
                        var url = new URI();
                        url.removeSearch("uploadId");
                        url.addSearch("uploadId", uploadId);

                        $("#upload-page-panel").load(url + " #upload-page-panel", function () {
                            $(document).ready(function () {

                                // re-registering upload submit action
                                registerUpload();

                            });
                        });

                    },
            error:
                    function (response, status, errorThrown) {
                        $.unblockUI();
                        // error
                        console.log("response: " + JSON.stringify(response));
                        console.log("status: " + status);
                        console.log("errorThrown: " + errorThrown);
                    }
        });

        return false;
    });

}

