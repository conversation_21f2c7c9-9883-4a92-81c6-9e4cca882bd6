$(function() {

    // Table setup
    // ------------------------------

    // Setting datatable defaults
    $.extend( $.fn.dataTable.defaults, {
        autoWidth: false,
        responsive: true,
        dom: '<"datatable-header"fBl><"datatable-scroll-wrap"t><"datatable-footer"ip>',
        language: {
            processing: 'Elaborazione in corso...',
            loadingRecords: 'Caricamento in corso...',
            search: '<span>Filtra:</span> _INPUT_',
            searchPlaceholder: 'Ricerca...',
            lengthMenu: '<span>Mostra:</span> _MENU_',
            emptyTable: 'Non ci sono dati disponibili',
            info:       'Visualizzo da _START_ a _END_ di _TOTAL_ elementi',
            infoEmpty:  'Visualizzo da 0 a 0 di 0 elementi',
            infoFiltered: '(filtrati da _MAX_ elementi totali)',
            zeroRecords: 'Nessun risultato corrisponde alla ricerca',
            paginate: { 'first': 'Prima', 'last': 'Ultima', 'next': '&rarr;', 'previous': '&larr;' }
        },
        drawCallback: function () {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').addClass('dropup');
        },
        preDrawCallback: function() {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').removeClass('dropup');
        }
    });

    $.extend( $.fn.dataTableExt.oSort, {
        'sortable-value-pre': function ( a ) {
            if (a) {
                var nodes = $.parseHTML(a);
                if (nodes) {
                    var node = nodes[0];
                    if (node) {
                        if (node.hasAttribute('sortable-value')) {
                            a = node.getAttribute('sortable-value');
                        }
                    }
                }
            }
            return a;
        },

        'sortable-value-asc': function ( a, b ) {
            return a - b;
        },

        'sortable-value-desc': function ( a, b ) {
            return b - a;
        }
    } );

    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    });

    // Control position
    $('.datatable-responsive-control-right').DataTable({
        responsive: {
            details: {
                type: 'column',
                target: -1
            }
        },
        buttons: {
            buttons: [
                {
                    extend: 'excelHtml5',
                    className: 'btn btn-default',
                    exportOptions: {
                        columns: [0, 1, 2, 3, 4]
                    }
                },
                {
                    extend: 'pdfHtml5',
                    className: 'btn btn-default',
                    exportOptions: {
                        columns: [0, 1, 2, 3, 4]
                    }
                }
            ]
        },
        columnDefs: [
            {
                className: 'control',
                orderable: false,
                targets: -1
            },
            {
                type: 'sortable-value',
                targets: [1, 3]
            }
        ],
        order: [[2, "asc"]]
    });

    // External table additions
    // ------------------------------

    // Enable Select2 select for the length option
    $('.dataTables_length select').select2({
        minimumResultsForSearch: Infinity,
        width: 'auto'
    });

});
