$(function () {

    // Switch
    // -------------------------
    $(".switchery").each(function () {
        if ($(this).val() === 'true') {
            $(this).attr("checked", "checked");
        }
    });

    if (Array.prototype.forEach) {
        var elems = Array.prototype.slice.call(document.querySelectorAll('.switchery'));
        elems.forEach(function (html) {
            var switchery = new Switchery(html);
        });
    } else {
        var elems = document.querySelectorAll('.switchery');

        for (var i = 0; i < elems.length; i++) {
            var switchery = new Switchery(elems[i]);
        }
    }

    // Initialize validation
    $(".form-horizontal").validate({
        ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
        errorClass: 'validation-error-label',
        successClass: 'validation-valid-label',
        highlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        unhighlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        // Different components require proper error label placement
        errorPlacement: function (error, element) {

            // Styled checkboxes, radios, bootstrap switch
            if (element.parents('div').hasClass("checker") || element.parents('div').hasClass("choice") || element.parent().hasClass('bootstrap-switch-container')) {
                if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                    error.appendTo(element.parent().parent().parent().parent());
                } else {
                    error.appendTo(element.parent().parent().parent().parent().parent());
                }
            }

            // Unstyled checkboxes, radios
            else if (element.parents('div').hasClass('checkbox') || element.parents('div').hasClass('radio')) {
                error.appendTo(element.parent().parent().parent());
            }

            // Input with icons and Select2
            else if (element.parents('div').hasClass('has-feedback') || element.hasClass('select2-hidden-accessible')) {
                error.appendTo(element.parent());
            }

            // Inline checkboxes, radios
            else if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                error.appendTo(element.parent().parent());
            }

            // Input group, styled file input
            else if (element.parent().hasClass('uploader') || element.parents().hasClass('input-group')) {
                error.appendTo(element.parent().parent());
            } else {
                error.insertAfter(element);
            }
        },
        rules: {
            email: {
                email: true
            }
        }
    });

    // Dropdown
    $('.select').select2({
        minimumResultsForSearch: Infinity
    });

    $("#form-edit-firm").on('submit', function (e) {
        $(this).find('input[type="checkbox"]').each(function () {
            var checkbox = $(this);
            if (checkbox.is(':checked')) {
                checkbox.attr('value', '1');
            } else {
                checkbox.after().append(checkbox.clone().attr({type: 'hidden', value: 0}));
                checkbox.prop('disabled', true);
            }
        });
    });

    $('.btn-cancel').click(function () {
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
            content: "Le modifiche andranno perse.",
            buttons: {
                annulla: {
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                },
                conferma: {
                    btnClass: 'bg-primary',
                    action: function () {
                        window.location.reload();
                    }
                }
            }
        });
    });

    $("#btn-identifier-page").click(function () {
        var postToUrl = $("#dataReloadPagesIdentifierUri").attr("href");
        $.ajax({
            url: postToUrl,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            success: function (returndata) {
                $.unblockUI();
                location.reload();
            },
            error: function (response, status, errorThrown) {
                $.unblockUI();
                // warn
                $.alert({
                    theme: 'supervan',
                    escapeKey: true,
                    animation: 'top',
                    closeAnimation: 'bottom',
                    backgroundDismiss: true,
                    title: 'Oh oh! :(',
                    content: 'Aggiornamento non riuscito.'
                });
            }
        });
    });
    
    $("#btn-identifier-event").click(function () {
        var postToUrl = $("#dataReloadEventsIdentifierUri").attr("href");
        $.ajax({
            url: postToUrl,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            success: function (returndata) {
                $.unblockUI();
                location.reload();
            },
            error: function (response, status, errorThrown) {
                $.unblockUI();
                // warn
                $.alert({
                    theme: 'supervan',
                    escapeKey: true,
                    animation: 'top',
                    closeAnimation: 'bottom',
                    backgroundDismiss: true,
                    title: 'Oh oh! :(',
                    content: 'Aggiornamento non riuscito.'
                });
            }
        });
    });
});
