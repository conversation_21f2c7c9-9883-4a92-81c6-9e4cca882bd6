$(document).ready(function () {

    registerUpload();

});

function registerUpload() {

    // registering upload submit action
    $("form#form-upload-events-verify").off("submit");
    $("form#form-upload-events-verify").submit(function (event) {
        event.preventDefault();

        var formData = new FormData($(this)[0]);
        var url = $(this).attr('action');
        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success:
                    function (returndata) {
                        console.log("returndata: " + returndata);

                        var uploadId = returndata;
                        $.unblockUI();
                        // refresh panel
                        var url = new URI();
                        url.removeSearch("uploadId");
                        url.addSearch("uploadId", uploadId);

                        $("#upload-event-panel").load(url + " #upload-event-panel", function () {
                            $(document).ready(function () {

                                // re-registering upload submit action
                                registerUpload();

                            });
                        });

                    },
            error:
                    function (response, status, errorThrown) {
                        $.unblockUI();
                        // error
                        console.log("response: " + JSON.stringify(response));
                        console.log("status: " + status);
                        console.log("errorThrown: " + errorThrown);
                    }
        });

        return false;
    });

}

