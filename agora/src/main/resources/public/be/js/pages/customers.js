// init
moment.locale('it');

// globals
var table = null;

$(function() {

    // const
    var customersDataUri = $("#customersDataUri").attr("href");
    var customerViewUri = $("#customerViewUri").attr("href");
    var shopperUrl = $("#shopperUrl").attr("href");
    
    // Table setup
    // ------------------------------

    // Setting datatable defaults
    $.extend( $.fn.dataTable.defaults, {
        autoWidth: false,
        responsive: true,        
        dom: '<"datatable-header"fBl><"datatable-scroll-wrap"t><"datatable-footer"ip>',
        language: {
            processing: 'Elaborazione in corso...',
            loadingRecords: 'Caricamento in corso...',
            search: '<span>Filtra:</span> _INPUT_',
            searchPlaceholder: 'Ricerca cliente...',
            lengthMenu: '<span>Mostra:</span> _MENU_',
            emptyTable: 'Non ci sono dati disponibili',
            info:       'Visualizzo da _START_ a _END_ di _TOTAL_ elementi',
            infoEmpty:  'Visualizzo da 0 a 0 di 0 elementi',
            infoFiltered: '(filtrati da _MAX_ elementi totali)',
            zeroRecords: 'Nessun risultato corrisponde alla ricerca',            
            paginate: { 'first': 'Prima', 'last': 'Ultima', 'next': '&rarr;', 'previous': '&larr;' }
        },
        drawCallback: function () {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').addClass('dropup');
        },
        preDrawCallback: function() {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').removeClass('dropup');
        }
    });    

    $.extend( $.fn.dataTableExt.oSort, {
        'sortable-value-pre': function ( a ) {
            if (a) {
                var nodes = $.parseHTML(a);
                if (nodes) {
                    var node = nodes[0];
                    if (node) {
                        if (node.hasAttribute('sortable-value')) {
                            a = node.getAttribute('sortable-value');
                        }
                    }
                }
            }
            return a;
        },

        'sortable-value-asc': function ( a, b ) {
            return a - b;
        },

        'sortable-value-desc': function ( a, b ) {
            return b - a;
        }
    } );
    
    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    });
    
    // Control position
    var current = new URI();
    var params = current.search(true);
    var firstUri = new URI(customersDataUri);
    if (params.startDate) {
        firstUri.addSearch("startDate", params.startDate);
    }
    if (params.endDate) {
        firstUri.addSearch("endDate", params.endDate);
    }    
    if (params.selectedVendors) {
        firstUri.addSearch("selectedVendors", params.selectedVendors);
    }
    if (params.selectedCities) {
        firstUri.addSearch("selectedCities", params.selectedCities);
    }
    
    var isHead = $("#isHead").val();
    if (isHead === 'false') {
        isHead = false;
    }
    table = $('.datatable-responsive-control-right').DataTable({
        processing: true,
        //serverSide: true,
        ajax: firstUri.toString(),
        responsive: {
            details: {
                type: 'column',
                target: -1
            }
        },
        order: [[ 6, "desc" ]],
        buttons: {            
            buttons: [
                {
                    extend: 'csvHtml5',
                    className: 'btn btn-default',
                    filename: 'clienti',
                    fieldSeparator: ';'                    
                },
                {
                    extend: 'excelHtml5',
                    className: 'btn btn-default',
                    exportOptions: {
                        columns: [0, 1, 2, 4, 5, 6 ,7]
                    }
                },
                {
                    extend: 'pdfHtml5',
                    className: 'btn btn-default',
                    exportOptions: {
                        columns: [0, 1, 2, 4, 5, 6 ,7]
                    }
                }
            ]
        }, 
        columnDefs: [
            {
                className: 'control',
                orderable: false,
                targets: -1
            },
            {
                targets: 0,
                data: null,
                render: function (entry, type, row, meta) {
                    var ragioneSociale = empty(entry.customer.fullname);
                    var anag = '<a target="_blank" href="' + customerViewUri + entry.customer._id + '" class="text-bold">' + name(entry.customer);
                    if (ragioneSociale !== '') {
                        anag + ' \\ ' + empty(entry.customer.fullname) + '</a>';
                    }
                    return  anag;
                }
            },
            {
                targets: 1,
                data: null,
                render: function (entry, type, row, meta) {
                    if (entry.customer.channel === 'B2C') {
                        return '<span class="label bg-info">Privato</span>'
                    } else if (entry.customer.channel === 'B2B') {
                        return '<span class="label bg-indigo">Azienda</span>'
                    }
                }
            },
            {
                targets: 2,
                data: null,
                render: function (entry, type, row, meta) {
                    return  empty(entry.customer.address) + ', ' + empty(entry.customer.postalCode) +
                            '<div class="text-muted text-size-small">' +
                            empty(entry.customer.city) + ' (' + empty(entry.customer.provinceCode) + ')' +
                            '</div>'
                    ;
                }
            },
            {
                targets: 3,
                data: null,
                render: function (entry, type, row, meta) {
                    return  '<ul class="list list-unstyled no-margin">' +
                            '    <li class="no-margin truncate">' +
                            '        <i class="icon-envelop5 text-size-base text-blue position-left"></i>' +
                            '        <a href="mailto:' + empty(entry.customer.email) + '">' + empty(entry.customer.email) + '</a>' +
                            '    </li>' +
                            '' +
                            '    <li class="no-margin">' +
                            '        <i class="icon-phone2 text-size-base text-success-700 position-left"></i>' +
                            '        <a href="tel:' + empty(entry.customer.phoneNumber) + '">' + empty(entry.customer.phoneNumber) + '</a>' +
                            '    </li>' +
                            '</ul>'
                    ;
                }
            },
            
            {
                targets: 4,
                data: null,
                render: function (entry, type, row, meta) {
                    return empty(entry.customer.email);
                },
                visible: false,     // export only
                orderable: false
            },
            {
                targets: 5,
                data: null,
                render: function (entry, type, row, meta) {
                    return empty(entry.customer.phoneNumber);
                },
                visible: false,     // export only
                orderable: false
            },
            
            {
                targets: 6,
                data: null,
                render: function (entry, type, row, meta) {
                    if (entry && entry.customer && entry.customer.creation) {
                        return '<span sortable-value="' + entry.customer.creation + '">' + date(entry.customer.creation, 'DD/MM/YYYY HH:mm') + '</span>';
                    } else {
                        return '';
                    }
                },
                orderable: true,
                type: 'sortable-value'
            },
            {
                targets: 7,
                visible: isHead,
                data: null,
                render: function (entry, type, row, meta) {
                    console.log(entry.user.profileType);
                    if (entry.user !== null && entry.user.profileType !== null) {
                        if (entry.user.profileType === 'customer') {
                            return '<span class="label label-flat border-success-700 text-success-700">CONFERMATO</span>'
                        } else if (entry.user.profileType === 'unconfirmed') {
                            return '<span class="label label-flat border-warning-600 text-warning-600">NON CONFERMATO</span>'
                        } else {
                            return '<span class="label label-flat border-slate-600 text-slate-600">INSERITO MANUALMENTE</span>'
                        }
                    } else {
                        return '<span class="label label-flat border-slate-600 text-slate-600">INSERITO MANUALMENTE</span>'
                    }
                }
            },
            {
                targets: 8,
                data: null,
                render: function (entry, type, row, meta) {
                    return '';
                },
                orderable: false
            }
        ]
    });
    
    table.on( 'draw.dt', function (  e, settings ) {
        bindInitCartAdd();
    });    
    
    table.on( 'responsive-display', function ( e, datatable, row, showHide, update ) {
        bindInitCartAdd();
    });
    
    table.on( 'responsive-resize', function ( e, datatable, columns ) {
        bindInitCartAdd();
    });

    
    // External table additions
    // ------------------------------

    // Enable Select2 select for the length option
    $('.dataTables_length select').select2({
        minimumResultsForSearch: Infinity,
        width: 'auto'
    });

    var startDate = moment($('#startDate').text());
    var endDate = moment($('#endDate').text());
    $('.daterange-predefined').daterangepicker(
        {            
            startDate: startDate,
            endDate: endDate,
            minDate: '01/01/2022',
            maxDate: '31/12/2099',            
            ranges: {
                'Oggi': [moment(), moment()],
                'Ieri': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Ultimi 7 giorni': [moment().subtract(6, 'days'), moment()],
                'Ultimi 30 giorni': [moment().subtract(29, 'days'), moment()],
                'Questo mese': [moment().startOf('month'), moment().endOf('month')],
                'Scorso mese': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
                'Ultimi 3 mesi': [moment().subtract(3, 'month').startOf('month'), moment().endOf('month')],
                'Da sempre': [moment('2022-01-01'), moment().endOf('month')]
            },
            locale: {
                format: 'DD/MM/YYYY',
                applyLabel: 'Applica',
                cancelLabel: 'Annulla',
                startLabel: 'Data inizio',
                endLabel: 'Data fine',
                customRangeLabel: 'Personalizzato',
                daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve','Sa'],
                monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
                firstDay: 1
            },
            opens: 'left',
            applyClass: 'btn-small bg-primary filter-date',
            cancelClass: 'btn-small btn-default'
        },
        function(start, end) {
            $('.daterange-predefined span').html(start.format('MMMM D, YYYY') + ' &nbsp; - &nbsp; ' + end.format('MMMM D, YYYY'));
            
            var url = new URI(customersDataUri);
            
            var startDate = start.format('DD/MM/YYYY');
            var endDate = end.format('DD/MM/YYYY');            

            var selectedVendors = "";
            $('.agora-vendor-filter:checked:enabled').each(function() {
                selectedVendors += $(this).attr('agora-vendor-value') + "|";
            });

            var selectedCities = "";
            $('.agora-city-filter:checked:enabled').each(function() {
                selectedCities += $(this).attr('agora-city-value') + "|";
            });

            url.removeSearch("startDate");
            if (startDate) {
                url.addSearch("startDate", startDate);
            }

            url.removeSearch("endDate");
            if (endDate) {
                url.addSearch("endDate", endDate);
            }            
            
            url.removeSearch("selectedVendors");
            if (selectedVendors) {
                url.addSearch("selectedVendors", selectedVendors);
            }
            
            url.removeSearch("selectedCities");
            if (selectedCities) {
                url.addSearch("selectedCities", selectedCities);
            }

            if (table) {
                table.ajax.url(url.toString()).load(function () {
                    bindInitCartAdd()
               });
            }
           
        }
    );

    // Display date format
    $('.daterange-predefined span').html(startDate.format('MMMM D, YYYY') + ' &nbsp; - &nbsp; ' + endDate.format('MMMM D, YYYY'));
    
    $("#filter-apply").off();
    $("#filter-apply").click(function(event) {
        var url = new URI(customersDataUri);

        var startDate = $('.daterange-predefined').data('daterangepicker').startDate.format('DD/MM/YYYY');
        var endDate = $('.daterange-predefined').data('daterangepicker').endDate.format('DD/MM/YYYY');
        
        var selectedVendors = "";
        $('.agora-vendor-filter:checked:enabled').each(function() {
            selectedVendors += $(this).attr('agora-vendor-value') + "|";
        });
        
        var selectedCities = "";
        $('.agora-city-filter:checked:enabled').each(function() {
            selectedCities += $(this).attr('agora-city-value') + "|";
        });

        url.removeSearch("startDate");
        if (startDate) {
            url.addSearch("startDate", startDate);
        }

        url.removeSearch("endDate");
        if (endDate) {
            url.addSearch("endDate", endDate);
        }        
        
        url.removeSearch("selectedVendors");
        if (selectedVendors) {
            url.addSearch("selectedVendors", selectedVendors);
        }
        
        url.removeSearch("selectedCities");
        if (selectedCities) {
            url.addSearch("selectedCities", selectedCities);
        }
        
        if (table) {
            table.ajax.url(url.toString()).load(function () {
                // ...
            });
        }
        
        return false;
    });
    
});

function name(entity) {
    
    var result = '*Nome* *Cognome*';
    
    if (entity) {
        // nome / cognome
        if (entity.name && entity.lastname) {
            var parts = [];
            parts.push(entity.name);
            parts.push(entity.lastname);
            result = parts.join(' ');
            result = result.trim();
        }
        if (entity.fullname) {
            result = entity.fullname.trim();
        }
    }
    
    return result;
}

function date(value, format) {
    
    var result = '';
    
    if (!format) {
        format = 'DD MMMM YYYY';
    }
    if (value && (value.length > 0)) {
        result = moment.utc(value).format(format);
    }
    
    return result;
}

function empty(value) {
    return value ? value : '';
}

// bind inicart action
function bindInitCartAdd() {

    $('.initcart').click(function (event) {

        // disable the default form submission
        event.preventDefault();

        var urlInitCart = new URI($('#ordersAddInitUri').attr('href'));
        urlInitCart.removeSearch("customerId");
        urlInitCart.addSearch("customerId", $(this).attr('data-customerId'));

        var urlOrdersAdd = new URI($('#ordersAddUri').attr('href'));
        urlOrdersAdd.removeSearch("customerId");
        urlOrdersAdd.addSearch("customerId", $(this).attr('data-customerId'));

        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class=" icon-checkmark3 icon-2x"></i><br/><br/> Procedo con un nuovo ordine?',
            content: "",
            columnClass: "col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                cancel: {
                    text: 'Annulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                confirm: {
                    text: 'Conferma',
                    btnClass: 'btn-danger mb-5',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: urlInitCart,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        window.location.href = urlOrdersAdd;
                                    },
                            error:
                                    function (response, status, errorThrown) {
                                        $.unblockUI();
                                        // warn
                                        $.alert({
                                            theme: 'supervan',
                                            escapeKey: true,
                                            animation: 'top',
                                            closeAnimation: 'bottom',
                                            backgroundDismiss: true,
                                            title: 'Oh oh! :(',
                                            content: 'Impossibile svuotare il carrello.'
                                        });
                                    }
                        });
                    }
                }
            }
        });

        return false;
    });       
}