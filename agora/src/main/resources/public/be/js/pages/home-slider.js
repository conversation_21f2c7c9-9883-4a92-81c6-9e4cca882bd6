$(function() {

    // Table setup
    // ------------------------------

    // Setting datatable defaults
    $.extend( $.fn.dataTable.defaults, {
        autoWidth: false,
        responsive: true,
        dom: '<"datatable-header"fBl><"datatable-scroll-wrap"t><"datatable-footer"ip>',        
        language: {
            search: '<span>Filtra:</span> _INPUT_',
            searchPlaceholder: 'Filtra home slider...',
            lengthMenu: '<span>Mostra:</span> _MENU_',
            emptyTable: 'Nessun dato disponibile',
            info:       'Visualizzati _START_ di _END_ su _TOTAL_ elementi',
            infoEmpty:  'Visualizzati 0 di 0 su 0 elementi',
            infoFiltered: '(filtrato da _MAX_ elementi)',
            paginate: { 'first': 'First', 'last': 'Last', 'next': '&rarr;', 'previous': '&larr;' }
        },        
        drawCallback: function () {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').addClass('dropup');
        },
        preDrawCallback: function() {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').removeClass('dropup');
        }
    });

    // State saving
    var table = $('.datatable-home-sliders').DataTable({
        responsive: {
            details: {
                type: 'column',
                target: -1
            }
        },
        buttons: [
            {
                extend: 'colvis',
                text: '<i class="icon-grid3"></i> <span class="caret"></span>',
                className: 'btn bg-primary btn-icon'
            }
        ],
        stateSave: true,
        columnDefs: [
            {
                className: 'control',
                orderable: false,
                targets: -1
            },
            {
                type: 'date-dd-mmm-yyyy',
                targets: 1
            }
            
        ],
        order: [1, 'desc']
    });

    // External table additions
    // ------------------------------

    // Launch Uniform styling for checkboxes
    $('.ColVis_Button').addClass('btn bg-green btn-icon').on('click mouseover', function() {
        $('.ColVis_collection input').uniform();
    });

    // Enable Select2 select for the length option
    $('.dataTables_length select').select2({
        minimumResultsForSearch: Infinity,
        width: 'auto'
    });

    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    });

    // Hide main sidebar in Dual Sidebar
    $(document).on('click', '.sidebar-main-hide', function (e) {
        e.preventDefault();
        $('.datatable-properties').DataTable()
         .columns.adjust()
         .responsive.recalc();
    });
    
});


