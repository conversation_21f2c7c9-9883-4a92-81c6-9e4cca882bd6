$(function() {

    // Table setup
    // ------------------------------

    // Setting datatable defaults
    $.extend( $.fn.dataTable.defaults, {
        autoWidth: false,
        responsive: true,
        dom: '<"datatable-header"fBl><"datatable-scroll-wrap"t><"datatable-footer"ip>',
        language: {
            processing: 'Elaborazione in corso...',
            loadingRecords: 'Caricamento in corso...',
            search: '<span>Filtra:</span> _INPUT_',
            searchPlaceholder: 'Ricerca...',
            lengthMenu: '<span>Mostra:</span> _MENU_',
            emptyTable: 'Non ci sono dati disponibili',
            info:       'Visualizzo da _START_ a _END_ di _TOTAL_ elementi',
            infoEmpty:  'Visualizzo da 0 a 0 di 0 elementi',
            infoFiltered: '(filtrati da _MAX_ elementi totali)',
            zeroRecords: 'Nessun risultato corrisponde alla ricerca',            
            paginate: { 'first': 'Prima', 'last': 'Ultima', 'next': '&rarr;', 'previous': '&larr;' }
        },
        drawCallback: function () {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').addClass('dropup');
        },
        preDrawCallback: function() {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').removeClass('dropup');
        }
    });

    $.extend( $.fn.dataTableExt.oSort, {
        'sortable-value-pre': function ( a ) {
            if (a) {
                var nodes = $.parseHTML(a);
                if (nodes) {
                    var node = nodes[0];
                    if (node) {
                        if (node.hasAttribute('sortable-value')) {
                            a = node.getAttribute('sortable-value');
                        }
                    }
                }
            }
            return a;
        },

        'sortable-value-asc': function ( a, b ) {
            return a - b;
        },

        'sortable-value-desc': function ( a, b ) {
            return b - a;
        }
    } );

    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    });
    
    // Switch
    // -------------------------
    if (Array.prototype.forEach) {
        var elems = Array.prototype.slice.call(document.querySelectorAll('.switchery'));
        elems.forEach(function(html) {
            var switchery = new Switchery(html);
        });
    }
    else {
        var elems = document.querySelectorAll('.switchery');

        for (var i = 0; i < elems.length; i++) {
            var switchery = new Switchery(elems[i]);
        }
    }
    
    $(".switchery").each(function() {
        if ($(this).val() === 'true') {
            $(this).attr("checked", "checked");
        }
    });
    
    // Default initialization
    $('.summernote').summernote({
        minHeight: 500,        
        toolbar: [                 
            ['style', ['style', 'bold', 'italic', 'underline', 'clear']],            
            ['font', ['strikethrough', 'superscript', 'subscript']],
            ['fontsize', ['fontsize']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['height', ['height']],
            ['insert', ['link', 'picture', 'video']],
            ['table', ['table']],
            ['view', ['fullscreen', 'codeview']]
        ],
        popover: {
            image: [
                ['custom', ['imageAttributes']],
                ['imagesize', ['imageSize100', 'imageSize50', 'imageSize25']],
                ['float', ['floatLeft', 'floatRight', 'floatNone']],
                ['remove', ['removeMedia']]
            ]
        },
        imageAttributes:{
            icon:'<i class="note-icon-pencil"/>',
            removeEmpty:false, // true = remove attributes | false = leave empty if present
            disableUpload: false // true = don't display Upload Options | Display Upload Options
        },
        disableDragAndDrop: false,
        lang: 'it-IT',
        callbacks: {
            onImageUpload: function(files, editor, welEditable) {
                // ajax loading of image as on
                // https://github.com/summernote/summernote/issues/1173#issuecomment-115730874
                if (files) {
                    
                    // use this code if you want to send ONLY one file each time
                    //sendFile(files[0], editor, welEditable);
                    
                    // use this code if you want to send MULTIPLE files per time
                    for (var i = 0; i < files.length; i++) {
                        sendFile(files[i], editor, welEditable);
                    }
                    
                }
            }
        }
    });
    
    // Control position
    var table = $('.datatable-responsive-control-right').DataTable({
        responsive: {
            details: {
                type: 'column',
                target: -1
            }
        },
        order: [[ 0, "desc" ]],
        buttons: {            
            buttons: [                
                {
                    extend: 'excelHtml5',
                    className: 'btn btn-default',
                    exportOptions: {
                        columns: [0, 1, 2, 3]
                    }
                },
                {
                    extend: 'pdfHtml5',
                    className: 'btn btn-default',
                    exportOptions: {
                        columns: [0, 1, 2, 3]
                    }
                }
            ]
        },
        columnDefs: [
            {
                className: 'control',
                orderable: false,
                targets: -1
            },
            { 
                orderable: true,
                type: 'sortable-value',
                targets: [3]
            }
        ]
    });
    
    table.on( 'responsive-display', function ( e, datatable, row, showHide, update ) {
        
    });
    
    table.on( 'responsive-resize', function ( e, datatable, columns ) {
       
    });
    
    // External table additions
    // ------------------------------

    // Enable Select2 select for the length option
    $('.dataTables_length select').select2({
        minimumResultsForSearch: Infinity,
        width: 'auto'
    });
    
    // Form components
    // ------------------------------

    // Select with search
    $('.select-search').select2(); 

    // Select2 selects
    $('.select').select2({
        minimumResultsForSearch: Infinity
    });

    $('.select-search-pills').select2({
        language: 'it',
        maximumSelectionLength: 1,
        containerCssClass: 'border-bottom-ddd text-333',
        tags: true
    });    

    // Styled file input
    $(".file-styled").uniform({
        fileButtonClass: 'action btn bg-warning'
    });


    // Styled checkboxes, radios
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    });               
    
    
    // Default initialization
    $('.wysihtml5-default').wysihtml5({
        parserRules:  wysihtml5ParserRules,
        stylesheets: ["https://siteria.it/tpls/be/1/2.1/layout_5/LTR/material/full/assets/css/components.css"]
    });


    // Simple toolbar
    $('.wysihtml5-min').wysihtml5({
        parserRules:  wysihtml5ParserRules,
        stylesheets: ["https://siteria.it/tpls/be/1/2.1/layout_5/LTR/material/full/assets/css/components.css"],
        "font-styles": true, // Font styling, e.g. h1, h2, etc. Default true
        "emphasis": true, // Italics, bold, etc. Default true
        "lists": false, // (Un)ordered lists, e.g. Bullets, Numbers. Default true
        "html": false, // Button which allows you to edit the generated HTML. Default false
        "link": false, // Button to insert a link. Default true
        "image": false, // Button to insert an image. Default true,
        "action": false, // Undo / Redo buttons,
        "color": true // Button to change color of font
    });

    // Data costituzione
    // -------------------------
    $('.pickadate').pickadate({
        monthsFull: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
        monthsShort: ['Gen', 'Feb', 'Mar', 'Apr', 'Mag', 'Giu', 'Lug', 'Ago', 'Set', 'Ott', 'Nov', 'Dic'],
        weekdaysFull: ['Domenica', 'Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato'],
        weekdaysShort: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve','Sa'],
        selectMonths: true,
        selectYears: true,        
        firstDay: 1,
        disable: [7],
        today: 'Oggi',
        clear: 'Pulisci',
        close: 'Chiudi',
        format: 'dd/mm/yyyy',
        formatSubmit: 'dd/mm/yyyy', //non va https://amsul.ca/pickadate.js/date/#formats_use_hidden_only
        hiddenName: true,
        buttonClear: 'hidden'       
    });

    // Initialize validation
    $(".form-horizontal").validate({
        ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
        errorClass: 'validation-error-label',
        successClass: 'validation-valid-label',
        highlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        unhighlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        // Different components require proper error label placement
        errorPlacement: function (error, element) {

            // Styled checkboxes, radios, bootstrap switch
            if (element.parents('div').hasClass("checker") || element.parents('div').hasClass("choice") || element.parent().hasClass('bootstrap-switch-container')) {
                if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                    error.appendTo(element.parent().parent().parent().parent());
                } else {
                    error.appendTo(element.parent().parent().parent().parent().parent());
                }
            }

            // Unstyled checkboxes, radios
            else if (element.parents('div').hasClass('checkbox') || element.parents('div').hasClass('radio')) {
                error.appendTo(element.parent().parent().parent());
            }

            // Input with icons and Select2
            else if (element.parents('div').hasClass('has-feedback') || element.hasClass('select2-hidden-accessible')) {
                error.appendTo(element.parent());
            }

            // Inline checkboxes, radios
            else if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                error.appendTo(element.parent().parent());
            }

            // Input group, styled file input
            else if (element.parent().hasClass('uploader') || element.parents().hasClass('input-group')) {
                error.appendTo(element.parent().parent());
            } else {
                error.insertAfter(element);
            }
        },
        rules: {
            email: {
                email: true
            }
        }
    });

    // Switch
    // -------------------------
    $(".switchery").each(function() {
        if ($(this).val() === 'true') {
            $(this).attr("checked", "checked");
        }
    });

    if (Array.prototype.forEach) {
        var elems = Array.prototype.slice.call(document.querySelectorAll('.switchery'));
        elems.forEach(function(html) {
            var switchery = new Switchery(html);
        });
    }
    else {
        var elems = document.querySelectorAll('.switchery');

        for (var i = 0; i < elems.length; i++) {
            var switchery = new Switchery(elems[i]);
        }
    }
    
    // Styled checkboxes, radios
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    });

    $("#form-add-customer").on('submit', function(e) {
        $(this).find('input[type="checkbox"]').each( function () {
            var checkbox = $(this);
            if( checkbox.is(':checked')) {
                checkbox.attr('value','1');
            } else {
                checkbox.after().append(checkbox.clone().attr({type:'hidden', value:0}));
                checkbox.prop('disabled', true);
            }
        });
    });

    // bind delete action
    $('#deleteCustomer').click(function (event) {

        // disable the default form submission
        event.preventDefault();

        var postToUrl = $('#customerRemoveUri').attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Eliminazione cliente!',
            content: "Conferma eliminazione?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-primary mb-5',
                    action: function () {
                        $.blockUI()
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#customersSuccessUri").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: label('common.delete.failed')
                                    });
                                }
                        });                    
                    }                    
                }
            }
        });        

        return false;
    });

    $('.btn-cancel').click(function () {
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
            content: "Le modifiche fatte fin'ora verranno perse.",
            buttons: {
                annulla: {
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                },
                conferma: {
                    btnClass: 'bg-primary',
                    action: function () {
                        var url = $("#customerViewAbortUri").attr("href");
                        window.location.href = url;
                    }
                }
            }
        });
    });

    bindAddressInsert();
    bindAddressEdit();
    
});

function statusUpdate(event, postToUrl) {
    // disable the default form submission
    event.preventDefault();

    $.confirm({
        theme: 'supervan',
        escapeKey: true,
        animation: 'top',
        closeAnimation: 'bottom',
        backgroundDismiss: true,
        title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Aggiornamento stato!',
        content: "Confermi?",
        columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
        buttons: {
            annulla: {
                text: 'Annulla',
                btnClass: 'btn-default mb-5',
                action: function () {
                    //close
                }
            },
            conferma: {
                text: 'Conferma',
                btnClass: 'bg-primary mb-5',
                action: function () {
                    $.blockUI();
                    $.ajax({
                        url: postToUrl,
                        type: 'POST',
                        data: null,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success:
                                function (returndata) {
                                    $.unblockUI();
                                    var url = $("#customerViewSuccessUri").attr("href");
                                    window.location.href = url;
                                },
                        error:
                            function (response, status, errorThrown) {
                                $.unblockUI();
                                // warn
                                $.alert({
                                    theme: 'supervan',
                                    escapeKey: true,
                                    animation: 'top',
                                    closeAnimation: 'bottom',
                                    backgroundDismiss: true,
                                    title: 'Oh oh! :(',
                                    content: 'Aggiornamento non riuscito.'
                                });
                            }
                    });
                }
            }
        }
    });

    return false;
}
