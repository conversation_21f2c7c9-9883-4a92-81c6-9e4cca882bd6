$(function() {

    // Lunghezza campi
    // -------------------------
    $('.maxlength').maxlength({
        alwaysShow: true,
        placement: 'top-right'
    });

    $('.btn-cancel').click(function () {
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sicuro?',
            content: "Tutte le modifiche andranno perse.",
            buttons: {
                cancel: {
                    text: 'Annulla',
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                },
                Conferma: {
                    btnClass: 'bg-primary',
                    action: function () {
                        window.location.href = $('#homeSlidersUri').attr('href');
                    }
                }
            }
        });
    });	
    
    // bind delete action
    $('#btn-delete').click(function (event) {

        // disable the default form submission
        event.preventDefault();

        var postToUrl = $('#homeSliderRemoveUri').attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-trash icon-2x"></i><br/><br/> Eliminazione home slider',
            content: "Procedi?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                cancel: {
                    text: 'Annulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                Conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-danger-600 mb-5',
                    action: function () {
                        $.blockUI()
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#homeSlidersSuccessUri").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Impossibile eliminare.'
                                    });
                                }
                        });                    
                    }                    
                }
            }
        });        

        return false;
    });    
});
