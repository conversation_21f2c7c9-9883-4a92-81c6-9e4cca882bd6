// init
moment.locale('it');

var REFRESH_AFTER = 500;
var REFRESH_EACH  = 1000;

$(function() {
    // Table setup
    // ------------------------------

    // Setting datatable defaults
    $.extend( $.fn.dataTable.defaults, {
        autoWidth: false,
        responsive: true,
        dom: '<"datatable-header"fBl><"datatable-scroll-wrap"t><"datatable-footer"ip>',
        language: {
            processing: 'Elaborazione in corso...',
            loadingRecords: 'Caricamento in corso...',
            search: '<span>Filtra:</span> _INPUT_',
            searchPlaceholder: 'Ricerca...',
            lengthMenu: '<span>Mostra:</span> _MENU_',
            emptyTable: 'Non ci sono dati disponibili',
            info:       'Visualizzo da _START_ a _END_ di _TOTAL_ elementi',
            infoEmpty:  'Visualizzo da 0 a 0 di 0 elementi',
            infoFiltered: '(filtrati da _MAX_ elementi totali)',
            zeroRecords: 'Nessun risultato corrisponde alla ricerca',            
            paginate: { 'first': 'Prima', 'last': 'Ultima', 'next': '&rarr;', 'previous': '&larr;' }
        },
        drawCallback: function () {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').addClass('dropup');
        },
        preDrawCallback: function() {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').removeClass('dropup');
        }
    });

    $.extend( $.fn.dataTableExt.oSort, {
        'sortable-value-pre': function ( a ) {
            if (a) {
                var nodes = $.parseHTML(a);
                if (nodes) {
                    var node = nodes[0];
                    if (node) {
                        if (node.hasAttribute('sortable-value')) {
                            a = node.getAttribute('sortable-value');
                        }
                    }
                }
            }
            return a;
        },

        'sortable-value-asc': function ( a, b ) {
            return a - b;
        },

        'sortable-value-desc': function ( a, b ) {
            return b - a;
        }
    } );

    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    });
    
    // Control position
    $('.datatable-responsive-control-right').DataTable({
        responsive: {
            details: {
                type: 'column',
                target: -1
            }
        },
        order: [[ 0, "desc" ]],
        buttons: {            
            buttons: [                
                {
                    extend: 'excelHtml5',
                    className: 'btn btn-default',
                    exportOptions: {
                        columns: [0, 1, 2, 3, 4, 5, 6]
                    }
                },
                {
                    extend: 'pdfHtml5',
                    className: 'btn btn-default',
                    exportOptions: {
                        columns: [0, 1, 2, 3, 4, 5, 6]
                    }
                }
            ]
        }, 
        columnDefs: [
            {
                className: 'control',
                orderable: false,
                targets: -1
            },
            {
                targets: [7],
                orderable: false,
                width: '100px'
            }
        ]
    });
    
    // External table additions
    // ------------------------------

    // Enable Select2 select for the length option
    $('.dataTables_length select').select2({
        minimumResultsForSearch: Infinity,
        width: 'auto'
    });
    
    var startDate = moment($('#startDate').text());
    var endDate = moment($('#endDate').text());
    $('.daterange-predefined').daterangepicker(
        {
            startDate: startDate,
            endDate: endDate,
            minDate: '01/01/2017',
            maxDate: '12/31/2099',
            //dateLimit: { days: 366 },
            ranges: {
                'Oggi': [moment(), moment()],
                'Ieri': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Ultimi 7 giorni': [moment().subtract(6, 'days'), moment()],
                'Ultimi 30 giorni': [moment().subtract(29, 'days'), moment()],
                'Questo mese': [moment().startOf('month'), moment().endOf('month')],
                'Ultimo mese': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            },
            locale: {
                format: 'DD/MM/YYYY',
                applyLabel: 'Applica',
                cancelLabel: 'Annulla',
                startLabel: 'Data inizio',
                endLabel: 'Data fine',
                customRangeLabel: 'Personalizzato',
                daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve','Sa'],
                monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
                firstDay: 1
            },
            opens: 'left',
            applyClass: 'btn-small bg-black filter-date',
            cancelClass: 'btn-small btn-default'
        },
        function(start, end) {
            $('.daterange-predefined span').html(start.format('MMMM D, YYYY') + ' &nbsp; - &nbsp; ' + end.format('MMMM D, YYYY'));
            
            var url = new URI();
            
            var startDate = start.format('DD/MM/YYYY');
            var endDate = end.format('DD/MM/YYYY');
            
            url.removeSearch("startDate");
            if (startDate) {
                url.addSearch("startDate", startDate);
            }

            url.removeSearch("endDate");
            if (endDate) {
                url.addSearch("endDate", endDate);
            }
            
            window.location.href = url.toString();
           
        }
    );

    // Display date format
    $('.daterange-predefined span').html(startDate.format('MMMM D, YYYY') + ' &nbsp; - &nbsp; ' + endDate.format('MMMM D, YYYY'));

    bindMailSend();
    startProgress();

});

function bindMailSend() {
    $('.mailnotification-send').off();
    $('.mailnotification-send').click(function(event) {
        
        var url = new URI($(this).attr('href'));
        
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Attenzione!',
            content: "La procedura potrà durare alcuni secondi. Vuoi continuare?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-cancel',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-black mb-5',
                    action: function () {
                        $.ajax({
                            url: url,
                            type: 'GET',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        console.info('Operazione completata!');
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    // warn
                                    var msg = 'Operazione non riuscita.';
                                    if (response) {
                                        if (response.responseText) {
                                            msg = response.responseText;
                                        }
                                    }
                                    console.error(msg);
                                }
                        });
                        
                        // page reload
                        setTimeout(function() {
                            window.location.reload();
                        }, REFRESH_AFTER);
                        
                    }
                }
            }
        });

        return false;
    });    
}

function startProgress() {
    
    var processing = $('#mailnotificationAlignmentProcessing').text() === "true";
    var url = $("#mailnotificationStatusUri").attr("href");

    if (url) {
        if (processing) {
            
            var timer = window.setInterval(function() {

                $.ajax({
                    url: url,
                    type: 'GET',
                    data: null,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success:
                            function (returndata) {
                                if (returndata && (returndata !== '') && (returndata !== 'null')) {
                                    var username = $('#mailnotificationAlignmentUser').text();
                                    var text = username || '-';
                                    var json = JSON.parse(returndata);
                                    text += ' ' + (json.operation || '-');
                                    text += ' (' + (json.row || 0);
                                    text += '/' + (json.count || 0) + ')';
                                    $('#progress-label').show();
                                    $('#progress-counter').text(text);
                                } else {
                                    window.location.reload();
                                }
                            },
                    error:
                        function (response, status, errorThrown) {
                            // warn
                            var msg = 'Operazione non riuscita.';
                            if (response) {
                                if (response.responseText) {
                                    msg = response.responseText;
                                }
                            }
                            console.error(msg);
                            $('#progress-label').hide();
                            $('#progress-counter').text('');
                        }
                });

            }, REFRESH_EACH);
            
        }
    }
    
}
