$(function() {

    // Initialize Select2 for event search
    $('#eventId').select2({
        minimumInputLength: 3,
        placeholder: 'Cerca evento per nome...',
        allowClear: true,
        ajax: {
            url: $('#dataEventsUri').attr('href'),
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    name: params.term,
                    page: params.page || 1
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;

                var results = [];
                if (data) {
                    results = data.map(function (item) {
                        return {
                            id: item[0],
                            text: item[1],
                            startDate: item[5] || null,
                            endDate: item[6] || null
                        };
                    });
                }

                return {
                    results: results,
                    pagination: {
                        more: data && data[0] && data[0][4] ? (params.page * 10) < data[0][4] : false
                    }
                };
            },
            cache: true
        },
        createTag: function (params) {
            return null; // Don't allow creating new tags
        }
    });

    // Auto-populate expiration date when event is selected
    $('#eventId').on('select2:select', function (e) {
        var data = e.params.data;

        var dateToUse = null;

        // Try to use end date first, then start date
        if (data.endDate) {
            dateToUse = new Date(parseInt(data.endDate));
        } else if (data.startDate) {
            dateToUse = new Date(parseInt(data.startDate));
        }

        if (dateToUse && !isNaN(dateToUse.getTime())) {
            // Format date as YYYY-MM-DD for HTML date input
            var year = dateToUse.getFullYear();
            var month = String(dateToUse.getMonth() + 1).padStart(2, '0');
            var day = String(dateToUse.getDate()).padStart(2, '0');
            var formattedDate = day + '/' + month + '/' + year;

            $('#expirationDate').data('daterangepicker').setStartDate(formattedDate);
            $('#expirationDate').data('daterangepicker').setEndDate(formattedDate);
        }
    });

    // Initialize date picker
    $('.daterange-single').daterangepicker({
        singleDatePicker: true,
        showDropdowns: true,
        locale: {
            format: 'DD/MM/YYYY',
            separator: ' - ',
            applyLabel: 'Applica',
            cancelLabel: 'Annulla',
            fromLabel: 'Da',
            toLabel: 'A',
            customRangeLabel: 'Personalizzato',
            weekLabel: 'S',
            daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve', 'Sa'],
            monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',
                'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
            firstDay: 1
        }
    });

    // Form validation
    $("#sponsor-event-form").validate({
        ignore: 'input[type=hidden], .select2-search__field',
        errorClass: 'validation-error-label',
        successClass: 'validation-valid-label',
        highlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        unhighlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        errorPlacement: function (error, element) {
            if (element.hasClass('select2-hidden-accessible')) {
                error.insertAfter(element.next('.select2-container'));
            } else {
                error.insertAfter(element);
            }
        },
        rules: {
            eventId: {
                required: true
            },
            expirationDate: {
                required: true
            },
            sort: {
                required: true,
                min: 1,
                digits: true
            }
        },
        messages: {
            eventId: {
                required: "Seleziona un evento"
            },
            expirationDate: {
                required: "Inserisci la data di scadenza",
                date: "Inserisci una data valida"
            },
            sort: {
                required: "Inserisci il numero di ordinamento",
                min: "Il numero deve essere almeno 1",
                digits: "Inserisci solo numeri"
            }
        }
    });

    // Handle cancel button
    $('.btn-cancel').click(function () {
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sicuro?',
            content: "Tutte le modifiche andranno perse.",
            buttons: {
                cancel: {
                    text: 'Annulla',
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                },
                Conferma: {
                    btnClass: 'bg-primary',
                    action: function () {
                        window.location.href = $('#sponsorEventCollectionUri').attr('href');
                    }
                }
            }
        });
    });

    // Handle delete button
    $('#btn-delete').click(function (event) {
        // disable the default form submission
        event.preventDefault();

        var postToUrl = $('#sponsorEventRemoveUri').attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-trash-alt icon-2x"></i><br/><br/> Eliminazione evento sponsorizzato',
            content: "Conferma eliminazione?",
            columnClass: "col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'btn-danger mb-5',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success: function (returndata) {
                                $.unblockUI();
                                var url = $("#sponsorEventCollectionSuccessUri").attr("href");
                                window.location.href = url;
                            },
                            error: function (response, status, errorThrown) {
                                $.unblockUI();
                                // warn
                                $.alert({
                                    theme: 'supervan',
                                    escapeKey: true,
                                    animation: 'top',
                                    closeAnimation: 'bottom',
                                    backgroundDismiss: true,
                                    title: 'Oh oh! :(',
                                    content: 'Eliminazione fallita. Riprova più tardi.'
                                });
                            }
                        });
                    }
                }
            }
        });

        return false;
    });

});
