// init
moment.locale('it');
var table = null;

$(function () {

    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    });

    // Table setup
    // ------------------------------

    // Setting datatable defaults
    $.extend($.fn.dataTable.defaults, {
        autoWidth: false,
        responsive: true,
        deferRender: true,
        dom: '<"datatable-header"fBl><"datatable-scroll-wrap"t><"datatable-footer"ip>',
        language: {
            search: '<span>Filtra:</span> _INPUT_',
            searchPlaceholder: 'Filtra...',
            lengthMenu: '<span>Mostra:</span> _MENU_',
            emptyTable: 'Non ci sono dati disponibili',
            info: 'Visualizzo da _START_ a _END_ di _TOTAL_ elementi',
            infoEmpty: 'Visualizzo da 0 a 0 di 0 elementi',
            infoFiltered: '(filtrati da _MAX_ elementi totali)',
            zeroRecords: 'N<PERSON>un risultato corrisponde alla ricerca',
            paginate: {'first': 'Prima', 'last': 'Ultima', 'next': '&rarr;', 'previous': '&larr;'}
        },
        drawCallback: function () {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').addClass('dropup');
        },
        preDrawCallback: function () {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').removeClass('dropup');
        }
    });

    $.extend($.fn.dataTableExt.oSort, {
        'sortable-value-pre': function (a) {
            if (a) {
                var nodes = $.parseHTML(a);
                if (nodes) {
                    var node = nodes[0];
                    if (node) {
                        if (node.hasAttribute('sortable-value')) {
                            a = node.getAttribute('sortable-value');
                        }
                    }
                }
            }
            return a;
        },

        'sortable-value-asc': function (a, b) {
            return a - b;
        },

        'sortable-value-desc': function (a, b) {
            return b - a;
        }
    });

    // State saving
    table = $('.datatable-posts').DataTable({
        ajax: {
            url: $("#dataUri").attr("href") + window.location.search,
            dataType: 'json',
            dataSrc: function (datas) {
                var json = JSON.parse(datas);
                if (jQuery.isEmptyObject(json)) {
                    return [];
                } else {
                    return json.data;
                }
            }
        },
        responsive: {
            details: {
                type: 'column',
                target: -1
            }
        },
        stateSave: true,
        buttons: {
            buttons: [
                {
                    extend: 'csvHtml5',
                    className: 'btn btn-default',
                    filename: 'pagine',
                    fieldSeparator: ';'
                },
                {
                    extend: 'excelHtml5',
                    className: 'btn btn-default'
                },
                {
                    extend: 'pdfHtml5',
                    className: 'btn btn-default',
                    orientation: 'landscape'
                },
                {extend: 'colvis', text: '<i class="icon-grid3"></i> <span class="caret"></span>', className: 'btn btn-default btn-icon'}
            ]
        },
        order: [5, 'desc'],
        columnDefs: [
            {
                width: '30px',
                targets: 0
            },
            {
                targets: 2,
                orderable: true,
                type: 'sortable-value'
            },
            {
                targets: 6,
                orderable: true,
                type: 'sortable-value'
            },
            {
                className: 'control',
                orderable: false,
                targets: -1
            }
        ]
    });

    // External table additions
    // ------------------------------

    // Enable Select2 select for the length option
    $('.dataTables_length select').select2({
        minimumResultsForSearch: Infinity,
        width: 'auto'
    });

// Initialize with options
    var startDate = moment($('#startDate').text());
    var endDate = moment($('#endDate').text());
    $('.daterange-predefined').daterangepicker(
            {
                startDate: startDate,
                endDate: endDate,
                minDate: '01/01/2022',
                maxDate: '31/12/2099',
                ranges: {
                    'Oggi': [moment(), moment()],
                    'Ieri': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    'Ultimi 7 giorni': [moment().subtract(6, 'days'), moment()],
                    'Ultimi 30 giorni': [moment().subtract(29, 'days'), moment()],
                    'Questo mese': [moment().startOf('month'), moment().endOf('month')],
                    'Scorso mese': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
                    'Ultimi 3 mesi': [moment().subtract(3, 'month').startOf('month'), moment().endOf('month')],
                    'Da sempre': [moment('2023-01-01'), moment().endOf('month')]
                },
                locale: {
                    format: 'DD/MM/YYYY',
                    applyLabel: 'Applica',
                    cancelLabel: 'Annulla',
                    startLabel: 'Data inizio',
                    endLabel: 'Data fine',
                    customRangeLabel: 'Personalizzato',
                    daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve', 'Sa'],
                    monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
                    firstDay: 1
                },
                opens: 'left',
                applyClass: 'btn-small bg-primary filter-date',
                cancelClass: 'btn-small btn-default'
            },
            function (start, end) {
                $('.daterange-predefined span').html(start.format('MMMM D, YYYY') + ' &nbsp; - &nbsp; ' + end.format('MMMM D, YYYY'));

                var url = new URI();

                var startDate = start.format('DD/MM/YYYY');
                var endDate = end.format('DD/MM/YYYY');

                url.removeSearch("startDate");
                if (startDate) {
                    url.addSearch("startDate", startDate);
                }
                
                url.removeSearch("endDate");
                if (endDate) {
                    url.addSearch("endDate", endDate);
                }

                var selectedGhosts = "";
                $('.page-ghost-filter:checked:enabled').each(function() {
                    selectedGhosts += $(this).attr('page-ghost-value') + "|";
                });


                window.location.href = url;

            }
    );

    // Display date format
    $('.daterange-predefined span').html(startDate.format('MMMM D, YYYY') + ' &nbsp; - &nbsp; ' + endDate.format('MMMM D, YYYY'));


    $("#filter-apply").off();
    $("#filter-apply").click(function (event) {
        var url = new URI();
        var selectedProvinces = "";
        $('.page-province-filter:checked:enabled').each(function () {
            selectedProvinces += $(this).attr('page-province-value') + "|";
        });

        url.removeSearch("selectedProvinces");
        if (selectedProvinces) {
            url.addSearch("selectedProvinces", selectedProvinces);
        }
        
        var selectedGhosts = "";
        $('.page-ghost-filter:checked:enabled').each(function() {
            selectedGhosts += $(this).attr('page-ghost-value') + "|";
        });
        
        url.removeSearch("selectedGhosts");
        if (selectedGhosts) {
            url.addSearch("selectedGhosts", selectedGhosts);
        }

        window.location.href = url;
    });

    bindRemovePage();

    table.on('draw.dt', function (e, settings) {
        bindRemovePage();
    });

    table.on('responsive-display', function (e, datatable, row, showHide, update) {
        bindRemovePage();
    });

    table.on('responsive-resize', function (e, datatable, columns) {
        bindRemovePage();
    });

});

function bindRemovePage() {
// bind delete action
    $('.remove-page').click(function (event) {

        // disable the default form submission
        event.preventDefault();

        var postToUrl = $(this).attr('href');

        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-trash-alt icon-2x"></i><br/><br/> Eliminazione pagina',
            content: "Conferma eliminazione?",
            columnClass: "col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'btn-danger mb-5',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#pagesSuccessUri").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                    function (response, status, errorThrown) {
                                        $.unblockUI();
                                        // warn
                                        $.alert({
                                            theme: 'supervan',
                                            escapeKey: true,
                                            animation: 'top',
                                            closeAnimation: 'bottom',
                                            backgroundDismiss: true,
                                            title: 'Oh oh! :(',
                                            content: label('common.delete.failed')
                                        });
                                    }
                        });
                    }
                }
            }
        });

        return false;
    });
}