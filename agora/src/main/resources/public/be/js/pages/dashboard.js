/* ------------------------------------------------------------------------------
*
*  # Customers
*
*  Specific JS code additions for e-commerce_customers.html page
*
*  Version: 1.0
*  Latest update: Mar 20, 2017
*
* ---------------------------------------------------------------------------- */

document.addEventListener('DOMContentLoaded', function() {

    var analysis = $('#analysis').text();
    var analysisValue = $('#analysisValue').text();
    var analysisValueDescription = $('#analysisValueDescription').text();

    var text = $('#customer-new-counters').text().slice(0, -1);
    var customerNewCounters = text.split(',').map(Number);

    text = $('#customer-old-counters').text().slice(0, -1);
    var customerOldCounters = text.split(',').map(Number);
    
    text = $('#page-new-counters').text().slice(0, -1);
    var pageNewCounters = text.split(',').map(Number);
    
    text = $('#best-names').text().slice(0, -1);
    var bestNames = text.split(',').map(String);
    
    text = $('#best-values').text().slice(0, -1);
    var bestValues = text.split(',').map(Number);
    
    
    // Chart configuration
    // ------------------------------

    // Base
    var customers_element = document.getElementById('customers_chart');
    
    if (customers_element) {
        var columns_stacked = echarts.init(customers_element);
        
    }
    
    // Configuration
    if (analysis === 'trend') {

        columns_stacked.setOption({

            // Add custom colors
            color: ['#d77451', '#00892F','#000000'],

            // Chart animation duration
            animationDuration: 750,

            // Setup grid
            grid: {
                left: 0,
                right: 10,
                top: 35,
                bottom: 0,
                containLabel: true
            },

            // Add legend
            legend: {
                data: ['Nuovi clienti','Già clienti'],
                type: 'scroll',
                itemHeight: 8,
                itemGap: 20
            },

            // Add tooltip
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(0,0,0,0.75)',
                padding: [10, 15],
                textStyle: {
                    fontSize: 13,
                    fontFamily: 'Roboto, sans-serif'
                },
                axisPointer: {
                    type: 'shadow',
                    shadowStyle: {
                        color: 'rgba(0,0,0,0.025)'
                    }
                }
            },

            // Horizontal axis
            xAxis: [{
                type: 'category',
                data: ['Gennaio','Febbraio','Marzo','Aprile','Maggio','Giugno','Luglio','Agosto','Settembre','Ottobre','Novembre','Dicembre']
            }],

            // Vertical axis
            yAxis: [
                {
                    type: 'value',
                    name: 'Clienti',
                    axisLabel: {
                        formatter: '{value}'
                    }
                },
                {
                    type: 'value',
                    name: 'Pagine',
                    axisLabel: {
                        formatter: '{value}'
                    }
                }
            ],

            // Add series
            series: [
                {
                    name: 'Nuovi clienti',
                    type: 'bar',
                    data: customerNewCounters
                },
                {
                    name: 'Già clienti',
                    type: 'bar',
                    data: customerOldCounters
                },
                {
                    name: 'Pagine',
                    type: 'line',
                    yAxisIndex: 1,
                    data: pageNewCounters
                }
            ]

        });
        
    } else if (analysis === 'bestPerformerCustomer') {

        columns_stacked.setOption({

            // Add custom colors
            color: ['#d77451', '#00892F','#000000'],

            // Chart animation duration
            animationDuration: 750,

            // Setup grid
            grid: {
                left: 0,
                right: 10,
                top: 35,
                bottom: 0,
                containLabel: true
            },

            // Add legend
            legend: {
                data: ['Clienti'],
                type: 'scroll',
                itemHeight: 8,
                itemGap: 20
            },

            // Add tooltip
            tooltip: {
                trigger: 'axis',
                backgroundColor: 'rgba(0,0,0,0.75)',
                padding: [10, 15],
                textStyle: {
                    fontSize: 13,
                    fontFamily: 'Roboto, sans-serif'
                },
                axisPointer: {
                    type: 'shadow',
                    shadowStyle: {
                        color: 'rgba(0,0,0,0.025)'
                    }
                }
            },

            // Horizontal axis
            xAxis: [{
                type: 'category',
                data: bestNames
            }],

            // Vertical axis
            yAxis: [
                {
                    type: 'value',
                    name: analysisValueDescription,
                    axisLabel: {
                        formatter: '{value}'
                    }
                }
            ],

            // Add series
            series: [
                {
                    name: 'Pagine',
                    type: 'bar',
                    data: bestValues
                }
            ]

        });
        
    } else {
        console.log('unsopported analysis type');
    }

    // Resize function
    var triggerChartResize = function() {
        customers_element && columns_stacked.resize();
    };

    // On window resize
    var resizeCharts;
    window.onresize = function () {
        clearTimeout(resizeCharts);
        resizeCharts = setTimeout(function () {
            triggerChartResize();
        }, 200);
    };

});
