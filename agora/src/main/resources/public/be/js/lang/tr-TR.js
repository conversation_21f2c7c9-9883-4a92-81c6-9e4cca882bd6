$.extend(true,$.summernote.lang,{
  'tr-TR':{ /* Turkish */
    imageAttributes:{
      dialogTitle: '<PERSON>sim Özellikleri',
      tooltip: '<PERSON>sim Özellikleri',
      tabImage: 'Yükleme',
        src: '<PERSON><PERSON><PERSON>',
        browse: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        title: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        alt: 'Alt. Metin',
        dimensions: 'boyutlar',
      tabAttributes: 'Öznitellikler',
        class: 'Sınıf',
        style: 'Stil',
        role: 'Rol',
      tabLink: 'bağlant<PERSON>',
        linkHref: 'URL',
        linkTarget: 'Hedef',
        linkTargetInfo: 'Seçenekler: _self, _blank, _top, _parent',
        linkClass: 'Sınıf',
        linkStyle: 'Stil',
        linkRel: 'Rol',
        linkRelInfo: '<PERSON><PERSON><PERSON>kler: alternate, author, bookmark, help, license, next, nofollow, noreferrer, prefetch, prev, search, tag',
        linkRole: 'Rol',
      tabUpload: 'Yükleme',
        upload: 'Yükleme',
      tabBrowse: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      editBtn: 'tamam'
    }
  }
});
