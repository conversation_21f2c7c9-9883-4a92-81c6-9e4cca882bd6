.bg-agora {
    background-color: #8B734D;
}
.btn-agora {
    background-color: #8B734D;
    border-color: #8B734D;
    color: #fff
}
.btn-agora:hover {    
    color: #fff;
}
.text-agora {
    color: #8B734D;  
}
.border-agora {
    border-color: #8B734D;  
}
.bg-light-grey {
    background-color: #ebebeb;
    color: #000;
}
.note-editor.panel {
    margin-bottom: 0;
}
.fix {
    display: flex;
    flex-wrap: wrap;
}
.navbar-brand {
    padding: 2px;
    line-height: 15px;
}
.navbar-brand>img {
    height: 40px;
    display: inline-block;
    padding: 10px;
}
.bg-black, .btn-black {
    background-color: #000000;
    border-color: #000000;    
}
.sp-choose {
    color: #fff;
    background-color: #2196f3;
}
.sp-cancel, .sp-choose, .sp-palette-toggle {    
    border-radius: 3px;    
}
.checkbox-switchery.always-on .switchery {
    opacity: 1 !important;
}
.img-entity {
    width: 120px;
    height: 120px;
    object-fit: cover;
}
.navbar-default .navbar-nav>.active>a:after {
    background-color: #2196f3;
}
.nav.navbar-nav li .badge {
    border: none;
}
.wizard>.steps>ul>li:after,.wizard>.steps>ul>li:before {    
    background-color: #2196f3;    
}
.wizard>.steps>ul>li.current .number {
    font-size: 0;
    border-color: #2196f3;
    background-color: #fff;
    color: #2196f3
}
.wizard>.steps>ul>li.error .number {
    border-color: #f44336;
    color: #f44336;
}
.wizard>.steps>ul>li.done .number {
    font-size: 0;
    background-color: #2196f3;
    border-color: #2196f3;
    color: #fff
}
@media (max-width: 768px) {
    .wizard>.steps>ul>li:last-child:after {
        background-color: #2196f3
    }
}
@media (max-width: 480px) {
    .wizard>.steps>ul>li.current:after {
        background-color: #2196f3
    }
}
.wizard>.actions>ul>li>a {
    background: #2196f3;
}
.picker__button--clear {
    display: none !important;
}
.picker__list-item--disabled {
    display: none;
}
.picker--focused .picker__day--highlighted, .picker__day--highlighted, .picker__day--highlighted:hover {
    cursor: pointer;
    color: #fff;
    background-color: #2196f3;
}
.bg-new {
    background-color: rgba(3, 169, 244, 0.06);
}
.ranges ul li.active {    
    background-color: #2196f3;
}
.dataTables_paginate .paginate_button.current, .dataTables_paginate .paginate_button.current:focus, .dataTables_paginate .paginate_button.current:hover {
    color: #fff;
    background-color: #2196f3;    
}
.dt-button-collection>.dt-button.active {
    color: #fff;
    background-color: #2196f3;
}
.daterangepicker td.active, .daterangepicker td.active:focus, .daterangepicker td.active:hover {
    background-color: #2196f3;    
}
.dataTables_filter input:focus {
    border-bottom-color: #2196f3;
    -webkit-box-shadow: 0 1px 0 #2196f3;
    box-shadow: 0 1px 0 #2196f3;
}
.select2-container--focus .select2-selection--single:not([class*=bg-]):not([class*=border-]), .select2-container--open .select2-selection--single:not([class*=bg-]):not([class*=border-]) {
    border-bottom-color: #2196f3;
    -webkit-box-shadow: 0 1px 0 #2196f3;
    box-shadow: 0 1px 0 #2196f3;
}
.select2-selection--multiple:not([class*=bg-])[class*=border-] {    
    border-bottom: 1px solid #ddd;
}
.select2-selection--multiple:not([class*=bg-]) .select2-selection__choice:focus,.select2-selection--multiple:not([class*=bg-]) .select2-selection__choice:hover {
    background-color: #2196f3;
    color: #fff
}
.select2-selection--multiple.border-bottom-ddd .select2-selection__rendered {
    padding-bottom: 0!important
}
.select2-selection--multiple:not([class*=bg-]) .select2-selection__choice:focus,.select2-selection--multiple:not([class*=bg-]) .select2-selection__choice:hover {
    background-color: #2196f3;
    color: #fff
}
.select2-results__option[aria-selected=true] {
    background-color: #2196f3;
    color: #fff;
}
.form-control:focus {
    outline: 0;
    border-color: transparent;
    border-bottom-color: #2196f3;
    -webkit-box-shadow: 0 1px 0 #2196f3;
    box-shadow: 0 1px 0 #2196f3;
}
.slim {    
    border-radius: 6px;
}
.file-drop-area label {
    display: block;
    padding: 2em;
    background: rgba(3,155,229,.05);
    text-align: center;
    cursor: pointer;
    border: 1px dashed #039be5;
    font-size: 16px;
    color: #039be5;
    border-radius: 10px
}
.drop_uploader.drop_zone .text {
    font-family: Roboto,Helvetica Neue,Helvetica,Arial,sans-serif;
    font-size: 16px;
    color: #039be5
}
.drop_uploader .fa {
    display: none
}
.drop_uploader.drop_zone ul.files li {
    font-family: Roboto,Helvetica Neue,Helvetica,Arial,sans-serif;
    font-size: 14px;
    color: #039be5;
    background-color: transparent;
    border: none
}
.drop_uploader.drop_zone {
    position: relative;
    border: 1px dashed #039be5;
    color: #039be5;
    background: rgba(3,155,229,.05);
    border-radius: 10px;
    min-height: 0
}
.file_browse {
    position: absolute;
    border-color: transparent!important;
    background-color: transparent!important;
    color: transparent!important;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0
}
legend .control-arrow {
    width: 100%;
    position: absolute;
    right: 20px;
    text-align: right
}

.datatable-scroll-wrap {
    overflow-x: visible
}

.truncate {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}
.sidebar .panel .label.pull-right-sm {
    display: table;
}
.media-body a {
    word-break: break-word;
}
.jconfirm.jconfirm-supervan .jconfirm-bg {
    background-color: rgba(0,0,0,.7)
}

.jconfirm.jconfirm-supervan .jconfirm-box .jconfirm-buttons button {
    font-family: Roboto,Helvetica Neue,Helvetica,Arial,sans-serif;
    display: inline-block;    
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    padding: 8px 16px;
    font-size: 13px;
    line-height: 1.5384616;
    border-radius: 3px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    font-weight: 500;
    text-transform: uppercase;
    border-width: 0;
    padding: 9px 17px
}

.jconfirm.jconfirm-supervan .jconfirm-box .jconfirm-buttons button:hover {
    transform: none;
    -webkit-transform: none
}

.jconfirm-content input[type=text] {
    color: #fff;
}
.jconfirm-content input[type=text]:focus {
    border-bottom-color: #fff;
}

.dropdown-menu>.dropdown-header>.badge, .dropdown-menu>.dropdown-header>.label, .dropdown-menu>li>a>.badge, .dropdown-menu>li>a>.label {
    float: none;    
}
@media (max-width: 768px) {   
    .nav-tabs:before {
        content: 'Lingue';
        color: inherit;
        margin-top: 7px;
        margin-left: 16px;
        margin-bottom: 15px;
        opacity: .5;
    }
    .text-center-xs {
        text-align: center;
    }
    .heading-elements.visible-elements .list-inline {
        margin-top: 20px !important;
    }    
    .login-options, .login-options .text-right {
        text-align: right;
    }
    .thumb img:not(.media-preview) {
        max-height: 500px;
    }
}

@media (min-width: 1025px) and (max-width:1199px) {
    .thumb img:not(.media-preview) {
        max-height: 259px;
    }
}
@media (min-width: 768px) {   
    .width-half-right {
        width: 50%;
        text-align: right;
    }
    .heading-elements.visible-elements .breadcrumb, .heading-elements.visible-elements .daterange-custom, .heading-elements.visible-elements .heading-btn, .heading-elements.visible-elements .heading-btn-group>.btn, .heading-elements.visible-elements .heading-form .form-group, .heading-elements.visible-elements .heading-text, .heading-elements.visible-elements .heading-thumbnails, .heading-elements.visible-elements .icons-list, .heading-elements.visible-elements .nav-pills, .heading-elements.visible-elements .nav-tabs, .heading-elements.visible-elements .noui-slider, .heading-elements.visible-elements .pager, .heading-elements.visible-elements .pagination, .heading-elements.visible-elements .progress, .heading-elements.visible-elements .ui-slider, .heading-elements.visible-elements>.btn-group {
        margin-top: 0;
    }    
}

.width-130 {
    width: 130px;
}
.panel-body h6 textarea {
    border: none;
    overflow: hidden;
    margin: 0px;    
    resize: none;
    text-align: center;
    background: transparent;
    cursor: pointer;
    max-width: 100%;
}
@media (min-width: 1200px) {
    .text-right-lg {
        text-align: right;
    }
   
}
.autocomplete-suggestions{text-align:left;cursor:default;border:2px solid #d9d9d9;border-top:0;background:#fff;box-shadow:0 2px 6px rgba(0,0,0,.3);;position:absolute;display:none;z-index:9999;max-height:254px;overflow:hidden;overflow-y:auto;box-sizing:border-box}.autocomplete-suggestion{position:relative;padding:10px 20px 10px 20px;line-height:30px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:18px;color:#999}.autocomplete-suggestion b{font-weight:400;color:#000}.autocomplete-suggestion.selected{background:#f0f0f0}

/*.footer.navbar-fixed-bottom {
    z-index: 99999;
}*/
.dropdown-menu.dropdown-menu-right {
     z-index: 99999999;
}