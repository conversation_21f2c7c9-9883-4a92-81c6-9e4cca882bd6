var autocomplete;
var selectCity;

$(document).ready(function () {
    bindSearch();
    bindCity();
});

function bindSearch() {
    $('#search-events').off();
    $('#search-events').click(function () {
        event.preventDefault();
        
        var url = new URI($('#resultsUri').attr('href'));
        if (!url) {
            console.errors('missing url');
            return;
        }
            
        var cityIdentifier = $('.input-search-city').val();
        if (!cityIdentifier) {
            cityIdentifier = "italia";
        }
        url.segment(cityIdentifier);

        var category = $(".input-search-category").val();
        if (category) {
            url.segment(category);
        }
        
        // navigate to...
        window.location.href = url.toString();
        
        return false;
    });
}

function bindCity() {
    var identifierSaved = $("#city").attr("data-value");
    var citySaved = $("#city").attr("data-city");
    var provinceCodeSaved = $("#city").attr("data-provincecode");
    var postalCodeSaved = $("#city").attr("data-postalcode");
    var regionSaved = $("#city").attr("data-region");
    selectCity = new TomSelect('#city',{
        onItemRemove: function() { 
            $("#city option").remove();
        },
        allowEmptyOption: false,
        dropdownWrapperClass: 'tom-select-custom',
        optionClass: 'option',
        valueField: 'identifier',
        labelField: 'city',
        searchField: ['city', 'postalCode'],
        options: [identifierSaved ? { identifier: identifierSaved, city: citySaved, provinceCode: provinceCodeSaved, postalCode: postalCodeSaved, region: regionSaved } : ''],
        items: [identifierSaved ? identifierSaved : ''],
        // fetch remote data
        load: function(query, callback) {

            var url = $("#dataCitiesUri").attr("href") + encodeURIComponent(query);
            fetch(url)
                .then(response => response.json())
                .then(json => {
                    callback(json);
                }).catch(()=>{
                    callback();
                });

        },
        // custom rendering functions for options and items
        render: {
            no_results:function(data,escape){
                return '<div class="no-results">Nessun risultato per "'+escape(data.input)+'"</div>';
            },
            option: function(item, escape) {
                if (item.postalCode === 'ZZZZZ') {
                    return '<div data-province=' + item.provinceCode + '>' + item.city + '</div>';
                } else {
                    return '<div data-province=' + item.provinceCode + ' data-postalCode=' + item.postalCode + '>' + item.city + ' (' + item.postalCode + ')' + '</div>';
                }
            },
            item: function(item, escape) {
                if (item.postalCode === 'ZZZZZ') {
                    return '<div data-province=' + item.provinceCode + '>' + item.city + '</div>';
                } else {
                    return '<div data-province=' + item.provinceCode + ' data-postalCode=' + item.postalCode + '>' + item.city + '</div>';
                }
            }
        },
        onItemAdd: function(value, $item) {            
            $(this).blur();
        }
    });    
}
