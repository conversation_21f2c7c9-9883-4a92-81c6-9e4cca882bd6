window.addEventListener("pageshow", function(evt){
    if(evt.persisted){
        if ($.cookie("agoraScrollPosition_notifications")) {
            var dataEventBack = $.cookie("agoraScrollPosition_notifications")
            const elementToScroll = document.querySelector("[data-notifications-back=" + dataEventBack + "]");
            if (elementToScroll) {
                elementToScroll.scrollIntoView({block: 'center'});
            }
            $.removeCookie("agoraScrollPosition_notifications");
        }
    }
}, false);
if ('scrollRestoration' in history) {
  // Back off, browser, I got this...
  history.scrollRestoration = 'manual';
}
var loading = false;
var limit = parseInt($('#pagination').text(), 0);
var skip = 0;


(function () {
    
    var hash = window.location.hash;
    if (hash) {
        var tabButton = document.querySelector('button[data-bs-target="' + hash + '-pane"]');

        if (tabButton) {
            var tab = new bootstrap.Tab(tabButton);
            tab.show();
        }
    }
    
    if ($.cookie("agoraScrollPosition_notifications")) {
        var dataNotificationBack = $.cookie("agoraScrollPosition_notifications")
        const elementToScroll = document.querySelector("[data-notifications-back=" + dataNotificationBack + "]");
        if (elementToScroll) {
            setTimeout(function() {
                elementToScroll.scrollIntoView({block: 'center'});
                bindWallLink("agora", null);
            }, 1000);
        }
        $.removeCookie("agoraScrollPosition_notifications");
    }
    
    bindIas();
    
    bindNotificationActive();
    bindNotificationRemove();
})();

function bindNotificationActive() {
    $('.notification-active').off();
    $('.notification-active').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#notificationActiveUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var user = event.target.getAttribute('data-user');
        if (!user) {
            user = event.target.parentNode.getAttribute('data-user');
        }
        if (!user) {
            console.error('missing user');
            return false;
        }
        
        var customerNotificationId = event.target.getAttribute('data-notification-id');
        if (!customerNotificationId) {
            customerNotificationId = event.target.parentNode.getAttribute('data-notification-id');
        }
        if (!customerNotificationId) {
            console.error('missing notification');
            return false;
        }
        
        url.removeSearch("customerNotificationId");
        url.addSearch("customerNotificationId", customerNotificationId);
        
        var active = event.target.getAttribute('data-active');
        if (!active) {
            active = event.target.parentNode.getAttribute('data-active');
        }
        if (!active) {
            console.error('missing active');
            return false;
        }
        
        url.removeSearch("active");
        url.addSearch("active", active);

        Swal.fire({
            position: 'center',
            icon: 'warning',
            title: label('notification.edit.activation'),
            showConfirmButton: true,
            confirmButtonText: label('common.confirm'),            
            showCancelButton: true,
            cancelButtonText: label('common.cancel'),                        
            buttonsStyling: false,
            customClass: {
                confirmButton: 'btn btn-danger',
                cancelButton: 'btn btn-default'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $.blockUI();
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: null,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success:
                            function (returndata) {
                                $.unblockUI();

                                var msg = label('common.operation.completed');

                                $.unblockUI();

                                Swal.fire({
                                    position: 'center',
                                    icon: 'success',
                                    title: msg,
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(function(){
                                    var url = new URI();
                                    $('#myTabContent').load(url + ' #myTabContent > *', function() {
                                        bindNotificationActive();
                                        bindNotificationRemove();
                                    });
                                });

                            },
                    error:
                            function (response, status, errorThrown) {
                                $.unblockUI();

                                // warn
                                $.alert({
                                    theme: 'supervan',
                                    escapeKey: true,
                                    animation: 'top',
                                    closeAnimation: 'bottom',
                                    backgroundDismiss: true,
                                    title: 'Oh oh! :(',
                                    content: label('common.operation.error')
                                });
                            }
                });                    

                return false;
            }
        })
    });

}
function bindNotificationRemove() {
    $('.notification-remove').off();
    $('.notification-remove').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#notificationRemoveUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var user = event.target.getAttribute('data-user');
        if (!user) {
            user = event.target.parentNode.getAttribute('data-user');
        }
        if (!user) {
            console.error('missing user');
            return false;
        }
        
        var customerNotificationId = event.target.getAttribute('data-notification-id');
        if (!customerNotificationId) {
            customerNotificationId = event.target.parentNode.getAttribute('data-notification-id');
        }
        if (!customerNotificationId) {
            console.error('missing customerNotificationId');
            return false;
        }
        
        url.removeSearch("customerNotificationId");
        url.addSearch("customerNotificationId", customerNotificationId);

        Swal.fire({
            position: 'center',
            icon: 'warning',
            title: label('notification.delete.notifications'),
            showConfirmButton: true,
            confirmButtonText: label('common.confirm'),            
            showCancelButton: true,
            cancelButtonText: label('common.cancel'),                        
            buttonsStyling: false,
            customClass: {
                confirmButton: 'btn btn-danger',
                cancelButton: 'btn btn-default'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $.blockUI();
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: null,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success:
                            function (returndata) {
                                $.unblockUI();

                                var msg = 'Notifiche rimossa!';

                                $.unblockUI();

                                Swal.fire({
                                    position: 'center',
                                    icon: 'success',
                                    title: msg,
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(function(){
                                    var url = new URI();
                                    $('#myTabContent').load(url + ' #myTabContent > *', function() {
                                        bindNotificationActive();
                                        bindNotificationRemove();
                                    });
                                });

                            },
                    error:
                            function (response, status, errorThrown) {
                                $.unblockUI();

                                // warn
                                $.alert({
                                    theme: 'supervan',
                                    escapeKey: true,
                                    animation: 'top',
                                    closeAnimation: 'bottom',
                                    backgroundDismiss: true,
                                    title: 'Oh oh! :(',
                                    content: label('common.remove.failed')
                                });
                            }
                });                    

                return false;
            }
        })
    });

}

function bindIas() {
    var notificationElements = document.getElementsByClassName("notification-element");
    var spinner = document.getElementsByClassName("spinner");
    var containerNotifications = $(".containerNotifications");
    if (containerNotifications.is(":visible")) {
        if (notificationElements.length > 0) {

            window.ias = new InfiniteAjaxScroll('.containerNotifications', {
                item: '.notification-element',
                next: '.pager__next',
                prev: '.pager__prev',
                pagination: '.pager',
                spinner: {
                    // element to show as spinner
                    element: '.spinner',

                    // delay in milliseconds
                    // this is the minimal time the loader should be displayed. If loading takes longer, the spinner
                    // will be shown for the duration of the loading. If the loading takes less then this duration,
                    // say 300ms, then the spinner is still shown for 600ms.
                    delay: 300,

                    // this function is called when the button has to be shown
                    show: function(element) {
                      element.style.opacity = '1'; // default behaviour
                    },

                    // this function is called when the button has to be hidden
                    hide: function(element) {
                      element.style.opacity = '0'; // default behaviour
                    }
                }
            });


            ias.on('page', (e) => {
                document.title = e.title;
                let state = history.state;
                history.replaceState(state, e.title, e.url);
            });

            // disable cache busting
            ias.on('load', function(event) {
              event.nocache = true;
            });
        }
    }
}