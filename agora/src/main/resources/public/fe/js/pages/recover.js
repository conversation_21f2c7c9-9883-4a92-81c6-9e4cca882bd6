$(function() {
    
    $('#form-recover').validate({});
    
    // bind recover button
    $('#form-recover').submit(function(event) {
        event.preventDefault();
        
        if ($("#form-recover").valid()) {
        
            // post to url
            var url = $("#forgotSendUri").attr("href");

            // data to post
            var data = new FormData();
            data.append('email', $('#username').val());

            $.blockUI();
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                success:
                    function (returndata) {
                        $.unblockUI();      
                       Swal.fire({
                            position: 'top',
                            icon: 'success',
                            title: label('email.sent.success'),
                            text: label('email.credentials.sent'),
                            buttonsStyling: false,
                            customClass: {
                                confirmButton: 'btn btn-primary btn-lg',
                            },
                            confirmButtonText: label('common.go.to.login'),
                            footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                        }).then((result) => {
                            if (result.isConfirmed) {             
                                Swal.close();
                                window.location.href = $('#loginUri').attr('href');
                            }
                        });
                    },
                error:
                    function (response, status, errorThrown) {
                        var msg = label('common.data.save.failed');
                        if (response) {
                            if (response.responseText) {
                                msg = response.responseText;
                            }
                        }
                        $.unblockUI();
                        // warn
                        Swal.fire({
                            position: 'top',
                            icon: 'error',
                            title: label('common.ops.error'),
                            text: msg,
                            buttonsStyling: false,
                            customClass: {
                                confirmButton: 'btn btn-primary btn-lg',
                            },
                            confirmButtonText: label('common.continue'),
                            footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                        });
                    }
            });                    
        }
        
        return false;
    });
    
});