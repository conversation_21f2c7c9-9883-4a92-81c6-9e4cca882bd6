$(function() {

    $('#form-login').validate({        
        errorPlacement : function(error, element) {
            if (element.hasClass('fakepassword')) {                
                $('.fakepassword-error').html(error);
            }
            else {
                element.after(error); // default error placement
            }
        }   
    });
    
    // bind login button
    $('#form-login').submit(function(event) {
        event.preventDefault();
        
        if ($("#form-login").valid()) {
            
            // post to url
            var url = $("#accessDoUri").attr("href");

            // data to post
            var data = new FormData(this);

            $.blockUI();
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                success:
                    function (returndata) {
                        $.unblockUI();
                        var url = $("#homeUri").attr("href");
                        window.location.href = url;
                    },
                error:
                    function (response, status, errorThrown) {                        
                        var url = $("#forgotUri").attr("href");
                        if (response) {
                            if (response.responseText) {
                                msg = response.responseText;
                            }
                        }
                        $.unblockUI();
                        // warn
                        Swal.fire({
                            position: 'top',
                            icon: 'warning',
                            title: label('access.error.credential'),
                            text: label('access.error.text'),
                            buttonsStyling: false,
                            customClass: {
                                confirmButton: 'btn btn-primary btn-lg'
                            },
                            confirmButtonText: label('common.tryagain'),
                            footer: '<span class="me-1">' +label("access.dont.remember") + '</span> <a class="hover-arrow ms-1" href="' + url +'">' + label("common.recover") + '</a>'
                        });
                    }
            });                    
        }
        
        return false;
    });

});