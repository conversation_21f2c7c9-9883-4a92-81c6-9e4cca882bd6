(function () {
   
    $("#pageClaimForm").validate({});

    // bind register button
    $('#pageClaimForm').submit(function(event) {
        event.preventDefault();

        if ($("#pageClaimForm").valid()) {
            // post to url
            var url = $("#pageClaimSendUri").attr("href");

            // data to post
            var data = new FormData($(document.getElementById("pageClaimForm"))[0]);

            $.blockUI();
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                success:
                    function (returndata) {
                        
                        $.unblockUI();

                        var msg = 'Richiesta inviata!';
                        
                        Swal.fire({
                            position: 'center',
                            icon: 'success',
                            title: msg,
                            text: 'Prenderemo al più presto in carico la tua richiesta!',
                            showConfirmButton: false,
                            timer: 2500
                        }).then(function(){
                            window.location.reload();
                        });
                        
                    },
                error:
                    function (response, status, errorThrown) {
                        $.unblockUI();
                        var msg = "Impossibile inviare il messaggio";
                        if (response) {
                            if (response.responseText) {
                                msg = response.responseText;
                            }
                        }

                        Swal.fire({
                            position: 'center',
                            icon: 'error',
                            title: label('common.ops.error'),
                            text: msg,
                            buttonsStyling: false,
                            customClass: {
                                confirmButton: 'btn btn-primary btn-lg',
                            },
                            confirmButtonText: 'Continua <i class="bi-chevron-right small"></i>',
                            footer: '<span class="me-1">Hai bisogno di aiuto?</span> <a class="hover-arrow" href="mailto:<EMAIL>">Contatta l\'assistenza</a>'
                        });
                    }
            });                

        }

        return false;
    });

})();