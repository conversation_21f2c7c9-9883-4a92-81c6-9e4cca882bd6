//var loading = false;
//var limit = parseInt($('#pagination').text(), 0);
//var skip = 0;

var selectCity;

$(function() {
    
    // INITIALIZATION OF DATERANGEPICKER
    // =======================================================

    moment.locale('it');
    var startDate = moment($('#startDate').text());
    var endDate = moment($('#endDate').text());
    $('#daterange-predefined').daterangepicker(
        {      
            startDate: startDate,
            endDate: endDate,
            dateLimit: { days: 365 },
            ranges: {
                'Oggi': [moment(), moment()],
                'Domani': [moment().add(1, 'days'), moment().add(1, 'days')],
                'Questo weekend': [moment().add((5 - moment().day()), 'days'), moment().add(((5 - moment().day()) + 2), 'days', 'days')],                    
                'Prossimi 90 giorni': [moment(), moment().add(90, 'days')],                    
            },
            locale: {
                format: 'DD/MM/YYYY',
                applyLabel: 'Applica',
                cancelLabel: 'Annulla',
                startLabel: 'Data inizio',
                endLabel: 'Data fine',
                customRangeLabel: 'Scegli date',
                daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve','Sa'],
                monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
                firstDay: 1
            },
            opens: 'left',
            applyClass: 'btn-primary',
            cancelClass: 'btn-white'
        },
        function(start, end) {
            $('#daterange-predefined span').html(start.format('D MMMM') + ' &nbsp; - &nbsp; ' + end.format('D MMMM'));                
        }
    );    

    $('#daterange-predefined span').html(startDate.format('D MMMM') + ' &nbsp; - &nbsp; ' + endDate.format('D MMMM'));

    bindCity();
    
    bindInitSwiper();
    bindChangeCity();
    bindChangeCategory();
    bindChangeSingleDate();
    bindChangeDate();
    
    bindPagination();
    
//    $(window).scroll(loadOnScroll);
    
});

function bindCity() {
    var identifierSaved = $("#city").attr("data-value");
    var citySaved = $("#city").attr("data-city");
    var provinceCodeSaved = $("#city").attr("data-provincecode");
    var postalCodeSaved = $("#city").attr("data-postalcode");
    var regionSaved = $("#city").attr("data-region");
    selectCity = new TomSelect('#city',{
        onItemRemove: function() { 
            $("#city option").remove();
        },
        allowEmptyOption: false,
        dropdownWrapperClass: 'tom-select-custom',
        optionClass: 'option',
        valueField: 'identifier',
        labelField: 'city',
        searchField: ['city', 'postalCode'],
        options: [identifierSaved ? { identifier: identifierSaved, city: citySaved, provinceCode: provinceCodeSaved, postalCode: postalCodeSaved, region: regionSaved } : ''],
        items: [identifierSaved ? identifierSaved : ''],
        // fetch remote data
        load: function(query, callback) {

            var url = $("#dataCitiesUri").attr("href") + encodeURIComponent(query);
            fetch(url)
                .then(response => response.json())
                .then(json => {
                    callback(json);
                }).catch(()=>{
                    callback();
                });

        },
        // custom rendering functions for options and items
        render: {
            no_results:function(data,escape){
                return '<div class="no-results">Nessun risultato per "'+escape(data.input)+'"</div>';
            },
            option: function(item, escape) {
                if (item.postalCode === 'ZZZZZ') {
                    return '<div data-province=' + item.provinceCode + '>' + item.city + '</div>';
                } else {
                    return '<div data-province=' + item.provinceCode + ' data-postalCode=' + item.postalCode + '>' + item.city + ' (' + item.postalCode + ')' + '</div>';
                }
            },
            item: function(item, escape) {
                if (item.postalCode === 'ZZZZZ') {
                    return '<div data-province=' + item.provinceCode + '>' + item.city + '</div>';
                } else {
                    return '<div data-province=' + item.provinceCode + ' data-postalCode=' + item.postalCode + '>' + item.city + '</div>';
                }
            }
        },
        onItemAdd: function(value, $item) {            
            $(this).blur();
        }
    });    
}

function bindChangeCategory() {
    $('.changeCategory').change(function(event) {
        
        var url = new URI($('#resultsUri').attr('href'));
        
        var cityIdentifier = $('.input-search-city').val();
        if (!cityIdentifier) {
            cityIdentifier = "italia";
        }
        url.segment(cityIdentifier);
        
        var category = $(".input-search-category").val();
        if (category) {
            url.segment(category);
        }
        
        var startDate=  $("#daterange-predefined").data('daterangepicker').startDate.format('DD/MM/YYYY');
        url.addSearch("startDate", startDate);
        var endDate=  $("#daterange-predefined").data('daterangepicker').endDate.format('DD/MM/YYYY');
        url.addSearch("endDate", endDate);
        
        url.removeSearch("singleDate");
        var singleDate = $('.singleDate.active')[0].getAttribute('data-val')
        if (singleDate) {
            url.addSearch("singleDate", singleDate);
        }
        
        $(this).closest(".dropdown-menu").prev().dropdown("toggle");
         
        $('#containerEvents').load(url + ' #containerEvents > *', function() {
            window.history.replaceState(null, null, url.toString());
            bindInitSwiper();
            bindPagination();
//            skip = 0;
//            $(window).scroll(loadOnScroll);
        });

    });
}

function bindChangeSingleDate() {
    $('.singleDate').click( function(event) {
        event.preventDefault();
        
        var url = new URI($('#resultsUri').attr('href'));
        
        var cityIdentifier = $('.input-search-city').val();
        if (!cityIdentifier) {
            cityIdentifier = "italia";
        }
        url.segment(cityIdentifier);

        var category = $(".input-search-category").val();
        if (category) {
            url.segment(category);
        }

        var startDate=  $("#daterange-predefined").data('daterangepicker').startDate.format('DD/MM/YYYY');
        url.addSearch("startDate", startDate);
        var endDate=  $("#daterange-predefined").data('daterangepicker').endDate.format('DD/MM/YYYY');
        url.addSearch("endDate", endDate);
        
        url.removeSearch("singleDate");
        var singleDate = event.target.getAttribute('data-val')
        if (singleDate) {
            url.addSearch("singleDate", singleDate);
        }
        
        $('#containerEvents').load(url + ' #containerEvents > *', function() {
            window.history.replaceState(null, null, url.toString());
            bindInitSwiper();
            bindPagination();
//            skip = 0;
//            $(window).scroll(loadOnScroll);
        });
        $('#containerSingleDate').load(url + ' #containerSingleDate > *', function() {
            bindChangeSingleDate();
        });
        
    });
}

function bindChangeDate() {
    $('#daterange-predefined').on('apply.daterangepicker', function(ev, picker) {
        event.preventDefault();
        
        var url = new URI($('#resultsUri').attr('href'));
        
        var cityIdentifier = $('.input-search-city').val();
        if (!cityIdentifier) {
            cityIdentifier = "italia";
        }
        url.segment(cityIdentifier);

        var category = $(".input-search-category").val();
        if (category) {
            url.segment(category);
        }
        
        var startDate=  picker.startDate.format('DD/MM/YYYY');
        url.addSearch("startDate", startDate);
        var endDate=  picker.endDate.format('DD/MM/YYYY');
        url.addSearch("endDate", endDate);
        
        url.removeSearch("singleDate");
        var singleDate = event.target.getAttribute('data-val')
        if (singleDate) {
            url.addSearch("singleDate", singleDate);
        }
        
        $('#containerEvents').load(url + ' #containerEvents > *', function() {
            window.history.replaceState(null, null, url.toString());
            bindInitSwiper();
            bindPagination();
//            skip = 0;
//            $(window).scroll(loadOnScroll);
        });
        
    });
}

function bindChangeCity() {
    $('.input-search-city').change( function(event) {
        
        var url = new URI($('#resultsUri').attr('href'));
        
        var cityIdentifier = $('.input-search-city').val();
        if (!cityIdentifier) {
            cityIdentifier = "italia";
        }
        url.segment(cityIdentifier);
        
        var category = $(".input-search-category").val();
        if (category) {
            url.segment(category);
        }
        
        var startDate=  $("#daterange-predefined").data('daterangepicker').startDate.format('DD/MM/YYYY');
        url.addSearch("startDate", startDate);
        var endDate=  $("#daterange-predefined").data('daterangepicker').endDate.format('DD/MM/YYYY');
        url.addSearch("endDate", endDate);
        
        url.removeSearch("singleDate");
        var singleDate = $('.singleDate.active')[0].getAttribute('data-val')
        if (singleDate) {
            url.addSearch("singleDate", singleDate);
        }
        
        $('#containerEvents').load(url + ' #containerEvents > *', function() {
            window.history.replaceState(null, null, url.toString());
            bindInitSwiper();
            bindPagination();
//            skip = 0;
//            $(window).scroll(loadOnScroll);
        });

    });
}
    
function bindInitSwiper() {

        // INITIALIZATION OF SWIPER
        // =======================================================
        var paginationProgress = new Swiper('.js-swiper-pagination-progress', {
            lazy: {
                checkInView: true,
                preloadImages: false,            
                loadPrevNext: true                
            },
          pagination: {
            el: '.js-swiper-pagination-progress-element',
            type: 'progressbar',
          },
          navigation: {
            nextEl: '.js-swiper-pagination-progress-button-next',
            prevEl: '.js-swiper-pagination-progress-button-prev',
          },
        });
}

//function loadOnScroll() {
//
//    if (skip < 0) {
//        return false;
//    }
//
//    // end of the document reached?
//    var bottom = $(window).scrollTop() + $(window).height() > $(document).height() - 100;
//    if (bottom) {
//
//        if (!loading) {
//
//            loading = true;
//
//            $('#loading').show();
//
//            // prepare call
//            var url = new URI($('#resultsUri').attr('href'));
//            
//            var cityIdentifier = $('.input-search-city').val();
//            if (!cityIdentifier) {
//                cityIdentifier = "italia";
//            }
//            url.segment(cityIdentifier);
//            
//            var category = $(".input-search-category").val();
//            if (category) {
//                url.segment(category);
//            }
//
//            var startDate=  $("#daterange-predefined").data('daterangepicker').startDate.format('DD/MM/YYYY');
//            url.addSearch("startDate", startDate);
//            var endDate=  $("#daterange-predefined").data('daterangepicker').endDate.format('DD/MM/YYYY');
//            url.addSearch("endDate", endDate);
//
//            url.removeSearch("singleDate");
//            var singleDate = $('.singleDate.active')[0].getAttribute('data-val');
//            if (singleDate) {
//                url.addSearch("singleDate", singleDate);
//            }
//            
//            skip += limit;
//            url.removeSearch("skip");
//            url.addSearch("skip", skip);
//
//            url.removeSearch("limit");
//            url.addSearch("limit", limit);
//
//            try {
//
//                $.ajax({
//                    url: url,
//                    dataType: 'html',
//                    success:
//                        function (html) {
//
//                            // update product list
//                            var replaced = false;
//                            var containers = $('#containerEvents');
//                            if (containers) {
//                                if (containers.length >= 1) {
//                                    var last = containers[containers.length - 1];
//                                    if (last) {
//                                        var replacements = $(html).find(".search-item");
//                                        if (replacements) {
//                                            //if (replacements.length > 0) {
//                                            //    replacements.splice(0, 1);
//                                            //}
//                                            if (replacements.length > 0) {
//
//                                                $(last).append(replacements);                                                
//                                                replaced = true;
//
//                                            }
//                                        }
//                                    }
//                                }
//                            }
//                            $(window).scroll(loadOnScroll);
//                            bindInitSwiper();
//                            if (!replaced) {
//                                // ...stop scrolling
//                                skip = -1;
//                            }
//
//                            $('#loading').hide();
//                            loading = false;
//                        },
//                    error: function(response, status, errorThrown) {
//                        console.log(response);
//
//                        $('#loading').hide();
//                        loading = false;
//                    }
//                });
//
//            } catch (err) {
//                $('#loading').hide();
//                loading = false;
//            }
//
//        }
//
//    }
//}

function bindPagination() {
    $('.navigate-to-page').off();
    $('.navigate-to-page').click(function(event) {
        event.preventDefault();
        
        var url = new URI();
        
        var page = (event && event.target && $(event.target).closest('a').length && $(event.target).closest('a').attr('page')) ? parseInt($(event.target).closest('a').attr('page')) : null;
        url.removeSearch("page");
        if (page && (page > 1)) {
            url.addSearch("page", page);
        }
        
        // navigate to...
        window.location.href = url.toString();
    });
}
