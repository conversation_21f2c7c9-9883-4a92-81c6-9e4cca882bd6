$(function () {

    var $select2Elm = $(".searchall");

    $select2Elm.select2({
        minimumInputLength: 3,
        maximumSelectionSize: 1,
        multiple: true,
        ajax: {
            url: $('#dataSearchUri').attr('href'),
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term, // search term
                    page: params.page
                };
            },
            processResults: function (data, params) {
                // parse the results into the format expected by Select2
                // since we are using custom formatting functions we do not need to
                // alter the remote JSON data, except to indicate that infinite
                // scrolling can be used
                params.page = params.page || 1;

                return {
                    results: data,
                    pagination: {
                        more: (params.page * 10) < (data[0] ? data[0][5] : 0)
                    }
                };
            },
            cache: true
        },
        placeholder: label('common.search.page') + '...',
        templateResult: formatRepo,
        templateSelection: formatRepoSelection
    }).on('change', function (e) {
       
        // get the original input tag
        var select2 = $select2Elm.data('select2'),
        // get the select2 input tag
        $select2Input = $('.select2-input', select2.searchContainer),
        // get the useless tag
        $tagToRemove = $('li', select2.selection).eq(0),
        newValue = $.trim($tagToRemove.text());

        // append the value chosen into the select2 text input
        $select2Input.val(newValue);
        $select2Input.trigger('keyup');
        // set the new value to the original text field
        $select2Elm.val(newValue);
        // remove the useless tag
        $tagToRemove.remove();
    }).data('select2').clearSearch = function () {
        return false;
    };    

    function formatRepo(repo) {
        if (repo.loading) {
            return repo.text;
        }

        if (repo[0]) {
            var imgUrl = "https://agor.app/fe/images/avatar/placeholder.jpg";
            if (repo[2]) {
                imgUrl = $('#imageSearchUri').attr('href') + repo[2];
            }
            var $container = $(
                    "<div class='select2-result-repository clearfix'><a href='" + $('#pageSearchUri').attr('href') + repo[3] + "'>" +
                    "<div class='select2-result-repository__avatar'><img src='" + imgUrl + "' /></div>" +
                    "<div class='select2-result-repository__meta'>" +
                    "<div class='select2-result-repository__title'></div>" +
                    "<div class='select2-result-repository__description' style='font-size: small; color: gray;'></div>" +
                    "</div>" +
                    "</a>" +
                    "</div>"
                    );
            $container.find(".select2-result-repository__title").text(repo[1]);
            
            if (repo[4]) {
                var shortDesc = repo[4].length > 50 ? repo[4].substring(0, 50) + "..." : repo[4];
                $container.find(".select2-result-repository__description").text(shortDesc);
            }
            
        } else {
            var $container = $(
                    "<div class='select2-result-repository clearfix'><a href='" + $('#resultsUri').attr('href') + repo[1] + "'>" +
                    "<div class='select2-result-repository__avatar'><i class='bi bi-search'></i></div>" +
                    "<div class='select2-result-repository__meta'>" +
                    "<div class='select2-result-repository__title'></div>" +
                    "</div>" +
                    "</a>" +
                    "</div>"
                    );
            $container.find(".select2-result-repository__title").text(label('common.search')  + " " + repo[1]);
        }


        return $container;
    }

    function formatRepoSelection(repo) {
        return repo.full_name || repo.text;
    }

});
