window.addEventListener("pageshow", function(evt){
    if(evt.persisted){
        if ($.cookie("agoraScrollPosition_results")) {
            var dataEventBack = $.cookie("agoraScrollPosition_results")
            const elementToScroll = document.querySelector("[data-results-back=" + dataEventBack + "]");
            if (elementToScroll) {
                elementToScroll.scrollIntoView({block: 'center'});
            }
            $.removeCookie("agoraScrollPosition_results");
        }
    }
}, false);
if ('scrollRestoration' in history) {
  // Back off, browser, I got this...
  history.scrollRestoration = 'manual';
}
var loading = false;
var limit = parseInt($('#pagination').text(), 0);
var skip = 0;
var countCol = 0;


(function () {
    
    if ($.cookie("agoraScrollPosition_results")) {
        var dataProductBack = $.cookie("agoraScrollPosition_results")
        const elementToScroll = document.querySelector("[data-results-back=" + dataProductBack + "]");
        if (elementToScroll) {
            setTimeout(function() {
                elementToScroll.scrollIntoView({block: 'center'});
                bindResultsLink("agora", null);
            }, 1000);
        }
        $.removeCookie("agoraScrollPosition_results");
    }
    
    bindEventResultsTypeLink();
        
    window.ias = new InfiniteAjaxScroll('.containerResults', {
        item: '.resultsEntity',
        next: '.pager__next',
        prev: '.pager__prev',
        pagination: '.pager',
        spinner: {
            // element to show as spinner
            element: '.spinner',

            // delay in milliseconds
            // this is the minimal time the loader should be displayed. If loading takes longer, the spinner
            // will be shown for the duration of the loading. If the loading takes less then this duration,
            // say 300ms, then the spinner is still shown for 600ms.
            delay: 600,

            // this function is called when the button has to be shown
            show: function(element) {
              element.style.opacity = '1'; // default behaviour
            },

            // this function is called when the button has to be hidden
            hide: function(element) {
              element.style.opacity = '0'; // default behaviour
            }
        }
    });
    

    ias.on('page', (e) => {
        document.title = e.title;
        let state = history.state;
        history.replaceState(state, e.title, e.url);
        bindWallLink(e.title, e.url);
    });

    // disable cache busting
    ias.on('load', function(event) {
      event.nocache = true;
    });

})();


function bindWallLink(title, url) {
    $('.resultslink').off();
    $('.resultslink').click(function(event) {
        let state = history.state;
        if (url) {
            history.replaceState(state, title, url);
        } else {
            history.replaceState(state, title, window.location.href);
        }
        $.cookie("agoraScrollPosition_results" , $(this).attr("data-results-back"), { expires: 1 });
    });
}


function bindEventResultsTypeLink() {
    $(".result-type-link").on("click", function(e) {
        e.preventDefault();
        var resultUrl = new URI($(this).attr("href"));

        resultUrl.removeSearch("q");
        resultUrl.addSearch("q", $(this).data("type-results"));
        
        window.location.href = resultUrl.toString();
        
    });
}