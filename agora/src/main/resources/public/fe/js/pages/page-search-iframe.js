(function () {
    bindPageSearch();
})();

function bindPageSearch() {
    // Inizializza Select2
    var initials = [];
    $('#pageId').select2({
        maximumSelectionLength: 1,
        minimumInputLength: 3,
        data: initials,
        tags: false,
        ajax: {
            url: $('#dataPagesUri').attr('href'),
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    name: params.term,
                    page: params.page || 1
                };
            },
        processResults: function (data, params) {
                params.page = params.page || 1;

                var results = [];
                if (data) {
                    results = data
                        .filter(function (item) {
                            return item[3];
                        })
                        .map(function (item) {
                            return {
                                id: item[0],
                                text: item[1],
                                avatar: item[2],
                                identifier: item[3],
                                descr: item[4]
                            };
                        });
                }

                return {
                    results: results,
                    pagination: {
                        more: data && data[0] && data[0][5] ? (params.page * 10) < data[0][5] : false
                    }
                };
            },
            cache: true
        },
        templateResult: formatRepo,
        templateSelection: formatRepoSelection,
        escapeMarkup: function (markup) {
            return markup;
        }
    });

    $('#pageId').on('select2:select', function (e) {
        var selectedId = e.params.data.id;
        // Cerca l'elemento nell'opzione selezionata per recuperare i data attributes
        var $selectedOption = $('#pageId option[value="' + selectedId + '"]');

        if ($selectedOption.length > 0) {
            var identifier = $selectedOption.data('identifier');
            if (identifier) {
                var iframeUrl = $("#iframeBaseUri").attr('href').replace(":identifier", identifier);
                var iframeCode = "<iframe src='" + iframeUrl + "' width='100%' height='400' frameborder='0'></iframe>";
                $("#iframe-container").text(iframeCode);
                $("#iframe-code-card").show();
            } else {
                // TODO: MOSTRARE MESSAGGIO DI ERRORE GENERICO
                $("#iframe-code-card").hide();
            }
        }
    });

    // Copy functionality
    $('#copy-btn').on('click', function () {
        var iframeCode = $('#iframe-container').text();
        if (iframeCode) {
            navigator.clipboard.writeText(iframeCode).then(function () {
                // Change button text temporarily to show success
                var $btn = $('#copy-btn');
                var originalHtml = $btn.html();
                $btn.html('<i class="bi bi-check"></i> Copied!');
                $btn.removeClass('btn-outline-primary').addClass('btn-success');

                setTimeout(function () {
                    $btn.html(originalHtml);
                    $btn.removeClass('btn-success').addClass('btn-outline-primary');
                }, 2000);
            }).catch(function (err) {
                console.error('Failed to copy: ', err);
                // Fallback for older browsers
                var textArea = document.createElement('textarea');
                textArea.value = iframeCode;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                var $btn = $('#copy-btn');
                var originalHtml = $btn.html();
                $btn.html('<i class="bi bi-check"></i> Copied!');
                $btn.removeClass('btn-outline-primary').addClass('btn-success');

                setTimeout(function () {
                    $btn.html(originalHtml);
                    $btn.removeClass('btn-success').addClass('btn-outline-primary');
                }, 2000);
            });
        }
    });

// Modifica il formatRepo per aggiungere data attributes quando possibile
    function formatRepo(repo) {
        // if it doesn't have the identifier, don't display it
        if (!repo.identifier) {
            return null;
        }

        $("#select2-pageId-results").parent().parent().css("z-index", 9999);

        if (repo.loading) {
            return repo.text;
        }

        if (repo.isCreate || repo.isNew) {
            return $(
                '<div class="select2-result-repository clearfix d-flex align-items-center create-option">' +
                '<div class="select2-result-repository__avatar"><i class="fa-2x bi bi-file-plus"></i></div>' +
                '<div class="select2-result-repository__meta">' +
                '<div class="select2-result-repository__title">Crea "' + repo.text + '"</div>' +
                '</div>' +
                '</div>'
            );
        } else {
            var imgUrl = "https://agor.app/fe/images/avatar/placeholder.jpg";
            if (repo.avatar) {
                imgUrl = $('#imageSearchUri').attr('href') + repo.avatar;
            }

            var $container = $(
                '<div class="select2-result-repository clearfix d-flex align-items-center">' +
                '<div class="select2-result-repository__avatar"><img src="' + imgUrl + '" /></div>' +
                '<div class="select2-result-repository__meta">' +
                '<div class="select2-result-repository__title">' + repo.text + '</div>' +
                "<div class='select2-result-repository__description' style='font-size: small; color: gray;'></div>" +
                '</div>' +
                '</div>'
            );

            if (repo.descr) {
                var shortDesc = repo.descr.length > 50 ? repo.descr.substring(0, 50) + "..." : repo.descr;
                $container.find(".select2-result-repository__description").text(shortDesc);
            }
            return $container;
        }
    }

    function formatRepoSelection(repo) {
        // Aggiungi data attributes al container per riferimento futuro
        $('#pageId').find("option[value='" + repo.id + "']").attr('data-identifier', repo.identifier);

        return repo.text;
    }
}
