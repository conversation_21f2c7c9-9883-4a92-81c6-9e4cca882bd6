var autocomplete;
(function () {
    $(window).keydown(function (event) {
        if (event.keyCode === 13) {
            event.preventDefault();
            return false;
        }
    });

    $('textarea').keydown(function (event) {
        if (event.keyCode === 13 && !event.shiftKey) {
            event.preventDefault();
            var content = this.value;
            var caret = getCaret(this);
            this.value = content.substring(0, caret) + "\n" + content.substring(caret, content.length);
            this.scrollTop = this.scrollHeight;
        }
    });

    var summerLang = 'en-US';
    if ($('#language').val() === 'it') {
        summerLang = 'it-IT';
    }
    $('.summernote').summernote({
        lang: summerLang,
        callbacks: {
            onPaste: function (e) {
                var bufferText = ((e.originalEvent || e).clipboardData || window.clipboardData).getData('Text');
                e.preventDefault();
                document.execCommand('insertText', false, bufferText);
            }
        },
        toolbar: [
            ['style', ['italic']],
            ['insert', ['link']]
        ],
        disableLinkTarget: false // Permette di scegliere se aprire il link in nuova finestra
    });


    // Funzione per ottenere la posizione del cursore all'interno di un textarea
    function getCaret(el) {
        if (el.selectionStart) {
            return el.selectionStart;
        } else if (document.selection) {
            el.focus();

            var r = document.selection.createRange();
            if (r == null) {
                return 0;
            }

            var re = el.createTextRange(),
                    rc = re.duplicate();
            re.moveToBookmark(r.getBookmark());
            rc.setEndPoint('EndToStart', re);

            return rc.text.length;
        }
        return 0;
    }

    $('#tags').select2({
        minimumInputLength: 2,
        tags: true,
        ajax: {
            url: $('#dataPageTagUri').attr('href'),
            dataType: 'json',
            delay: 250,
            quietMillis: 250,
            data: function (term) {
                term.term = term.term.toLowerCase();
                return {name: term};
            },
            processResults: function (data) {
                var results;
                results = [];
                $.each(data, function (idx, item) {
                    results.push({
                        'id': item.toLowerCase(),
                        'text': item.toLowerCase()
                    });
                });
                return {results: results};
            }
        }
    }).on("select2:selecting", function (e) {
        // Converte il nuovo valore in lowercase prima di selezionarlo
        e.params.args.data.text = e.params.args.data.text.toLowerCase();
        e.params.args.data.id = e.params.args.data.id.toLowerCase();
    });

    $('#ownerId').select2({
        minimumInputLength: 3,
        allowClear: true,
        placeholder: '',
        ajax: {
            url: $('#dataPageUserUri').attr('href'),
            dataType: 'json',
            delay: 250,
            data: function (term) {
                term.term = term.term.toLowerCase();
                return {name: term};
            },
            processResults: function (data, params) {
                var results = [];
                if (data) {
                    // evito di prendere me stesso
                    var filteredData = [];
                    data.forEach(function (element) {
                        if (element) {
                            if (element[0]) {
                                filteredData.push(element);
                            }
                        }
                    });

                    results = filteredData.map(function (item) {
                        return {
                            id: item[0],
                            text: item[1]
                        };
                    });
                }

                return {
                    results: results
                };
            },
            cache: true
        },
        createTag: function (params) {
            return null; // Non creare duplicati
        },
        templateResult: formatRepo,
        templateSelection: formatRepoSelection,
        escapeMarkup: function (markup) {
            return markup;
        }
    });

    function formatRepo(repo) {
        if (repo.loading) {
            return repo.text;
        }

        // Se è un'opzione "Crea"
        if (repo.isCreate || repo.isNew) {
            return $(
                '<div class="select2-result-repository clearfix d-flex align-items-center create-option">' +
                '<div class="select2-result-repository__avatar"><i class="fa-2x bi bi-file-plus"></i></div>' +
                '<div class="select2-result-repository__meta">' +
                '<div class="select2-result-repository__title">Crea "' + repo.text + '"</div>' +
                '</div>' +
                '</div>'
            );
        } else {
            var imgUrl = "https://agor.app/fe/images/avatar/placeholder.jpg";
            if (repo.avatar) {
                imgUrl = $('#imageSearchUri').attr('href') + repo.avatar;
            }

            var $container = $(
                '<div class="select2-result-repository clearfix d-flex align-items-center">' +
                '<div class="select2-result-repository__avatar"><img src="' + imgUrl + '" /></div>' +
                '<div class="select2-result-repository__meta">' +
                '<div class="select2-result-repository__title">' + repo.text + '</div>' +
                "<div class='select2-result-repository__description' style='font-size: small; color: gray;'></div>" +
                '</div>' +
                '</div>'
            );

            if (repo.descr) {
                var shortDesc = repo.descr.length > 50 ? repo.descr.substring(0, 50) + "..." : repo.descr;
                $container.find(".select2-result-repository__description").text(shortDesc);
            }
            return $container;
        }

    }

    function formatRepoSelection(repo) {
        return repo.text;
    }

    // Verifica il valore iniziale al caricamento della pagina
    var countryCodeValue = $('#countryCode').val();
    if (countryCodeValue !== 'IT') {
        $('#provinceDiv').hide();
        $('#provinceExtDiv').show();
        $('.provinceCodeIt').prop('name', 'provinceCodeIt').prop('id', 'provinceCodeIt');
        $('.provinceCodeIt').removeAttr("required");
        $('.provinceCodeExt').prop('name', 'provinceCode').prop('id', 'provinceCode');
    } else {
        $('#provinceDiv').show();
        $('#provinceExtDiv').hide();
        $('.provinceCodeIt').prop('name', 'provinceCode').prop('id', 'provinceCode');
        $('.provinceCodeIt').attr("required", "required");
        $('.provinceCodeExt').prop('name', 'provinceCodeExt').prop('id', 'provinceCodeExt');
    }

    // Aggiungi un gestore per l'evento change sull'elemento con id "countrycode"
    $('#countryCode').on('change', function () {
        checkCountryCode();
    });

    $('#form-page-edit').submit(function (event) {
        event.preventDefault();
        // Validation rules
        if ($("#form-page-edit").valid()) {
            // post to url
            var url = $("#pageEditSaveUri").attr("href");
            var data = new FormData($(this)[0]);

            $.blockUI();
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                success:
                        function (returndata) {
                            $.unblockUI();
                            var url = $("#backUri").attr("href");
                            if (url) {
                                Swal.fire({
                                    position: 'center',
                                    icon: 'success',
                                    title: label('common.page.update.success'),
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(function () {
                                    window.location.href = url + returndata;
                                });
                            } else {
                                Swal.fire({
                                    position: 'center',
                                    icon: 'success',
                                    title: label('common.page.update.success'),
                                    showConfirmButton: false,
                                    timer: 1500
                                });
                            }
                        },
                error:
                        function (response, status, errorThrown) {
                            var msg = label('common.operation.error');
                            if (response) {
                                if (response.responseText) {
                                    msg = response.responseText;
                                }
                            }
                            $.unblockUI();
                            // warn
                            Swal.fire({
                                position: 'top',
                                icon: 'error',
                                title: msg,
                                text: label('common.something.went.wrong'),
                                buttonsStyling: false,
                                customClass: {
                                    confirmButton: 'btn btn-primary btn-lg',
                                },
                                confirmButtonText: label('common.continue'),                                
                                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                            });
                        }
            });
        }
        return false;
    });

    /*$(".camel-case").on("input", function () {
        $(this).val(toCamelCase($(this).val()));
    });*/

    bindChangePageType();
    initAutocomplete();
})();

// async registering autocomplete address
function initAutocomplete() {
    (function () {
        var input = $('#fulladdress')[0];
        var options = {
            offset: 2
        };
        autocomplete = new google.maps.places.Autocomplete(input, options);
        autocomplete.addListener('place_changed', selectAddress);

        // hacking google logo
        setTimeout(function () {
            $('.pac-container').removeClass('pac-logo');
        }, 1000);
    })();
}

function selectAddress() {
    (function () {
        // get city
        var city = '';
        var address = '';
        var number = '';
        var postalCode = '';
        var countryCode = '';
        var provinceCode = '';
        var venue = '';
        var place = autocomplete.getPlace();
        if (place !== null) {
            if (typeof place.address_components !== 'undefined') {
                for (var i = 0; i < place.address_components.length; i++) {
                    var type = place.address_components[i].types[0];
                    if (type === 'locality') {
                        city = place.address_components[i]['long_name'];
                    }
                    if (type === 'route') {
                        address = place.address_components[i]['long_name'];
                    }
                    if (type === 'street_number') {
                        number = place.address_components[i]['long_name'];
                    }
                    if (type === 'postal_code') {
                        postalCode = place.address_components[i]['long_name'];
                    }
                    if (type === 'administrative_area_level_2') {
                        provinceCode = place.address_components[i]['short_name'];
                    }
                    if (type === 'country') {
                        countryCode = place.address_components[i]['short_name'];
                    }
                    //                  type for venue
                    //                "point_of_interest"
                    //                "establishment"
                    if (place.types.includes("point_of_interest") || place.types.includes("establishment")) {
                        venue = place.name;
                    }
                }
                if (address !== '') {
                    if (number !== '') {
                        address += ' ' + number;
                    }
                }
            }
        }
        $('#countryCode').val(countryCode).change();
        checkCountryCode();
        $('#city').val(city);
        $('#address').val(address);
        $('#postalCode').val(postalCode);
        $('#provinceCode').val(provinceCode).change();
    })();
}

function bindChangePageType() {
    $('.pageType').off();
    $('.pageType').change(function (event) {
        event.preventDefault();

        var url = new URI($("#pageEditUri").attr("href"));
        url.removeSearch("pageType");
        url.addSearch("pageType", $('#pageType').val());

        $("#categoryPage").load(url + ' ' + "#categoryPage" + ' > *', function () {
            var categoryElement = document.getElementById('category');
            if (categoryElement) {
                var removeItemBtn = categoryElement.getAttribute('data-remove-item-button') == 'true' ? true : false;
                var placeHolder = categoryElement.getAttribute('data-placeholder') == 'false' ? false : true;
                var placeHolderVal = categoryElement.getAttribute('data-placeholder-val') ? categoryElement.getAttribute('data-placeholder-val') : 'Type and hit enter';
                var maxItemCount = categoryElement.getAttribute('data-max-item-count') ? categoryElement.getAttribute('data-max-item-count') : 3;
                var searchEnabled = categoryElement.getAttribute('data-search-enabled') == 'true' ? true : false;
                var position = categoryElement.getAttribute('data-position') ? categoryElement.getAttribute('data-position') : 'auto';
                var choiceCatgegory = new Choices(categoryElement, {
                    removeItemButton: removeItemBtn,
                    placeholder: placeHolder,
                    placeholderValue: placeHolderVal,
                    maxItemCount: maxItemCount,
                    searchEnabled: searchEnabled,
                    position: position,
                    shouldSort: false
                });
            }
        });

    });
}

function checkCountryCode() {
    var countryCodeValue = $('#countryCode').val();
    if (countryCodeValue !== 'IT') {
        $('#provinceDiv').hide();
        $('#provinceExtDiv').show();
        $('.provinceCodeIt').prop('name', 'provinceCodeIt').prop('id', 'provinceCodeIt');
        $('.provinceCodeIt').removeAttr("required");
        $('.provinceCodeExt').prop('name', 'provinceCode').prop('id', 'provinceCode');
    } else {
        $('#provinceDiv').show();
        $('#provinceExtDiv').hide();
        $('.provinceCodeIt').prop('name', 'provinceCode').prop('id', 'provinceCode');
        $('.provinceCodeIt').attr("required", "required");
        $('.provinceCodeExt').prop('name', 'provinceCodeExt').prop('id', 'provinceCodeExt');
    }
    $('.provinceCode').val('');
}

function toCamelCase(str) {
    return str
            .split(' ') // Split the string into words
            .map((word, index) => {
                return word.charAt(0).toUpperCase() + word.slice(1); // Capitalize the first letter of subsequent words
            })
            .join(' '); // Join the words back into a single string
}