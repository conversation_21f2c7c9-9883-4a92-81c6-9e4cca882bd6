$(function () {

    bindRegister();
});

function bindRegister() {

    // Validation rules
    $("#form-register").validate({
        errorPlacement: function (error, element) {
            if (element.attr("type") === "checkbox") {
                error.appendTo(element.parent());
            } else if (element.is("select")) {
                error.appendTo(element.parent());
            } else if (element.hasClass("fakepassword")) {
                error.appendTo(element.parent().parent());
            } else {
                error.insertAfter(element);
            }
        }
    });

    // Form submit
    $('#form-register').submit(function (event) {
        event.preventDefault();
    // Validation rules
        if ($("#form-register").valid()) {

//            var form = $(this).closest("form");
//            _iub.cons_instructions.push(["submit", {
//                writeOnLocalStorage: false, // default: false
//                    form: {
//                        selector: form
//                    },
//                    consent: {
//                        legal_notices: [
//                            {
//                                identifier: 'privacy_policy'
//                            },
//                            {
//                                identifier: 'cookie_policy'
//                            },
//                            {
//                                identifier: 'terms'
//                            }
//                      ]
//                    }    
//                },
//                {
//                    success: function(response) {
            // continue with form submit...
//                        console.log('iubenda ' + response.responseText);
            // post to url
            var url = $("#registerSendUri").attr("href");

            // data to post
            var data = new FormData();
            data.append('email', $('#username').val());
            data.append('password', $('#psw-input').val());
            data.append('privacy', $('#privacy').prop('checked'));
            data.append('lastname', $('#lastname').val());
            data.append('name', $('#name').val());

            $.blockUI();
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                success:
                        function (returndata) {
                            $.unblockUI();
                            var url = $("#accountUri").attr("href");
                            window.location.href = url;
                        },
                error:
                        function (response, status, errorThrown) {
                            var msg = label('common.operation.error');
                            if (response) {
                                if (response.responseText) {
                                    msg = response.responseText;
                                }
                            }
                            $.unblockUI();
                            // warn
                            Swal.fire({
                                position: 'top',
                                icon: 'error',
                                title: label('common.ops.error'),
                                text: msg,
                                buttonsStyling: false,
                                customClass: {
                                    confirmButton: 'btn btn-primary btn-lg',
                                },
                                confirmButtonText: label('common.continue'),
                                // todo
                                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                            });
                        }
            });
        }
        return false;
    });    
}
