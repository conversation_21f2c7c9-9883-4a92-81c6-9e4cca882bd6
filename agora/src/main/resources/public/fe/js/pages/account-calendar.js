window.addEventListener("pageshow", function (evt) {
    if (evt.persisted) {
        if ($.cookie("agoraScrollPosition_myevents")) {
            var dataEventBack = $.cookie("agoraScrollPosition_myevents")
            const elementToScroll = document.querySelector("[data-myevents-back=" + dataEventBack + "]");
            if (elementToScroll) {
                elementToScroll.scrollIntoView({block: 'center'});
            }
            $.removeCookie("agoraScrollPosition_myevents");
        }
    }
}, false);
if ('scrollRestoration' in history) {
    // Back off, browser, I got this...
    history.scrollRestoration = 'manual';
}
var ias, iasFollow, iasFollowPast;
var loading = false;
var limit = parseInt($('#pagination').text(), 0);
var skip = 0;

var loadingEventsFollow = false;
var limitEventsFollow = parseInt($('#paginationEventsFollow').text(), 0);
var skipEventsFollow = 0;

var loadingEventsFollowPast = false;
var limitEventsFollowPast = parseInt($('#paginationEventsFollowPast').text(), 0);
var skipEventsFollowPast = 0;

(function () {
    
    if ($.cookie("agoraScrollPosition_myevents")) {
        var dataEventBack = $.cookie("agoraScrollPosition_myevents")
        const elementToScroll = document.querySelector("[data-myevents-back=" + dataEventBack + "]");
        if (elementToScroll) {
            setTimeout(function () {
                elementToScroll.scrollIntoView({block: 'center'});
                bindEventLink("agora", null);
            }, 1000);
        }
        $.removeCookie("agoraScrollPosition_myevents");
    }

    bindIas();
    
    bindEventFollow();
    bindEventRemove();
    bindTabChange();

})();

function bindEventFollow() {
    $('.event-add-follow').off();
    $('.event-add-follow').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#eventFollowToggleUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var user = event.target.getAttribute('data-user');
        if (!user) {
            user = event.target.parentNode.getAttribute('data-user');
        }
        if (!user) {
            console.error('missing user');
            return false;
        }
        
        var eventId = event.target.getAttribute('data-event-id');
        if (!eventId) {
            eventId = event.target.parentNode.getAttribute('data-event-id');
        }
        if (!eventId) {
            console.error('missing eventId');
            return false;
        }
        
        var value = event.target.getAttribute('data-value');
        if (!value) {
            value = event.target.parentNode.getAttribute('data-value');
        }
        if (!value) {
            console.error('missing value');
            return false;
        }
        
        if (user === 'unlogged') {
            
            // warn
            Swal.fire({
                position: 'top',
                icon: 'error',
                title: 'Oh oh! :(',
                text: label('common.must.registered.event'),
                buttonsStyling: false,
                customClass: {
                    confirmButton: 'btn btn-primary btn-lg',
                },
                confirmButtonText: '<a class="btn btn-primary btn-lg" href=' + $("#registerUri").attr("href") + '>' + label('common.register') + '</a>',
                // todo
                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
            });

            
            return false;
        }
        
        url.removeSearch("eventId");
        url.addSearch("eventId", eventId);

        
        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            success:
                    function (returndata) {
                        $.unblockUI();

                        var msg = label('common.event.added');
                        if (value === 'active') {
                            var msg = label('common.event.removed');
                        }
                       
                        $.unblockUI();

                        Swal.fire({
                            position: 'center',
                            icon: 'success',
                            title: msg,
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function(){
                            var url = new URI();
                            $('#myTabContent').load(url + ' #myTabContent > *', function() {
                                bindEventFollow();
                                bindEventRemove();
                                bindIas();
                                bindTabChange();
                            });
                        });

                    },
            error:
                    function (response, status, errorThrown) {
                        $.unblockUI();

                        // warn
                        $.alert({
                            theme: 'supervan',
                            escapeKey: true,
                            animation: 'top',
                            closeAnimation: 'bottom',
                            backgroundDismiss: true,
                            title: 'Oh oh! :(',
                            content: label('common.add.failed')
                        });
                    }
        });                    

        return false;
    });
}
function bindEventRemove() {
    $('.event-remove').off();
    $('.event-remove').click(function (event) {
        event.preventDefault();

        // prepare call
        var url = new URI($('#eventRemoveUri').attr('href'));
        if (!url) {
            console.error('missing url');
            return false;
        }

        var user = event.target.getAttribute('data-user');
        if (!user) {
            user = event.target.parentNode.getAttribute('data-user');
        }
        if (!user) {
            console.error('missing user');
            return false;
        }
        
        var eventId = event.target.getAttribute('data-event-id');
        if (!eventId) {
            eventId = event.target.parentNode.getAttribute('data-event-id');
        }
        if (!eventId) {
            console.error('missing eventId');
            return false;
        }
        
        url.removeSearch("eventId");
        url.addSearch("eventId", eventId);

        Swal.fire({
            position: 'center',
            icon: 'warning',
            title: label('event.delete.event'),
            showConfirmButton: true,
            confirmButtonText: label('common.confirm'),            
            showCancelButton: true,
            cancelButtonText: label('common.cancel'),                        
            buttonsStyling: false,
            customClass: {
                confirmButton: 'btn btn-danger',
                cancelButton: 'btn btn-default'
            }
        }).then((result) => {
            if (result.isConfirmed) {
                $.blockUI();
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: null,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success:
                            function (returndata) {
                                $.unblockUI();

                                var msg = label('common.event.removed');

                                $.unblockUI();

                                Swal.fire({
                                    position: 'center',
                                    icon: 'success',
                                    title: msg,
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(function(){
                                    var url = new URI();
                                    $('#myTabContent').load(url + ' #myTabContent > *', function() {
                                        bindEventFollow();
                                        bindEventRemove();
                                        bindIas();
                                        bindTabChange();
                                    });
                                });

                            },
                    error:
                            function (response, status, errorThrown) {
                                $.unblockUI();

                                // warn
                                $.alert({
                                    theme: 'supervan',
                                    escapeKey: true,
                                    animation: 'top',
                                    closeAnimation: 'bottom',
                                    backgroundDismiss: true,
                                    title: 'Oh oh! :(',
                                    content: label('common.remove.failed')
                                });
                            }
                });                    

                return false;
            }
        });
    });

}

function bindEventLink(title, url) {
    $('.eventlink').off();
    $('.eventlink').click(function (event) {
        let state = history.state;
        if (url) {
            history.replaceState(state, title, url);
        } else {
            history.replaceState(state, title, window.location.href);
        }
        $.cookie("agoraScrollPosition_myevents", $(this).attr("data-myevents-back"), {expires: 1});
    });
}

function bindIas() {

    var eventElements = document.getElementsByClassName("myEvents");
    var spinner = document.getElementsByClassName("spinner");
    var containerMyEvents = $(".containerMyEvents");
    var containerEventsFollow = $(".containerEventsFollow");
    var containerEventsFollowPast = $(".containerEventsFollowPast");
    if (containerMyEvents.is(":visible")) {
        resetIas(); // Resetta ias
        if (eventElements.length > 0) {
            ias = new InfiniteAjaxScroll('.containerMyEvents', {
                item: '.myEvents',
                next: '.pager__next',
                prev: '.pager__prev',
                pagination: '.pager',
                spinner: {
                    // element to show as spinner
                    element: '.spinner',

                    // delay in milliseconds
                    // this is the minimal time the loader should be displayed. If loading takes longer, the spinner
                    // will be shown for the duration of the loading. If the loading takes less then this duration,
                    // say 300ms, then the spinner is still shown for 600ms.
                    delay: 300,

                    // this function is called when the button has to be shown
                    show: function (element) {
                        element.style.opacity = '1'; // default behaviour
                    },

                    // this function is called when the button has to be hidden
                    hide: function (element) {
                        element.style.opacity = '0'; // default behaviour
                    }
                }
            });


            ias.on('page', (e) => {
                let contEv = $(".containerMyEvents");
                if (contEv.is(":visible")) {
                    document.title = e.title;
                    let state = history.state;
                    history.replaceState(state, e.title, e.url);
                    bindEventLink(e.title, e.url);
                    bindEventFollow();
                    bindEventRemove();
                }
            });

            // disable cache busting
            ias.on('load', function (event) {
                event.nocache = true;
            });
        } else {
            bindEventFollow();
            bindEventRemove();
        }
    } else if (containerEventsFollow.is(":visible")) {
        resetIas(); // Resetta ias
        var eventElementsFollow = document.getElementsByClassName("eventsFollow");
        var spinnerFollow = document.getElementsByClassName("spinnerFollow");
        if (eventElementsFollow.length > 0 && spinnerFollow.length > 0) {
            iasFollow = new InfiniteAjaxScroll('.containerEventsFollow', {
                item: '.eventsFollow',
                next: '.pager__nextFollow',
                prev: '.pager__prevFollow',
                pagination: '.pagerFollow',
                spinner: {
                    // element to show as spinner
                    element: '.spinnerFollow',

                    // delay in milliseconds
                    // this is the minimal time the loader should be displayed. If loading takes longer, the spinner
                    // will be shown for the duration of the loading. If the loading takes less then this duration,
                    // say 300ms, then the spinner is still shown for 600ms.
                    delay: 600,

                    // this function is called when the button has to be shown
                    show: function (element) {
                        element.style.opacity = '1'; // default behaviour
                    },

                    // this function is called when the button has to be hidden
                    hide: function (element) {
                        element.style.opacity = '0'; // default behaviour
                    }
                }
            });


            iasFollow.on('page', (e) => {
                let contEv = $(".containerEventsFollow");
                if (contEv.is(":visible")) {
                    document.title = e.title;
                    let state = history.state;
                    history.replaceState(state, e.title, e.url);
                    bindEventLink(e.title, e.url);
                    bindEventFollow();
                    bindEventRemove();
                }
            });

            // disable cache busting
            iasFollow.on('load', function (event) {
                event.nocache = true;
            });
        } else {
            bindEventFollow();
            bindEventRemove();
        }
    } else if (containerEventsFollowPast.is(":visible")) {
        resetIas(); // Resetta ias
        var eventElementsFollowPast = document.getElementsByClassName("eventsFollowPast");
        var spinnerFollowPast = document.getElementsByClassName("spinnerFollowPast");
        if (eventElementsFollowPast.length > 0 && spinnerFollowPast.length > 0) {
            iasFollowPast = new InfiniteAjaxScroll('.containerEventsFollowPast', {
                item: '.eventsFollowPast',
                next: '.pager__nextFollowPast',
                prev: '.pager__prevFollowPast',
                pagination: '.pagerFollowPast',
                spinner: {
                    // element to show as spinner
                    element: '.spinnerFollowPast',

                    // delay in milliseconds
                    // this is the minimal time the loader should be displayed. If loading takes longer, the spinner
                    // will be shown for the duration of the loading. If the loading takes less then this duration,
                    // say 300ms, then the spinner is still shown for 600ms.
                    delay: 600,

                    // this function is called when the button has to be shown
                    show: function (element) {
                        element.style.opacity = '1'; // default behaviour
                    },

                    // this function is called when the button has to be hidden
                    hide: function (element) {
                        element.style.opacity = '0'; // default behaviour
                    }
                }
            });


            iasFollowPast.on('page', (e) => {
                let contEv = $(".containerEventsFollowPast");
                if (contEv.is(":visible")) {
                    document.title = e.title;
                    let state = history.state;
                    history.replaceState(state, e.title, e.url);
                    bindEventLink(e.title, e.url);
                    bindEventFollow();
                    bindEventRemove();
                }
            });

            // disable cache busting
            iasFollowPast.on('load', function (event) {
                event.nocache = true;
            });
        } else {
            bindEventFollow();
            bindEventRemove();
        }
    }
}

function resetIas() {
    if (ias) {
        ias.off('page'); // Scollega l'evento 'page'
        ias.off('load'); // Scollega l'evento 'load'
        ias = null; // Resetta la variabile
    }
    if (iasFollow) {
        iasFollow.off('page'); // Scollega l'evento 'page'
        iasFollow.off('load'); // Scollega l'evento 'load'
        iasFollow = null; // Resetta la variabile
    }
}
function bindTabChange() {
    $('#myTab button[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
        var href = $(e.target).attr('data-bs-target');
        var tabId = href.substring(1);
        var eventsUri = new URI($('#accountCalendarUri').attr('href'));
        if (tabId === 'myevents-tab-pane') {
            eventsUri.removeSearch("tab");
            eventsUri.addSearch("tab", "myevents");
            $('#myevents-tab-pane').load(eventsUri + ' #myevents-tab-pane > *', function () {
                loading = false;
                bindEventFollow();
                bindEventRemove();
                bindIas();
            });
        } else if (tabId === 'followedevents-tab-pane') {
            eventsUri.removeSearch("tab");
            eventsUri.addSearch("tab", "follow");
            $('#followedevents-tab-pane').load(eventsUri + ' #followedevents-tab-pane > *', function () {
                loadingEventsFollow = false;
                bindEventFollow();
                bindEventRemove();
                bindIas();
            });
        } else if (tabId === 'followedeventspast-tab-pane') {
            eventsUri.removeSearch("tab");
            eventsUri.addSearch("tab", "followPast");
            $('#followedeventspast-tab-pane').load(eventsUri + ' #followedeventspast-tab-pane > *', function () {
                loadingEventsFollowPast = false;
                bindEventFollow();
                bindEventRemove();
                bindIas();
            });
        }
    });
}