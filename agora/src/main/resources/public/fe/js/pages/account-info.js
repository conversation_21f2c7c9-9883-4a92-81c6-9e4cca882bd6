(function () {

    $('#account-confirm-send').click(function (event) {
        event.preventDefault();

        // post to url
        var url = $("#account-confirm-send").attr("href");

        // data to post
        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            success:
                    function (returndata) {
                        $.unblockUI();

                        Swal.fire({
                            position: 'center',
                            icon: 'success',
                            title: label('common.email.sent'),
                            text: label('common.check.inbox'),
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function () {
                            location.reload();
                        }
                        );
                    },
            error:
                    function (response, status, errorThrown) {
                        $.unblockUI();

                        Swal.fire({
                            position: 'center',
                            icon: 'error',
                            title: label('common.ops.error'),
                            text: label('common.mail.not.sent'),
                            buttonsStyling: false,
                            customClass: {
                                confirmButton: 'btn btn-primary btn-lg',
                            },
                            confirmButtonText: 'Continua <i class="bi-chevron-right small"></i>',
                            footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                        });

                    }
        });

        return false;

    });

    $('#form-info-edit').submit(function (event) {
        event.preventDefault();
        // Validation rules
        if ($("#form-info-edit").valid()) {
            // post to url
            var url = $("#accountInfoEditSaveUri").attr("href");
            var data = new FormData($(this)[0]);

            $.blockUI();
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                success:
                        function (returndata) {
                            $.unblockUI();
                            var url = $("#accountUri").attr("href");
                            Swal.fire({
                                position: 'center',
                                icon: 'success',
                                title: label('common.data.saved.correctly'),
                                showConfirmButton: false,
                                timer: 1500
                            });
                        },
                error:
                        function (response, status, errorThrown) {
                            var msg = label('common.operation.error');
                            if (response) {
                                if (response.responseText) {
                                    msg = response.responseText;
                                }
                            }
                            $.unblockUI();
                            // warn
                            Swal.fire({
                                position: 'top',
                                icon: 'error',
                                title: label('common.ops.error'),
                                text: msg,
                                buttonsStyling: false,
                                customClass: {
                                    confirmButton: 'btn btn-primary btn-lg',
                                },
                                confirmButtonText: label('common.continue'),
                                // todo
                                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                            });
                        }
            });
        }
        return false;
    });

})();

function deleteProfile() {
    event.preventDefault();
    
    var postToUrl = $('#accountRemoveUri').attr('href');
    $.confirm({
        theme: 'supervan',
        escapeKey: true,
        animation: 'top',
        closeAnimation: 'bottom',
        backgroundDismiss: true,
        title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
        content: "Il tuo account verrà cancellato definitivamente.",
        buttons: {
            annulla: {
                text: 'Annulla',
                btnClass: 'btn-default mb-5',
                action: function () {
                    //
                }
            },
            conferma: {
                text: 'Conferma',
                btnClass: 'bg-primary mb-5',
                action: function () {
                    $.blockUI();
                    $.ajax({
                        url: postToUrl,
                        type: 'POST',
                        data: null,
                        cache: false,
                        contentType: false,
                        processData: false,
                        success:
                                function (returndata) {
                                    $.unblockUI();
                                },
                        error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: label('common.delete.failed')
                                    });
                                }
                    });
                }
            }
        }
    });
}
