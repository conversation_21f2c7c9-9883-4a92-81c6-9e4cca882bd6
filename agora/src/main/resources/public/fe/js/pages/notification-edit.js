var autocomplete;
var map, circle;

(function () {
    $(window).keydown(function (event) {
        if (event.keyCode == 13) {
            event.preventDefault();
            return false;
        }
    });
    $('#form-notifications-edit').submit(function (event) {
        event.preventDefault();
        // Validation rules
        if ($("#form-notifications-edit").valid()) {
            // post to url
            var url = $("#notificationEditSaveUri").attr("href");
            var data = new FormData($(this)[0]);

            $.blockUI();
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                success:
                        function (returndata) {
                            $.unblockUI();
                            var url = $("#accountNotificationsUri").attr("href");
                            Swal.fire({
                                position: 'center',
                                icon: 'success',
                                title: label('common.notification.update.success'),
                                showConfirmButton: false,
                                timer: 1500
                            });
                        },
                error:
                        function (response, status, errorThrown) {
                            var msg = label('common.operation.error');
                            if (response) {
                                if (response.responseText) {
                                    msg = response.responseText;
                                }
                            }
                            $.unblockUI();
                            // warn
                            Swal.fire({
                                position: 'top',
                                icon: 'error',
                                title: label('common.ops.error'),
                                text: msg,
                                buttonsStyling: false,
                                customClass: {
                                    confirmButton: 'btn btn-primary btn-lg',
                                },
                                confirmButtonText: label('common.continue'),
                                // todo
                                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                            });
                        }
            });
        }
        return false;
    });
    initAutocomplete();
    initializeMap();
})();

// async registering autocomplete address
function initAutocomplete() {
    (function () {
        var input = $('#fulladdress')[0];
        var options = {
            offset: 2
        };
        autocomplete = new google.maps.places.Autocomplete(input, options);
        autocomplete.addListener('place_changed', selectAddress);

        // hacking google logo
        setTimeout(function () {
            $('.pac-container').removeClass('pac-logo');
        }, 1000);
    })();
}

function selectAddress() {
    (function () {
        // get city
        var city = '';
        var address = '';
        var number = '';
        var postalCode = '';
        var countryCode = '';
        var provinceCode = '';
        var venue = '';
        var place = autocomplete.getPlace();
        if (place !== null) {
            if (typeof place.address_components !== 'undefined') {
                for (var i = 0; i < place.address_components.length; i++) {
                    var type = place.address_components[i].types[0];
                    if (type === 'locality') {
                        city = place.address_components[i]['long_name'];
                    }
                    if (type === 'route') {
                        address = place.address_components[i]['long_name'];
                    }
                    if (type === 'street_number') {
                        number = place.address_components[i]['long_name'];
                    }
                    if (type === 'postal_code') {
                        postalCode = place.address_components[i]['long_name'];
                    }
                    if (type === 'administrative_area_level_2') {
                        provinceCode = place.address_components[i]['short_name'];
                    }
                    if (type === 'country') {
                        countryCode = place.address_components[i]['short_name'];
                    }
                    //                  type for venue
                    //                "point_of_interest"
                    //                "establishment"
                    if (place.types.includes("point_of_interest") || place.types.includes("establishment")) {
                        venue = place.name;
                    }
                }
                if (address !== '') {
                    if (number !== '') {
                        address += ' ' + number;
                    }
                }
            }
        }
        $('#city').val(city);
        $('#address').val(address);
        $('#postalCode').val(postalCode);
        $('#provinceCode').val(provinceCode).change();
        $('#countryCode').val(countryCode).change();
        
        mapPoint.first.center.lat = place.geometry.location.lat();
        mapPoint.first.center.lng = place.geometry.location.lng();
        if (typeof map !== "undefined") {
            map.setCenter(mapPoint.first.center);
            updateCircle();
        } else {
            initializeMap();
        }
    })();
}

function initializeMap() {
    // Create the map.
    map = new google.maps.Map(document.getElementById("map"), {
        zoom: 6,
        center: mapPoint.first.center,
        mapTypeId: "terrain"
    });

    printCircles();
}

function updateCircle() {
    mapPoint.first.radius = $("#rangeKm").val();
    mapPoint.first.radiusType = $("#rangeType").val();

    printCircles();
}

function printCircles() {
    if (typeof circle !== "undefined") {
        circle.setMap(null);
    }

    // Construct the circle for each value in mapPoint.
    // Note: We scale the area of the circle based on the population.
    for (const city in mapPoint) {
        var radius = mapPoint[city].radius * 1000;
        if (mapPoint[city].radiusType === "Mi") {
            radius = radius * 1.60934;
        }

        // Add the circle for this city to the map.
        circle = new google.maps.Circle({
            strokeColor: "#FF0000",
            strokeOpacity: 0.8,
            strokeWeight: 2,
            fillColor: "#FF0000",
            fillOpacity: 0.35,
            map: map,
            center: mapPoint[city].center,
            radius: radius
        });
    }
}