var autocomplete;
(function () {
    $(window).keydown(function (event) {
        if (event.keyCode == 13) {
            event.preventDefault();
            return false;
        }
    });

    // Inizializza Select2
    $('#pageIds').select2({
        minimumInputLength: 3,
        ajax: {
            url: $('#dataPagesUri').attr('href'),
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    name: params.term,
                    page: params.page || 1
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;

                var results = [];
                if (data) {
                    results = data.map(function (item) {
                        return {
                            id: item[0],
                            text: item[1],
                            avatar: item[2],
                            descr: item[4],
                            identifier: item[3]
                        };
                    });
                }

                return {
                    results: results,
                    pagination: {
                        more: data && data[0] && data[0][5] ? (params.page * 10) < data[0][5] : false
                    }
                };
            },
            cache: true
        },
        createTag: function (params) {
            return null; // Non creare duplicati
        },
        templateResult: formatRepo,
        templateSelection: formatRepoSelection,
        escapeMarkup: function (markup) {
            return markup;
        }
    });

    $('#pageIds').on('select2:select', function (e) {
        var selectedId = e.params.data.id;
        // Cerca l'elemento nell'opzione selezionata per recuperare i data attributes
        var $selectedOption = $('#pageIds option[value="' + selectedId + '"]');

        if ($selectedOption.length > 0) {
            var identifier = $selectedOption.data('identifier');
            if (identifier) {
                var url = $("#dataPageDetailUri").attr("href").replace(":identifier", identifier);
                window.open(url, '_blank');
            }
        }
    });

    function formatRepo(repo) {
        if (repo.loading) {
            return repo.text;
        }

        // Se è un'opzione "Crea"
        if (!(repo.isCreate || repo.isNew)) {
            var imgUrl = "https://agor.app/fe/images/avatar/placeholder.jpg";
            if (repo.avatar) {
                imgUrl = $('#imageSearchUri').attr('href') + repo.avatar;
            }

            var $container = $(
                    '<div class="select2-result-repository clearfix d-flex align-items-center">' +
                    '<div class="select2-result-repository__avatar"><img src="' + imgUrl + '" /></div>' +
                    '<div class="select2-result-repository__meta">' +
                    '<div class="select2-result-repository__title">' + repo.text + '</div>' +
                    "<div class='select2-result-repository__description' style='font-size: small; color: gray;'></div>" +
                    '</div>' +
                    '</div>'
                    );

            if (repo.descr) {
                var shortDesc = repo.descr.length > 50 ? repo.descr.substring(0, 50) + "..." : repo.descr;
                $container.find(".select2-result-repository__description").text(shortDesc);
            }
            return $container;
        }
    }

    function formatRepoSelection(event, repo) {
        // Aggiungi data attributes al container per riferimento futuro
        $('#pageIds').find("option[value='" + event.id + "']").attr('data-identifier', event.identifier);

        return null;
    }

    $("#name").on("input", function (event) {
        $(".page-name-container").find(".select2-search__field").val($("#name").val()).trigger("input");
        $("#name").focus();
    });

    // $(".page-name-container").find(".select2-container--default").addClass("d-none");
    $(".page-name-container").find(".select2-search__field").css("width", "100%");
    $(".page-name-container").find(".select2-container--default").css("z-index", "1");
    $(".page-name-container").find(".select2-container--default").css("margin-top", "-41px");

    $('textarea').keydown(function (event) {
        if (event.keyCode == 13 && !event.shiftKey) {
            event.preventDefault();
            var content = this.value;
            var caret = getCaret(this);
            this.value = content.substring(0, caret) + "\n" + content.substring(caret, content.length);
            this.scrollTop = this.scrollHeight;
        }
    });

    var summerLang = 'en-US';
    if ($('#language').val() === 'it') {
        summerLang = 'it-IT';
    }
    $('.summernote').summernote({
        lang: summerLang,
        callbacks: {
            onPaste: function (e) {
                var bufferText = ((e.originalEvent || e).clipboardData || window.clipboardData).getData('Text');
                e.preventDefault();
                document.execCommand('insertText', false, bufferText);
            }
        },
        toolbar: [
            ['style', ['italic']],
            ['insert', ['link']]
        ],
        disableLinkTarget: false // Permette di scegliere se aprire il link in nuova finestra
    });


    // Funzione per ottenere la posizione del cursore all'interno di un textarea
    function getCaret(el) {
        if (el.selectionStart) {
            return el.selectionStart;
        } else if (document.selection) {
            el.focus();

            var r = document.selection.createRange();
            if (r == null) {
                return 0;
            }

            var re = el.createTextRange(),
                    rc = re.duplicate();
            re.moveToBookmark(r.getBookmark());
            rc.setEndPoint('EndToStart', re);

            return rc.text.length;
        }
        return 0;
    }

    $('#tags').select2({
        minimumInputLength: 2,
        tags: true,
        ajax: {
            url: $('#dataPageTagUri').attr('href'),
            dataType: 'json',
            delay: 250,
            quietMillis: 250,
            data: function (term) {
                term.term = term.term.toLowerCase();
                return {name: term};
            },
            processResults: function (data) {
                var results;
                results = [];
                $.each(data, function (idx, item) {
                    results.push({
                        'id': item.toLowerCase(),
                        'text': item.toLowerCase()
                    });
                });
                return {results: results};
            }
        }
    }).on("select2:selecting", function (e) {
        // Converte il nuovo valore in lowercase prima di selezionarlo
        e.params.args.data.text = e.params.args.data.text.toLowerCase();
        e.params.args.data.id = e.params.args.data.id.toLowerCase();
    });

    // Aggiungi un gestore per l'evento change sull'elemento con id "countrycode"
    $('#countryCode').on('change', function () {
        checkCountryCode();
    });

//    bindChangePageType();
    initAutocomplete();
    $('#form-page-add').submit(function (event) {
        event.preventDefault();
        // Validation rules
        if ($("#form-page-add").valid()) {
            // post to url
            var url = $("#pageAddSaveUri").attr("href");
            var data = new FormData($(this)[0]);

            $.blockUI();
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                success:
                        function (returndata) {
                            $.unblockUI();
                            var url = $("#accountPagesUri").attr("href");
                            Swal.fire({
                                position: 'center',
                                icon: 'success',
                                title: label('common.page.add.success'),
                                showConfirmButton: false,
                                timer: 1500
                            }).then(function () {
                                window.location.href = url;
                            });
                        },
                error:
                        function (response, status, errorThrown) {
                            var msg = label('common.insertion.failed');
                            if (response) {
                                if (response.responseText) {
                                    msg = response.responseText;
                                }
                            }
                            $.unblockUI();
                            // warn
                            Swal.fire({
                                position: 'top',
                                icon: 'error',
                                title: label('common.ops.error'),
                                text: msg,
                                buttonsStyling: false,
                                customClass: {
                                    confirmButton: 'btn btn-primary btn-lg',
                                },
                                confirmButtonText: label('common.continue'),
                                // todo
                                footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.assistance') + '</a>'
                            });
                        }
            });
        }
        return false;
    });

})();

function checkCountryCode() {
    var countryCodeValue = $('#countryCode').val();
    if (countryCodeValue !== 'IT') {
        $('#provinceDiv').hide();
        $('#provinceExtDiv').show();
        $('.provinceCodeIt').prop('name', 'provinceCodeIt').prop('id', 'provinceCodeIt');
        $('.provinceCodeIt').removeAttr("required");
        $('.provinceCodeExt').prop('name', 'provinceCode').prop('id', 'provinceCode');
    } else {
        $('#provinceDiv').show();
        $('#provinceExtDiv').hide();
        $('.provinceCodeIt').prop('name', 'provinceCode').prop('id', 'provinceCode');
        $('.provinceCodeIt').attr("required", "required");
        $('.provinceCodeExt').prop('name', 'provinceCodeExt').prop('id', 'provinceCodeExt');
    }
    $('.provinceCode').val('');
}

function bindChangePageType() {
    $('.pageType').off();
    $('.pageType').change(function (event) {
        event.preventDefault();

        var url = new URI($("#pageAddUri").attr("href"));
        url.removeSearch("pageType");
        url.addSearch("pageType", $('#pageType').val());

        $("#categoryPage").load(url + ' ' + "#categoryPage" + ' > *', function () {
            var categoryElement = document.getElementById('category');
            if (categoryElement) {
                var removeItemBtn = categoryElement.getAttribute('data-remove-item-button') == 'true' ? true : false;
                var placeHolder = categoryElement.getAttribute('data-placeholder') == 'false' ? false : true;
                var placeHolderVal = categoryElement.getAttribute('data-placeholder-val') ? categoryElement.getAttribute('data-placeholder-val') : 'Type and hit enter';
                var maxItemCount = categoryElement.getAttribute('data-max-item-count') ? categoryElement.getAttribute('data-max-item-count') : 3;
                var searchEnabled = categoryElement.getAttribute('data-search-enabled') == 'true' ? true : false;
                var position = categoryElement.getAttribute('data-position') ? categoryElement.getAttribute('data-position') : 'auto';
                var choiceCatgegory = new Choices(categoryElement, {
                    removeItemButton: removeItemBtn,
                    placeholder: placeHolder,
                    placeholderValue: placeHolderVal,
                    maxItemCount: maxItemCount,
                    searchEnabled: searchEnabled,
                    position: position,
                    shouldSort: false
                });
            }
        });


    });
}
