// fbq replacement with server fb conversion api

var facebookUrl;
var eventIDCounter = 0;

function fbqxUrl(url) {
    console.log('FBQX: setting url to ' + url);
    facebookUrl = url;
}

function fbqx(action, event_name, custom_data, eventID) {
    console.log('FBQX: action', action, 'event', event_name, 'data', (custom_data), 'id', (eventID));

    // mandatory action
    if (!action) {
        console.error('FBQX: unavailable action');
        return false;
    }
    
    // mandatory event_name
    if (!event_name) {
        console.error('FBQX: unavailable event_name');
        return false;
    }
    
    // available actions
    if (!['init', 'track', 'trackCustom'].includes(action)) {
        console.error('FBQX: unhandled action');
        return false;
    }
    
    // no need to call init
    if (action === 'init') {
        console.info('FBQX: useless init action');
        return true;
    }
    
    // event_time (unix GMT time, in seconds)
    var now = new Date();
    var event_time = now.getTime() / 1000;
    event_time = Math.floor(event_time);
    event_time += now.getTimezoneOffset();
    event_time = event_time | 0;

    // url
    if (!facebookUrl) {
        console.error('FBQX: unable to find facebook url');
        return false;
    }
    
    var url = facebookUrl;
    url += '?event_name=' + encodeURIComponent(event_name);
    url += '&event_time=' + encodeURIComponent(event_time);

    if (custom_data) {
        url += '&custom_data=' + encodeURIComponent(JSON.stringify(custom_data));
    }

    if (eventID && eventID.eventID) {
        url += '&eventID=' + encodeURIComponent(eventID.eventID);
    }

    var xhr = new XMLHttpRequest();
    xhr.open("POST", url, true);
    xhr.setRequestHeader("Content-Type", "application/json");
    xhr.onreadystatechange = function () {
        if (xhr.readyState === 4 && xhr.status === 200) {
            console.info('FBQX: facebook called ' + event_name);
        }
        if (xhr.readyState === 4 && xhr.status !== 200) {
            console.error('FBQX: error calling facebook: ' + xhr.status + ' ' + xhr.statusText);
        }
    };
    var data = null;
    xhr.send(data);
    
    return true;
}

function generateEventID(event_name) {

    // mandatory event_name
    if (!event_name) {
        console.error('FBQX: unavailable event_name');
        return false;
    }

    eventIDCounter++;
    return event_name + '.' + Date.now() + '.' + eventIDCounter;
}
