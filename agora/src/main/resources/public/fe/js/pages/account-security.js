(function () {
    $('#account-confirm-send').click(function (event) {
        event.preventDefault();

        // post to url
        var url = $("#account-confirm-send").attr("href");

        // data to post
        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: null,
            cache: false,
            contentType: false,
            processData: false,
            success:
                    function (returndata) {
                        $.unblockUI();

                        Swal.fire({
                            position: 'center',
                            icon: 'success',
                            title: label('email.sent'),
                            text: label('email.check.inbox'),
                            showConfirmButton: false,
                            timer: 1500
                        }).then(function () {
                            location.reload();
                        }
                        );
                    },
            error:
                    function (response, status, errorThrown) {
                        $.unblockUI();

                        Swal.fire({
                            position: 'center',
                            icon: 'error',
                            title: label('common.ops.error'),
                            text: label('email.send.error'),
                            buttonsStyling: false,
                            customClass: {
                                confirmButton: 'btn btn-primary btn-lg',
                            },
                            confirmButtonText: label('common.continue') + ' <i class="bi-chevron-right small"></i>',
                            footer: '<span class="me-1">' + label('common.need.help') + '</span> <a class="hover-arrow" href="mailto:<EMAIL>">' + label('common.contact.support') + '</a>'
                        });

                    }
        });

        return false;

    });

    $('#form-account-edit').submit(function (event) {
        event.preventDefault();
        if ($("#form-account-edit").valid()) {

            // post to url
            var url = $("#accountSecurityUsernameSaveUri").attr("href");
            // data to post
            var data = new FormData($(this)[0]);
            $.blockUI();
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                success:
                        function (returndata) {
                            $.unblockUI();
                            Swal.fire({
                                position: 'center',
                                icon: 'success',
                                title: label('common.data.saved'),
                                text: label('common.information.saved.success'),
                                showConfirmButton: false,
                                timer: 1500
                            });
                        },
                error:
                        function (response, status, errorThrown) {
                            var msg = label('common.data.save.error');
                            if (response) {
                                if (response.responseText) {
                                    msg = response.responseText;
                                }
                            }
                            $.unblockUI();
                            // warn
                            Swal.fire({
                                position: 'center',
                                icon: 'error',
                                title: label('common.ops.error'),
                                text: msg,
                                buttonsStyling: false,
                                customClass: {
                                    confirmButton: 'btn btn-primary btn-lg',
                                },
                                confirmButtonText: 'Continua <i class="bi-chevron-right small"></i>',
                                footer: '<span class="me-1">Hai bisogno di aiuto?</span> <a class="hover-arrow" href="mailto:<EMAIL>">Contatta l\'assistenza</a>'
                            });
                        }
            });
        }
    });        
    
    // Initialize validation
    $('#form-account-change-password-edit').submit(function (event) {
        event.preventDefault();
        if ($("#form-account-change-password-edit").valid()) {

            // post to url
            var url = $("#accountSecurityChangePasswordSaveUri").attr("href");
            // data to post
            var data = new FormData($(this)[0]);
            $.blockUI();
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                success:
                        function (returndata) {
                            $.unblockUI();
                            Swal.fire({
                                position: 'center',
                                icon: 'success',
                                title: 'Password salvata',
                                text: label('common.information.saved.success'),
                                showConfirmButton: false,
                                timer: 1500
                            });
                        },
                error:
                        function (response, status, errorThrown) {
                            var msg = label('common.data.save.error');
                            if (response) {
                                if (response.responseText) {
                                    msg = response.responseText;
                                }
                            }
                            $.unblockUI();
                            // warn
                            Swal.fire({
                                position: 'center',
                                icon: 'error',
                                title: label('common.ops.error'),
                                text: msg,
                                buttonsStyling: false,
                                customClass: {
                                    confirmButton: 'btn btn-primary btn-lg',
                                },
                                confirmButtonText: 'Continua <i class="bi-chevron-right small"></i>',
                                footer: '<span class="me-1">Hai bisogno di aiuto?</span> <a class="hover-arrow" href="mailto:<EMAIL>">Contatta l\'assistenza</a>'
                            });
                        }
            });
        }
    });

    $('#form-account-keyword-edit').submit(function (event) {
        event.preventDefault();
        if ($("#form-account-keyword-edit").valid()) {

            // post to url
            var url = $("#accountSecurityKeywordSaveUri").attr("href");
            // data to post
            var data = new FormData($(this)[0]);
            $.blockUI();
            $.ajax({
                url: url,
                type: 'POST',
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                success:
                        function (returndata) {
                            $.unblockUI();
                            Swal.fire({
                                position: 'center',
                                icon: 'success',
                                title: 'Parola chiave salvata',
                                text: label('common.information.saved.success'),
                                showConfirmButton: false,
                                timer: 1500
                            }).then(function () {
                                location.reload();
                            }
                            );
                        },
                error:
                        function (response, status, errorThrown) {
                            var msg = label('common.data.save.error');
                            if (response) {
                                if (response.responseText) {
                                    msg = response.responseText;
                                }
                            }
                            $.unblockUI();
                            // warn
                            Swal.fire({
                                position: 'center',
                                icon: 'error',
                                title: label('common.ops.error'),
                                text: msg,
                                buttonsStyling: false,
                                customClass: {
                                    confirmButton: 'btn btn-primary btn-lg',
                                },
                                confirmButtonText: 'Continua <i class="bi-chevron-right small"></i>',
                                footer: '<span class="me-1">Hai bisogno di aiuto?</span> <a class="hover-arrow" href="mailto:<EMAIL>">Contatta l\'assistenza</a>'
                            });
                        }
            });
        }
    });
})();
