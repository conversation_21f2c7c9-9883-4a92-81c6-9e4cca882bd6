(function () {
        $('#account-subscription-cancel').click(function(event) {
        event.preventDefault();
        
        // post to url
        var url = $("#account-subscription-cancel").attr("href");
                
        Swal.fire({
            position: 'center',
            icon: 'warning',
            title: 'Annulla abbonamento',                            
            html: 'Terminare la sottoscrizione al servizio comporta:<br><br><ul class="list-pointer list-pointer-primary text-start"><li class="list-pointer-item">Cancellazione dei pagamenti ricorrenti (dal prossimo rinnovo)</li><li class="list-pointer-item">Cancellazione degli annunci in esubero (dal prossimo rinnovo)</li><li class="list-pointer-item">Chiusura della pagina aziendale pubblica (il profilo rimane accessibile ma senza la possibiltà di pubblicare annunci)</li></ul>Vuoi davvero perdere i vantaggi del tuo piano attuale?',
            customClass: {
                confirmButton: 'btn btn-primary mb-2 w-100',
                cancelButton: 'btn btn-white me-2 mb-2 w-100'
            },
            buttonsStyling: false,
            showCancelButton: true,
            showConfirmButton: true,
            confirmButtonText: 'Sì, annulla sottoscrizione',
            cancelButtonText: 'No, torna indietro'          
        }).then((result) => {
            if (result.isConfirmed) {
        
                // post to url
                var url = $("#accountSubscriptionRemoveUri").attr("href");
                if (!url) {
                    console.error('missing url');
                    return;
                }
                
                // data to post
                $.blockUI();
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: null,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success:
                            function (returndata) {
                                $.unblockUI();
                                Swal.fire({
                                    position: 'center',
                                    icon: 'success',
                                    title: 'Abbonamento annullato',
                                    text: 'Segui ora le istruzioni nel messaggio ricevuto',
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(function(){ 
                                        location.reload();
                                    }
                                );
                            },
                    error:
                            function (response, status, errorThrown) {
                                var msg = 'Non siamo riusciti ad annullare l\'abbonamento.';
                                if (response) {
                                    if (response.responseText) {
                                        msg = response.responseText.substring(0, 200);
                                    }
                                }
                                $.unblockUI();
                                // warn
                                Swal.fire({
                                    position: 'center',
                                    icon: 'error',
                                    title: label('common.ops.error'),
                                    text: msg,
                                    buttonsStyling: false,
                                    customClass: {
                                        confirmButton: 'btn btn-primary btn-lg',
                                    },
                                    confirmButtonText: 'Continua <i class="bi-chevron-right small"></i>',
                                    footer: '<span class="me-1">Hai bisogno di aiuto?</span> <a class="hover-arrow" href="mailto:<EMAIL>">Contatta l\'assistenza</a>'
                                });
                            }
                });
                
            } else {
                //...
            }
        });

    });        
})();
