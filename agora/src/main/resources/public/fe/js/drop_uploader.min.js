!function(e){jQuery.fn.drop_uploader=function(t){t=e.extend({uploader_text:"Drop files to upload, or",browse_text:"Browse",only_one_error_text:"Only one file allowed",not_allowed_error_text:"File type is not allowed",big_file_before_error_text:"Files, bigger than",big_file_after_error_text:"is not allowed",big_files_before_error_text:"Files, bigger than",big_files_after_error_text:"is not allowed",allowed_before_error_text:"Only",allowed_after_error_text:"files allowed",browse_css_class:"button button-primary",browse_css_selector:"file_browse",uploader_icon:'<i class="pe-7s-cloud-upload"></i>',file_icon:'<i class="pe-7s-file"></i>',time_show_errors:5,layout:"thumbnails",method:"normal",url:"ajax_upload.php",delete_url:"ajax_delete.php"},t),this.each(function(a,r){var i=r,l=e(i).attr("accept"),n=e(i).prop("multiple"),o=parseInt(e(i).data("count")),s=e(i).prop("name"),d=0,f=0,_="drop_uploader_"+a,u=0,p=0,c=e(i).parent("form"),v=e(c).find("input[name=MAX_FILE_SIZE]").val();void 0!==v&&(d=parseInt(v));var h=e(i).data("maxfilesize");void 0!==h&&(d=parseInt(h));var m=e(i).data("maxfilessize");void 0!==m&&(f=parseInt(m));var g=t.layout;"thumbnails"==e(i).data("layout")?g="thumbnails":"list"==e(i).data("layout")&&(g="list");var b=t.method;"normal"==e(i).data("method")?b="normal":"ajax"==e(i).data("method")&&(b="ajax");var x=t.url;""!=e(i).data("url")&&void 0!==e(i).data("url")&&(x=e(i).data("url"));var w=t.delete_url;""!=e(i).data("deleteurl")&&void 0!==e(i).data("deleteurl")&&(w=e(i).data("deleteurl")),e(i).attr("id",T()),e(i).wrap('<div id="'+_+'" class="drop_uploader drop_zone"></div>'),e(i).before('<div class="text_wrapper">'+t.uploader_icon+' <span class="text">'+t.uploader_text+' <a href="#" class="'+t.browse_css_class+" "+t.browse_css_selector+'">'+t.browse_text+"</a></span></div>"),e(i).before('<span class="errors"></span>'),"ajax"==b&&e(i).attr("name","");var y="files";"thumbnails"==g&&(y+=" thumb"),"ajax"==b&&(y+=" ajax"),e(i).before('<ul class="'+y+'"></ul>');var M=e("#"+_);function j(a){if(void 0===a)a=e(i)[0].files;"normal"==b&&e("#"+_+" .files").html("");for(var r=0;r<a.length;r++)"thumbnails"==g?(e("#"+_+" .files.thumb").append('<li id="selected_file_'+p+'"><div class="thumbnail"></div><span class="title" title="'+a[r].name+'"> '+a[r].name+" </span></li>"),D(a[r],p)):e("#"+_+" .files").append('<li id="selected_file_'+p+'">'+t.file_icon+" <span>"+a[r].name+"</span> </li>"),"ajax"==b&&I(a[r],p),p++,"ajax"==b&&u++}function D(t,a){var r,i,l,n=new FileReader;r=t,i=function(t){var r="";8==t?r="rotate_90":3==t?r="rotate_180":6==t&&(r="rotate_270"),e("#"+_+" #selected_file_"+a+" div.thumbnail").addClass(r)},(l=new FileReader).onload=function(e){var t=new DataView(e.target.result);if(65496!=t.getUint16(0,!1))return i(-2);for(var a=t.byteLength,r=2;r<a;){var l=t.getUint16(r,!1);if(r+=2,65505==l){if(1165519206!=t.getUint32(r+=2,!1))return i(-1);var n=18761==t.getUint16(r+=6,!1);r+=t.getUint32(r+4,n);var o=t.getUint16(r,n);r+=2;for(var s=0;s<o;s++)if(274==t.getUint16(r+12*s,n))return i(t.getUint16(r+12*s+8,n))}else{if(65280!=(65280&l))break;r+=t.getUint16(r,!1)}}return i(-1)},l.readAsArrayBuffer(r),t.type.match("image/*")?n.readAsDataURL(t):t.type.match("video/*")?e("#"+_+" #selected_file_"+a+" div.thumbnail").html('<i class="pe-7s-video"></i>'):t.type.match("audio/*")?e("#"+_+" #selected_file_"+a+" div.thumbnail").html('<i class="pe-7s-volume"></i>'):e("#"+_+" #selected_file_"+a+" div.thumbnail").html('<i class="pe-7s-file"></i>'),n.onloadend=function(){e("#"+_+" #selected_file_"+a+" div.thumbnail").attr("style",'background-image: url("'+n.result+'")'),e("#"+_+" #selected_file_"+a+" div.thumbnail").append('<div class="du_hover_layer"></div>')}}function I(a,r){e("#"+_).trigger("file_upload_start",[a.name]);var i=new XMLHttpRequest;"thumbnails"==g?e("#"+_+" #selected_file_"+r+" div.thumbnail").after('<div class="du_progress"></div>'):e("#"+_+" #selected_file_"+r).append('<div class="du_progress"></div>');var l=e("#"+_+" #selected_file_"+r+" .du_progress");(i.upload||i).addEventListener("progress",function(e){var t=e.position||e.loaded,a=e.totalSize||e.total,r=Math.round(t/a*100);!function(e,t,a){var r=e.children[0];void 0===r&&(r=document.createElement("canvas"));if("thumbnails"==a){r.width=100,r.height=100,r.style.width="50px",r.style.height="50px";var i=96,l=8}else{r.width=48,r.height=48,r.style.width="24px",r.style.height="24px";var i=48,l=4}e.appendChild(r),"undefined"!=typeof G_vmlCanvasManager&&G_vmlCanvasManager.initElement(r);var n=r.getContext("2d");n.translate(i/2,i/2),n.rotate(-.5*Math.PI);var o=(i-l)/2;t=Math.min(Math.max(0,t||1),1),n.beginPath(),n.arc(0,0,o,0,2*Math.PI*t,!1),n.strokeStyle="rgba(74, 144, 226, .8)",n.lineCap="round",n.lineWidth=l,n.stroke()}(l[0],r/100,g)}),i.addEventListener("load",function(i){var l=JSON.parse(this.response);if(e("#"+_+" #selected_file_"+r+" .du_progress").fadeOut("slow"),l.success){e("#"+_).trigger("file_upload_end",[a.name]);var n=e('<i class="pe-7s-trash action-delete" data-fileid="'+l.file_id+'"></i>').hide();"thumbnails"==g?e("#"+_+" #selected_file_"+r+" div.thumbnail").append(n):"list"==g&&e("#"+_+" #selected_file_"+r).append(n),n.delay(500).fadeIn("slow"),e("#"+_).append('<input id="hidden_file_'+r+'" type="hidden" name="'+s+'" value="'+l.file_id+'" >'),e("#"+_+" #selected_file_"+r+" i.action-delete").on("click",function(t){var a=e(this).data("fileid");e.ajax({url:w,data:"fileid="+a}).done(function(){e("#"+_+" #selected_file_"+r).delay(500).fadeOut("slow"),e("#"+_+" #hidden_file_"+r).remove(),u--})})}else L(l.message),e("#"+_+" #selected_file_"+r).delay(1e3*t.time_show_errors).fadeOut("slow")}),i.open("post",x,!0);var n=new FormData;n.append(s.replace("[]",""),a),i.send(n)}function L(a){e("#"+_+" .errors").html("<p>"+a+"</p>"),t.time_show_errors>0&&setTimeout(F,1e3*t.time_show_errors)}function F(){e("#"+_+" .errors p").fadeOut("slow",function(){e("#"+_+" .errors p").remove()})}function U(e){return e>=1e9?e=(e/1e9).toFixed(2)+" GB":e>=1e6?e=(e/1e6).toFixed(2)+" MB":e>=1e3?e=(e/1e3).toFixed(2)+" KB":e>1?e+=" bytes":1==e?e+=" byte":e="0 byte",e}function k(e){var a=!0;if(n)if(o){if(e.length+u>o)return L(t.allowed_before_error_text+" "+o+" "+t.allowed_after_error_text),"normal"==b&&(u=0),!1;a=!0}else a=!0;else{if(e.length>1||u>0)return L(t.only_one_error_text),!1;a=!0}if(void 0===l)a=!0;else for(var r=l.split(","),i=0;i<e.length;i++){for(var s=0,_=0;_<r.length;_++){var p=r[_].replace("/",".").trim();null!=e[i].type.match(p)&&s++}if(0==s)return L(t.not_allowed_error_text),!1}var c=0;for(i=0;i<e.length;i++){if(c+=e[i].size,e[i].size>d&&d>0)return L(t.big_file_before_error_text+" "+U(d)+" "+t.big_file_after_error_text),!1;if(c>f&&f>0)return L(t.big_files_before_error_text+" "+U(f)+" "+t.big_files_after_error_text),!1}return a}function C(t){e(t).css({display:"none"})}function T(){for(var e="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",a=0;a<15;a++)e+=t.charAt(Math.floor(Math.random()*t.length));return e}M[0].ondragover=function(t){return M.addClass("hover"),"normal"==b?(r=e(a=i).parent(".drop_zone"),l=r.position(),n=l.top+parseInt(r.css("marginTop"),10),o=l.left+parseInt(r.css("marginLeft"),10),e(a).css({top:n,left:o,position:"absolute",width:r.width(),height:r.height(),display:"block"}),!1):"ajax"==b?(C(i),!1):void 0;var a,r,l,n,o},M[0].ondragleave=function(e){return M.removeClass("hover"),!1},M[0].ondrop=function(t){if(C(i),F(),"normal"==b){if(0==k(n=t.dataTransfer.files)){e("#"+_+" .files").html("");var a=T(),r=e(i)[0].outerHTML,l=e.parseHTML(r);e(l).attr("id",a),e(i).before(l),e(i).remove(),i=e("#"+a)[0],e(i).change(function(){j()}),t.preventDefault?t.preventDefault():t.returnValue=!1}}else if("ajax"==b){var n;t.preventDefault?t.preventDefault():t.returnValue=!1,k(n=t.dataTransfer.files)&&j(n)}},e(M).find("."+t.browse_css_selector).click(function(t){t.preventDefault?t.preventDefault():t.returnValue=!1,e(i).click()}),e(i).change(function(){var t=e(i)[0].files;u=0;var a=k(t);if("normal"==b)if(0==a){e("#"+_+" .files").html("");var r=T(),l=e(i)[0].outerHTML,n=e.parseHTML(l);e(n).attr("id",r),e(i).before(n),e(i).remove(),i=e("#"+r)[0],e(i).change(function(){j()}),event.preventDefault?event.preventDefault():event.returnValue=!1}else j(t);else"ajax"==b&&a&&j(t)})})}}(jQuery);