.daterangepicker {
    position: absolute;
    z-index: 1000;
    display: none;
    min-width: 15rem;
    width: calc(100% - 18px);    
    margin: 0;
    font-size: 1rem;
    color: #677788;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 0 solid rgba(0,0,0,.15);    
    box-shadow: 0 0.75rem 1rem rgb(189 197 209 / 30%);
    margin-top: 0.3125rem;
    /*margin-right: 9px;*/
}

.daterangepicker.opensleft:before {
    right: 9px;
}

.daterangepicker.opensleft:after {
    right: 10px;
}

.daterangepicker.openscenter:before {
    left: 0;
    right: 0;
    width: 0;
    margin-left: auto;
    margin-right: auto;
}

.daterangepicker.openscenter:after {
    left: 0;
    right: 0;
    width: 0;
    margin-left: auto;
    margin-right: auto;
}

.daterangepicker.opensright:before {
    left: 9px;
}

.daterangepicker.opensright:after {
    left: 10px;
}

.daterangepicker.drop-up {
    margin-top: -7px;
}

.daterangepicker.drop-up:before {
    top: initial;
    bottom: -7px;
    border-bottom: initial;
    border-top: 7px solid #ccc;
}

.daterangepicker.drop-up:after {
    top: initial;
    bottom: -6px;
    border-bottom: initial;
    border-top: 6px solid #fff;
}

.daterangepicker.single .daterangepicker .ranges, .daterangepicker.single .drp-calendar {
    float: none;
}

.daterangepicker.single .drp-selected {
    display: none;
}

.daterangepicker.show-calendar .drp-calendar {
    display: block;
}

.daterangepicker.show-calendar .drp-buttons {
    display: block;
}

.daterangepicker.auto-apply .drp-buttons {
    display: none;
}

.daterangepicker .drp-calendar {
    display: none;    
}

.daterangepicker .drp-calendar.left {
    padding: 8px;
}

.daterangepicker .drp-calendar.right {
    padding: 8px;
}

.daterangepicker .drp-calendar.single .calendar-table {
    border: none;
}

.daterangepicker .calendar-table .next span, .daterangepicker .calendar-table .prev span {
    color: #fff;
    border: solid #777788;
    border-width: 0 2px 2px 0;    
    display: inline-block;
    padding: 3px;
}

.daterangepicker .calendar-table .next:hover span, .daterangepicker .calendar-table .prev:hover span {
    border: solid #8B734D;
    border-width: 0 2px 2px 0;
}
.daterangepicker .calendar-table .next span {
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
}

.daterangepicker .calendar-table .prev span {
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
}

.daterangepicker .calendar-table th, .daterangepicker .calendar-table td {
    white-space: nowrap;
    text-align: center;
    vertical-align: middle;
    min-width: 36px;
    width: 36px;
    height: 32px;
    line-height: 32px;
    font-size: .875rem;    
    border: 1px solid transparent;
    white-space: nowrap;
    cursor: pointer;
}

.daterangepicker .calendar-table th {
    font-size: 1rem;
}

.daterangepicker .calendar-table {
    border: 1px solid #fff;    
    background-color: #fff;
}

.daterangepicker .calendar-table table {
    width: 100%;
    margin: 0;
    border-spacing: 0;
    border-collapse: collapse;
}

.daterangepicker td.available:hover, .daterangepicker th.available:hover {
    color: #8B734D;
    background-color: rgba(139,115,77,.1);    
    border-color: transparent;    
}

.daterangepicker td.week, .daterangepicker th.week {
    font-size: 80%;
    color: #ccc;
}

.daterangepicker td.off, .daterangepicker td.off.in-range, .daterangepicker td.off.start-date, .daterangepicker td.off.end-date {
    background-color: #fff;
    border-color: transparent;
    color: #999;
}

.daterangepicker td.in-range {
    background-color: rgba(231, 234, 243, 0.5);    
    border-color: transparent;
    color: #1e2022;
    border-radius: 0;
}

.daterangepicker td.start-date {    
}

.daterangepicker td.end-date {
    
}

.daterangepicker td.start-date.end-date {
    
}

.daterangepicker td.active, .daterangepicker td.active:hover {
    background-color: #8B734D;
    border-color: transparent;
    color: #fff;
}

.daterangepicker th.month {
    width: auto;
}

.daterangepicker .calendar-table th:not(.month) {
    color: rgb(151, 164, 175);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 12px;
}
.daterangepicker .calendar-table th.month {
    font-size: 0.875rem;
    font-weight: 600;
}
.daterangepicker td.disabled, .daterangepicker option.disabled {
    color: #999;
    cursor: not-allowed;
    text-decoration: line-through;
}

.daterangepicker select.monthselect, .daterangepicker select.yearselect {
    font-size: 1rem;
    padding: 1px;
    height: auto;
    margin: 0;
    cursor: default;
}

.daterangepicker select.monthselect {
    margin-right: 2%;
    width: 56%;
}

.daterangepicker select.yearselect {
    width: 40%;
}

.daterangepicker select.hourselect, .daterangepicker select.minuteselect, .daterangepicker select.secondselect, .daterangepicker select.ampmselect {
    width: 50px;
    margin: 0 auto;
    background: #eee;
    border: 1px solid #eee;
    padding: 2px;
    outline: 0;
    font-size: 1rem;
}

.daterangepicker .calendar-time {
    text-align: center;
    margin: 4px auto 0 auto;
    line-height: 30px;
    position: relative;
}

.daterangepicker .calendar-time select.disabled {
    color: #ccc;
    cursor: not-allowed;
}

.daterangepicker .drp-buttons {
    clear: both;
    text-align: right;
    padding: 8px;
    border-top: .0625rem solid rgba(33,50,91,.1)!important;
    display: none;
    line-height: 12px;
    vertical-align: middle;
}

.daterangepicker .drp-selected {
    display: none;
    font-size: 1rem;
    padding-right: 8px;
}

.daterangepicker .drp-buttons .btn {
    margin-left: 8px;  
}

.daterangepicker.show-ranges.single.rtl .drp-calendar.left {
    border-right: .0625rem solid rgba(33,50,91,.1)!important;
}

.daterangepicker.show-ranges.single.ltr .drp-calendar.left {
    border-left: .0625rem solid rgba(33,50,91,.1)!important;
}

.daterangepicker.show-ranges.rtl .drp-calendar.right {
    border-right: .0625rem solid rgba(33,50,91,.1)!important;
}

.daterangepicker.show-ranges.ltr .drp-calendar.left {
    border-left: .0625rem solid rgba(33,50,91,.1)!important;
}

.daterangepicker .ranges {
    padding: 1rem;
    float: none;
    text-align: left;
    margin: 0;
}

.daterangepicker .ranges ul {
    list-style: none;
    margin: 0 auto;
    padding: 0;
    width: 100%;
}

.daterangepicker .ranges li {
    font-size: 1rem;
    padding: 8px 12px;
    margin-bottom: 4px;
    cursor: pointer;    
}

.daterangepicker .ranges li:hover {
    background-color: rgba(189,197,209,.3);
}

.daterangepicker .ranges li.active {
    color: #1e2022;
    background-color: rgba(189,197,209,.3);
}

/*  Larger Screen Styling */
@media (min-width: 575px) {
    .daterangepicker {
        width: auto;
        margin-right: 0;
    }

    .daterangepicker .ranges ul {
        width: 100%;
    }

    .daterangepicker.single .ranges ul {
        width: 100%;
    }

    .daterangepicker.single .drp-calendar.left {
        clear: none;
    }

    .daterangepicker.single .ranges, .daterangepicker.single .drp-calendar {
        float: left;
        padding: 0;
    }
    
    .daterangepicker .drp-selected {
        display: inline-block;        
    }

    .daterangepicker {
        direction: ltr;
        text-align: left;
    }

    .daterangepicker .drp-calendar.left {
        clear: left;
        margin-right: 0;
        padding: 8px 0 8px 8px;
    }
    
    .daterangepicker .drp-calendar.right {
        padding: 8px 8px 8px 0;
    }
    
    .daterangepicker .drp-calendar.left .calendar-table {
        border-right: none;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    .daterangepicker .drp-calendar.right {
        margin-left: 0;
    }

    .daterangepicker .drp-calendar.right .calendar-table {
        border-left: none;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    .daterangepicker .drp-calendar.left .calendar-table {
        padding-right: 8px;
    }

    .daterangepicker.show-calendar .ranges, .daterangepicker .drp-calendar {
        float: left;
    }
}

@media (min-width: 768px) {
    .daterangepicker .ranges {
        width: auto;
    }

    .daterangepicker.show-calendar .ranges {
        float: left;
    }

    .daterangepicker.rtl .ranges {
        float: right;
    }

    .daterangepicker .drp-calendar.left {
        clear: none !important;
    }
}
