@font-face {
    font-family: 'Circular Book';
    src: url('CircularStd-Book.woff2') format('woff2'),
        url('CircularStd-Book.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Circular Black';
    src: url('CircularStd-Black.woff2') format('woff2'),
        url('CircularStd-Black.woff') format('woff');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Circular Black Light';
    src: url('CircularSpotifyText-Light.woff2') format('woff2'),
        url('CircularSpotifyText-Light.woff') format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Circular Black Bold';
    src: url('CircularStd-Bold.woff2') format('woff2'),
        url('CircularStd-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Circular Black Medium';
    src: url('CircularStd-Medium.woff2') format('woff2'),
        url('CircularStd-Medium.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

