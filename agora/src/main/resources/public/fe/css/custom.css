@media (min-width: 768px) {
    .w-md-auto {
      width: auto !important;
    }
    .img-vertical img {
        max-width: 250px;
    }
  }
.hidden {
    visibility: hidden;
}
.home-search .select2-selection.select2-selection--multiple {
    padding: 6px;
}
#daterange-predefined {        
    padding: 0.5rem 1rem;
    line-height: 1;
}
.search-header {
    max-width: 376px;
}
.alert-agora {    
    color: #8b734d;
    background-color: rgba(222,153,87,.1);
    border: 1px solid #8b734d;
}
.border-agora {
    border-color: #8b734d!important;
}
.bg-agora {
    background-color: #8b734d!important;
}
.select2-dropdown {
    border-radius: 0;
}
.select2-pages .select2-container--open .select2-dropdown--below {
    margin-top: -28px;
}
.select2-result-repository.clearfix a {
    display: flex;
    align-items: center;
}
.select2-result-repository__avatar {
    width: 50px;
    height: 50px;
    background-size: cover;
    margin-right: 10px;
    line-height: 50px;
    text-align: center;
}
.select2-result-repository__avatar img {
    object-fit: cover;
    width: 50px;
    height: 50px;
    max-width: 50px;
}
.wallEvent .card .img-event {
    height: 200px;
    object-fit: cover;
}
header {
    border-bottom: 1px solid #8b734d;
}
.border-share {
    border-right: 1px solid #8b734d;    
}
.border-card {
    border: 1px solid #8b734d;            
}
.text-agora {
     color: #8b734d;
}
.select2-container {    
    width: 100%!important;
}
.select2-container--default .select2-selection--multiple,
.select2-container--default.select2-container--focus .select2-selection--multiple{
    border-top: 1px solid #8b734d;
    border-left: 1px solid #8b734d;
    border-right: 1px solid #8b734d;
    border-bottom: 1px solid #8b734d;
    border-radius: 0;
}
.select2-container .select2-search--inline .select2-search__field {
    margin-top: 8.5px;
}
@media (max-width: 991.98px) {
.navbar-expand-lg .navbar-collapse {        
        border-top: 0;
    }
    .navbar-brand .navbar-brand-item {
        height: 30px;
    }
}
@media (min-width: 992px) {    
    .select2-container--default .select2-selection--multiple  {
        border: 1px solid #8b734d;
    }
    
    .border-lg {
        border: 1px solid #8b734d;            
    }
    .border-lg-b {
        border-bottom: 1px solid #8b734d;            
    }
    .border-lg-t {
        border-top: 1px solid #8b734d;            
    }
    .border-lg-r {
        border-right: 1px solid #8b734d;            
    }
    .border-lg-l {
        border-left: 1px solid #8b734d;            
    }
    .border-lg-lr {
        border-left: 1px solid #8b734d;
        border-right: 1px solid #8b734d;            
    }
   
}
header.fixed-top+main {
    padding-top: 55px;    
}
h6, .h6, h5, .h5, h4, .h4, h3, .h3, h2, .h2, h1, .h1 {    
    letter-spacing: 0.05rem;
}
.nav-even .nav-item {
    flex: 1 1 0;
}
.drop_uploader.drop_zone .text_wrapper {
    margin: 15px 0 40px;
}
label.error, .custom-error {
    font-size: 0.875em;
    color: #F44336;
}
.rounded-1-end {
    border-top-right-radius: 0.3125rem!important;
    border-bottom-right-radius: 0.3125rem!important;   
}

.fancybox-loading {
    -webkit-animation: a 1s linear infinite;
    animation: a 1s linear infinite;
    background: transparent;
    border: 4px solid #888;
    border-bottom-color: #fff;
    border-radius: 50%;
    height: 50px;
    left: 50%;
    margin: -25px 0 0 -25px;
    opacity: .7;
    padding: 0;
    position: absolute;
    top: 50%;
    width: 50px;
    z-index: 99999; }

@-webkit-keyframes a {
    to {
        transform: rotate(1turn); } }

@keyframes a {
    to {
        transform: rotate(1turn); } }

.choices__list.choices__list--dropdown {
    z-index: 999
}
@media (min-width: 992px) {
    .sidebar-sticky {
        position: sticky;
        top: 60px;
        height: calc(100vh - 60px);
        overflow-y: auto;
    }
}

.note-modal-footer {
    height: 60px;
    padding: 10px;
    text-align: center;
}
.tiny-slider-page-home .tns-item {
    height: 350px;
}
.tiny-slider-page-home .card {       
    height: 100%;
}
.select2-container--default .select2-selection--single {
    display: block;
    width: 100%;
    padding: 0.5rem 1rem;
    font-size: 0.9375rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--bs-gray-700);
    background-color: #fff;
    border: 1px solid #8b734d;
    border-radius: 0;
    position: relative;
    height: auto;
}

/* Testo selezionato */
.select2-container--default .select2-selection--single .select2-selection__rendered {
    padding: 0;
    color: var(--bs-gray-700);
    line-height: 1.5;
}

/* Freccia allineata */
.select2-container--default .select2-selection--single .select2-selection__arrow {
    top: 50%;
    transform: translateY(-50%);
    right: 1rem;
    position: absolute;
    pointer-events: none;
}

/* Focus */
.select2-container--default .select2-selection--single {
    border-color: #8b734d;
    box-shadow: none;
    min-height: 40.5px;
}
