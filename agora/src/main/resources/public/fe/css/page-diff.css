.diff-container {
    border: 1px solid #e0e0e0;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 0;
}

.table tr td {
    padding: 12px 15px;
    vertical-align: top;
    border-bottom: 1px solid #f0f0f0;
}

.table tr td.text-center.h4 {
    background-color: #f9f9f9;
    font-weight: 500;
    font-size: 16px;
    text-align: left !important;
    padding: 12px 15px;
    border-bottom: 1px solid #e0e0e0;
    color: #555;
}

tr[field] {
    transition: background-color 0.2s;
}

tr[field]:hover {
    background-color: #f5f5f5;
}

tr[field].different {
    background-color: #fff8e1;
}

tr[field].different:hover {
    background-color: #fff5c8;
}

tr[field].same {
    background-color: #f0f8f0;
}

.form-check-input.fake-check[type="radio"] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    margin: 0;
    cursor: pointer;
    position: relative;
    transition: all 0.2s;
}

.form-check-input.fake-check[type="radio"]:checked {
    border-color: var(--bs-primary);
    background-color: var(--bs-primary);
}

.form-check-input.fake-check[type="radio"]:checked::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.form-check-input.fake-check[type="radio"]:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(139, 115, 77, 0.25);    
}

.text-agora {
    color: var(--bs-primary);
    text-decoration: none;
    font-weight: 500;
    padding: 10px 20px;
    border: 2px solid var(--bs-primary);
    border-radius: 4px;
    transition: all 0.3s;
}

.text-agora:hover {
    background-color: var(--bs-primary);
    color: white;
    text-decoration: none;
}

.empty-value {
    font-style: italic;
    color: #999;
}

@media (max-width: 768px) {
    .table tr td {
        padding: 10px;
    }

    .form-check-input.fake-check[type="radio"] {
        width: 18px;
        height: 18px;
    }

    .form-check-input.fake-check[type="radio"]:checked::after {
        left: 5px;
        top: 2px;
        width: 4px;
        height: 9px;
    }
}

.diff-container tr[field] td.diff-highlight {
    background-color: #fffbe6 !important;
    border-left: 4px solid #ffc107;
}