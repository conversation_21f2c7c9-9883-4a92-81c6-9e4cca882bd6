<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.4.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 277.6 293.1" style="enable-background:new 0 0 277.6 293.1;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FFCE00;}
	.st1{fill:#F9C9AF;}
	.st2{fill:#FFB600;}
	.st3{fill:#F7B208;}
	.st4{fill:#F4BBA2;}
	.st5{fill:#FFC200;}
	.st6{fill:#F9BA06;}
	.st7{fill:#F4B49D;}
	.st8{fill:#FFE1C5;}
	.st9{filter:url(#Adobe_OpacityMaskFilter);}
	.st10{fill:url(#SVGID_00000152967591809943822630000017774763337226608260_);}
	.st11{mask:url(#SVGID_1_);fill:#FFE9D9;}
	.st12{filter:url(#Adobe_OpacityMaskFilter_00000155145684616353993380000006682403710848681118_);}
	.st13{fill:url(#SVGID_00000103222663642558222820000014870928180631043251_);}
	.st14{mask:url(#SVGID_00000180362678864781845410000015309931826679523973_);fill:#FFE9D9;}
	.st15{filter:url(#Adobe_OpacityMaskFilter_00000083769914188973792700000000300816610631944873_);}
	.st16{fill:url(#SVGID_00000080182084671918770560000004250175969851252887_);}
	.st17{mask:url(#SVGID_00000170256997134455780340000017272715951852615065_);fill:#FFE9D9;}
	.st18{filter:url(#Adobe_OpacityMaskFilter_00000072982035691366308480000017845223201696629678_);}
	.st19{fill:url(#SVGID_00000043447490273872126860000015259894822082923413_);}
	.st20{mask:url(#SVGID_00000134251788342323538240000000387410306206650261_);fill:#FFE9D9;}
	.st21{opacity:0.3;fill:#FFE1CC;}
	.st22{fill:#FFCFAE;}
	.st23{filter:url(#Adobe_OpacityMaskFilter_00000109720796761808517170000000319186182893354116_);}
	.st24{fill:url(#SVGID_00000036211220821451335780000009762476507336763062_);}
	.st25{mask:url(#SVGID_00000102538787444420938830000004542837701728866953_);fill:#FFE9D9;}
</style>
<g>
	<g>
		<path class="st0" d="M257.9,189.3c3.5,14.6,4.9,17.6,5.4,25.3c0.5,8.3,1.6,13.8,1.3,18.8c-0.2,4.1-4.6,17.6-25,36.9
			c-17.9,17-32.7,22.5-38,22.7c-4.6,0.2-6.3-1.2-17.1-2.5c-13.1-1.6-29.1-4-43.8-9.8c-16-6.3-29.5-19.4-37-26.3
			c-11.5-10.5-36.6-39.3-46.4-49.9C46.3,192.4,13.3,155,5.1,144c-7.2-9.6-6.3-19.9-0.4-25.7c7.6-7.4,16.1-3.8,21.7,1
			c7.7,6.6,24.2,22.8,33.6,31.3c9.5,8.5,43.5,39.3,43.5,39.3s9.7-16.6,21.3-29.3c14.6-16,27.1-25.7,27.1-25.7s-16.4-14-39.2-35.4
			c-22.8-21.4-41.1-39-45.8-43.4c-5.9-5.5-15.8-17-4.8-29.1c11.3-12.3,23-3.3,29.7,2.5c6.1,5.3,31,27.7,52.3,48.2
			c38.3,36.7,60.8,41.9,60.8,41.9l41.3,44.6L257.9,189.3z"/>
		<path class="st0" d="M257.9,189.3c3.5,14.6,4.9,17.6,5.4,25.3c0.5,8.3,1.6,13.8,1.3,18.8c-0.2,4.1-4.6,17.6-25,36.9
			c-17.9,17-32.7,22.5-38,22.7c-4.6,0.2-6.3-1.2-17.1-2.5c-13.1-1.6-29.1-4-43.8-9.8c-16-6.3-29.5-19.4-37-26.3
			c-11.5-10.5-36.6-39.3-46.4-49.9C46.3,192.4,13.3,155,5.1,144c-7.2-9.6-6.3-19.9-0.4-25.7c7.6-7.4,16.1-3.8,21.7,1
			c7.7,6.6,24.2,22.8,33.6,31.3c9.5,8.5,43.5,39.3,43.5,39.3s9.7-16.6,21.3-29.3c14.6-16,27.1-25.7,27.1-25.7s-16.4-14-39.2-35.4
			c-22.8-21.4-41.1-39-45.8-43.4c-5.9-5.5-15.8-17-4.8-29.1c11.3-12.3,23-3.3,29.7,2.5c6.1,5.3,31,27.7,52.3,48.2
			c38.3,36.7,60.8,41.9,60.8,41.9l41.3,44.6L257.9,189.3z"/>
		<path class="st1" d="M257.9,189.3c3.5,14.6,4.6,17.6,5.1,25.3c0.5,8.3,1.9,13.8,1.7,18.8c-0.2,4.1-4.6,17.6-25,36.9
			c-17.9,17-32.7,22.5-38,22.7c-4.6,0.2-6.3-1.2-17.1-2.5c-13.1-1.6-29.1-4-43.8-9.8c-16-6.3-29.5-19.4-37-26.3
			c-11.5-10.5-36.6-39.3-46.4-49.9C46.3,192.4,13.3,155,5.1,144c-7.2-9.6-6.3-19.9-0.4-25.7c7.6-7.4,16.1-3.8,21.7,1
			c7.7,6.6,24.2,22.8,33.6,31.3c9.5,8.5,43.5,39.3,43.5,39.3s9.7-16.6,21.3-29.3c14.6-16,27.1-25.7,27.1-25.7s-16.4-14-39.2-35.4
			c-22.8-21.4-41.1-39-45.8-43.4c-5.9-5.5-15.8-17-4.8-29.1c11.3-12.3,23-3.3,29.7,2.5c6.1,5.3,31,27.7,52.3,48.2
			c38.3,36.7,60.8,41.9,60.8,41.9l41.3,44.6L257.9,189.3z"/>
		<path class="st1" d="M7.9,77.5c-9.1,9.9-2.1,21,4.2,27.5c5.6,5.8,13.3,13.6,25.8,25.2c14.8,13.8,66,60.6,66,60.6s4-7.4,9.9-15.4
			c5.8-7.9,11.4-14.4,11.4-14.4S41.3,84.1,34.4,78.1C28.4,73,17.5,67.1,7.9,77.5z"/>
		<path class="st1" d="M23.1,40.8c-9.2,9.8-2.8,21.4,3.4,27.9c5.5,5.8,13.3,13.7,25.7,25.5c14.7,14,72.9,68,72.9,68
			s5.6-6.8,12.8-13.6c7.4-7,15.1-13.1,15.1-13.1S57.4,47.2,50.5,41.1C44.5,35.9,32.9,30.4,23.1,40.8z"/>
		<g>
			<polygon class="st2" points="167.7,98.1 167.7,98.1 167.7,98.1 			"/>
			<path class="st3" d="M185,132.2c-0.7,11.5-11.6,19.4-10,39.8c0.8,9.7,9.8,23.1,9.8,23.1l26.2-25.4l25.4-20.7l-31.3-29.4
				c0,0-13.5-3.1-37.4-21.6C169.1,99.2,186.4,110.8,185,132.2z"/>
		</g>
		<path class="st4" d="M239.6,265.6c-11.4,10.4-25,20.9-36.7,21.8c-10.3,0.7-12.7-0.9-24.3-3.4c-10-2.1-22.6-3.3-36.8-9.9
			c-9.4-4.4-21-11.9-32.9-23.2c-20.1-19-32.6-34.8-50.4-53.7c-10-10.6-49.8-52.5-54.2-67C1.8,122,7.9,116,7.9,116
			c-1,0.6-2.1,1.4-3.1,2.4c-5.9,5.8-6.8,16,0.4,25.7c8.2,10.9,41.2,48.4,52.4,60.5c9.8,10.7,34.8,39.4,46.4,50
			c7.5,6.9,21,20,37,26.3c14.7,5.8,30.6,8.2,43.8,9.8c10.8,1.3,12.5,2.7,17.1,2.5c5.2-0.2,20-5.7,38-22.7
			c12.7-12,19.2-21.8,22.3-28.5C262.1,241.8,250.3,255.8,239.6,265.6z"/>
		<path class="st3" d="M95.8,207.4c1.1,0.3,8.8-19,32.8-45.1c27.5-29.9,44-37.5,44-38.5c0-1-21.3,2.8-49,33.6
			C98,186,94.7,207.1,95.8,207.4z"/>
		<path class="st4" d="M66.9,23.2c-1.6,1-3.1,2.2-4.7,4c-11,12-1.1,23.6,4.8,29.1c4.7,4.3,23,22,45.8,43.4
			c13.7,12.9,25.1,23.1,32,29.2c3.6,3.2,4.1,5.1,1.3,7.8l18-10.7c0,0-7,2.5-14-0.6c-3.6-1.6-8.8-6.6-12.9-10.2
			c-6-5.3-11.7-11.3-15.1-14.5c-4.2-3.9-9.4-10.3-19.7-19.7c-9.2-8.3-14.4-12.5-22.4-19.7C71.9,53.5,62,45.3,61,38.6
			C59.5,29,66.4,23.7,66.9,23.2z"/>
		<path class="st1" d="M183.4,200.8c0,0-5.9-16.3-2.5-33.6c4-20,11.2-28.1,11.7-43.7c0.4-12.2-6.6-28.5,0.1-47.1
			c7.9-22.1,27.9-19.3,27.9-19.3s4.4,12.4,7.1,20.4c2.7,8.1,8,20,10,28.7c2.3,9.9,2.4,25.2,5.1,37.5c2.9,13,7.9,24.7,7.9,24.7
			L183.4,200.8z"/>
		<path class="st4" d="M198.1,125.2c0.2-13.1-5.8-18-3.1-37.9c2.2-16.4,13.4-28,13.4-28l-0.8,0c-5.5,2.4-11.4,7.3-14.9,17.1
			c-6.7,18.7,0.3,34.9-0.1,47.1c-0.5,15.6-7.7,23.7-11.7,43.7c-3.4,17.3,2.5,33.6,2.5,33.6s-4.4-18.4-0.4-32.5
			C186.9,154.3,197.9,138.4,198.1,125.2z"/>
		<path class="st5" d="M231.7,88.2c0,0,0.4,5.7,2.9,20.5c2.6,14.8-1.2,30.4,4.5,49.4c5.7,18.9,13.5,30.1,15.4,38.7
			c1.9,8.6,1.3,14.3,3.5,21.1c2.1,6.8,5.3,21,5.3,21c3.1-7.3,1.1-16.7-0.1-24.9c-1.1-7.6,0-16.4-3.2-25.1c-2-4.5-9.2-20.4-9.2-20.4
			l0,0c0,0-5-11.7-7.9-24.7c-2.7-12.3-2.8-27.6-5.1-37.5C236.5,100.9,234,94.4,231.7,88.2z"/>
		<path class="st6" d="M158.9,186.8c0,0-8.4,18.7-1.8,30.4c6.4,11.3,38.2,20.7,38.2,20.7l-44-7l-10.9-6.2l-2.2-6.6l8.6-10.9
			L158.9,186.8z"/>
		<g>
			<path class="st3" d="M139.7,215.7c-1.5,1.5-2.9,2.8-4.2,4c-8.2,7.2-14.8,10.5-15.1,10.6c-0.7,0.4-1.6,0.1-2-0.7
				c-0.4-0.7-0.1-1.6,0.7-2l0,0c0.3-0.1,26.2-13.1,37.7-43.3c0.3-0.8,1.1-1.1,1.9-0.9c0.8,0.3,1.1,1.1,0.9,1.9
				C154.4,198.8,146.5,208.9,139.7,215.7z"/>
		</g>
		<g>
			<path class="st3" d="M207.4,245.3c-0.4,0.4-1.1,0.6-1.7,0.3c-40.1-19.2-76.9-10.7-77.3-10.6c-0.8,0.2-1.6-0.3-1.8-1.1
				c-0.2-0.8,0.3-1.6,1.1-1.8c0.4-0.1,9.7-2.3,24.1-2.1c13.3,0.2,33.7,2.6,55.1,12.9c0.7,0.4,1,1.2,0.7,2
				C207.6,245,207.5,245.2,207.4,245.3z"/>
		</g>
		<path class="st3" d="M152,200.4c0,0-12,18.9-10.6,22.9c1.9,5.5,14.7,5.6,20.1,6.2c5.3,0.7,18,3.8,18,3.8s-21.4-2.2-29.4-2.7
			c-8-0.4-21.3,2.3-21.3,2.3s0-2.3-2.1-3.6c-1.7-1.1-4.5,0.1-4.5,0.1s8.3-5.3,15.6-12.5C144.9,209.8,152,200.4,152,200.4z"/>
		<path class="st2" d="M194.2,200.4c0,0,10.5,10.3,21.7,15.2c11.2,4.9,23.9,5.4,23.9,5.4s-12.8,3-24.8-3
			C203,212,194.2,200.4,194.2,200.4z"/>
	</g>
	<path class="st7" d="M264.6,233.8c0.8-5.8-0.3-12.3-1.2-18.2c0-0.3,0-0.7-0.1-1c-0.1-2-0.3-3.7-0.6-5.4c-0.4-6.4-0.2-13.3-2.7-20.3
		c-2-4.5-9.2-20.4-9.2-20.4l0,0c0,0-5-11.7-7.9-24.7c-2.7-12.3-2.8-27.6-5.1-37.5c-0.1-0.5-0.2-1-0.4-1.5c0-0.1-0.1-0.2-0.1-0.3
		c-0.1-0.5-0.3-1-0.4-1.5c0-0.1-0.1-0.2-0.1-0.3c-0.1-0.5-0.3-1-0.5-1.5c0-0.1,0-0.1,0-0.2c-0.2-0.5-0.3-1.1-0.5-1.6
		c0-0.1-0.1-0.2-0.1-0.3c-0.2-0.5-0.4-1.1-0.6-1.6c0-0.1,0-0.1-0.1-0.2c-0.2-0.5-0.4-1.1-0.6-1.6c0-0.1-0.1-0.2-0.1-0.2
		c-0.2-0.6-0.4-1.1-0.6-1.7c0-0.1-0.1-0.2-0.1-0.3c-0.4-1.2-0.9-2.3-1.3-3.5c0-0.1-0.1-0.2-0.1-0.3c-0.2-0.5-0.4-0.9-0.5-1.4l0,0
		c-1.5-3.8-2.9-7.5-4-10.6c-2-5.9-4.9-14.2-6.3-18.2c-0.9,5.1-1.7,11.1-2.3,18.3c-0.7,7.8-0.4,15-0.1,20.1c0.1,1.9,0.2,4.2,0.2,5.8
		c-1.5-0.7-3.6-2.2-5.2-3.7c-6.8-7.1-13.8-15.4-20.7-24.9c0,0,0,0,0,0c0,0.1-0.1,0.2-0.1,0.3c-0.1,0.3-0.2,0.6-0.4,1
		c-5.1,14.2-2.3,27-0.8,37.6c-5.3-2.8-12.2-7-20.4-13.1c-0.2-0.2-0.4-0.3-0.7-0.5c-0.3-0.2-0.7-0.5-1-0.8c-0.9-0.7-1.6-1.2-1.9-1.4
		c-7-5.4-14.9-12.1-23.7-20.5C122.9,57.3,98,34.9,91.9,29.6c-6.7-5.8-18.4-14.8-29.7-2.5c0,0,0,0-0.1,0.1c0.5,2.9,1.5,5.6,2.5,7.9
		c-1.3-0.3-2.6-0.4-4-0.4c-1,0-2,0.1-3,0.3c-2.2,8.2,3.5,15.6,8,20.1c-8-7.3-13.7-12.5-15.2-13.9c-0.7-0.6-1.6-1.3-2.5-1.9
		C38,46.6,37.2,59,45.7,72.5c0.6,0.9,1.1,1.8,1.7,2.7c-0.1,0-0.2,0-0.3,0c-3.4,0-6.8,1.1-10,3.1c-0.3,0.2-0.5,0.3-0.8,0.5
		c4.3,4.3,9.4,9.3,15.8,15.4c0.5,0.5,1.1,1,1.7,1.6C45.4,88.1,38.8,82,35.7,79.3c-8.1,6.3-11,19-5.7,30.4
		c6,12.7,29.2,57.3,37.7,71.4c1.9,3.1,4.5,7.5,7.5,12.7c9,15.3,21.2,36.2,28.2,45.8c0.4,0.6,0.9,1.2,1.4,1.9
		c6.3,8.7,19.3,21.1,33.3,30.2c12.7,8.2,27.1,13.8,39.8,18c2.3,0.3,4.6,0.6,6.7,0.9c10.8,1.3,12.5,2.7,17.1,2.5
		c5.2-0.2,20-5.7,38-22.7c12.7-12,19.2-21.8,22.3-28.5c0,0-0.1,0.1-0.2,0.3c0.6-1.2,1-2.2,1.4-3.2c0,0,0,0,0,0
		c0.5-1.1,0.8-2.3,1.1-3.5C264.5,234.8,264.6,234.3,264.6,233.8z"/>
	<g>
		<path class="st8" d="M277.5,202.7c0.5,15-2.8,17.4-3.9,25c-1.1,8.2-1.2,13.8-2.4,18.6c-1,4-8,16.3-31.8,31.2
			c-20.9,13.1-36.6,15.5-41.7,14.7c-4.5-0.7-5.9-2.4-16.3-5.9c-12.6-4.2-27.7-9.7-41-18.3c-14.4-9.3-25.1-24.9-31-33.1
			c-9.2-12.6-28-45.8-35.5-58.2c-8.5-14.1-33.4-57.4-39.2-69.7c-5.1-10.9-2.3-20.7,4.7-25.2c8.9-5.7,16.5-0.5,21.1,5.3
			c6.2,8,19.2,27.2,26.7,37.3c7.6,10.2,34.8,47.2,34.8,47.2s12.8-14.3,26.7-24.4c17.5-12.8,31.6-19.8,31.6-19.8s-13.3-17-31.4-42.5
			c-18.1-25.5-32.5-46.5-36.2-51.6c-4.7-6.5-12.1-19.8,1.1-29.4c13.6-9.8,23.2,1.3,28.7,8.4c4.9,6.4,24.8,33.3,41.7,57.6
			c30.2,43.6,51.2,53.2,51.2,53.2l38.3,54.1L277.5,202.7z"/>
		<path class="st8" d="M50.7,42.4c-10.8,7.8-6.2,20.1-1.4,27.7C53.6,77,59.6,86.1,69.5,100c11.8,16.5,52.6,72.5,52.6,72.5
			s5.4-6.5,12.7-13.2c7.3-6.6,14.1-11.9,14.1-11.9s-66.8-92-72.4-99.2C71.6,42.1,62.2,34.1,50.7,42.4z"/>
		<path class="st8" d="M72.9,9.4c-10.9,7.7-7,20.4-2.3,28c4.2,6.8,10.3,16.1,20.1,30.1c11.6,16.6,57.9,81.2,57.9,81.2
			s6.8-5.5,15.2-10.8c8.6-5.4,17.4-9.9,17.4-9.9S105.2,22.5,99.7,15.3C94.9,9,84.5,1.2,72.9,9.4z"/>
		<defs>
			<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="34.6" y="80.5" width="82.7" height="121.9">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		<mask maskUnits="userSpaceOnUse" x="34.6" y="80.5" width="82.7" height="121.9" id="SVGID_1_">
			<g class="st9">
				
					<linearGradient id="SVGID_00000117671192450215288000000014654227848842914492_" gradientUnits="userSpaceOnUse" x1="578.4839" y1="-193.4004" x2="578.4839" y2="-338.3167" gradientTransform="matrix(0.8339 -0.5519 0.5519 0.8339 -276.0644 655.676)">
					<stop  offset="0" style="stop-color:#000000"/>
					<stop  offset="1" style="stop-color:#FFFFFF"/>
				</linearGradient>
				<path style="fill:url(#SVGID_00000117671192450215288000000014654227848842914492_);" d="M40.1,82.9c-9.9,6.5-3.9,17.4-1.5,22.4
					c14.1,29,58.3,97.1,58.3,97.1s4.3-10.8,9.2-18.2c5-7.3,11.2-13.5,11.2-13.5S62.8,92.3,59.8,88.1C55.8,82.7,48.7,77.2,40.1,82.9z
					"/>
			</g>
		</mask>
		<path class="st11" d="M40.1,82.9c-9.9,6.5-3.9,17.4-1.5,22.4c14.1,29,58.3,97.1,58.3,97.1s4.3-10.8,9.2-18.2
			c5-7.3,11.2-13.5,11.2-13.5S62.8,92.3,59.8,88.1C55.8,82.7,48.7,77.2,40.1,82.9z"/>
		<defs>
			
				<filter id="Adobe_OpacityMaskFilter_00000167396545781066047470000003651391486191576704_" filterUnits="userSpaceOnUse" x="46.7" y="40" width="96.4" height="120">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		
			<mask maskUnits="userSpaceOnUse" x="46.7" y="40" width="96.4" height="120" id="SVGID_00000129926851298958261910000002236405390832443552_">
			<g style="filter:url(#Adobe_OpacityMaskFilter_00000167396545781066047470000003651391486191576704_);">
				
					<linearGradient id="SVGID_00000183214096139252261460000006459542770071457692_" gradientUnits="userSpaceOnUse" x1="615.3178" y1="-192.1471" x2="615.3178" y2="-344.2273" gradientTransform="matrix(0.8339 -0.5519 0.5519 0.8339 -276.0644 655.676)">
					<stop  offset="0" style="stop-color:#000000"/>
					<stop  offset="1" style="stop-color:#FFFFFF"/>
				</linearGradient>
				<path style="fill:url(#SVGID_00000183214096139252261460000006459542770071457692_);" d="M53.6,42.8c11.3-7.6,19.7,2.7,22.5,6.4
					c5.1,6.5,50.7,69.5,54.7,75.2c6.3,8.8,12,15.9,12.3,20.8c0.1,3-13.2,12.9-17.4,14.5c-4.1,1.6-7.8-2.5-12.7-8.7
					c-6-7.5-9.5-12.6-15.2-20.3c-5.8-7.9-25-33.2-28.7-38.2c-3.7-5-5.8-7.5-10.1-13.4c-3.4-4.7-9.3-13.2-11.3-18.3
					C45.9,55.6,45.9,48,53.6,42.8z"/>
			</g>
		</mask>
		<path style="mask:url(#SVGID_00000129926851298958261910000002236405390832443552_);fill:#FFE9D9;" d="M53.6,42.8
			c11.3-7.6,19.7,2.7,22.5,6.4c5.1,6.5,50.7,69.5,54.7,75.2c6.3,8.8,12,15.9,12.3,20.8c0.1,3-13.2,12.9-17.4,14.5
			c-4.1,1.6-7.8-2.5-12.7-8.7c-6-7.5-9.5-12.6-15.2-20.3c-5.8-7.9-25-33.2-28.7-38.2c-3.7-5-5.8-7.5-10.1-13.4
			c-3.4-4.7-9.3-13.2-11.3-18.3C45.9,55.6,45.9,48,53.6,42.8z"/>
		<defs>
			
				<filter id="Adobe_OpacityMaskFilter_00000067208640789670865280000017247655035170129050_" filterUnits="userSpaceOnUse" x="68.5" y="6.6" width="106.2" height="128.8">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		
			<mask maskUnits="userSpaceOnUse" x="68.5" y="6.6" width="106.2" height="128.8" id="SVGID_00000142165338838560363900000004628371877247224195_">
			<g style="filter:url(#Adobe_OpacityMaskFilter_00000067208640789670865280000017247655035170129050_);">
				
					<linearGradient id="SVGID_00000103977656855125119610000009710799143079783316_" gradientUnits="userSpaceOnUse" x1="651.7749" y1="-201.5135" x2="651.7749" y2="-374.6182" gradientTransform="matrix(0.8339 -0.5519 0.5519 0.8339 -276.0644 655.676)">
					<stop  offset="0" style="stop-color:#000000"/>
					<stop  offset="1" style="stop-color:#FFFFFF"/>
				</linearGradient>
				<path style="fill:url(#SVGID_00000103977656855125119610000009710799143079783316_);" d="M75.2,9.7c-6.8,5-7.4,10.8-6.3,16.5
					c0.9,4.6,5.1,10.3,10.3,17.8c5.1,7.4,16.5,23.1,22.4,31.2c7.3,10,14.6,19.6,24.6,32.3c9.1,11.5,20.3,26.1,22.2,26.9
					c6.8,2.8,13.1-1.5,17-3.6c3.9-2.1,9-4.1,9.3-7.2c0.1-1.4-6.5-10.1-14.8-22c-6.7-9.6-13.2-19.3-16.1-23.2
					c-6.1-8.3-23.1-32.7-30.4-42.7c-6.6-9.1-14-21.3-21.7-26.3C87.6,6.8,82.2,4.6,75.2,9.7z"/>
			</g>
		</mask>
		<path style="mask:url(#SVGID_00000142165338838560363900000004628371877247224195_);fill:#FFE9D9;" d="M75.2,9.7
			c-6.8,5-7.4,10.8-6.3,16.5c0.9,4.6,5.1,10.3,10.3,17.8c5.1,7.4,16.5,23.1,22.4,31.2c7.3,10,14.6,19.6,24.6,32.3
			c9.1,11.5,20.3,26.1,22.2,26.9c6.8,2.8,13.1-1.5,17-3.6c3.9-2.1,9-4.1,9.3-7.2c0.1-1.4-6.5-10.1-14.8-22
			c-6.7-9.6-13.2-19.3-16.1-23.2c-6.1-8.3-23.1-32.7-30.4-42.7c-6.6-9.1-14-21.3-21.7-26.3C87.6,6.8,82.2,4.6,75.2,9.7z"/>
		<defs>
			
				<filter id="Adobe_OpacityMaskFilter_00000145761991127449585440000015424985955991427485_" filterUnits="userSpaceOnUse" x="109.9" y="1.5" width="99.4" height="120.1">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		
			<mask maskUnits="userSpaceOnUse" x="109.9" y="1.5" width="99.4" height="120.1" id="SVGID_00000030464426723356300680000014919051087253129109_">
			<g style="filter:url(#Adobe_OpacityMaskFilter_00000145761991127449585440000015424985955991427485_);">
				
					<linearGradient id="SVGID_00000156581549625827716350000017818771768113741726_" gradientUnits="userSpaceOnUse" x1="692.4207" y1="-196.2876" x2="684.1261" y2="-358.7623" gradientTransform="matrix(0.8339 -0.5519 0.5519 0.8339 -276.0644 655.676)">
					<stop  offset="0" style="stop-color:#000000"/>
					<stop  offset="1" style="stop-color:#FFFFFF"/>
				</linearGradient>
				<path style="fill:url(#SVGID_00000156581549625827716350000017818771768113741726_);" d="M117.1,4.1c10.4-7.3,19.2,2.1,24.7,9.1
					c1.3,1.6,6.9,9.4,13,17.5c12.5,16.9,20.8,29,26.8,37.4c9.6,13.6,22.8,29.1,25.9,34.4c3.1,5.3,2.1,9.8-2.1,13.3
					c-7.8,6.4-12.6,6-16.2,5.5c-3.6-0.6-6.1-2-11.4-6.9c-4.8-4.4-11.5-13-20.2-25.6c-4.8-6.9-9.4-13.6-13.5-19.2
					c-4.2-5.7-11-15.4-18.4-25.4c-6.5-8.7-10.6-15.1-12.4-18.1C110.9,22,105.4,12.2,117.1,4.1z"/>
			</g>
		</mask>
		<path style="mask:url(#SVGID_00000030464426723356300680000014919051087253129109_);fill:#FFE9D9;" d="M117.1,4.1
			c10.4-7.3,19.2,2.1,24.7,9.1c1.3,1.6,6.9,9.4,13,17.5c12.5,16.9,20.8,29,26.8,37.4c9.6,13.6,22.8,29.1,25.9,34.4
			c3.1,5.3,2.1,9.8-2.1,13.3c-7.8,6.4-12.6,6-16.2,5.5c-3.6-0.6-6.1-2-11.4-6.9c-4.8-4.4-11.5-13-20.2-25.6
			c-4.8-6.9-9.4-13.6-13.5-19.2c-4.2-5.7-11-15.4-18.4-25.4c-6.5-8.7-10.6-15.1-12.4-18.1C110.9,22,105.4,12.2,117.1,4.1z"/>
		<path class="st7" d="M67.8,23.4c-0.6-11.9,12.1-17,12.3-17.1c-2.3,0.5-4.7,1.5-7.2,3.2c-10.9,7.7-7,20.4-2.3,28
			c4.2,6.8,10.3,16.1,20.1,30.1c9.3,13.3,33.8,46,46.1,63.2c4.6,6.4,7.9,11.8,9,13.6c1.8,2.9,4.4,2.8,6,1.5c1.6-1.3,2.3-2.6-0.2-6
			c-1.9-2.6-5.1-5.7-12.4-14.7c-7-8.5-9.9-12.2-15.4-19.2c-5-6.4-7.1-9.2-12.2-16.2c-5.1-7-8.3-11.2-15.7-21.4
			c-6.4-8.9-15.3-21.1-17.8-24.8C73,35.6,68.2,30.9,67.8,23.4z"/>
		<path class="st7" d="M46.2,52.4c0.7-9.1,10.6-13,10.6-13c-2,0.5-4,1.5-6.1,3.1c-10.8,7.8-6.2,20.1-1.4,27.7
			C53.6,77,59.6,86.1,69.5,100c7.8,11,26.6,37.2,39.6,55c3,4.1,6.5,9.9,7.6,11.5c2.3,3.2,7.3-0.1,5-3.1c-2.4-3.1-4.5-5.6-10.3-13.3
			c-5.7-7.7-8.4-10.6-15.4-20.7c-7.1-10.3-17.7-23.9-26.1-35.2c-4.1-5.5-9.4-12-15.7-21.4C50,66.5,45.6,60.6,46.2,52.4z"/>
		<path class="st7" d="M119.4,1c-1.7,0.6-3.5,1.6-5.4,2.9c-13.2,9.6-5.8,22.9-1.1,29.4C116.5,38.5,131,59.5,149,85
			c10.9,15.3,19.2,28,24.7,35.3c2.9,3.8,4.7,4.6,7,3.1c1.5-1,2.9-2.4-1.3-7c-2.7-2.9-5.5-4.7-10.3-10.9c-4.9-6.4-9.8-13-12.5-16.8
			c-3.3-4.7-8-11.6-16.2-22.8c-7.4-10-11.1-15.4-17.5-24c-6.4-9.1-14.6-19.3-14.4-26.1C108.8,5.7,118.7,1.4,119.4,1z"/>
		<path class="st8" d="M210.9,103c0,0,7.7,7.5,11.5,4.8c2.3-1.6-0.5-14.1,0.9-29.6c2.2-24.6,5.6-33.5,11.6-38.5
			c11.8-9.8,21-3.8,21.9-2.5c1.2,1.5,2.9,6.9,0.2,22.4c-1.9,11,2.4,23.3,4,31.1c2.2,11,2.1,19.9,3.9,29.2c3,15.7,4.7,19.1,7.2,33.5
			c2.4,14.4,2.5,28.5,2.5,28.5l-22.3-30.6l-16.5-21.1L210.9,103z"/>
		<path class="st21" d="M258.2,41.7c-1.1-0.6-4-1.9-8.9-2.1c-4-0.2-9.9,2.7-11,10.2c-1.2,8.3,3.7,12.1,6.3,12.9
			c5.5,1.5,10.1,1.5,11.9,1.4c0-1.5,0.2-3.1,0.5-4.6C258.5,50.7,258.6,45.1,258.2,41.7z"/>
		<path class="st7" d="M234.9,39.6c-6,5-9.4,13.9-11.6,38.5c-1.4,15.6,1.4,28.1-0.9,29.6c-3.8,2.7-11.5-4.8-11.5-4.8
			s10,11.2,13.5,14.8c3.5,3.5,9.7,11.1,10.9,9.8c1.3-1.3-2.9-12.8-5.1-22.2c-2-8.3-2-16.7-1.6-26.5c0.4-10.2,0.7-27.2,6.8-34.7
			c7-8.6,16.5-9.4,16.5-9.4l0,0C247.9,33.6,241.8,33.9,234.9,39.6z"/>
		<path class="st22" d="M256.5,64.1c0,0-1.3,6.6-0.5,12.9c0.6,4.3-0.2,20.6,0.4,31.9c0.5,9.5,3.9,20.9,4.9,25.5
			c2.1,9.7,3.1,15,3.7,20.9c0.8,7.5-1.1,19.5-0.2,26.1c1.3,10,2.8,15.3,3.6,24.2c1,11-3.9,18.7-3.3,24.1c0.4,3.4,3.7,6.2,4.5,10.3
			c1.2,5.6,0,10.2,0,10.2c0.9-1.6,1.5-2.9,1.7-3.8c1.2-4.8,1.3-10.4,2.4-18.6c1-7.6,4.4-10,3.9-25l-3-20.8c0,0,0-14.1-2.5-28.5
			c-2.5-14.4-4.2-17.8-7.2-33.5c-1.8-9.3-1.7-18.2-3.9-29.2C259.6,83.9,256.2,73.8,256.5,64.1z"/>
		<path class="st21" d="M121.6,34.5c1.9,0.4,21.3-13.1,21.5-16c0.2-2.8-3.6-9.1-8.9-13.9c-2.3-2.1-9-6.7-18-0.3
			c-8.5,6.1-5.9,14.8-4.3,17.8C114.2,26.4,119.7,34.1,121.6,34.5z"/>
		<path class="st21" d="M81.3,42.8c2,0.5,22.7-13.5,23-16.6c0.2-3-4.8-11.7-9.6-15.4c-2.6-2-8.5-7.5-18.7-0.6
			c-9.2,6.3-6.7,15-4.9,19.5C73,34.4,79.3,42.4,81.3,42.8z"/>
		<path class="st21" d="M57.9,72c1.8,0.4,20.9-12.5,21.2-15.2c0.2-2.7-3.9-10.3-8.5-14.1c-2.4-1.9-8.3-6.5-17.6-0.1
			c-8.5,5.8-6,13.8-4.3,18C50.3,64.8,56.1,71.6,57.9,72z"/>
		<path class="st21" d="M43.2,109.1c1.6,0.4,19-11.4,19.2-13.8c0.3-2.4-2.2-7.9-6.8-12.1c-2-1.8-7.5-5.8-15.2-0.5
			C33,87.9,34.2,94.8,36,99.1C37.8,103.2,41.6,108.7,43.2,109.1z"/>
		<defs>
			
				<filter id="Adobe_OpacityMaskFilter_00000181795098640168189980000003896901896651619007_" filterUnits="userSpaceOnUse" x="218.9" y="35.3" width="48.4" height="189.5">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		
			<mask maskUnits="userSpaceOnUse" x="218.9" y="35.3" width="48.4" height="189.5" id="SVGID_00000121270952348932841840000017428436597619939757_">
			<g style="filter:url(#Adobe_OpacityMaskFilter_00000181795098640168189980000003896901896651619007_);">
				
					<linearGradient id="SVGID_00000046333228083996619470000015075498494524009123_" gradientUnits="userSpaceOnUse" x1="705.5891" y1="-124.6311" x2="865.6918" y2="-288.0673" gradientTransform="matrix(0.8339 -0.5519 0.5519 0.8339 -276.0644 655.676)">
					<stop  offset="0" style="stop-color:#000000"/>
					<stop  offset="1" style="stop-color:#FFFFFF"/>
				</linearGradient>
				<path style="fill:url(#SVGID_00000046333228083996619470000015075498494524009123_);" d="M255.8,37.1c-2.7-2.6-6.3-2-10-0.3
					c-3.7,1.7-8.8,4.6-11.4,10c-5.1,10.5-4.7,25.2-5,33.3c-0.3,6.7-0.7,14,1.2,22.7c2,9,6.8,21.6,6,27c-0.7,4.4-5,8.7-9.4,16.2
					c-8.4,14.5-9.5,27.8-7.2,43.6c2.3,15.8,27.4,37,34.3,35c7-2,13.5-8.6,13-18.4c-0.5-9.8-3.1-22.9-3.5-29.2
					c-0.3-5.2,1.6-11.7,0-26.5c-1.2-10.9-3.6-16.9-5.5-26c-1.4-6.6-2.7-12.8-2.5-26.9c0.1-6.2,0.2-11.7,0.2-13.5
					c0-5.2-1-13.6-0.2-18.3c0.8-4.6,1.9-9.2,2.1-16C258,43.8,258.7,39.8,255.8,37.1z"/>
			</g>
		</mask>
		<path style="mask:url(#SVGID_00000121270952348932841840000017428436597619939757_);fill:#FFE9D9;" d="M255.8,37.1
			c-2.7-2.6-6.3-2-10-0.3c-3.7,1.7-8.8,4.6-11.4,10c-5.1,10.5-4.7,25.2-5,33.3c-0.3,6.7-0.7,14,1.2,22.7c2,9,6.8,21.6,6,27
			c-0.7,4.4-5,8.7-9.4,16.2c-8.4,14.5-9.5,27.8-7.2,43.6c2.3,15.8,27.4,37,34.3,35c7-2,13.5-8.6,13-18.4c-0.5-9.8-3.1-22.9-3.5-29.2
			c-0.3-5.2,1.6-11.7,0-26.5c-1.2-10.9-3.6-16.9-5.5-26c-1.4-6.6-2.7-12.8-2.5-26.9c0.1-6.2,0.2-11.7,0.2-13.5
			c0-5.2-1-13.6-0.2-18.3c0.8-4.6,1.9-9.2,2.1-16C258,43.8,258.7,39.8,255.8,37.1z"/>
	</g>
</g>
</svg>
