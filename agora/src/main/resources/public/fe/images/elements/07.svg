<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.4.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 138.7 140.2" style="enable-background:new 0 0 138.7 140.2;" xml:space="preserve">
<style type="text/css">
	.st0{enable-background:new    ;}
	.st1{fill:url(#SVGID_1_);}
	.st2{fill:url(#SVGID_00000052101607201367279260000000713997805414832768_);}
	.st3{fill:url(#SVGID_00000082366055953718417570000010825212403753519774_);}
	.st4{opacity:0.7;fill:url(#SVGID_00000057861093431980415820000002770782140080367262_);}
	.st5{fill:url(#SVGID_00000087401905356977274930000007163256895225928846_);}
</style>
<g class="st0">
	
		<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="-354.0617" y1="35.6249" x2="-314.2905" y2="89.9009" gradientTransform="matrix(0.9954 9.580000e-02 -9.580000e-02 0.9954 418.2132 77.9179)">
		<stop  offset="0" style="stop-color:#ED1D77"/>
		<stop  offset="1.451034e-02" style="stop-color:#E91D78"/>
		<stop  offset="0.2263" style="stop-color:#B72089"/>
		<stop  offset="0.3304" style="stop-color:#A3218F"/>
		<stop  offset="0.4371" style="stop-color:#810D7A"/>
		<stop  offset="0.4964" style="stop-color:#761277"/>
		<stop  offset="0.5704" style="stop-color:#611A71"/>
		<stop  offset="0.6629" style="stop-color:#5C1E76"/>
		<stop  offset="0.7749" style="stop-color:#4F2A85"/>
		<stop  offset="0.8966" style="stop-color:#3A3E9D"/>
		<stop  offset="1" style="stop-color:#2254B8"/>
	</linearGradient>
	<polygon class="st1" points="87.2,121.5 70.3,127.5 96.8,137.1 113.7,131.1 	"/>
	
		<linearGradient id="SVGID_00000096758234530069468420000007710953780961989822_" gradientUnits="userSpaceOnUse" x1="-384.1172" y1="-46.5265" x2="-357.6126" y2="112.5012" gradientTransform="matrix(0.9954 9.580000e-02 -9.580000e-02 0.9954 418.2132 77.9179)">
		<stop  offset="0" style="stop-color:#ED1D77"/>
		<stop  offset="1.451034e-02" style="stop-color:#E91D78"/>
		<stop  offset="0.2263" style="stop-color:#B72089"/>
		<stop  offset="0.3304" style="stop-color:#A3218F"/>
		<stop  offset="0.4371" style="stop-color:#810D7A"/>
		<stop  offset="0.5435" style="stop-color:#761277"/>
		<stop  offset="0.6766" style="stop-color:#611A71"/>
		<stop  offset="0.7236" style="stop-color:#5C1E76"/>
		<stop  offset="0.7806" style="stop-color:#4F2A85"/>
		<stop  offset="0.8425" style="stop-color:#3A3E9D"/>
		<stop  offset="0.8951" style="stop-color:#2254B8"/>
		<stop  offset="0.9047" style="stop-color:#2D51B4"/>
		<stop  offset="0.9218" style="stop-color:#4C49AB"/>
		<stop  offset="0.9445" style="stop-color:#7E3B9B"/>
		<stop  offset="0.9715" style="stop-color:#C22985"/>
		<stop  offset="0.9875" style="stop-color:#ED1D77"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000096758234530069468420000007710953780961989822_);" d="M17.3,55.9c0.1-0.5,0.2-0.9,0.3-1.4
		c0.1-0.3,0.1-0.5,0.2-0.8c0.1-0.5,0.3-0.9,0.4-1.4c0.1-0.2,0.1-0.4,0.2-0.6c0.2-0.6,0.5-1.2,0.8-1.8c0,0,0-0.1,0-0.1
		c0.2-0.3,0.4-0.6,0.5-0.9c0.1-0.2,0.3-0.4,0.4-0.7c0.2-0.3,0.4-0.6,0.6-0.8c0.2-0.2,0.3-0.4,0.4-0.6c0.3-0.3,0.6-0.6,0.9-0.9
		c0.1-0.1,0.2-0.3,0.4-0.4c0.4-0.4,0.9-0.8,1.4-1.1l58.8-42c1.1-0.8,2.2-1.3,3.3-1.7l-16.9,6c-1.2,0.4-2.3,1-3.3,1.7L7,50.5
		c-0.5,0.3-0.9,0.7-1.4,1.1c-0.2,0.1-0.3,0.3-0.4,0.4c-0.3,0.3-0.6,0.5-0.8,0.8c-0.2,0.2-0.3,0.4-0.4,0.6c-0.2,0.3-0.4,0.5-0.6,0.8
		C3.1,54.5,3,54.7,2.9,55c-0.2,0.3-0.4,0.6-0.5,0.9c0,0,0,0,0,0c0,0,0,0.1,0,0.1c-0.3,0.6-0.6,1.2-0.8,1.8c-0.1,0.2-0.2,0.4-0.2,0.6
		c-0.2,0.5-0.3,0.9-0.4,1.4c-0.1,0.3-0.1,0.5-0.2,0.8c-0.1,0.5-0.2,0.9-0.3,1.4c0,0.3-0.1,0.5-0.1,0.8c-0.1,0.5-0.1,1.1-0.2,1.6
		c0,0.2,0,0.3,0,0.5c0,0.1,0,0.1,0,0.2c0,0.2,0,0.5,0,0.7c0,0.4,0,0.8,0,1.2c0,0.3,0,0.5,0,0.8c0,0.4,0.1,0.8,0.1,1.2
		c0,0.3,0.1,0.6,0.1,0.8c0.1,0.4,0.1,0.8,0.2,1.2c0.1,0.3,0.1,0.6,0.2,0.8c0.1,0.4,0.2,0.8,0.3,1.2C1,73.1,1,73.4,1.1,73.6
		c0.1,0.4,0.3,0.8,0.4,1.2c0.1,0.3,0.2,0.5,0.3,0.8c0.2,0.4,0.3,0.9,0.5,1.3c0.1,0.2,0.2,0.4,0.3,0.6c0.3,0.6,0.6,1.3,1,1.9
		l27.7,49.4c2.9,5.1,6.9,8.6,11.3,10.3c1.4,0.6,2.9,0.9,4.4,1c0.5,0,1,0.1,1.5,0.1c0.5,0,1,0,1.4-0.1c1-0.1,1.9-0.3,2.8-0.7l16.9-6
		c-7.4,2.6-16.3-1.6-21.4-10.7L20.5,73.4c-0.3-0.6-0.7-1.3-1-1.9c-0.1-0.2-0.2-0.4-0.3-0.6c-0.2-0.4-0.4-0.9-0.5-1.3
		c-0.1-0.3-0.2-0.5-0.3-0.8c-0.1-0.4-0.3-0.8-0.4-1.2c-0.1-0.3-0.2-0.5-0.2-0.8c-0.1-0.4-0.2-0.8-0.3-1.2c-0.1-0.3-0.1-0.6-0.2-0.8
		c-0.1-0.4-0.1-0.8-0.2-1.2c0-0.3-0.1-0.6-0.1-0.8c0-0.4-0.1-0.8-0.1-1.2c0-0.3,0-0.5,0-0.8c0-0.4,0-0.8,0-1.2c0-0.2,0-0.5,0-0.7
		c0-0.2,0-0.4,0.1-0.7c0-0.5,0.1-1.1,0.2-1.6C17.2,56.4,17.2,56.2,17.3,55.9z"/>
	
		<radialGradient id="SVGID_00000008843448796844085180000005794401842533738168_" cx="-324.666" cy="141.3068" r="191.4474" gradientTransform="matrix(0.9954 9.580000e-02 -9.580000e-02 0.9954 418.2132 77.9179)" gradientUnits="userSpaceOnUse">
		<stop  offset="0.2804" style="stop-color:#008CFF"/>
		<stop  offset="0.5203" style="stop-color:#9435FF"/>
		<stop  offset="0.7036" style="stop-color:#D124DC"/>
		<stop  offset="0.7519" style="stop-color:#D629D1"/>
		<stop  offset="0.8378" style="stop-color:#E235B2"/>
		<stop  offset="0.9507" style="stop-color:#F64981"/>
		<stop  offset="0.9964" style="stop-color:#FF526B"/>
	</radialGradient>
	<path style="fill:url(#SVGID_00000008843448796844085180000005794401842533738168_);" d="M135.1,60.8l-27.7-49.4
		C101.6,0.9,90.5-3,82.7,2.5l-58.8,42c-7.8,5.5-9.3,18.5-3.4,29l27.7,49.4c5.9,10.5,16.9,14.5,24.7,8.9l14.3-10.2l26.5,9.6l3.7-31.1
		l14.3-10.2C139.5,84.2,141,71.2,135.1,60.8z"/>
	
		<linearGradient id="SVGID_00000168795215233859752590000002839144478522769798_" gradientUnits="userSpaceOnUse" x1="289.9775" y1="83.6456" x2="239.7351" y2="244.3112" gradientTransform="matrix(0.9988 -4.990000e-02 4.990000e-02 0.9988 -196.1873 -78.5085)">
		<stop  offset="7.574820e-02" style="stop-color:#FFFFFF"/>
		<stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
	</linearGradient>
	<path style="opacity:0.7;fill:url(#SVGID_00000168795215233859752590000002839144478522769798_);" d="M135.1,60.8l-27.7-49.4
		C101.6,0.9,90.5-3,82.7,2.5l-58.8,42c-7.8,5.5-9.3,18.5-3.4,29l27.7,49.4c5.9,10.5,16.9,14.5,24.7,8.9l14.3-10.2l26.5,9.6l3.7-31.1
		l14.3-10.2C139.5,84.2,141,71.2,135.1,60.8z M131.3,89.2l-14.5,10.4l-3.6,30.6l-26.1-9.4l-14.6,10.4c-2.5,1.8-5.6,2.6-8.7,2.3
		c-5.8-0.6-11.5-4.7-15-11L21.1,73.1c-5.7-10.2-4.2-22.8,3.2-28.1L83.1,3c2.5-1.8,5.6-2.6,8.7-2.3c5.8,0.6,11.4,4.7,15,11l27.7,49.4
		C140.3,71.3,138.8,83.9,131.3,89.2z"/>
	
		<linearGradient id="SVGID_00000147927666777930652710000013759002710984582332_" gradientUnits="userSpaceOnUse" x1="-314.5475" y1="-10.4273" x2="-364.2156" y2="57.0903" gradientTransform="matrix(0.9954 9.580000e-02 -9.580000e-02 0.9954 418.2132 77.9179)">
		<stop  offset="0" style="stop-color:#F2F9FF"/>
		<stop  offset="0.7955" style="stop-color:#C0D6F2"/>
		<stop  offset="0.999" style="stop-color:#B3CDEC"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000147927666777930652710000013759002710984582332_);" d="M94.4,48.3c-4.3-7.7-12.4-10.6-18.1-6.5
		c-3.9,2.8-5.7,8.1-5,13.7c-4.2-3-9.2-3.5-13.1-0.8c-5.7,4.1-6.8,13.6-2.5,21.3c4.6,8.2,12.4,11,18.6,12.5c4.3,1,8.1,1.5,8.5,1.5
		l8.3,0.9L94,82c0.1-0.5,1.4-4.5,2.4-9.3C97.9,65.6,99,56.5,94.4,48.3z"/>
</g>
</svg>
