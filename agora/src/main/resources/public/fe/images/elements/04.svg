<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.4.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 174.9 157.8" style="enable-background:new 0 0 174.9 157.8;" xml:space="preserve">
<style type="text/css">
	.st0{enable-background:new    ;}
	.st1{fill:url(#SVGID_1_);}
	.st2{fill:url(#SVGID_00000068660747255354462740000004613227927540790191_);}
	.st3{fill:url(#SVGID_00000005235815648311877140000000583698692967965340_);}
	.st4{opacity:0.8;}
	.st5{opacity:0;fill:#D7C8FF;}
	.st6{opacity:6.369427e-03;fill:#D6C7FE;}
	.st7{opacity:1.273885e-02;fill:#D5C6FE;}
	.st8{opacity:1.910828e-02;fill:#D4C6FD;}
	.st9{opacity:2.547771e-02;fill:#D3C5FC;}
	.st10{opacity:3.184713e-02;fill:#D2C4FB;}
	.st11{opacity:3.821656e-02;fill:#D1C3FB;}
	.st12{opacity:4.458599e-02;fill:#D0C3FA;}
	.st13{opacity:5.095541e-02;fill:#CFC2F9;}
	.st14{opacity:5.732484e-02;fill:#CEC1F9;}
	.st15{opacity:6.369427e-02;fill:#CDC0F8;}
	.st16{opacity:7.006370e-02;fill:#CCC0F7;}
	.st17{opacity:7.643312e-02;fill:#CBBFF6;}
	.st18{opacity:8.280255e-02;fill:#CABEF6;}
	.st19{opacity:8.917198e-02;fill:#C9BDF5;}
	.st20{opacity:9.554140e-02;fill:#C8BDF4;}
	.st21{opacity:0.1019;fill:#C7BCF3;}
	.st22{opacity:0.1083;fill:#C6BBF3;}
	.st23{opacity:0.1146;fill:#C5BAF2;}
	.st24{opacity:0.121;fill:#C4B9F1;}
	.st25{opacity:0.1274;fill:#C3B9F1;}
	.st26{opacity:0.1338;fill:#C2B8F0;}
	.st27{opacity:0.1401;fill:#C1B7EF;}
	.st28{opacity:0.1465;fill:#C0B6EE;}
	.st29{opacity:0.1529;fill:#BFB6EE;}
	.st30{opacity:0.1592;fill:#BEB5ED;}
	.st31{opacity:0.1656;fill:#BDB4EC;}
	.st32{opacity:0.172;fill:#BCB3EC;}
	.st33{opacity:0.1783;fill:#BBB3EB;}
	.st34{opacity:0.1847;fill:#BAB2EA;}
	.st35{opacity:0.1911;fill:#B9B1E9;}
	.st36{opacity:0.1975;fill:#B8B0E9;}
	.st37{opacity:0.2038;fill:#B7B0E8;}
	.st38{opacity:0.2102;fill:#B6AFE7;}
	.st39{opacity:0.2166;fill:#B5AEE7;}
	.st40{opacity:0.2229;fill:#B4ADE6;}
	.st41{opacity:0.2293;fill:#B3ACE5;}
	.st42{opacity:0.2357;fill:#B2ACE4;}
	.st43{opacity:0.242;fill:#B1ABE4;}
	.st44{opacity:0.2484;fill:#B0AAE3;}
	.st45{opacity:0.2548;fill:#AFA9E2;}
	.st46{opacity:0.2611;fill:#AEA9E1;}
	.st47{opacity:0.2675;fill:#ADA8E1;}
	.st48{opacity:0.2739;fill:#ACA7E0;}
	.st49{opacity:0.2803;fill:#ABA6DF;}
	.st50{opacity:0.2866;fill:#AAA6DF;}
	.st51{opacity:0.293;fill:#A9A5DE;}
	.st52{opacity:0.2994;fill:#A8A4DD;}
	.st53{opacity:0.3057;fill:#A7A3DC;}
	.st54{opacity:0.3121;fill:#A6A3DC;}
	.st55{opacity:0.3185;fill:#A5A2DB;}
	.st56{opacity:0.3248;fill:#A4A1DA;}
	.st57{opacity:0.3312;fill:#A3A0DA;}
	.st58{opacity:0.3376;fill:#A29FD9;}
	.st59{opacity:0.3439;fill:#A19FD8;}
	.st60{opacity:0.3503;fill:#A09ED7;}
	.st61{opacity:0.3567;fill:#9F9DD7;}
	.st62{opacity:0.3631;fill:#9E9CD6;}
	.st63{opacity:0.3694;fill:#9D9CD5;}
	.st64{opacity:0.3758;fill:#9C9BD5;}
	.st65{opacity:0.3822;fill:#9B9AD4;}
	.st66{opacity:0.3885;fill:#9A99D3;}
	.st67{opacity:0.3949;fill:#9999D2;}
	.st68{opacity:0.4013;fill:#9898D2;}
	.st69{opacity:0.4076;fill:#9797D1;}
	.st70{opacity:0.414;fill:#9696D0;}
	.st71{opacity:0.4204;fill:#9596CF;}
	.st72{opacity:0.4268;fill:#9495CF;}
	.st73{opacity:0.4331;fill:#9394CE;}
	.st74{opacity:0.4395;fill:#9293CD;}
	.st75{opacity:0.4459;fill:#9192CD;}
	.st76{opacity:0.4522;fill:#9092CC;}
	.st77{opacity:0.4586;fill:#8F91CB;}
	.st78{opacity:0.465;fill:#8E90CA;}
	.st79{opacity:0.4713;fill:#8D8FCA;}
	.st80{opacity:0.4777;fill:#8C8FC9;}
	.st81{opacity:0.4841;fill:#8B8EC8;}
	.st82{opacity:0.4904;fill:#8A8DC8;}
	.st83{opacity:0.4968;fill:#898CC7;}
	.st84{opacity:0.5032;fill:#888CC6;}
	.st85{opacity:0.5096;fill:#878BC5;}
	.st86{opacity:0.5159;fill:#868AC5;}
	.st87{opacity:0.5223;fill:#8589C4;}
	.st88{opacity:0.5287;fill:#8489C3;}
	.st89{opacity:0.535;fill:#8388C3;}
	.st90{opacity:0.5414;fill:#8287C2;}
	.st91{opacity:0.5478;fill:#8186C1;}
	.st92{opacity:0.5541;fill:#8086C0;}
	.st93{opacity:0.5605;fill:#7F85C0;}
	.st94{opacity:0.5669;fill:#7E84BF;}
	.st95{opacity:0.5732;fill:#7D83BE;}
	.st96{opacity:0.5796;fill:#7C82BE;}
	.st97{opacity:0.586;fill:#7B82BD;}
	.st98{opacity:0.5924;fill:#7A81BC;}
	.st99{opacity:0.5987;fill:#7980BB;}
	.st100{opacity:0.6051;fill:#787FBB;}
	.st101{opacity:0.6115;fill:#777FBA;}
	.st102{opacity:0.6178;fill:#767EB9;}
	.st103{opacity:0.6242;fill:#757DB8;}
	.st104{opacity:0.6306;fill:#747CB8;}
	.st105{opacity:0.6369;fill:#737CB7;}
	.st106{opacity:0.6433;fill:#727BB6;}
	.st107{opacity:0.6497;fill:#717AB6;}
	.st108{opacity:0.6561;fill:#7079B5;}
	.st109{opacity:0.6624;fill:#6F79B4;}
	.st110{opacity:0.6688;fill:#6E78B3;}
	.st111{opacity:0.6752;fill:#6D77B3;}
	.st112{opacity:0.6815;fill:#6C76B2;}
	.st113{opacity:0.6879;fill:#6B75B1;}
	.st114{opacity:0.6943;fill:#6A75B1;}
	.st115{opacity:0.7006;fill:#6974B0;}
	.st116{opacity:0.707;fill:#6873AF;}
	.st117{opacity:0.7134;fill:#6772AE;}
	.st118{opacity:0.7197;fill:#6672AE;}
	.st119{opacity:0.7261;fill:#6571AD;}
	.st120{opacity:0.7325;fill:#6470AC;}
	.st121{opacity:0.7389;fill:#636FAC;}
	.st122{opacity:0.7452;fill:#626FAB;}
	.st123{opacity:0.7516;fill:#616EAA;}
	.st124{opacity:0.758;fill:#606DA9;}
	.st125{opacity:0.7643;fill:#5F6CA9;}
	.st126{opacity:0.7707;fill:#5E6CA8;}
	.st127{opacity:0.7771;fill:#5D6BA7;}
	.st128{opacity:0.7834;fill:#5C6AA6;}
	.st129{opacity:0.7898;fill:#5B69A6;}
	.st130{opacity:0.7962;fill:#5A68A5;}
	.st131{opacity:0.8025;fill:#5968A4;}
	.st132{opacity:0.8089;fill:#5867A4;}
	.st133{opacity:0.8153;fill:#5766A3;}
	.st134{opacity:0.8217;fill:#5665A2;}
	.st135{opacity:0.828;fill:#5565A1;}
	.st136{opacity:0.8344;fill:#5464A1;}
	.st137{opacity:0.8408;fill:#5363A0;}
	.st138{opacity:0.8471;fill:#52629F;}
	.st139{opacity:0.8535;fill:#51629F;}
	.st140{opacity:0.8599;fill:#50619E;}
	.st141{opacity:0.8662;fill:#4F609D;}
	.st142{opacity:0.8726;fill:#4E5F9C;}
	.st143{opacity:0.879;fill:#4D5F9C;}
	.st144{opacity:0.8854;fill:#4C5E9B;}
	.st145{opacity:0.8917;fill:#4B5D9A;}
	.st146{opacity:0.8981;fill:#4A5C9A;}
	.st147{opacity:0.9045;fill:#495B99;}
	.st148{opacity:0.9108;fill:#485B98;}
	.st149{opacity:0.9172;fill:#475A97;}
	.st150{opacity:0.9236;fill:#465997;}
	.st151{opacity:0.9299;fill:#455896;}
	.st152{opacity:0.9363;fill:#445895;}
	.st153{opacity:0.9427;fill:#435794;}
	.st154{opacity:0.949;fill:#425694;}
	.st155{opacity:0.9554;fill:#415593;}
	.st156{opacity:0.9618;fill:#405592;}
	.st157{opacity:0.9682;fill:#3F5492;}
	.st158{opacity:0.9745;fill:#3E5391;}
	.st159{opacity:0.9809;fill:#3D5290;}
	.st160{opacity:0.9873;fill:#3C528F;}
	.st161{opacity:0.9936;fill:#3B518F;}
	.st162{fill:#3A508E;}
	.st163{clip-path:url(#SVGID_00000137818904911540909470000013966589907257901725_);enable-background:new    ;}
	.st164{fill:#70166B;}
	.st165{fill:#74176F;}
	.st166{fill:#781873;}
	.st167{fill:#7D1977;}
	.st168{fill:#811A7B;}
	.st169{fill:#851B7F;}
	.st170{fill:#8A1C83;}
	.st171{fill:#8E1C87;}
	.st172{fill:#921D8B;}
	.st173{clip-path:url(#SVGID_00000155122501167576116620000000577175529572804484_);enable-background:new    ;}
	.st174{fill:#616B85;}
	.st175{fill:#656F8A;}
	.st176{fill:#69738F;}
	.st177{fill:#6C7794;}
	.st178{fill:#707B99;}
	.st179{fill:#747F9E;}
	.st180{fill:#7883A3;}
	.st181{fill:#7B87A8;}
	.st182{fill:#7F8BAD;}
	.st183{clip-path:url(#SVGID_00000101086601274508917010000002066889395448949951_);enable-background:new    ;}
	.st184{fill:#9D1F95;}
	.st185{clip-path:url(#SVGID_00000100367222557209542750000005527343656717389988_);enable-background:new    ;}
	.st186{clip-path:url(#SVGID_00000055680059053033292130000005354197715249956237_);enable-background:new    ;}
	.st187{fill:#767385;}
	.st188{fill:#6C1667;}
	.st189{clip-path:url(#SVGID_00000019680079846707815780000000559960060532730286_);enable-background:new    ;}
	.st190{fill:#7C798C;}
	.st191{clip-path:url(#SVGID_00000124123968110157995690000004956082044807125944_);enable-background:new    ;}
	.st192{fill:#918DA3;}
	.st193{fill:#9592A8;}
	.st194{fill:#9A96AD;}
	.st195{fill:#8C899E;}
	.st196{fill:#888599;}
	.st197{fill:#838094;}
	.st198{fill:#7F7C8F;}
	.st199{fill:#7A778A;}
	.st200{fill:url(#SVGID_00000070822707054140031080000015080221930855533444_);}
	.st201{fill:url(#SVGID_00000070098633936852627330000014337289128579453320_);}
	.st202{fill:url(#SVGID_00000155115604523083866490000008755999302406932141_);}
	.st203{fill:url(#SVGID_00000078027088818298565570000003519779565890921619_);}
	.st204{fill:url(#SVGID_00000008832742230028328490000015883145822818180489_);}
	.st205{opacity:0;fill:#012B7E;}
	.st206{opacity:2.941176e-02;fill:#012B7E;}
	.st207{opacity:5.882353e-02;fill:#012B7E;}
	.st208{opacity:8.823530e-02;fill:#012B7E;}
	.st209{opacity:0.1176;fill:#012B7E;}
	.st210{opacity:0.1471;fill:#012B7E;}
	.st211{opacity:0.1765;fill:#012B7E;}
	.st212{opacity:0.2059;fill:#012B7E;}
	.st213{opacity:0.2353;fill:#012B7E;}
	.st214{opacity:0.2647;fill:#012B7E;}
	.st215{opacity:0.2941;fill:#012B7E;}
	.st216{opacity:0.3235;fill:#012B7E;}
	.st217{opacity:0.3529;fill:#012B7E;}
	.st218{opacity:0.3824;fill:#012B7E;}
	.st219{opacity:0.4118;fill:#012B7E;}
	.st220{opacity:0.4412;fill:#012B7E;}
	.st221{opacity:0.4706;fill:#012B7E;}
	.st222{opacity:0.5;fill:#012B7E;}
	.st223{opacity:0.5294;fill:#012B7E;}
	.st224{opacity:0.5588;fill:#012B7E;}
	.st225{opacity:0.5882;fill:#012B7E;}
	.st226{opacity:0.6176;fill:#012B7E;}
	.st227{opacity:0.6471;fill:#012B7E;}
	.st228{opacity:0.6765;fill:#012B7E;}
	.st229{opacity:0.7059;fill:#012B7E;}
	.st230{opacity:0.7353;fill:#012B7E;}
	.st231{opacity:0.7647;fill:#012B7E;}
	.st232{opacity:0.7941;fill:#012B7E;}
	.st233{opacity:0.8235;fill:#012B7E;}
	.st234{opacity:0.8529;fill:#012B7E;}
	.st235{opacity:0.8824;fill:#012B7E;}
	.st236{opacity:0.9118;fill:#012B7E;}
	.st237{opacity:0.9412;fill:#012B7E;}
	.st238{opacity:0.9706;fill:#012B7E;}
	.st239{fill:#012B7E;}
	.st240{fill:url(#SVGID_00000106145022311921256100000007406332794588692915_);}
	.st241{fill:url(#SVGID_00000034057589515847297070000006514479349249741481_);}
	.st242{fill:url(#SVGID_00000018237445562031406740000004175275807758539176_);}
	.st243{fill:url(#SVGID_00000077303375015240071870000018389762371207760276_);}
	.st244{fill:url(#SVGID_00000008130263970707283430000011153060582877302461_);}
	.st245{fill:url(#SVGID_00000171697637868243096240000009622027073758688907_);}
	.st246{fill:url(#SVGID_00000048478937329049946920000003794312210647502749_);}
</style>
<g class="st0">
	<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="77.4604" y1="181.6132" x2="56.0715" y2="69.148">
		<stop  offset="6.655080e-02" style="stop-color:#005BC3"/>
		<stop  offset="0.2702" style="stop-color:#2075D1"/>
		<stop  offset="0.7144" style="stop-color:#6FB8F4"/>
		<stop  offset="0.8184" style="stop-color:#83C8FD"/>
	</linearGradient>
	<path class="st1" d="M17.2,79.8l-16,28c-2.7,4.8-0.9,10.2,4.2,12.1l94.9,35.3c5.1,1.9,11.4-0.5,14.2-5.3l16-28L17.2,79.8z"/>
	
		<linearGradient id="SVGID_00000106845452304398432950000004817403902261073065_" gradientUnits="userSpaceOnUse" x1="65.15" y1="5.0873" x2="127.7811" y2="119.1359">
		<stop  offset="0" style="stop-color:#F2F9FF"/>
		<stop  offset="0.2269" style="stop-color:#EEF1FF"/>
		<stop  offset="0.5763" style="stop-color:#E1DCFF"/>
		<stop  offset="0.9972" style="stop-color:#CEB9FF"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000106845452304398432950000004817403902261073065_);" d="M168.6,36c5.1,1.9,7,7.3,4.2,12.1
		l-42.3,73.9L17.3,79.8L59.5,5.9c2.7-4.8,9.1-7.2,14.2-5.3L168.6,36z"/>
	
		<linearGradient id="SVGID_00000152229812607789316850000001131276869551483561_" gradientUnits="userSpaceOnUse" x1="79.9741" y1="-3.1509" x2="93.4286" y2="151.402">
		<stop  offset="7.574820e-02" style="stop-color:#FFFFFF"/>
		<stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000152229812607789316850000001131276869551483561_);" d="M168.6,36L73.7,0.7
		c-5.1-1.9-11.4,0.5-14.2,5.3L17.3,79.8l0.4,0.2l-0.5-0.2l-16,28c-2.7,4.8-0.9,10.2,4.2,12.1l94.9,35.3c5.1,1.9,11.4-0.5,14.2-5.3
		l16-28l-0.2-0.1l0.2,0l42.3-73.9C175.5,43.3,173.7,37.8,168.6,36z M172.5,47.9l-42.3,73.9l0.1-0.1l-0.1,0.3l-0.1-0.2l-16,28
		c-2,3.5-6,5.8-10.2,5.8c-1.3,0-2.5-0.2-3.6-0.6L5.6,119.6c-2.3-0.9-4-2.5-4.8-4.7c-0.8-2.2-0.5-4.7,0.8-7l15.9-27.7l0.2,0.1
		l0.2-0.6l-0.1-0.1L59.8,6.1c2-3.5,6-5.8,10.2-5.8c1.3,0,2.5,0.2,3.6,0.6l94.9,35.3c2.3,0.9,4,2.5,4.8,4.7
		C174.1,43.1,173.8,45.6,172.5,47.9z"/>
	<g class="st4">
		<path class="st5" d="M115.5,39.9c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.9,61.3,130.4,45.4,115.5,39.9z"/>
		<path class="st6" d="M115.5,39.9c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.8,61.2,130.3,45.4,115.5,39.9z"/>
		<path class="st7" d="M115.5,39.9c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.8,61.2,130.3,45.4,115.5,39.9z"/>
		<path class="st8" d="M115.5,39.9c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.8,61.2,130.3,45.4,115.5,39.9z"/>
		<path class="st9" d="M115.5,39.8c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.8,61.2,130.3,45.3,115.5,39.8z"/>
		<path class="st10" d="M115.5,39.8c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.8,61.1,130.3,45.3,115.5,39.8z"/>
		<path class="st11" d="M115.4,39.8c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.7,61.1,130.2,45.3,115.4,39.8z"/>
		<path class="st12" d="M115.4,39.8c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.7,61.1,130.2,45.3,115.4,39.8z"/>
		<path class="st13" d="M115.4,39.7c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.7,61.1,130.2,45.2,115.4,39.7z"/>
		<path class="st14" d="M115.4,39.7c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.7,61,130.2,45.2,115.4,39.7z"/>
		<path class="st15" d="M115.4,39.7c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.7,61,130.2,45.2,115.4,39.7z"/>
		<path class="st16" d="M115.4,39.7c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.7,61,130.2,45.2,115.4,39.7z"/>
		<path class="st17" d="M115.3,39.6C100.5,34.1,82.1,41,74,55c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.6,61,130.1,45.1,115.3,39.6z"/>
		<path class="st18" d="M115.3,39.6C100.5,34.1,82,41,74,55c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.6,60.9,130.1,45.1,115.3,39.6z"/>
		<path class="st19" d="M115.3,39.6C100.5,34.1,82,41,74,55c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.6,60.9,130.1,45.1,115.3,39.6z"/>
		<path class="st20" d="M115.3,39.6C100.5,34.1,82,41,74,55c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.6,60.9,130.1,45.1,115.3,39.6z"/>
		<path class="st21" d="M115.3,39.5C100.5,34,82,40.9,74,55c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.6,60.9,130.1,45,115.3,39.5z"/>
		<path class="st22" d="M115.3,39.5C100.5,34,82,40.9,74,54.9c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.6,60.9,130.1,45,115.3,39.5z"/>
		<path class="st23" d="M115.2,39.5c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.5,60.8,130,45,115.2,39.5z"/>
		<path class="st24" d="M115.2,39.5c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.5,60.8,130,45,115.2,39.5z"/>
		<path class="st25" d="M115.2,39.4c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.5,60.8,130,45,115.2,39.4z"/>
		<path class="st26" d="M115.2,39.4c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.5,60.8,130,44.9,115.2,39.4z"/>
		<path class="st27" d="M115.2,39.4c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.5,60.7,130,44.9,115.2,39.4z"/>
		<path class="st28" d="M115.2,39.4c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.5,60.7,130,44.9,115.2,39.4z"/>
		<path class="st29" d="M115.1,39.4c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.4,60.7,129.9,44.9,115.1,39.4z"/>
		<path class="st30" d="M115.1,39.3c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.4,60.7,129.9,44.8,115.1,39.3z"/>
		<path class="st31" d="M115.1,39.3c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.4,60.6,129.9,44.8,115.1,39.3z"/>
		<path class="st32" d="M115.1,39.3c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.4,60.6,129.9,44.8,115.1,39.3z"/>
		<path class="st33" d="M115.1,39.3c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.4,60.6,129.9,44.8,115.1,39.3z"/>
		<path class="st34" d="M115.1,39.2c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.4,60.6,129.9,44.7,115.1,39.2z"/>
		<path class="st35" d="M115,39.2c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.3,60.5,129.8,44.7,115,39.2z"/>
		<path class="st36" d="M115,39.2c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.3,60.5,129.8,44.7,115,39.2z"/>
		<path class="st37" d="M115,39.2c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.3,60.5,129.8,44.7,115,39.2z"/>
		<path class="st38" d="M115,39.1c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.3,60.5,129.8,44.6,115,39.1z"/>
		<path class="st39" d="M115,39.1c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.3,60.4,129.8,44.6,115,39.1z"/>
		<path class="st40" d="M115,39.1c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.3,60.4,129.8,44.6,115,39.1z"/>
		<path class="st41" d="M114.9,39.1c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.2,60.4,129.7,44.6,114.9,39.1z"/>
		<path class="st42" d="M114.9,39c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.2,60.4,129.7,44.6,114.9,39z"/>
		<path class="st43" d="M114.9,39c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.2,60.4,129.7,44.5,114.9,39z"/>
		<path class="st44" d="M114.9,39c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.2,60.3,129.7,44.5,114.9,39z"/>
		<path class="st45" d="M114.9,39c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.2,60.3,129.7,44.5,114.9,39z"/>
		<path class="st46" d="M114.8,39c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.2,60.3,129.7,44.5,114.8,39z"/>
		<path class="st47" d="M114.8,38.9c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.1,60.3,129.6,44.4,114.8,38.9z"/>
		<path class="st48" d="M114.8,38.9c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.1,60.2,129.6,44.4,114.8,38.9z"/>
		<path class="st49" d="M114.8,38.9c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.1,60.2,129.6,44.4,114.8,38.9z"/>
		<path class="st50" d="M114.8,38.9c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.1,60.2,129.6,44.4,114.8,38.9z"/>
		<path class="st51" d="M114.8,38.8c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.1,60.2,129.6,44.3,114.8,38.8z"/>
		<path class="st52" d="M114.7,38.8c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C135.1,60.1,129.5,44.3,114.7,38.8z"/>
		<path class="st53" d="M114.7,38.8c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3C100.5,95,119,88.1,127,74.1
			C135,60.1,129.5,44.3,114.7,38.8z"/>
		<path class="st54" d="M114.7,38.8c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3C100.5,95,119,88.1,127,74.1
			C135,60.1,129.5,44.3,114.7,38.8z"/>
		<path class="st55" d="M114.7,38.7c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3C100.5,95,119,88.1,127,74.1
			C135,60.1,129.5,44.2,114.7,38.7z"/>
		<path class="st56" d="M114.7,38.7c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3C100.5,95,119,88.1,127,74.1
			C135,60,129.5,44.2,114.7,38.7z"/>
		<path class="st57" d="M114.7,38.7c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3C100.4,94.9,118.9,88,127,74
			C135,60,129.5,44.2,114.7,38.7z"/>
		<path class="st58" d="M114.6,38.7c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.9,60,129.4,44.2,114.6,38.7z"/>
		<path class="st59" d="M114.6,38.6C99.8,33.1,81.3,40,73.3,54c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.9,60,129.4,44.1,114.6,38.6z"/>
		<path class="st60" d="M114.6,38.6C99.8,33.1,81.3,40,73.3,54c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.9,59.9,129.4,44.1,114.6,38.6z"/>
		<path class="st61" d="M114.6,38.6C99.8,33.1,81.3,40,73.3,54c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.9,59.9,129.4,44.1,114.6,38.6z"/>
		<path class="st62" d="M114.6,38.6C99.8,33.1,81.3,40,73.3,54c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.9,59.9,129.4,44.1,114.6,38.6z"/>
		<path class="st63" d="M114.6,38.5C99.8,33,81.3,39.9,73.3,54c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.9,59.9,129.4,44.1,114.6,38.5z"/>
		<path class="st64" d="M114.5,38.5C99.7,33,81.3,39.9,73.2,53.9c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.8,59.9,129.3,44,114.5,38.5z"/>
		<path class="st65" d="M114.5,38.5C99.7,33,81.2,39.9,73.2,53.9c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.8,59.8,129.3,44,114.5,38.5z"/>
		<path class="st66" d="M114.5,38.5C99.7,33,81.2,39.9,73.2,53.9c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.8,59.8,129.3,44,114.5,38.5z"/>
		<path class="st67" d="M114.5,38.5C99.7,33,81.2,39.9,73.2,53.9c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.8,59.8,129.3,44,114.5,38.5z"/>
		<path class="st68" d="M114.5,38.4c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.8,59.8,129.3,43.9,114.5,38.4z"/>
		<path class="st69" d="M114.5,38.4c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.8,59.7,129.3,43.9,114.5,38.4z"/>
		<path class="st70" d="M114.4,38.4c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.7,59.7,129.2,43.9,114.4,38.4z"/>
		<path class="st71" d="M114.4,38.4c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.7,59.7,129.2,43.9,114.4,38.4z"/>
		<path class="st72" d="M114.4,38.3c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.7,59.7,129.2,43.8,114.4,38.3z"/>
		<path class="st73" d="M114.4,38.3c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.7,59.6,129.2,43.8,114.4,38.3z"/>
		<path class="st74" d="M114.4,38.3c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.7,59.6,129.2,43.8,114.4,38.3z"/>
		<path class="st75" d="M114.4,38.3c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.7,59.6,129.2,43.8,114.4,38.3z"/>
		<path class="st76" d="M114.3,38.2C99.5,32.7,81,39.6,73,53.6c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.6,59.6,129.1,43.7,114.3,38.2z"/>
		<path class="st77" d="M114.3,38.2C99.5,32.7,81,39.6,73,53.6c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.6,59.5,129.1,43.7,114.3,38.2z"/>
		<path class="st78" d="M114.3,38.2C99.5,32.7,81,39.6,73,53.6c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.6,59.5,129.1,43.7,114.3,38.2z"/>
		<path class="st79" d="M114.3,38.2C99.5,32.7,81,39.6,73,53.6c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.6,59.5,129.1,43.7,114.3,38.2z"/>
		<path class="st80" d="M114.3,38.1C99.5,32.6,81,39.5,73,53.6c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.6,59.5,129.1,43.6,114.3,38.1z"/>
		<path class="st81" d="M114.3,38.1C99.5,32.6,81,39.5,72.9,53.5c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.6,59.4,129.1,43.6,114.3,38.1z"/>
		<path class="st82" d="M114.2,38.1c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.5,59.4,129,43.6,114.2,38.1z"/>
		<path class="st83" d="M114.2,38.1c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.5,59.4,129,43.6,114.2,38.1z"/>
		<path class="st84" d="M114.2,38c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.5,59.4,129,43.6,114.2,38z"/>
		<path class="st85" d="M114.2,38c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.5,59.4,129,43.5,114.2,38z"/>
		<path class="st86" d="M114.2,38c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.5,59.3,129,43.5,114.2,38z"/>
		<path class="st87" d="M114.2,38c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.5,59.3,129,43.5,114.2,38z"/>
		<path class="st88" d="M114.1,38c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.4,59.3,128.9,43.5,114.1,38z"/>
		<path class="st89" d="M114.1,37.9c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.4,59.3,128.9,43.4,114.1,37.9z"/>
		<path class="st90" d="M114.1,37.9c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.4,59.2,128.9,43.4,114.1,37.9z"/>
		<path class="st91" d="M114.1,37.9c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.4,59.2,128.9,43.4,114.1,37.9z"/>
		<path class="st92" d="M114.1,37.9c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.4,59.2,128.9,43.4,114.1,37.9z"/>
		<path class="st93" d="M114,37.8c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.4,59.2,128.9,43.3,114,37.8z"/>
		<path class="st94" d="M114,37.8c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.3,59.1,128.8,43.3,114,37.8z"/>
		<path class="st95" d="M114,37.8c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.3,59.1,128.8,43.3,114,37.8z"/>
		<path class="st96" d="M114,37.8c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.3,59.1,128.8,43.3,114,37.8z"/>
		<path class="st97" d="M114,37.7c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.3,59.1,128.8,43.2,114,37.7z"/>
		<path class="st98" d="M114,37.7c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.3,59,128.8,43.2,114,37.7z"/>
		<path class="st99" d="M113.9,37.7c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3C99.7,94,118.2,87,126.2,73
			C134.3,59,128.7,43.2,113.9,37.7z"/>
		<path class="st100" d="M113.9,37.7c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.2,59,128.7,43.2,113.9,37.7z"/>
		<path class="st101" d="M113.9,37.6C99.1,32.1,80.6,39,72.6,53.1c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.2,59,128.7,43.2,113.9,37.6z"/>
		<path class="st102" d="M113.9,37.6C99.1,32.1,80.6,39,72.6,53c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.2,59,128.7,43.1,113.9,37.6z"/>
		<path class="st103" d="M113.9,37.6C99.1,32.1,80.6,39,72.6,53c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.2,58.9,128.7,43.1,113.9,37.6z"/>
		<path class="st104" d="M113.9,37.6C99.1,32.1,80.6,39,72.6,53c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.2,58.9,128.7,43.1,113.9,37.6z"/>
		<path class="st105" d="M113.8,37.6C99.1,32,80.6,38.9,72.5,53c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.1,58.9,128.6,43.1,113.8,37.6z"/>
		<path class="st106" d="M113.8,37.5C99,32,80.5,38.9,72.5,52.9c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.1,58.9,128.6,43,113.8,37.5z"/>
		<path class="st107" d="M113.8,37.5C99,32,80.5,38.9,72.5,52.9c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.1,58.8,128.6,43,113.8,37.5z"/>
		<path class="st108" d="M113.8,37.5C99,32,80.5,38.9,72.5,52.9c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.1,58.8,128.6,43,113.8,37.5z"/>
		<path class="st109" d="M113.8,37.5C99,32,80.5,38.9,72.5,52.9c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.1,58.8,128.6,43,113.8,37.5z"/>
		<path class="st110" d="M113.8,37.4C99,31.9,80.5,38.8,72.5,52.8c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134.1,58.8,128.6,42.9,113.8,37.4z"/>
		<path class="st111" d="M113.7,37.4c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134,58.7,128.5,42.9,113.7,37.4z"/>
		<path class="st112" d="M113.7,37.4c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134,58.7,128.5,42.9,113.7,37.4z"/>
		<path class="st113" d="M113.7,37.4c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134,58.7,128.5,42.9,113.7,37.4z"/>
		<path class="st114" d="M113.7,37.3c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134,58.7,128.5,42.8,113.7,37.3z"/>
		<path class="st115" d="M113.7,37.3c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134,58.6,128.5,42.8,113.7,37.3z"/>
		<path class="st116" d="M113.7,37.3c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C134,58.6,128.5,42.8,113.7,37.3z"/>
		<path class="st117" d="M113.6,37.3c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.9,58.6,128.4,42.8,113.6,37.3z"/>
		<path class="st118" d="M113.6,37.2c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.9,58.6,128.4,42.7,113.6,37.2z"/>
		<path class="st119" d="M113.6,37.2c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.9,58.5,128.4,42.7,113.6,37.2z"/>
		<path class="st120" d="M113.6,37.2c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.9,58.5,128.4,42.7,113.6,37.2z"/>
		<path class="st121" d="M113.6,37.2c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.9,58.5,128.4,42.7,113.6,37.2z"/>
		<path class="st122" d="M113.6,37.1c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.9,58.5,128.4,42.7,113.6,37.1z"/>
		<path class="st123" d="M113.5,37.1c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.8,58.5,128.3,42.6,113.5,37.1z"/>
		<path class="st124" d="M113.5,37.1c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.8,58.4,128.3,42.6,113.5,37.1z"/>
		<path class="st125" d="M113.5,37.1c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.8,58.4,128.3,42.6,113.5,37.1z"/>
		<path class="st126" d="M113.5,37.1c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.8,58.4,128.3,42.6,113.5,37.1z"/>
		<path class="st127" d="M113.5,37c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.8,58.4,128.3,42.5,113.5,37z"/>
		<path class="st128" d="M113.5,37c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.8,58.3,128.3,42.5,113.5,37z"/>
		<path class="st129" d="M113.4,37c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.7,58.3,128.2,42.5,113.4,37z"/>
		<path class="st130" d="M113.4,37c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.7,58.3,128.2,42.5,113.4,37z"/>
		<path class="st131" d="M113.4,36.9c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.7,58.3,128.2,42.4,113.4,36.9z"/>
		<path class="st132" d="M113.4,36.9c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.7,58.2,128.2,42.4,113.4,36.9z"/>
		<path class="st133" d="M113.4,36.9c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.7,58.2,128.2,42.4,113.4,36.9z"/>
		<path class="st134" d="M113.4,36.9C98.6,31.4,80.1,38.3,72,52.3c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.7,58.2,128.2,42.4,113.4,36.9z"/>
		<path class="st135" d="M113.3,36.8C98.5,31.3,80,38.2,72,52.2c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.6,58.2,128.1,42.3,113.3,36.8z"/>
		<path class="st136" d="M113.3,36.8C98.5,31.3,80,38.2,72,52.2c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.6,58.1,128.1,42.3,113.3,36.8z"/>
		<path class="st137" d="M113.3,36.8C98.5,31.3,80,38.2,72,52.2c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.6,58.1,128.1,42.3,113.3,36.8z"/>
		<path class="st138" d="M113.3,36.8C98.5,31.3,80,38.2,72,52.2c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.6,58.1,128.1,42.3,113.3,36.8z"/>
		<path class="st139" d="M113.3,36.7C98.5,31.2,80,38.1,72,52.2c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.6,58.1,128.1,42.2,113.3,36.7z"/>
		<path class="st140" d="M113.2,36.7C98.5,31.2,80,38.1,71.9,52.1c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.6,58,128.1,42.2,113.2,36.7z"/>
		<path class="st141" d="M113.2,36.7c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3C99,93,117.5,86.1,125.5,72
			C133.5,58,128,42.2,113.2,36.7z"/>
		<path class="st142" d="M113.2,36.7c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3C99,92.9,117.5,86,125.5,72
			C133.5,58,128,42.2,113.2,36.7z"/>
		<path class="st143" d="M113.2,36.6C98.4,31.1,79.9,38,71.9,52.1c-8,14-2.5,29.8,12.3,35.3C99,92.9,117.5,86,125.5,72
			C133.5,58,128,42.2,113.2,36.6z"/>
		<path class="st144" d="M113.2,36.6C98.4,31.1,79.9,38,71.9,52c-8,14-2.5,29.8,12.3,35.3C99,92.9,117.5,86,125.5,72
			C133.5,58,128,42.1,113.2,36.6z"/>
		<path class="st145" d="M113.2,36.6C98.4,31.1,79.9,38,71.9,52c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.5,57.9,128,42.1,113.2,36.6z"/>
		<path class="st146" d="M113.1,36.6C98.4,31.1,79.9,38,71.8,52c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.4,57.9,127.9,42.1,113.1,36.6z"/>
		<path class="st147" d="M113.1,36.6C98.3,31.1,79.8,38,71.8,52c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.4,57.9,127.9,42.1,113.1,36.6z"/>
		<path class="st148" d="M113.1,36.5C98.3,31,79.8,37.9,71.8,51.9c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.4,57.9,127.9,42,113.1,36.5z"/>
		<path class="st149" d="M113.1,36.5C98.3,31,79.8,37.9,71.8,51.9c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.4,57.8,127.9,42,113.1,36.5z"/>
		<path class="st150" d="M113.1,36.5C98.3,31,79.8,37.9,71.8,51.9c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.4,57.8,127.9,42,113.1,36.5z"/>
		<path class="st151" d="M113.1,36.5C98.3,31,79.8,37.9,71.8,51.9c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.4,57.8,127.9,42,113.1,36.5z"/>
		<path class="st152" d="M113,36.4c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.3,57.8,127.8,41.9,113,36.4z"/>
		<path class="st153" d="M113,36.4c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.3,57.7,127.8,41.9,113,36.4z"/>
		<path class="st154" d="M113,36.4c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.3,57.7,127.8,41.9,113,36.4z"/>
		<path class="st155" d="M113,36.4c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.3,57.7,127.8,41.9,113,36.4z"/>
		<path class="st156" d="M113,36.3c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.3,57.7,127.8,41.8,113,36.3z"/>
		<path class="st157" d="M113,36.3c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.3,57.6,127.8,41.8,113,36.3z"/>
		<path class="st158" d="M112.9,36.3c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.2,57.6,127.7,41.8,112.9,36.3z"/>
		<path class="st159" d="M112.9,36.3c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.2,57.6,127.7,41.8,112.9,36.3z"/>
		<path class="st160" d="M112.9,36.2c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.2,57.6,127.7,41.8,112.9,36.2z"/>
		<path class="st161" d="M112.9,36.2c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.2,57.6,127.7,41.7,112.9,36.2z"/>
		<path class="st162" d="M112.9,36.2c-14.8-5.5-33.3,1.4-41.3,15.4c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4
			C133.2,57.5,127.7,41.7,112.9,36.2z"/>
	</g>
	<g class="st0">
		<defs>
			<path id="SVGID_00000119098447274543182120000015362640535195515821_" class="st0" d="M97.5,46.2l0.9,2c-0.1-0.1-0.2-0.2-0.4-0.3
				c-0.5-0.2-1.1,0-1.4,0.5l-0.9-2c0.3-0.5,0.9-0.7,1.4-0.5C97.3,45.9,97.4,46,97.5,46.2z"/>
		</defs>
		<clipPath id="SVGID_00000116195342570725193630000015354768164787694998_">
			<use xlink:href="#SVGID_00000119098447274543182120000015362640535195515821_"  style="overflow:visible;"/>
		</clipPath>
		<g style="clip-path:url(#SVGID_00000116195342570725193630000015354768164787694998_);enable-background:new    ;">
			<path class="st164" d="M97.5,46.2l0.9,2c-0.1-0.1-0.2-0.2-0.4-0.3c-0.2-0.1-0.4-0.1-0.6,0l-0.9-2c0.2,0,0.4,0,0.6,0
				C97.3,45.9,97.4,46,97.5,46.2"/>
			<path class="st165" d="M96.5,45.8l0.9,2c-0.1,0-0.1,0-0.2,0l-0.9-2C96.4,45.9,96.5,45.9,96.5,45.8"/>
			<path class="st166" d="M96.4,45.9l0.9,2c0,0-0.1,0-0.1,0.1l-0.9-2C96.3,45.9,96.3,45.9,96.4,45.9"/>
			<path class="st167" d="M96.2,45.9l0.9,2c0,0-0.1,0-0.1,0.1l-0.9-2C96.2,46,96.2,46,96.2,45.9"/>
			<path class="st168" d="M96.1,46l0.9,2c0,0-0.1,0-0.1,0.1L96.1,46C96.1,46,96.1,46,96.1,46"/>
			<path class="st169" d="M96,46.1l0.9,2c0,0-0.1,0-0.1,0.1L96,46.1C96,46.1,96,46.1,96,46.1"/>
			<path class="st170" d="M96,46.1l0.9,2c0,0-0.1,0.1-0.1,0.1L96,46.1C95.9,46.2,95.9,46.2,96,46.1"/>
			<path class="st171" d="M95.9,46.2l0.9,2c0,0-0.1,0.1-0.1,0.1l-0.9-2C95.8,46.3,95.8,46.3,95.9,46.2"/>
			<path class="st172" d="M95.8,46.4l0.9,2c0,0,0,0,0,0L95.8,46.4C95.7,46.4,95.7,46.4,95.8,46.4"/>
		</g>
	</g>
	<g class="st0">
		<defs>
			<path id="SVGID_00000023981026915261532880000005208883104327828156_" class="st0" d="M71.6,51.6c-4,7-4.6,14.5-2.4,20.9
				c-0.3-0.7-0.6-1.3-0.9-2c-2.2-6.4-1.6-13.9,2.4-20.9c8-14,26.5-20.9,41.3-15.4c6.7,2.5,11.6,7.2,14,12.8l0.9,2
				c-2.5-5.6-7.3-10.3-14-12.8C98.1,30.7,79.6,37.6,71.6,51.6z"/>
		</defs>
		<clipPath id="SVGID_00000183233277854397490850000010812618450555520909_">
			<use xlink:href="#SVGID_00000023981026915261532880000005208883104327828156_"  style="overflow:visible;"/>
		</clipPath>
		<g style="clip-path:url(#SVGID_00000183233277854397490850000010812618450555520909_);enable-background:new    ;">
			<path class="st174" d="M126.1,47l0.9,2c-2.5-5.6-7.3-10.3-14-12.8c-5.6-2.1-11.8-2.4-17.7-1.2l-0.9-2c5.9-1.2,12.1-0.9,17.7,1.2
				C118.8,36.7,123.6,41.4,126.1,47"/>
			<path class="st175" d="M94.3,33l0.9,2c-1.6,0.3-3.1,0.7-4.6,1.3l-0.9-2C91.2,33.8,92.7,33.4,94.3,33"/>
			<path class="st176" d="M89.7,34.3l0.9,2c-1.3,0.4-2.5,0.9-3.7,1.5l-0.9-2C87.2,35.2,88.4,34.7,89.7,34.3"/>
			<path class="st177" d="M86,35.8l0.9,2c-1.1,0.5-2.2,1.1-3.2,1.7l-0.9-2C83.8,36.9,84.9,36.3,86,35.8"/>
			<path class="st178" d="M82.8,37.5l0.9,2c-1,0.6-2,1.3-2.9,1.9l-0.9-2C80.8,38.8,81.8,38.1,82.8,37.5"/>
			<path class="st179" d="M79.9,39.5l0.9,2c-1,0.7-1.9,1.5-2.8,2.3l-0.9-2C78,40.9,78.9,40.2,79.9,39.5"/>
			<path class="st180" d="M77.1,41.7l0.9,2c-1,0.9-1.9,1.8-2.7,2.8l-0.9-2C75.2,43.5,76.1,42.6,77.1,41.7"/>
			<path class="st181" d="M74.4,44.5l0.9,2c-1.1,1.2-2,2.5-2.9,3.8l-0.9-2C72.4,47,73.3,45.7,74.4,44.5"/>
			<path class="st182" d="M71.5,48.4l0.9,2c-0.3,0.4-0.5,0.8-0.8,1.3C68.7,56.7,67.6,62,68,67l-0.9-2c-0.4-5,0.7-10.3,3.6-15.4
				C71,49.2,71.2,48.8,71.5,48.4"/>
			<path class="st181" d="M67.1,65l0.9,2c0.1,1.4,0.4,2.9,0.8,4.2l-0.9-2C67.5,67.9,67.3,66.5,67.1,65"/>
			<path class="st180" d="M67.9,69.2l0.9,2c0.3,1,0.6,2,1.1,2.9l-0.9-2C68.5,71.2,68.2,70.2,67.9,69.2"/>
		</g>
	</g>
	<g class="st0">
		<defs>
			<path id="SVGID_00000166648001784212835740000015049052093397766582_" class="st0" d="M69.2,72.5c0.2,0.6,0.4,1.1,0.7,1.7l-0.9-2
				c-0.2-0.6-0.5-1.1-0.7-1.7c0,0.1,0.1,0.2,0.1,0.2c0,0.1,0.1,0.2,0.1,0.2c0,0.1,0.1,0.2,0.1,0.3c0,0.1,0.1,0.2,0.1,0.3
				C68.9,71.8,69.1,72.2,69.2,72.5z"/>
		</defs>
		<clipPath id="SVGID_00000091698335106433941740000017696567874132417432_">
			<use xlink:href="#SVGID_00000166648001784212835740000015049052093397766582_"  style="overflow:visible;"/>
		</clipPath>
		<g style="clip-path:url(#SVGID_00000091698335106433941740000017696567874132417432_);enable-background:new    ;">
			<path class="st180" d="M67.9,69.2l0.9,2c0.3,1,0.6,2,1.1,2.9l-0.9-2C68.5,71.2,68.2,70.2,67.9,69.2"/>
		</g>
	</g>
	<g>
		<polygon class="st184" points="95.7,46.4 96.6,48.4 84.8,69.1 83.9,67 		"/>
	</g>
	<g class="st0">
		<defs>
			<path id="SVGID_00000176746564074896880930000004621888778214096776_" class="st0" d="M84.8,69.1c-0.2,0.3-0.2,0.5-0.1,0.8
				l-0.9-2c-0.1-0.2-0.1-0.5,0.1-0.8C84.2,67.7,84.6,68.5,84.8,69.1z"/>
		</defs>
		<clipPath id="SVGID_00000175311493379000640530000013666649562130613927_">
			<use xlink:href="#SVGID_00000176746564074896880930000004621888778214096776_"  style="overflow:visible;"/>
		</clipPath>
		<g style="clip-path:url(#SVGID_00000175311493379000640530000013666649562130613927_);enable-background:new    ;">
			<path class="st172" d="M83.9,67l0.9,2c-0.1,0.2-0.1,0.4-0.1,0.5l-0.9-2C83.8,67.4,83.8,67.2,83.9,67"/>
			<path class="st171" d="M83.8,67.6l0.9,2c0,0,0,0.1,0,0.1l-0.9-2C83.8,67.7,83.8,67.6,83.8,67.6"/>
			<path class="st170" d="M83.8,67.7l0.9,2c0,0,0,0.1,0,0.1l-0.9-2C83.9,67.8,83.8,67.7,83.8,67.7"/>
		</g>
	</g>
	<g class="st0">
		<defs>
			<path id="SVGID_00000121996480001397265400000009604628031850683521_" class="st0" d="M84.8,69.8l-0.9-2c0.1,0.2,0.3,0.4,0.5,0.4
				c0.2,0.1,0.3,0.1,0.5,0l0.9,2c-0.2,0-0.4,0-0.5-0.1C85,70.2,84.8,70,84.8,69.8z"/>
		</defs>
		<clipPath id="SVGID_00000056398739265493184190000001793510987126948260_">
			<use xlink:href="#SVGID_00000121996480001397265400000009604628031850683521_"  style="overflow:visible;"/>
		</clipPath>
		<g style="clip-path:url(#SVGID_00000056398739265493184190000001793510987126948260_);enable-background:new    ;">
			<path class="st187" d="M84.9,68.3l0.9,2c-0.2,0-0.4,0-0.5-0.1c-0.2-0.1-0.4-0.2-0.5-0.4l-0.9-2c0.1,0.2,0.3,0.4,0.5,0.4
				C84.5,68.3,84.7,68.3,84.9,68.3"/>
		</g>
	</g>
	<g>
		<polygon class="st188" points="110.5,63.5 111.4,65.5 98.4,48.2 97.5,46.2 		"/>
	</g>
	<g class="st0">
		<defs>
			<path id="SVGID_00000156568229687907086770000011956269997200178320_" class="st0" d="M110.6,63.7l0.9,2c0-0.1-0.1-0.1-0.1-0.2
				l-0.9-2C110.5,63.6,110.6,63.6,110.6,63.7z"/>
		</defs>
		<clipPath id="SVGID_00000031920033830427951660000001304953868128613792_">
			<use xlink:href="#SVGID_00000156568229687907086770000011956269997200178320_"  style="overflow:visible;"/>
		</clipPath>
		<g style="clip-path:url(#SVGID_00000031920033830427951660000001304953868128613792_);enable-background:new    ;">
			<path class="st164" d="M110.6,63.7l0.9,2c0-0.1-0.1-0.1-0.1-0.2l-0.9-2C110.5,63.6,110.6,63.6,110.6,63.7"/>
		</g>
	</g>
	<g>
		<polygon class="st190" points="109.6,65 110.5,67 85.8,70.3 84.9,68.3 		"/>
	</g>
	<g class="st0">
		<defs>
			<path id="SVGID_00000142861428717750194210000002138868381572872882_" class="st0" d="M110.6,63.7l0.9,2c0.2,0.5-0.2,1.3-1,1.4
				l-0.9-2C110.4,64.9,110.8,64.2,110.6,63.7z"/>
		</defs>
		<clipPath id="SVGID_00000120527699665040028880000012062170049448025253_">
			<use xlink:href="#SVGID_00000142861428717750194210000002138868381572872882_"  style="overflow:visible;"/>
		</clipPath>
		<g style="clip-path:url(#SVGID_00000120527699665040028880000012062170049448025253_);enable-background:new    ;">
			<path class="st192" d="M110.6,63.7l0.9,2c0,0,0,0.1,0,0.1L110.6,63.7C110.6,63.7,110.6,63.7,110.6,63.7"/>
			<path class="st193" d="M110.6,63.8l0.9,2c0,0,0,0.1,0,0.1l-0.9-2C110.7,63.9,110.6,63.8,110.6,63.8"/>
			<path class="st194" d="M110.7,63.9l0.9,2c0,0.2,0,0.4-0.2,0.6l-0.9-2C110.6,64.3,110.7,64.1,110.7,63.9"/>
			<path class="st193" d="M110.5,64.5l0.9,2c0,0-0.1,0.1-0.1,0.1l-0.9-2C110.4,64.6,110.5,64.5,110.5,64.5"/>
			<path class="st192" d="M110.4,64.6l0.9,2c0,0-0.1,0.1-0.1,0.1L110.4,64.6C110.3,64.7,110.4,64.6,110.4,64.6"/>
			<path class="st195" d="M110.3,64.7l0.9,2c0,0-0.1,0.1-0.1,0.1L110.3,64.7C110.3,64.8,110.3,64.7,110.3,64.7"/>
			<path class="st196" d="M110.2,64.8l0.9,2c0,0-0.1,0-0.1,0.1l-0.9-2C110.2,64.8,110.2,64.8,110.2,64.8"/>
			<path class="st197" d="M110.1,64.9l0.9,2c0,0-0.1,0-0.1,0.1l-0.9-2C110,64.9,110.1,64.9,110.1,64.9"/>
			<path class="st198" d="M110,64.9l0.9,2c0,0-0.1,0-0.1,0.1l-0.9-2C109.9,64.9,110,64.9,110,64.9"/>
			<path class="st199" d="M109.9,65l0.9,2c-0.1,0-0.1,0-0.2,0l-0.9-2C109.8,65,109.8,65,109.9,65"/>
			<path class="st187" d="M109.7,65l0.9,2c0,0,0,0-0.1,0L109.7,65C109.7,65,109.7,65,109.7,65"/>
		</g>
	</g>
	
		<linearGradient id="SVGID_00000181791972190675215690000016797213394502803379_" gradientUnits="userSpaceOnUse" x1="77.6772" y1="86.9567" x2="127.3835" y2="53.5138">
		<stop  offset="0" style="stop-color:#A81C69"/>
		<stop  offset="7.734946e-02" style="stop-color:#89125A"/>
		<stop  offset="0.128" style="stop-color:#7A0D52"/>
		<stop  offset="0.2843" style="stop-color:#700D52"/>
		<stop  offset="0.5839" style="stop-color:#610D52"/>
		<stop  offset="0.6597" style="stop-color:#670E53"/>
		<stop  offset="0.7561" style="stop-color:#761157"/>
		<stop  offset="0.8636" style="stop-color:#90155E"/>
		<stop  offset="0.9785" style="stop-color:#B41B67"/>
		<stop  offset="1" style="stop-color:#BC1C69"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000181791972190675215690000016797213394502803379_);" d="M126.4,69.3c0.2-0.4,0.4-0.9,0.6-1.3
		c0.1-0.2,0.1-0.3,0.2-0.5c0.2-0.6,0.4-1.2,0.6-1.7c0,0,0,0,0,0c0.2-0.6,0.3-1.2,0.5-1.7c0-0.2,0.1-0.3,0.1-0.5
		c0.1-0.4,0.2-0.9,0.2-1.3c0-0.2,0.1-0.3,0.1-0.5c0.1-0.6,0.1-1.1,0.2-1.7c0,0,0,0,0,0c0-0.6,0.1-1.1,0.1-1.7c0-0.2,0-0.3,0-0.5
		c0-0.4,0-0.8-0.1-1.3c0-0.1,0-0.2,0-0.3c0-0.1,0-0.1,0-0.2c-0.1-0.6-0.1-1.2-0.3-1.9c0-0.1,0-0.2-0.1-0.3c-0.1-0.5-0.2-1.1-0.4-1.6
		c0-0.1,0-0.2-0.1-0.2c0-0.1,0-0.1-0.1-0.2c-0.1-0.3-0.2-0.6-0.3-0.9c-0.1-0.2-0.1-0.3-0.2-0.5c-0.2-0.5-0.3-0.9-0.5-1.4
		c0,0,0,0,0,0l-0.9-2c0.2,0.5,0.4,0.9,0.5,1.4c0.1,0.2,0.1,0.3,0.2,0.5c0.1,0.3,0.2,0.6,0.3,0.9c0,0.1,0.1,0.3,0.1,0.4
		c0.1,0.5,0.3,1.1,0.4,1.6c0,0.1,0,0.2,0.1,0.3c0.1,0.6,0.2,1.2,0.3,1.9c0,0.2,0,0.3,0,0.5c0,0.4,0.1,0.8,0.1,1.3c0,0.2,0,0.3,0,0.5
		c0,0.6,0,1.1-0.1,1.7c0,0,0,0,0,0c0,0.6-0.1,1.1-0.2,1.7c0,0.2-0.1,0.3-0.1,0.5c-0.1,0.4-0.2,0.9-0.2,1.3c0,0.2-0.1,0.3-0.1,0.5
		c-0.1,0.6-0.3,1.2-0.5,1.7c0,0,0,0,0,0c-0.2,0.6-0.4,1.2-0.6,1.7c-0.1,0.2-0.1,0.3-0.2,0.5c-0.2,0.4-0.4,0.9-0.6,1.3
		c-0.1,0.2-0.2,0.3-0.2,0.5c-0.3,0.6-0.6,1.2-0.9,1.7c-0.2,0.4-0.5,0.9-0.8,1.3c-0.4,0.6-0.8,1.2-1.2,1.8c-0.1,0.1-0.2,0.2-0.3,0.4
		c-0.4,0.6-0.9,1.1-1.3,1.6c-0.1,0.1-0.2,0.2-0.3,0.3c-0.3,0.3-0.6,0.7-0.9,1c-0.1,0.1-0.3,0.3-0.4,0.4c-0.3,0.3-0.6,0.6-0.9,0.9
		c-0.1,0.1-0.2,0.2-0.4,0.3c-0.4,0.3-0.8,0.7-1.1,1c0,0-0.1,0.1-0.1,0.1c-0.4,0.4-0.9,0.7-1.3,1c-0.1,0.1-0.3,0.2-0.4,0.3
		c-0.3,0.2-0.6,0.4-0.9,0.6c-0.2,0.1-0.3,0.2-0.5,0.3c-0.3,0.2-0.6,0.4-0.9,0.6c-0.2,0.1-0.4,0.2-0.5,0.3c-0.3,0.2-0.6,0.4-1,0.6
		c-0.2,0.1-0.3,0.2-0.5,0.3c-0.4,0.2-0.8,0.4-1.2,0.6c-0.1,0-0.2,0.1-0.3,0.1c-0.5,0.3-1.1,0.5-1.6,0.7c-0.2,0.1-0.3,0.1-0.5,0.2
		c-0.4,0.2-0.8,0.3-1.2,0.5c-0.2,0.1-0.3,0.1-0.5,0.2c-0.7,0.2-1.3,0.4-2,0.6c-0.1,0-0.2,0.1-0.3,0.1c-0.6,0.2-1.2,0.3-1.9,0.4
		c-0.1,0-0.2,0.1-0.3,0.1c-0.6,0.1-1.3,0.2-2,0.3c-0.1,0-0.3,0-0.4,0.1c-0.6,0.1-1.2,0.1-1.8,0.2c-0.1,0-0.2,0-0.3,0
		c-0.7,0-1.3,0.1-2,0.1c-0.2,0-0.3,0-0.5,0c-0.6,0-1.2,0-1.8,0c-0.1,0-0.2,0-0.2,0c-0.7,0-1.3-0.1-2-0.2c-0.2,0-0.3,0-0.5-0.1
		c-0.6-0.1-1.2-0.2-1.8-0.3c0,0-0.1,0-0.1,0c-0.6-0.1-1.3-0.3-1.9-0.5c-0.2,0-0.3-0.1-0.5-0.1c-0.6-0.2-1.3-0.4-1.9-0.6
		c-5.1-1.9-9-5-11.7-8.8c-0.9-1.3-1.7-2.6-2.3-4l0,0c0,0,0,0,0,0l0.9,2c2.5,5.6,7.3,10.3,14,12.8c0.6,0.2,1.3,0.4,1.9,0.6
		c0.2,0,0.3,0.1,0.5,0.1c0.6,0.2,1.3,0.3,1.9,0.5c0,0,0.1,0,0.1,0c0.6,0.1,1.2,0.2,1.8,0.3c0.2,0,0.3,0,0.5,0.1
		c0.7,0.1,1.3,0.1,2,0.2c0.1,0,0.2,0,0.2,0c0.6,0,1.2,0,1.8,0c0.2,0,0.3,0,0.5,0c0.7,0,1.3,0,2-0.1c0.1,0,0.2,0,0.3,0
		c0.6,0,1.2-0.1,1.7-0.2c0.1,0,0.3,0,0.4-0.1c0.7-0.1,1.3-0.2,2-0.3c0,0,0.1,0,0.1,0c0.1,0,0.2,0,0.2-0.1c0.6-0.1,1.2-0.3,1.9-0.4
		c0.1,0,0.2-0.1,0.3-0.1c0.7-0.2,1.3-0.4,2-0.6c0.1,0,0.1,0,0.2-0.1c0.1,0,0.2-0.1,0.3-0.1c0.4-0.1,0.8-0.3,1.2-0.5
		c0.2-0.1,0.3-0.1,0.5-0.2c0.5-0.2,1.1-0.5,1.6-0.7c0,0,0,0,0,0c0.1,0,0.1-0.1,0.2-0.1c0.4-0.2,0.8-0.4,1.2-0.6
		c0.2-0.1,0.3-0.2,0.5-0.3c0.3-0.2,0.7-0.4,1-0.6c0.1-0.1,0.2-0.1,0.3-0.1c0.1-0.1,0.2-0.1,0.3-0.2c0.3-0.2,0.6-0.4,0.9-0.6
		c0.2-0.1,0.3-0.2,0.5-0.3c0.3-0.2,0.6-0.4,0.9-0.6c0.1-0.1,0.2-0.2,0.3-0.2c0,0,0.1,0,0.1-0.1c0.4-0.3,0.9-0.7,1.3-1
		c0,0,0.1-0.1,0.1-0.1c0.4-0.3,0.8-0.7,1.1-1c0,0,0.1-0.1,0.1-0.1c0.1-0.1,0.2-0.2,0.2-0.2c0.3-0.3,0.6-0.6,0.9-0.9
		c0.1-0.1,0.3-0.3,0.4-0.4c0.3-0.3,0.6-0.6,0.9-1c0.1-0.1,0.2-0.2,0.2-0.2c0,0,0-0.1,0.1-0.1c0.5-0.5,0.9-1.1,1.3-1.6
		c0.1-0.1,0.2-0.2,0.3-0.4c0.4-0.6,0.8-1.2,1.2-1.8c0,0,0,0,0,0c0.3-0.4,0.5-0.8,0.8-1.3c0.3-0.6,0.6-1.1,0.9-1.7
		C126.2,69.7,126.3,69.5,126.4,69.3z"/>
	
		<radialGradient id="SVGID_00000042732509811839169840000014504184034872019616_" cx="104.6599" cy="122.5881" r="98.57" gradientUnits="userSpaceOnUse">
		<stop  offset="0.3422" style="stop-color:#DA334F"/>
		<stop  offset="0.6759" style="stop-color:#FF597D"/>
		<stop  offset="0.9892" style="stop-color:#FFA19A"/>
	</radialGradient>
	<path style="fill:url(#SVGID_00000042732509811839169840000014504184034872019616_);" d="M112,34.2c-14.8-5.5-33.3,1.4-41.3,15.4
		c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4C132.3,55.5,126.8,39.7,112,34.2z"/>
	
		<linearGradient id="SVGID_00000067195009788064552980000000110329746437511851_" gradientUnits="userSpaceOnUse" x1="85.4772" y1="30.985" x2="107.3151" y2="82.8792">
		<stop  offset="7.574820e-02" style="stop-color:#FFFFFF"/>
		<stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000067195009788064552980000000110329746437511851_);" d="M112,34.2c-14.8-5.5-33.3,1.4-41.3,15.4
		c-8,14-2.5,29.8,12.3,35.3c14.8,5.5,33.3-1.4,41.3-15.4C132.3,55.5,126.8,39.7,112,34.2z M124,69.4c-6,10.4-17.8,17.2-30.3,17.2
		c-3.7,0-7.3-0.6-10.7-1.9c-7-2.6-12.1-7.6-14.4-14C66.3,64,67.1,56.6,71,49.8c6-10.4,17.8-17.1,30.2-17.1c3.7,0,7.3,0.6,10.7,1.9
		c7,2.6,12.1,7.6,14.4,14C128.7,55.2,127.9,62.6,124,69.4z"/>
	<g>
		
			<linearGradient id="SVGID_00000016779879225849557820000011839830994552808083_" gradientUnits="userSpaceOnUse" x1="85.7831" y1="48.5034" x2="109.4996" y2="84.1955">
			<stop  offset="0" style="stop-color:#F2F9FF"/>
			<stop  offset="0.9569" style="stop-color:#D0BCFF"/>
			<stop  offset="1" style="stop-color:#CEB9FF"/>
		</linearGradient>
		<path style="fill:url(#SVGID_00000016779879225849557820000011839830994552808083_);" d="M97.1,45.9c0.2,0.1,0.3,0.2,0.4,0.3
			l13,17.3c0.4,0.6-0.1,1.4-0.9,1.5l-24.8,3.3c-0.2,0-0.4,0-0.5,0c-0.5-0.2-0.7-0.7-0.4-1.2l11.8-20.6C96,45.9,96.7,45.7,97.1,45.9z
			"/>
	</g>
	
		<linearGradient id="SVGID_00000047779977890383076870000006084236795539104650_" gradientUnits="userSpaceOnUse" x1="184.285" y1="56.4579" x2="49.0516" y2="165.4728">
		<stop  offset="2.179480e-03" style="stop-color:#E0D4FF"/>
		<stop  offset="1.644577e-02" style="stop-color:#C9BBF5"/>
		<stop  offset="2.954578e-02" style="stop-color:#BBABEF"/>
		<stop  offset="3.976460e-02" style="stop-color:#B6A5ED"/>
		<stop  offset="0.1763" style="stop-color:#A696E2"/>
		<stop  offset="0.4435" style="stop-color:#7B6EC5"/>
		<stop  offset="0.5341" style="stop-color:#6B5FBA"/>
		<stop  offset="0.7181" style="stop-color:#6D61BB"/>
		<stop  offset="0.7844" style="stop-color:#7468BF"/>
		<stop  offset="0.8316" style="stop-color:#7F73C6"/>
		<stop  offset="0.8698" style="stop-color:#9084D0"/>
		<stop  offset="0.9025" style="stop-color:#A69ADD"/>
		<stop  offset="0.931" style="stop-color:#C1B5ED"/>
		<stop  offset="0.9563" style="stop-color:#E0D4FF"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000047779977890383076870000006084236795539104650_);" d="M174.7,47.6c0-0.1,0.1-0.2,0.1-0.4
		c0.1-0.4,0.1-0.8,0.2-1.2c0,0,0,0,0,0c0-0.4,0-0.8,0-1.2c0,0,0-0.1,0-0.1c0-0.2,0-0.3-0.1-0.5c0-0.1,0-0.1,0-0.2
		c0-0.3-0.1-0.5-0.2-0.8c0,0,0,0,0-0.1c-0.1-0.3-0.2-0.6-0.3-1l-0.9-2c0.1,0.3,0.2,0.6,0.3,1c0.1,0.3,0.1,0.5,0.2,0.8
		c0,0.1,0,0.1,0,0.2c0,0.2,0.1,0.4,0.1,0.6c0,0.4,0,0.8,0,1.2c0,0,0,0,0,0c0,0.4-0.1,0.8-0.2,1.2c0,0.1-0.1,0.2-0.1,0.4
		c-0.1,0.3-0.2,0.6-0.3,0.9c0,0.1-0.1,0.2-0.1,0.4c-0.2,0.4-0.3,0.8-0.6,1.2l-42.3,73.9l0.9,2l42.3-73.9c0.2-0.4,0.4-0.8,0.6-1.2
		c0-0.1,0.1-0.2,0.1-0.4C174.5,48.2,174.6,47.9,174.7,47.6z"/>
	<g>
		<path class="st205" d="M85.2,134.5l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C87.3,136.7,86.7,135,85.2,134.5z"/>
		<path class="st206" d="M85.1,134.4l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C87.3,136.6,86.7,135,85.1,134.4z"/>
		<path class="st207" d="M85.1,134.3l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C87.2,136.5,86.7,134.9,85.1,134.3z"/>
		<path class="st208" d="M85.1,134.2l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7L82,139.5
			c1.6,0.6,3.5-0.1,4.3-1.6C87.2,136.4,86.6,134.8,85.1,134.2z"/>
		<path class="st209" d="M85.1,134.1l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7L82,139.4
			c1.6,0.6,3.5-0.1,4.3-1.6C87.2,136.3,86.6,134.7,85.1,134.1z"/>
		<path class="st210" d="M85,134l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7L82,139.3c1.6,0.6,3.5-0.1,4.3-1.6
			C87.2,136.2,86.6,134.6,85,134z"/>
		<path class="st211" d="M85,133.9l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7L82,139.2c1.6,0.6,3.5-0.1,4.3-1.6
			C87.1,136.1,86.6,134.5,85,133.9z"/>
		<path class="st212" d="M85,133.8L42.6,118c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8c1.6,0.6,3.5-0.1,4.3-1.6
			C87.1,136,86.5,134.4,85,133.8z"/>
		<path class="st213" d="M84.9,133.7l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7L81.9,139
			c1.6,0.6,3.5-0.1,4.3-1.6C87.1,135.9,86.5,134.3,84.9,133.7z"/>
		<path class="st214" d="M84.9,133.6l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C87,135.8,86.5,134.2,84.9,133.6z"/>
		<path class="st215" d="M84.9,133.5l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C87,135.7,86.4,134.1,84.9,133.5z"/>
		<path class="st216" d="M84.9,133.4l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C87,135.7,86.4,134,84.9,133.4z"/>
		<path class="st217" d="M84.8,133.3l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C87,135.6,86.4,133.9,84.8,133.3z"/>
		<path class="st218" d="M84.8,133.2l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.9,135.5,86.4,133.8,84.8,133.2z"/>
		<path class="st219" d="M84.8,133.1l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.9,135.4,86.3,133.7,84.8,133.1z"/>
		<path class="st220" d="M84.8,133l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.9,135.3,86.3,133.6,84.8,133z"/>
		<path class="st221" d="M84.7,132.9l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.9,135.2,86.3,133.5,84.7,132.9z"/>
		<path class="st222" d="M84.7,132.8l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.8,135.1,86.3,133.4,84.7,132.8z"/>
		<path class="st223" d="M84.7,132.7L42.3,117c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.8,135,86.2,133.3,84.7,132.7z"/>
		<path class="st224" d="M84.6,132.7l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7L81.6,138
			c1.6,0.6,3.5-0.1,4.3-1.6C86.8,134.9,86.2,133.2,84.6,132.7z"/>
		<path class="st225" d="M84.6,132.6l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.8,134.8,86.2,133.1,84.6,132.6z"/>
		<path class="st226" d="M84.6,132.5l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.7,134.7,86.1,133,84.6,132.5z"/>
		<path class="st227" d="M84.6,132.4l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.7,134.6,86.1,132.9,84.6,132.4z"/>
		<path class="st228" d="M84.5,132.3l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.7,134.5,86.1,132.8,84.5,132.3z"/>
		<path class="st229" d="M84.5,132.2l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.6,134.4,86.1,132.8,84.5,132.2z"/>
		<path class="st230" d="M84.5,132.1l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.6,134.3,86,132.7,84.5,132.1z"/>
		<path class="st231" d="M84.5,132l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.6,134.2,86,132.6,84.5,132z"/>
		<path class="st232" d="M84.4,131.9L42,116.1c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.6,134.1,86,132.5,84.4,131.9z"/>
		<path class="st233" d="M84.4,131.8L42,116c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8c1.6,0.6,3.5-0.1,4.3-1.6
			C86.5,134,86,132.4,84.4,131.8z"/>
		<path class="st234" d="M84.4,131.7L42,115.9c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7L81.3,137c1.6,0.6,3.5-0.1,4.3-1.6
			C86.5,133.9,85.9,132.3,84.4,131.7z"/>
		<path class="st235" d="M84.4,131.6L42,115.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.5,133.8,85.9,132.2,84.4,131.6z"/>
		<path class="st236" d="M84.3,131.5l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.5,133.7,85.9,132.1,84.3,131.5z"/>
		<path class="st237" d="M84.3,131.4l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.4,133.6,85.9,132,84.3,131.4z"/>
		<path class="st238" d="M84.3,131.3l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.4,133.6,85.8,131.9,84.3,131.3z"/>
		<path class="st239" d="M84.2,131.2l-42.4-15.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8
			c1.6,0.6,3.5-0.1,4.3-1.6C86.4,133.5,85.8,131.8,84.2,131.2z"/>
	</g>
	
		<linearGradient id="SVGID_00000183951514668436397430000003957937837222248875_" gradientUnits="userSpaceOnUse" x1="38.6433" y1="133.0437" x2="83.4886" y2="120.4117">
		<stop  offset="0" style="stop-color:#E07CEC"/>
		<stop  offset="2.305414e-02" style="stop-color:#C86CDA"/>
		<stop  offset="6.427359e-02" style="stop-color:#A453BF"/>
		<stop  offset="0.1028" style="stop-color:#8A41AC"/>
		<stop  offset="0.1372" style="stop-color:#7A36A0"/>
		<stop  offset="0.164" style="stop-color:#74329C"/>
		<stop  offset="0.615" style="stop-color:#74329C"/>
		<stop  offset="0.6892" style="stop-color:#74329C"/>
		<stop  offset="0.8029" style="stop-color:#74329C"/>
		<stop  offset="0.8216" style="stop-color:#74329C"/>
		<stop  offset="0.8528" style="stop-color:#79359F"/>
		<stop  offset="0.8884" style="stop-color:#863FAA"/>
		<stop  offset="0.9261" style="stop-color:#9D4EBB"/>
		<stop  offset="0.965" style="stop-color:#BD64D2"/>
		<stop  offset="1" style="stop-color:#E07CEC"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000183951514668436397430000003957937837222248875_);" d="M85.9,133.8
		C85.9,133.8,85.9,133.8,85.9,133.8c0-0.1,0-0.3,0-0.4c0,0,0-0.1,0-0.1c0-0.1,0-0.1,0-0.2c0-0.1,0-0.1,0-0.2c0-0.1-0.1-0.2-0.1-0.3
		l-0.9-2c0.1,0.1,0.1,0.3,0.1,0.5c0,0.1,0,0.1,0,0.2c0,0.2,0,0.3,0,0.5c0,0,0,0,0,0c0,0.2-0.1,0.4-0.1,0.5c0,0,0,0.1,0,0.1
		c-0.1,0.2-0.1,0.4-0.2,0.5c-0.1,0.2-0.2,0.3-0.3,0.5c0,0,0,0,0,0.1c-0.1,0.1-0.3,0.3-0.4,0.4c0,0-0.1,0-0.1,0.1
		c-0.1,0.1-0.3,0.2-0.4,0.3c0,0-0.1,0-0.1,0c-0.1,0.1-0.3,0.1-0.4,0.2c0,0-0.1,0-0.1,0c-0.2,0.1-0.4,0.1-0.6,0.2c0,0-0.1,0-0.1,0
		c-0.2,0-0.3,0-0.5,0.1c-0.1,0-0.1,0-0.2,0c-0.1,0-0.3,0-0.4,0c-0.1,0-0.1,0-0.2,0c-0.2,0-0.4-0.1-0.5-0.1l-42.4-15.8
		c-0.5-0.2-0.9-0.5-1.2-0.9c-0.1-0.1-0.2-0.3-0.2-0.4l0,0c0,0,0,0,0,0l0.9,2c0.3,0.6,0.8,1.1,1.5,1.3l42.4,15.8
		c0.2,0.1,0.4,0.1,0.5,0.1c0.1,0,0.1,0,0.2,0c0.1,0,0.3,0,0.4,0c0.1,0,0.1,0,0.2,0c0.2,0,0.3,0,0.5-0.1c0,0,0.1,0,0.1,0c0,0,0,0,0,0
		c0.2,0,0.3-0.1,0.5-0.1c0,0,0,0,0.1,0c0,0,0.1,0,0.1,0c0.1,0,0.2-0.1,0.2-0.1c0.1,0,0.1-0.1,0.2-0.1c0,0,0.1,0,0.1,0c0,0,0,0,0,0
		c0.1-0.1,0.2-0.1,0.3-0.2c0,0,0.1-0.1,0.1-0.1c0,0,0.1,0,0.1-0.1c0,0,0.1-0.1,0.1-0.1c0.1-0.1,0.2-0.2,0.3-0.3c0,0,0,0,0,0
		c0,0,0,0,0-0.1c0.1-0.1,0.2-0.2,0.2-0.3c0,0,0.1-0.1,0.1-0.1c0.1-0.2,0.2-0.4,0.2-0.5c0,0,0-0.1,0-0.1
		C85.9,134.1,85.9,133.9,85.9,133.8z"/>
	
		<linearGradient id="SVGID_00000016783819525005149600000013527870302202347950_" gradientUnits="userSpaceOnUse" x1="49.1404" y1="170.1234" x2="60.6043" y2="124.2674">
		<stop  offset="0.2849" style="stop-color:#CB25DF"/>
		<stop  offset="0.9937" style="stop-color:#E07CEC"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000016783819525005149600000013527870302202347950_);" d="M83.4,129.3L41,113.5
		c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8c1.6,0.6,3.5-0.1,4.3-1.6C85.5,131.5,84.9,129.8,83.4,129.3z"/>
	
		<linearGradient id="SVGID_00000135690331125713627560000009875563865026158739_" gradientUnits="userSpaceOnUse" x1="63.7154" y1="111.5579" x2="59.2681" y2="129.7337">
		<stop  offset="7.574820e-02" style="stop-color:#FFFFFF"/>
		<stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000135690331125713627560000009875563865026158739_);" d="M83.4,129.3L41,113.5
		c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l42.4,15.8c1.6,0.6,3.5-0.1,4.3-1.6C85.5,131.5,84.9,129.8,83.4,129.3z
		 M84.4,132.8c-0.6,1-1.7,1.7-2.9,1.7c-0.4,0-0.7-0.1-1-0.2l-42.4-15.8c-0.7-0.2-1.1-0.7-1.3-1.3c-0.2-0.6-0.1-1.3,0.2-1.9
		c0.6-1,1.7-1.7,2.9-1.7c0.4,0,0.7,0.1,1,0.2l42.4,15.8c0.7,0.2,1.1,0.7,1.3,1.3C84.8,131.5,84.8,132.2,84.4,132.8z"/>
	
		<linearGradient id="SVGID_00000054955302031268955610000016033486924820072850_" gradientUnits="userSpaceOnUse" x1="128.1844" y1="99.155" x2="13.305" y2="157.4571">
		<stop  offset="0" style="stop-color:#32A5FC"/>
		<stop  offset="1.905284e-02" style="stop-color:#2B97EE"/>
		<stop  offset="7.297315e-02" style="stop-color:#1874CD"/>
		<stop  offset="0.124" style="stop-color:#0B5BB5"/>
		<stop  offset="0.1704" style="stop-color:#034CA6"/>
		<stop  offset="0.2081" style="stop-color:#0047A1"/>
		<stop  offset="0.2507" style="stop-color:#00429B"/>
		<stop  offset="0.3047" style="stop-color:#00368C"/>
		<stop  offset="0.3456" style="stop-color:#00287B"/>
		<stop  offset="0.5052" style="stop-color:#002C81"/>
		<stop  offset="0.7077" style="stop-color:#003990"/>
		<stop  offset="0.8612" style="stop-color:#0047A1"/>
		<stop  offset="0.8856" style="stop-color:#034DA6"/>
		<stop  offset="0.9167" style="stop-color:#0B5CB6"/>
		<stop  offset="0.9514" style="stop-color:#1976CF"/>
		<stop  offset="0.9884" style="stop-color:#2C9AF2"/>
		<stop  offset="0.998" style="stop-color:#32A5FC"/>
	</linearGradient>
	<path style="fill:url(#SVGID_00000054955302031268955610000016033486924820072850_);" d="M130.5,122l-16,28
		c-0.1,0.2-0.2,0.4-0.4,0.6c-0.3,0.4-0.6,0.8-0.9,1.2c0,0.1-0.1,0.1-0.1,0.2c-0.2,0.2-0.5,0.5-0.7,0.7c-0.1,0.1-0.2,0.2-0.3,0.3
		c-0.2,0.2-0.4,0.3-0.6,0.5c-0.1,0.1-0.2,0.2-0.3,0.3c-0.2,0.1-0.4,0.3-0.6,0.4c-0.1,0.1-0.2,0.1-0.3,0.2c-0.3,0.2-0.6,0.3-0.9,0.5
		c0,0-0.1,0-0.1,0.1c-0.4,0.2-0.7,0.3-1.1,0.5c-0.1,0-0.2,0.1-0.3,0.1c-0.4,0.1-0.9,0.3-1.3,0.4c0,0-0.1,0-0.1,0
		c-0.4,0.1-0.9,0.1-1.3,0.2c-0.1,0-0.2,0-0.3,0c-0.4,0-0.9,0-1.3,0c0,0-0.1,0-0.1,0c-0.5,0-0.9-0.1-1.3-0.2c-0.1,0-0.2,0-0.3-0.1
		c-0.4-0.1-0.9-0.2-1.3-0.4l0,0L5.5,119.9c-0.6-0.2-1.1-0.5-1.6-0.8c-1.5-0.9-2.6-2.2-3.2-3.6l0.9,2c0.9,1.9,2.5,3.5,4.8,4.4
		l94.9,35.3c0.4,0.2,0.9,0.3,1.3,0.4c0.1,0,0.2,0,0.3,0.1c0.4,0.1,0.9,0.2,1.3,0.2c0,0,0.1,0,0.1,0c0.4,0,0.9,0,1.3,0
		c0.1,0,0.2,0,0.3,0c0.5,0,0.9-0.1,1.3-0.2c0,0,0,0,0,0c0,0,0.1,0,0.1,0c0.4-0.1,0.9-0.2,1.3-0.4c0.1,0,0.1,0,0.2,0c0,0,0.1,0,0.1,0
		c0.4-0.1,0.8-0.3,1.1-0.5c0,0,0,0,0,0c0,0,0.1,0,0.1-0.1c0.3-0.1,0.6-0.3,0.9-0.5c0,0,0.1,0,0.1-0.1c0.1,0,0.2-0.1,0.2-0.2
		c0.2-0.1,0.4-0.3,0.6-0.4c0.1,0,0.1-0.1,0.2-0.1c0.1,0,0.1-0.1,0.2-0.2c0.2-0.1,0.4-0.3,0.6-0.5c0.1-0.1,0.1-0.1,0.2-0.2
		c0,0,0.1-0.1,0.1-0.1c0.2-0.2,0.5-0.5,0.7-0.7c0,0,0.1-0.1,0.1-0.1c0,0,0,0,0,0c0.3-0.4,0.6-0.8,0.9-1.2c0,0,0.1-0.1,0.1-0.1
		c0.1-0.1,0.2-0.3,0.3-0.4l16-28L130.5,122z"/>
	<g>
		<path class="st205" d="M108.9,131.7l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C111,133.9,110.5,132.3,108.9,131.7z"/>
		<path class="st206" d="M108.9,131.6l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C111,133.8,110.4,132.2,108.9,131.6z"/>
		<path class="st207" d="M108.9,131.5L29.8,102c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C111,133.8,110.4,132.1,108.9,131.5z"/>
		<path class="st208" d="M108.8,131.4l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C111,133.7,110.4,132,108.8,131.4z"/>
		<path class="st209" d="M108.8,131.3l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.9,133.6,110.4,131.9,108.8,131.3z"/>
		<path class="st210" d="M108.8,131.2l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.9,133.5,110.3,131.8,108.8,131.2z"/>
		<path class="st211" d="M108.7,131.1l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.9,133.4,110.3,131.7,108.7,131.1z"/>
		<path class="st212" d="M108.7,131l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.8,133.3,110.3,131.6,108.7,131z"/>
		<path class="st213" d="M108.7,130.9l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.8,133.2,110.2,131.5,108.7,130.9z"/>
		<path class="st214" d="M108.7,130.8l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.8,133.1,110.2,131.4,108.7,130.8z"/>
		<path class="st215" d="M108.6,130.8l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.8,133,110.2,131.3,108.6,130.8z"/>
		<path class="st216" d="M108.6,130.7l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.7,132.9,110.2,131.2,108.6,130.7z"/>
		<path class="st217" d="M108.6,130.6l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.7,132.8,110.1,131.1,108.6,130.6z"/>
		<path class="st218" d="M108.6,130.5L29.5,101c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.7,132.7,110.1,131,108.6,130.5z"/>
		<path class="st219" d="M108.5,130.4l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.7,132.6,110.1,130.9,108.5,130.4z"/>
		<path class="st220" d="M108.5,130.3l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.6,132.5,110.1,130.9,108.5,130.3z"/>
		<path class="st221" d="M108.5,130.2l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.6,132.4,110,130.8,108.5,130.2z"/>
		<path class="st222" d="M108.4,130.1l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.6,132.3,110,130.7,108.4,130.1z"/>
		<path class="st223" d="M108.4,130l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.5,132.2,110,130.6,108.4,130z"/>
		<path class="st224" d="M108.4,129.9l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.5,132.1,109.9,130.5,108.4,129.9z"/>
		<path class="st225" d="M108.4,129.8l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.5,132,109.9,130.4,108.4,129.8z"/>
		<path class="st226" d="M108.3,129.7l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.5,131.9,109.9,130.3,108.3,129.7z"/>
		<path class="st227" d="M108.3,129.6l-79.1-29.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.4,131.8,109.9,130.2,108.3,129.6z"/>
		<path class="st228" d="M108.3,129.5L29.2,100c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.4,131.7,109.8,130.1,108.3,129.5z"/>
		<path class="st229" d="M108.3,129.4L29.2,99.9c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.4,131.6,109.8,130,108.3,129.4z"/>
		<path class="st230" d="M108.2,129.3L29.1,99.8c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.4,131.6,109.8,129.9,108.2,129.3z"/>
		<path class="st231" d="M108.2,129.2L29.1,99.7c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.3,131.5,109.8,129.8,108.2,129.2z"/>
		<path class="st232" d="M108.2,129.1L29.1,99.6c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.3,131.4,109.7,129.7,108.2,129.1z"/>
		<path class="st233" d="M108.1,129L29.1,99.5c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.3,131.3,109.7,129.6,108.1,129z"/>
		<path class="st234" d="M108.1,128.9L29,99.4c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.3,131.2,109.7,129.5,108.1,128.9z"/>
		<path class="st235" d="M108.1,128.8L29,99.3c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.2,131.1,109.6,129.4,108.1,128.8z"/>
		<path class="st236" d="M108.1,128.7L29,99.2c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.2,131,109.6,129.3,108.1,128.7z"/>
		<path class="st237" d="M108,128.6L29,99.1c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7L105,134c1.6,0.6,3.5-0.1,4.3-1.6
			C110.2,130.9,109.6,129.2,108,128.6z"/>
		<path class="st238" d="M108,128.6L28.9,99c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5c1.6,0.6,3.5-0.1,4.3-1.6
			C110.1,130.8,109.6,129.1,108,128.6z"/>
		<path class="st239" d="M108,128.5L28.9,98.9c-1.6-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.5
			c1.6,0.6,3.5-0.1,4.3-1.6C110.1,130.7,109.5,129,108,128.5z"/>
	</g>
	<g>
		
			<linearGradient id="SVGID_00000173844448486198953430000007053837120203632033_" gradientUnits="userSpaceOnUse" x1="25.7237" y1="126.6043" x2="109.706" y2="107.1954">
			<stop  offset="0" style="stop-color:#31FFAE"/>
			<stop  offset="2.907401e-03" style="stop-color:#2BF3A6"/>
			<stop  offset="1.033325e-02" style="stop-color:#1EDA96"/>
			<stop  offset="1.711914e-02" style="stop-color:#17CB8C"/>
			<stop  offset="2.263390e-02" style="stop-color:#14C689"/>
			<stop  offset="3.873640e-02" style="stop-color:#11BB84"/>
			<stop  offset="7.932932e-02" style="stop-color:#0BA579"/>
			<stop  offset="0.1273" style="stop-color:#069471"/>
			<stop  offset="0.1866" style="stop-color:#02896B"/>
			<stop  offset="0.2698" style="stop-color:#018268"/>
			<stop  offset="0.5009" style="stop-color:#008067"/>
			<stop  offset="0.7995" style="stop-color:#008067"/>
			<stop  offset="0.842" style="stop-color:#008067"/>
			<stop  offset="0.8601" style="stop-color:#018569"/>
			<stop  offset="0.882" style="stop-color:#069471"/>
			<stop  offset="0.9059" style="stop-color:#0DAC7D"/>
			<stop  offset="0.9254" style="stop-color:#14C689"/>
			<stop  offset="0.9434" style="stop-color:#17CC8B"/>
			<stop  offset="0.9669" style="stop-color:#21DC92"/>
			<stop  offset="0.9934" style="stop-color:#31F79C"/>
			<stop  offset="1" style="stop-color:#36FF9F"/>
		</linearGradient>
		<path style="fill:url(#SVGID_00000173844448486198953430000007053837120203632033_);" d="M109.8,131
			C109.8,131,109.8,131,109.8,131c0-0.1,0-0.3,0-0.4c0,0,0-0.1,0-0.1c0-0.1,0-0.1,0-0.2c0-0.1,0-0.1,0-0.2c0-0.1-0.1-0.2-0.1-0.3
			l-0.9-2c0.1,0.1,0.1,0.3,0.1,0.5c0,0.1,0,0.1,0,0.2c0,0.2,0,0.3,0,0.5c0,0,0,0,0,0c0,0.2-0.1,0.4-0.1,0.5c0,0,0,0.1,0,0.1
			c-0.1,0.2-0.1,0.4-0.2,0.5c-0.1,0.2-0.2,0.3-0.3,0.5c0,0,0,0-0.1,0.1c-0.1,0.1-0.3,0.3-0.4,0.4c0,0-0.1,0-0.1,0.1
			c-0.1,0.1-0.3,0.2-0.4,0.3c0,0-0.1,0-0.1,0c-0.1,0.1-0.3,0.1-0.4,0.2c0,0-0.1,0-0.1,0c-0.2,0.1-0.4,0.1-0.6,0.2c0,0-0.1,0-0.1,0
			c-0.2,0-0.3,0-0.5,0.1c-0.1,0-0.1,0-0.2,0c-0.1,0-0.3,0-0.4,0c-0.1,0-0.1,0-0.2,0c-0.2,0-0.4-0.1-0.5-0.1l-79.1-29.4
			c-0.4-0.1-0.7-0.3-0.9-0.5c-0.2-0.2-0.4-0.5-0.6-0.8l0,0c0,0,0,0,0,0l0.9,2c0.3,0.6,0.8,1.1,1.5,1.3l79.1,29.4
			c0.2,0.1,0.4,0.1,0.5,0.1c0.1,0,0.1,0,0.2,0c0.1,0,0.3,0,0.4,0c0.1,0,0.1,0,0.2,0c0.2,0,0.3,0,0.5-0.1c0,0,0.1,0,0.1,0
			c0,0,0,0,0,0c0.2,0,0.3-0.1,0.5-0.1c0,0,0,0,0.1,0c0,0,0.1,0,0.1,0c0.1,0,0.2-0.1,0.2-0.1c0.1,0,0.1-0.1,0.2-0.1c0,0,0.1,0,0.1,0
			c0,0,0,0,0,0c0.1-0.1,0.2-0.1,0.3-0.2c0,0,0.1-0.1,0.1-0.1c0,0,0.1,0,0.1-0.1c0,0,0.1-0.1,0.1-0.1c0.1-0.1,0.2-0.2,0.3-0.3
			c0,0,0,0,0,0c0,0,0,0,0.1-0.1c0.1-0.1,0.2-0.2,0.2-0.3c0,0,0.1-0.1,0.1-0.1c0.1-0.2,0.2-0.4,0.2-0.5c0,0,0-0.1,0-0.1
			C109.8,131.3,109.8,131.1,109.8,131z"/>
		<g>
			
				<linearGradient id="SVGID_00000142868374753800026780000007177731823763262867_" gradientUnits="userSpaceOnUse" x1="64.3658" y1="157.5421" x2="67.4707" y2="83.3704">
				<stop  offset="2.370300e-04" style="stop-color:#08BF8C"/>
				<stop  offset="0.4107" style="stop-color:#20E5A0"/>
				<stop  offset="0.6729" style="stop-color:#31FFAE"/>
				<stop  offset="0.9897" style="stop-color:#6EFFCD"/>
			</linearGradient>
			<path style="fill:url(#SVGID_00000142868374753800026780000007177731823763262867_);" d="M107.3,126.4c1.6,0.6,2.1,2.2,1.3,3.7
				c-0.8,1.5-2.8,2.2-4.3,1.6l-79.1-29.4c-1.5-0.6-2.1-2.2-1.3-3.7c0.8-1.5,2.8-2.2,4.3-1.6L107.3,126.4z"/>
			
				<linearGradient id="SVGID_00000046312532185045017230000011362039738134339762_" gradientUnits="userSpaceOnUse" x1="64.6192" y1="91.4496" x2="67.7129" y2="137.179">
				<stop  offset="7.574820e-02" style="stop-color:#FFFFFF"/>
				<stop  offset="1" style="stop-color:#FFFFFF;stop-opacity:0"/>
			</linearGradient>
			<path style="fill:url(#SVGID_00000046312532185045017230000011362039738134339762_);" d="M107.3,126.4L28.1,97
				c-1.5-0.6-3.5,0.1-4.3,1.6c-0.8,1.5-0.3,3.1,1.3,3.7l79.1,29.4c1.6,0.6,3.5-0.1,4.3-1.6C109.4,128.7,108.8,127,107.3,126.4z
				 M108.3,130c-0.6,1-1.7,1.7-2.9,1.7c-0.4,0-0.7-0.1-1-0.2l-79.1-29.4c-0.6-0.2-1.1-0.7-1.3-1.3c-0.2-0.6-0.1-1.3,0.2-2
				c0.6-1,1.7-1.7,2.9-1.7c0.4,0,0.7,0.1,1,0.2l79.1,29.4c0.7,0.2,1.1,0.7,1.3,1.3C108.7,128.7,108.6,129.4,108.3,130z"/>
		</g>
	</g>
</g>
</svg>
