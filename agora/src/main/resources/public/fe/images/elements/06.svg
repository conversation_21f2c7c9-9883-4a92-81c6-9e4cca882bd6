<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.4.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 41.4 41.4" style="enable-background:new 0 0 41.4 41.4;" xml:space="preserve">
<style type="text/css">
	.st0{fill:url(#SVGID_1_);}
	.st1{clip-path:url(#SVGID_00000178911705568370238620000000787167851380749974_);}
	.st2{fill:#FFFFFF;}
	.st3{fill:#1278DA;}
	.st4{opacity:0.8;clip-path:url(#SVGID_00000178911705568370238620000000787167851380749974_);}
	.st5{opacity:0.7;}
</style>
<g>
	<g>
		
			<radialGradient id="SVGID_1_" cx="8.0376" cy="-108.3773" r="23.7982" gradientTransform="matrix(1 -4.000000e-03 4.000000e-03 1 13.5638 132.3753)" gradientUnits="userSpaceOnUse">
			<stop  offset="0" style="stop-color:#4A90ED"/>
			<stop  offset="0.1964" style="stop-color:#4996EE"/>
			<stop  offset="0.4456" style="stop-color:#48A5F0"/>
			<stop  offset="0.6935" style="stop-color:#46BCF4"/>
			<stop  offset="0.805" style="stop-color:#55C7FA"/>
			<stop  offset="0.8796" style="stop-color:#62D0FF"/>
			<stop  offset="1" style="stop-color:#BBEAFF"/>
		</radialGradient>
		<path class="st0" d="M41.4,19.6C41.4,19.5,41.4,19.5,41.4,19.6c0-0.1,0-0.7-0.1-1.6c0-0.1,0-0.1,0-0.2c0-0.1,0-0.2-0.1-0.3
			c-0.4-2.7-1.4-5.3-2.8-7.5c0,0,0,0,0,0c-0.2-0.2-0.3-0.5-0.5-0.7c0,0,0,0,0,0c-0.8-1.2-1.7-2.2-2.7-3.2c-0.5-0.5-1-1-1.5-1.4
			c-0.3-0.3-0.7-0.5-1-0.8c0,0,0,0-0.1,0l0,0c-0.1,0-0.1-0.1-0.2-0.1c-0.2-0.2-0.5-0.3-0.7-0.5l0,0c0,0,0,0,0,0
			c-0.1-0.1-0.3-0.2-0.4-0.2c-0.1,0-0.1-0.1-0.2-0.1c-0.1,0-0.1-0.1-0.2-0.1c0,0-0.1,0-0.1-0.1c-0.8-0.4-1.7-0.8-2.5-1.2
			c-0.2-0.1-0.4-0.2-0.5-0.2l-0.2-0.1l-0.2-0.1c0,0,0,0,0,0l0,0C27.2,1.1,27.1,1,27,1l0,0c0,0,0,0,0,0l-0.2-0.1
			c-0.1,0-0.2,0-0.2-0.1l-0.2-0.1c0,0-0.1,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0l-0.2-0.1c-0.1,0-0.1,0-0.2,0
			c-0.1,0-0.1,0-0.2,0l0,0c0,0-0.1,0-0.1,0c-0.1,0-0.2,0-0.2,0l-0.2,0c-0.1,0-0.1,0-0.2,0c0,0,0,0,0,0c-0.1,0-0.1,0-0.2,0l-0.1,0
			l-0.1,0c0,0,0,0,0,0l0,0c0,0,0,0-0.1,0c-0.1,0-0.1,0-0.2,0c0,0-0.1,0-0.1,0l0,0c-0.1,0-0.1,0-0.2,0l-0.2,0c0,0-0.1,0-0.1,0
			c0,0-0.1,0-0.1,0c0,0-0.1,0-0.1,0c-0.1,0-0.2,0-0.2,0l-0.2,0c0,0,0,0-0.1,0l0,0c-0.1,0-0.1,0-0.2,0c-0.1,0-0.2,0-0.3,0
			c-0.1,0-0.1,0-0.2,0c-0.1,0-0.1,0-0.2,0c-0.1,0-0.2,0-0.2,0c0,0-0.1,0-0.1,0l-0.1,0c0,0-0.1,0-0.1,0c0,0-0.1,0-0.1,0
			c-0.4,0-0.8,0-1.2,0c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.3,0c-0.1,0-0.1,0-0.2,0c-0.1,0-0.3,0-0.4,0c0,0-0.1,0-0.1,0
			c-0.2,0-0.4,0-0.5,0.1c-0.1,0-0.2,0-0.3,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c-0.1,0-0.3,0-0.4,0.1c-0.1,0-0.2,0-0.3,0
			c-0.2,0-0.4,0.1-0.5,0.1c-0.6,0.1-1.2,0.3-1.8,0.4c-0.4,0.1-0.7,0.2-1,0.3c-1.6,0.6-3.1,1.3-4.6,2.3c0,0,0,0,0,0l0,0
			C9.1,3.6,8.9,3.7,8.8,3.8c0,0,0,0-0.1,0C8.5,4,8.3,4.2,8,4.4c0,0,0,0-0.1,0l0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
			C7.8,4.5,7.7,4.6,7.7,4.6C7.5,4.7,7.4,4.8,7.3,4.9C7.2,5,7.2,5.1,7.1,5.1C7,5.2,6.8,5.3,6.7,5.5c0,0,0,0,0,0l0,0
			c0,0-0.1,0.1-0.1,0.1c-0.6,0.6-1,1-1.1,1.1C5.3,6.8,5.1,7.1,4.9,7.3C4.8,7.5,4.6,7.6,4.5,7.8c0,0,0,0,0,0l0,0
			C4.2,8.2,3.9,8.7,3.5,9.1l0,0c0,0,0,0,0,0c0,0,0,0,0,0C3.3,9.4,3.2,9.7,3,9.9c-0.1,0.2-0.3,0.5-0.4,0.6l-0.4,0.8
			C2.1,11.6,2,11.8,1.9,12c-0.2,0.4-0.4,1-0.6,1.6c0,0,0,0,0,0l0,0c-0.2,0.5-0.4,1.1-0.5,1.6l0,0c0,0,0,0,0,0
			c-0.2,0.7-0.3,1.3-0.4,1.8C0,19,0,19.2,0,20.5c0,1.6,0.1,2.7,0.2,3.3c0.2,1.6,0.6,2.8,0.7,3.2c0.4,1.4,0.8,2.2,1.2,3
			c0.2,0.5,0.4,0.8,0.7,1.2c0.3,0.5,0.6,1,0.9,1.4c0,0.1,0.1,0.1,0.1,0.2c0.3,0.5,0.8,1.1,1.2,1.5c0.3,0.3,0.7,0.8,1,1.1
			c0.3,0.3,0.7,0.7,1.1,1l0,0C7.5,36.7,7.6,36.8,8,37c0.4,0.3,0.8,0.6,1.3,0.9c0,0,0,0,0,0c0.1,0.1,0.3,0.2,0.4,0.3
			c0.2,0.1,0.3,0.2,0.5,0.3c0.1,0,0.2,0.1,0.3,0.2c0,0,0,0,0.1,0c0.1,0.1,0.3,0.2,0.5,0.2c0,0,0.1,0,0.1,0.1
			c0.5,0.3,1.1,0.6,1.8,0.8c0.2,0.1,0.4,0.1,0.6,0.2c0.1,0,0.1,0.1,0.2,0.1l0.4,0.1c0.1,0,0.2,0.1,0.3,0.1l0.1,0c0,0,0,0,0,0l0.1,0
			c0.1,0,0.2,0,0.2,0.1l0.2,0.1c0,0,0,0,0,0c0.9,0.3,1.7,0.4,2.6,0.5l0.1,0c0.1,0,0.1,0,0.2,0c0,0,0,0,0,0c0.1,0,0.1,0,0.2,0
			l0.5,0.1c0.2,0,0.5,0,0.7,0.1c0.2,0,0.4,0,0.6,0l0,0c0,0,0,0,0,0c0.1,0,0.2,0,0.3,0c0.3,0,0.7,0,1,0c0.3,0,0.7,0,1,0
			c0.6-0.1,1.4-0.2,2.1-0.3c0.5-0.1,1.1-0.2,1.6-0.3c0.1,0,0.2-0.1,0.4-0.1c0.4-0.1,0.8-0.2,1.1-0.3l0.4-0.1
			c0.3-0.1,0.6-0.2,0.9-0.3l0.1-0.1c0.3-0.1,0.8-0.3,1.1-0.5c0.2-0.1,0.3-0.2,0.5-0.2c0.4-0.2,1-0.5,1.3-0.8c0,0,0,0,0,0
			c0.4-0.2,0.7-0.5,1-0.7c0.1-0.1,0.2-0.1,0.2-0.2l0.4-0.3c0.1-0.1,0.2-0.2,0.3-0.2l0.6-0.4c0.2-0.2,0.6-0.5,1.2-1.1c0,0,0,0,0,0
			l0.1-0.1c0.3-0.3,0.6-0.7,1-1.1c0,0,0,0,0,0c0,0,0,0,0,0c0.4-0.4,1-1.2,1.7-2.4c0,0,0,0,0,0c0.2-0.3,0.3-0.5,0.5-0.8c0,0,0,0,0,0
			c0,0,0,0,0,0c0.1-0.1,0.1-0.2,0.2-0.4c0.1-0.2,0.2-0.4,0.3-0.5c0-0.1,0-0.1,0.1-0.2c0.1-0.3,0.3-0.6,0.4-0.9
			c0.2-0.4,0.4-0.8,0.5-1.2c0,0,0,0,0,0c0.2-0.6,0.4-1.2,0.6-1.9c0.1-0.4,0.2-0.8,0.3-1.2c0.1-0.4,0.2-0.8,0.2-1.3
			c0-0.1,0-0.2,0-0.3c0.1-0.6,0.1-1.2,0.1-1.8c0,0,0,0,0-0.1l0,0C41.4,20.6,41.4,20.1,41.4,19.6z"/>
		<g>
			<defs>
				<path id="SVGID_00000039100762741090058660000008650830318594161822_" d="M37.8,9L37.8,9C37.8,9,37.8,9,37.8,9
					C37.8,9,37.8,9,37.8,9c0.2,0.3,0.3,0.5,0.5,0.8c0.1,0.2,0.3,0.5,0.4,0.6l0.4,0.8c0.1,0.2,0.3,0.5,0.4,0.7c0.2,0.4,0.4,1,0.7,1.6
					c0,0,0,0,0,0l0,0c0.2,0.5,0.4,1.1,0.5,1.6l0,0c0,0,0,0,0,0c0.2,0.7,0.4,1.3,0.4,1.8c0.3,1.9,0.3,2.1,0.3,3.3
					c0,1.6-0.1,2.7-0.2,3.3c-0.2,1.6-0.6,2.8-0.7,3.2c-0.4,1.4-0.8,2.2-1.2,3c-0.2,0.5-0.4,0.8-0.6,1.2c-0.3,0.5-0.6,1-0.9,1.4
					c0,0.1-0.1,0.1-0.1,0.2c-0.3,0.5-0.8,1.1-1.2,1.6c-0.3,0.3-0.7,0.8-1,1.1c-0.3,0.3-0.7,0.7-1,1l0,0c-0.4,0.3-0.5,0.5-0.9,0.7
					c-0.4,0.3-0.8,0.6-1.3,0.9c0,0,0,0,0,0c-0.1,0.1-0.3,0.2-0.4,0.3c-0.2,0.1-0.3,0.2-0.5,0.3c-0.1,0-0.2,0.1-0.3,0.2
					c0,0,0,0-0.1,0c-0.1,0.1-0.3,0.2-0.4,0.2c0,0-0.1,0-0.1,0.1c-0.5,0.3-1.1,0.6-1.8,0.9c-0.2,0.1-0.4,0.1-0.6,0.2
					c-0.1,0-0.1,0.1-0.2,0.1l-0.4,0.1c-0.1,0-0.2,0.1-0.3,0.1l-0.1,0c0,0,0,0,0,0l-0.1,0c-0.1,0-0.2,0.1-0.2,0.1l-0.2,0.1
					c0,0,0,0,0,0c-0.9,0.3-1.7,0.4-2.6,0.6l-0.1,0c-0.1,0-0.1,0-0.2,0c0,0,0,0,0,0c-0.1,0-0.1,0-0.2,0l-0.5,0.1
					c-0.2,0-0.5,0-0.7,0.1c-0.2,0-0.4,0-0.6,0l0,0c0,0,0,0,0,0c-0.1,0-0.2,0-0.3,0c-0.3,0-0.7,0-1,0c-0.3,0-0.7,0-1,0
					c-0.6-0.1-1.4-0.1-2.1-0.2c-0.5-0.1-1.1-0.2-1.6-0.3c-0.1,0-0.2-0.1-0.4-0.1c-0.4-0.1-0.8-0.2-1.1-0.3l-0.4-0.1
					c-0.3-0.1-0.6-0.2-0.9-0.3l-0.1-0.1c-0.3-0.1-0.8-0.3-1.1-0.5c-0.2-0.1-0.3-0.2-0.5-0.2c-0.4-0.2-1-0.5-1.3-0.8c0,0,0,0,0,0
					c-0.4-0.2-0.7-0.5-1.1-0.7c-0.1-0.1-0.2-0.1-0.2-0.1l-0.4-0.3C8.1,37.1,8,37,7.9,37l-0.6-0.4c-0.2-0.2-0.6-0.5-1.2-1.1
					c0,0,0,0,0,0l-0.1-0.1c-0.3-0.3-0.6-0.7-1-1.1c0,0,0,0,0,0c0,0,0,0,0,0c-0.4-0.4-1-1.2-1.7-2.3c0,0,0,0,0,0
					c-0.2-0.3-0.3-0.5-0.5-0.8c0,0,0,0,0,0c0,0,0,0,0,0c-0.1-0.1-0.1-0.2-0.2-0.4c-0.1-0.2-0.2-0.4-0.3-0.5c0-0.1-0.1-0.1-0.1-0.2
					c-0.1-0.3-0.3-0.6-0.4-0.9c-0.2-0.4-0.4-0.8-0.5-1.2c0,0,0,0,0,0c-0.2-0.6-0.4-1.2-0.6-1.9c-0.1-0.4-0.2-0.8-0.3-1.2
					c-0.1-0.4-0.2-0.8-0.2-1.3c0-0.1,0-0.2,0-0.3C0.1,22.6,0,22,0,21.4c0,0,0,0,0-0.1l0,0c0-0.5,0-1,0-1.5c0,0,0,0,0-0.1
					c0-0.1,0-0.7,0.1-1.5c0-0.1,0-0.1,0-0.2c0-0.1,0-0.2,0-0.3C0.6,14.9,1.6,12.3,3,10c0,0,0,0,0,0c0.2-0.2-1.2,2.3-1.1,2
					c0,0,1.5-2.8,1.5-2.8C4.2,8.1,5.1,7,6.1,6.1c0.5-0.5,1-1,1.5-1.4c0.3-0.3,0.7-0.5,1-0.8c0,0,0,0,0.1,0l0,0
					c0.1,0,0.1-0.1,0.2-0.1c0.2-0.2,0.5-0.3,0.7-0.5l0,0c0,0,0,0,0,0C9.8,3.1,9.9,3,10.1,2.9c0.1,0,0.1-0.1,0.2-0.1
					c0.1,0,0.1-0.1,0.2-0.1c0,0,0.1,0,0.1-0.1c0.8-0.5,1.7-0.9,2.5-1.2c0.2-0.1,0.4-0.2,0.5-0.2l0.2-0.1l0.2-0.1c0,0,0,0,0,0l0,0
					c0.1,0,0.2-0.1,0.3-0.1l0,0c0,0,0,0,0,0L14.5,1c0.1,0,0.2-0.1,0.2-0.1l0.2-0.1c0,0,0.1,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0,0
					c0,0,0,0,0.1,0l0.2-0.1c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0,0.2,0l0,0c0,0,0.1,0,0.1,0c0.1,0,0.2,0,0.2-0.1l0.2,0c0.1,0,0.1,0,0.2,0
					c0,0,0,0,0,0c0.1,0,0.1,0,0.2,0l0.1,0l0.1,0c0,0,0,0,0,0l0,0c0,0,0,0,0.1,0c0.1,0,0.1,0,0.2,0c0,0,0.1,0,0.1,0l0,0
					c0.1,0,0.1,0,0.2,0l0.2,0c0,0,0.1,0,0.1,0c0,0,0.1,0,0.1,0c0,0,0.1,0,0.1,0c0.1,0,0.2,0,0.2,0l0.2,0c0,0,0,0,0.1,0l0,0
					c0.1,0,0.1,0,0.2,0c0.1,0,0.2,0,0.3,0c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0,0.2,0c0.1,0,0.2,0,0.2,0c0,0,0.1,0,0.1,0l0.1,0
					c0,0,0.1,0,0.1,0c0,0,0.1,0,0.1,0c0.4,0,0.8,0,1.2,0c0.1,0,0.2,0,0.3,0c0.1,0,0.2,0,0.3,0c0.1,0,0.1,0,0.2,0c0.1,0,0.3,0,0.4,0
					c0,0,0.1,0,0.1,0c0.2,0,0.4,0,0.6,0.1c0.1,0,0.2,0,0.3,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0.1,0,0.3,0,0.4,0.1
					c0.1,0,0.2,0,0.3,0c0.2,0,0.4,0.1,0.5,0.1c0.6,0.1,1.2,0.3,1.8,0.4c0.4,0.1,0.7,0.2,1,0.3c1.6,0.6,3.1,1.3,4.6,2.3c0,0,0,0,0,0
					l0,0c0.1,0.1,0.3,0.2,0.4,0.3c0,0,0,0,0.1,0c0.2,0.2,0.5,0.3,0.7,0.5c0,0,0,0,0.1,0l0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
					c0.1,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.3,0.2,0.4,0.3c0.1,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.3,0.2,0.4,0.3c0,0,0,0,0,0l0,0
					c0,0,0.1,0.1,0.1,0.1c0.6,0.6,1,1,1.1,1.1c0.2,0.2,0.4,0.4,0.6,0.7c0.1,0.2,0.3,0.3,0.4,0.5c0,0,0,0,0,0"/>
			</defs>
			<clipPath id="SVGID_00000030474476970875724910000005786457179183073430_">
				<use xlink:href="#SVGID_00000039100762741090058660000008650830318594161822_"  style="overflow:visible;"/>
			</clipPath>
			<g style="clip-path:url(#SVGID_00000030474476970875724910000005786457179183073430_);">
				<path class="st2" d="M29.2,2.2c0.4,0.2,1.3,0.9,1.1,1.6c0,0-0.1,0.1-0.1,0.1c1.1-1.1,3.6,1.7,3.5,2.4c0,0.1,0,0.1-0.1,0.2
					c0,0-0.1,0.1-0.1,0.1c0.8-0.8,2.7,1.4,2.8,2.6c0,0.1,0,0.3-0.1,0.4c-0.1,0.1-0.1,0.2-0.2,0.3c0.4-0.4,0.8-0.1,1.4,0.8
					c0,0,0,0,0,0c0.5,1,0.7,1.7,0.5,2.2c-1.1,1.8-3,4.5-5.2,7.7c-0.3,0.3-0.6,0.3-0.9,0.1c-2-2-4.4-4-7.1-6
					c-0.9-0.7-1.9-1.3-2.8-1.9c-0.3-0.2-0.3-0.2-0.9-1.8c-0.5-1.3-1.7-3.9-3.5-6.1c-0.7-0.2-1.4-0.3-2.1-0.5
					c-0.7-0.2-0.9-0.4-0.7-0.8c0.7-0.8,1.8-1.1,3.4-1c0.3,0,0.6,0.1,1,0.2C21.4,3.3,22.6,4,24,5.5c1-1.5,1.8-2.8,2.4-3.7
					C27.2,1,29.2,2.2,29.2,2.2"/>
				<path class="st3" d="M32.4,22.8c0.4,0.4,0.4,0.9,0.1,1.4c-0.2,0.3-0.4,0.6-0.6,0.9c-0.6,0.8-1.2,1.7-1.8,2.5
					c-0.4,0.4-0.8,0.5-1.3,0.3c-2.7-1.6-5.7-3.7-9-6c-0.9-0.7-1.8-1.3-2.7-2c-0.5-0.5-0.6-1-0.3-1.6c0.7-1,1.4-2,2.1-3
					c0.3-0.4,0.5-0.7,0.7-1c0.5-0.5,1-0.6,1.6-0.3c0.9,0.6,1.8,1.2,2.8,1.9C27.3,18.2,30.1,20.6,32.4,22.8"/>
			</g>
			<g style="opacity:0.8;clip-path:url(#SVGID_00000030474476970875724910000005786457179183073430_);">
				
					<image style="overflow:visible;" width="750" height="730" xlink:href="BD7D5ED1.html"  transform="matrix(0.24 0 0 0.24 -64.563 -58.2628)">
				</image>
			</g>
			<g style="clip-path:url(#SVGID_00000030474476970875724910000005786457179183073430_);">
				<g>
					
						<image style="overflow:visible;" width="730" height="707" xlink:href="BD7D5ED3.html"  transform="matrix(0.24 0 0 0.24 -74.403 -74.8228)">
					</image>
				</g>
				<g class="st5">
					
						<image style="overflow:visible;" width="651" height="648" xlink:href="BD7D5ED6.html"  transform="matrix(0.24 0 0 0.24 -46.803 -68.1028)">
					</image>
				</g>
			</g>
		</g>
	</g>
</g>
</svg>
