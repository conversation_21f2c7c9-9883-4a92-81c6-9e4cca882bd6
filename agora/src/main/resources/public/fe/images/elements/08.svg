<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.4.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 53.2 53.2" style="enable-background:new 0 0 53.2 53.2;" xml:space="preserve">
<style type="text/css">
	.st0{fill:url(#SVGID_1_);}
	.st1{opacity:0.8;clip-path:url(#SVGID_00000062906545802003880600000006181462784985566611_);}
	.st2{clip-path:url(#SVGID_00000062906545802003880600000006181462784985566611_);}
	.st3{opacity:0.7;}
</style>
<g>
	<g>
		
			<radialGradient id="SVGID_1_" cx="14.9834" cy="-101.4979" r="30.5573" gradientTransform="matrix(1 -4.000000e-03 4.000000e-03 1 13.54 132.3999)" gradientUnits="userSpaceOnUse">
			<stop  offset="0" style="stop-color:#F79F40"/>
			<stop  offset="0.1632" style="stop-color:#F7A542"/>
			<stop  offset="0.3708" style="stop-color:#F8B44A"/>
			<stop  offset="0.6014" style="stop-color:#FACE55"/>
			<stop  offset="0.7708" style="stop-color:#FBE660"/>
			<stop  offset="0.9104" style="stop-color:#FBF36F"/>
			<stop  offset="0.9318" style="stop-color:#FBF672"/>
			<stop  offset="0.9466" style="stop-color:#FBF676"/>
			<stop  offset="0.9621" style="stop-color:#FBF783"/>
			<stop  offset="0.9778" style="stop-color:#FCF897"/>
			<stop  offset="0.9936" style="stop-color:#FDFAB4"/>
			<stop  offset="1" style="stop-color:#FDFBC2"/>
		</radialGradient>
		<path class="st0" d="M53.1,25.1C53.1,25.1,53.1,25.1,53.1,25.1c0-0.2,0-0.9-0.2-2c0-0.1,0-0.2,0-0.3c0-0.1,0-0.3-0.1-0.4
			c-0.6-3.5-1.8-6.8-3.6-9.7c0,0,0,0,0,0c-0.2-0.3-0.4-0.6-0.6-0.9c0,0,0,0,0,0c-1-1.5-2.1-2.9-3.4-4.1c-0.6-0.6-1.3-1.3-2-1.8
			c-0.4-0.3-0.9-0.7-1.3-1c0,0,0,0-0.1-0.1l-0.1,0c-0.1-0.1-0.2-0.1-0.2-0.2c-0.3-0.2-0.6-0.4-1-0.6l0,0c0,0,0,0,0,0
			c-0.2-0.1-0.3-0.2-0.5-0.3c-0.1,0-0.2-0.1-0.2-0.1c-0.1,0-0.1-0.1-0.2-0.1c-0.1,0-0.1-0.1-0.2-0.1c-1-0.6-2.1-1.1-3.3-1.5
			c-0.3-0.1-0.5-0.2-0.6-0.2l-0.2-0.1l-0.2-0.1c0,0,0,0,0,0l0,0c-0.1,0-0.2-0.1-0.3-0.1l0,0c0,0,0,0,0,0l-0.2-0.1
			c-0.1,0-0.2-0.1-0.3-0.1L33.9,1c0,0-0.1,0-0.1,0c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0-0.1,0-0.1,0l-0.3-0.1c-0.1,0-0.2,0-0.2-0.1
			c-0.1,0-0.2,0-0.2-0.1l0,0c0,0-0.1,0-0.1,0c-0.1,0-0.2,0-0.3-0.1l-0.2,0c-0.1,0-0.2,0-0.3-0.1c0,0,0,0,0,0c-0.1,0-0.1,0-0.2,0
			l-0.1,0l-0.1,0c0,0,0,0,0,0l0,0c0,0-0.1,0-0.1,0c-0.1,0-0.1,0-0.2,0c0,0-0.1,0-0.1,0l0,0c-0.1,0-0.2,0-0.2,0l-0.2,0
			c0,0-0.1,0-0.1,0c-0.1,0-0.1,0-0.2,0c-0.1,0-0.1,0-0.2,0c-0.1,0-0.2,0-0.3,0l-0.3,0c0,0-0.1,0-0.1,0l0,0c-0.1,0-0.2,0-0.3,0
			c-0.1,0-0.2,0-0.4,0c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.3,0c-0.1,0-0.1,0-0.2,0l-0.2,0c0,0-0.1,0-0.1,0
			c0,0-0.1,0-0.1,0c-0.5,0-1.1,0-1.6,0c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.4,0c-0.1,0-0.1,0-0.2,0c-0.2,0-0.3,0-0.5,0
			c-0.1,0-0.1,0-0.2,0c-0.2,0-0.5,0.1-0.7,0.1c-0.1,0-0.2,0-0.3,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0-0.1,0c-0.2,0-0.3,0.1-0.5,0.1
			c-0.1,0-0.3,0-0.4,0.1c-0.3,0.1-0.5,0.1-0.7,0.1c-0.8,0.2-1.5,0.3-2.3,0.6c-0.4,0.1-0.8,0.3-1.3,0.4c-2.1,0.7-4,1.7-5.9,2.9
			c0,0,0,0,0,0l0,0c-0.2,0.1-0.4,0.2-0.5,0.4c0,0,0,0-0.1,0.1c-0.3,0.2-0.6,0.4-0.9,0.7c0,0,0,0-0.1,0.1l0,0c0,0,0,0,0,0
			c0,0,0,0,0,0c0,0,0,0,0,0C10,5.8,9.9,5.9,9.9,5.9C9.7,6.1,9.5,6.2,9.4,6.3C9.3,6.4,9.2,6.5,9.1,6.6C8.9,6.7,8.8,6.9,8.6,7
			c0,0,0,0,0,0l0,0c0,0-0.1,0.1-0.1,0.1C7.6,7.9,7.1,8.5,7.1,8.5C6.8,8.8,6.6,9.1,6.3,9.4C6.2,9.6,6,9.8,5.8,10c0,0,0,0,0,0l0,0
			c-0.4,0.5-0.8,1-1.2,1.6l0,0c0,0,0,0,0,0c0,0,0,0,0,0c-0.2,0.3-0.4,0.6-0.6,1c-0.2,0.3-0.3,0.6-0.5,0.8l-0.5,1
			c-0.2,0.3-0.3,0.6-0.5,0.9c-0.3,0.5-0.6,1.3-0.8,2c0,0,0,0,0,0l0,0c-0.2,0.7-0.5,1.4-0.6,2l0,0c0,0,0,0,0,0
			c-0.2,0.9-0.4,1.7-0.5,2.4C0.1,24.4,0,24.6,0,26.3c0,2,0.2,3.5,0.3,4.2c0.3,2,0.8,3.6,0.9,4.1c0.6,1.8,1.1,2.9,1.6,3.9
			c0.3,0.6,0.5,1.1,0.8,1.6c0.4,0.6,0.8,1.2,1.2,1.8C4.9,41.9,4.9,42,5,42c0.4,0.6,1,1.4,1.6,2c0.4,0.4,0.9,1,1.3,1.4
			c0.4,0.4,0.9,0.9,1.4,1.3l0,0c0.5,0.4,0.7,0.6,1.1,0.9c0.5,0.4,1.1,0.8,1.6,1.2c0,0,0,0,0,0c0.2,0.1,0.4,0.2,0.5,0.3
			c0.2,0.1,0.4,0.3,0.6,0.4c0.1,0.1,0.2,0.1,0.4,0.2c0,0,0,0,0.1,0c0.2,0.1,0.4,0.2,0.6,0.3c0,0,0.1,0.1,0.1,0.1
			c0.6,0.3,1.5,0.7,2.4,1.1c0.2,0.1,0.5,0.2,0.8,0.3c0.1,0,0.2,0.1,0.3,0.1l0.5,0.2c0.1,0,0.2,0.1,0.3,0.1l0.1,0c0,0,0,0,0,0
			l0.2,0.1c0.1,0,0.2,0.1,0.3,0.1l0.3,0.1c0,0,0,0,0,0c1.1,0.3,2.2,0.6,3.3,0.7l0.1,0c0.1,0,0.2,0,0.3,0c0,0,0,0,0,0
			c0.1,0,0.2,0,0.3,0l0.6,0.1c0.3,0,0.6,0.1,0.9,0.1c0.2,0,0.5,0,0.8,0l0,0c0,0,0,0,0,0c0.1,0,0.3,0,0.4,0c0.4,0,0.9,0,1.2,0
			c0.4,0,0.9,0,1.3-0.1c0.8-0.1,1.9-0.2,2.6-0.3c0.6-0.1,1.4-0.3,2-0.4c0.2,0,0.3-0.1,0.5-0.1c0.5-0.1,1-0.3,1.4-0.4l0.5-0.2
			c0.4-0.1,0.8-0.3,1.1-0.4l0.2-0.1c0.4-0.2,1-0.4,1.5-0.7c0.2-0.1,0.4-0.2,0.6-0.3c0.5-0.3,1.2-0.7,1.7-1c0,0,0,0,0.1,0
			c0.5-0.3,0.9-0.6,1.3-0.9c0.1-0.1,0.2-0.1,0.3-0.2l0.5-0.4c0.1-0.1,0.3-0.2,0.4-0.3l0.7-0.6c0.3-0.2,0.8-0.7,1.5-1.4c0,0,0,0,0,0
			l0.1-0.1c0.4-0.4,0.8-0.9,1.3-1.4c0,0,0,0,0,0c0,0,0,0,0,0c0.5-0.6,1.3-1.6,2.2-3c0,0,0,0,0,0c0.2-0.3,0.4-0.7,0.6-1.1
			c0,0,0,0,0,0c0,0,0,0,0,0c0.1-0.2,0.2-0.3,0.3-0.5c0.1-0.2,0.2-0.5,0.3-0.7c0-0.1,0.1-0.1,0.1-0.2c0.2-0.4,0.3-0.7,0.5-1.1
			c0.2-0.5,0.4-1,0.6-1.5c0,0,0,0,0,0c0.3-0.8,0.5-1.6,0.8-2.5c0.1-0.5,0.3-1.1,0.4-1.6c0.1-0.5,0.2-1.1,0.3-1.7c0-0.1,0-0.3,0-0.4
			c0.1-0.8,0.1-1.6,0.2-2.4c0,0,0-0.1,0-0.1l0-0.1C53.2,26.4,53.2,25.8,53.1,25.1z"/>
		<g>
			<defs>
				<path id="SVGID_00000011022011286322440240000007876020496939409816_" d="M0,25.3C0,25.3,0,25.3,0,25.3c0-0.2,0-0.9,0.2-2
					c0-0.1,0-0.2,0-0.3c0-0.1,0-0.3,0.1-0.4c0.5-3.5,1.7-6.8,3.5-9.7c0,0,0,0,0,0c0.2-0.3-1.6,2.9-1.4,2.6c0,0,2-3.5,2-3.5
					c1-1.5,2.1-2.9,3.4-4.1c0.6-0.6,1.3-1.3,2-1.8c0.4-0.3,0.9-0.7,1.3-1c0,0,0,0,0.1-0.1l0.1,0c0.1-0.1,0.2-0.1,0.2-0.2
					c0.3-0.2,0.6-0.4,0.9-0.6l0,0c0,0,0,0,0,0c0.2-0.1,0.3-0.2,0.5-0.3c0.1,0,0.2-0.1,0.2-0.1c0.1,0,0.1-0.1,0.2-0.1
					c0.1,0,0.1-0.1,0.2-0.1c1-0.6,2.1-1.1,3.3-1.5c0.3-0.1,0.5-0.2,0.6-0.3l0.2-0.1l0.2-0.1c0,0,0,0,0,0l0,0c0.1,0,0.2-0.1,0.3-0.1
					l0,0c0,0,0,0,0,0l0.2-0.1c0.1,0,0.2-0.1,0.3-0.1l0.3-0.1c0,0,0.1,0,0.1,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0,0,0.1,0,0.1,0l0.3-0.1
					c0.1,0,0.2,0,0.2-0.1c0.1,0,0.2,0,0.2-0.1l0,0c0,0,0.1,0,0.1,0c0.1,0,0.2,0,0.3-0.1l0.2,0c0.1,0,0.2,0,0.3-0.1c0,0,0,0,0,0
					c0.1,0,0.1,0,0.2,0l0.1,0l0.1,0c0,0,0,0,0,0l0,0c0,0,0.1,0,0.1,0c0.1,0,0.1,0,0.2,0c0,0,0.1,0,0.1,0l0,0c0.1,0,0.2,0,0.2,0
					l0.2,0c0,0,0.1,0,0.1,0c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0,0.2,0c0.1,0,0.2,0,0.3,0l0.3,0c0,0,0.1,0,0.1,0l0,0c0.1,0,0.2,0,0.3,0
					c0.1,0,0.2,0,0.4,0c0.1,0,0.2,0,0.3,0c0.1,0,0.2,0,0.3,0c0.1,0,0.2,0,0.3,0c0.1,0,0.1,0,0.2,0l0.2,0c0,0,0.1,0,0.1,0
					c0,0,0.1,0,0.1,0c0.5,0,1.1,0,1.6,0c0.1,0,0.2,0,0.3,0c0.1,0,0.2,0,0.4,0c0.1,0,0.1,0,0.2,0c0.2,0,0.3,0,0.5,0
					c0.1,0,0.1,0,0.2,0c0.2,0,0.5,0,0.7,0.1c0.1,0,0.2,0,0.3,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0.1,0c0.2,0,0.3,0,0.5,0.1
					c0.1,0,0.3,0,0.4,0.1c0.3,0,0.5,0.1,0.7,0.1C32.4,0.6,33.2,0.8,34,1c0.5,0.1,0.8,0.2,1.3,0.4c2.1,0.7,4,1.7,5.9,2.9c0,0,0,0,0,0
					l0,0c0.2,0.1,0.4,0.2,0.5,0.4c0,0,0,0,0.1,0c0.3,0.2,0.6,0.4,0.9,0.7c0,0,0,0,0.1,0.1l0,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
					c0.1,0.1,0.2,0.1,0.2,0.2c0.2,0.1,0.3,0.3,0.5,0.4c0.1,0.1,0.2,0.1,0.3,0.2c0.2,0.2,0.3,0.3,0.5,0.4c0,0,0,0,0,0l0,0
					c0,0,0.1,0.1,0.1,0.1c0.8,0.8,1.3,1.3,1.4,1.4c0.3,0.3,0.5,0.6,0.8,0.9c0.2,0.2,0.4,0.4,0.6,0.7c0,0,0,0,0,0l0,0
					c0.4,0.5,0.8,1,1.2,1.6l0,0c0,0,0,0,0,0c0,0,0,0,0,0c0.2,0.3,0.4,0.6,0.6,1c0.2,0.3,0.4,0.6,0.5,0.8l0.5,1
					c0.2,0.3,0.3,0.6,0.5,0.9c0.3,0.5,0.6,1.2,0.8,2c0,0,0,0,0,0l0,0c0.2,0.7,0.5,1.4,0.7,2l0,0c0,0,0,0,0,0
					c0.3,0.9,0.5,1.7,0.6,2.4c0.4,2.5,0.4,2.7,0.4,4.3c0,2-0.2,3.5-0.2,4.2c-0.3,2-0.7,3.6-0.9,4.1c-0.6,1.8-1.1,2.9-1.5,3.9
					c-0.3,0.6-0.5,1.1-0.8,1.6c-0.4,0.6-0.7,1.2-1.2,1.8c0,0.1-0.1,0.1-0.1,0.2c-0.4,0.6-1,1.4-1.5,2c-0.4,0.4-0.9,1-1.3,1.4
					c-0.4,0.4-0.9,0.9-1.3,1.3l0,0c-0.5,0.4-0.7,0.6-1.1,0.9c-0.5,0.4-1.1,0.8-1.6,1.2c0,0,0,0,0,0c-0.2,0.1-0.4,0.2-0.5,0.4
					c-0.2,0.1-0.4,0.3-0.6,0.4c-0.1,0.1-0.2,0.1-0.4,0.2c0,0,0,0-0.1,0c-0.2,0.1-0.4,0.2-0.6,0.3c0,0-0.1,0.1-0.1,0.1
					c-0.6,0.3-1.4,0.7-2.3,1.1c-0.2,0.1-0.5,0.2-0.8,0.3c-0.1,0-0.2,0.1-0.3,0.1l-0.5,0.2c-0.1,0-0.2,0.1-0.3,0.1l-0.1,0
					c0,0,0,0,0,0l-0.2,0.1c-0.1,0-0.2,0.1-0.3,0.1L34,52.1c0,0,0,0,0,0c-1.1,0.3-2.2,0.6-3.3,0.7l-0.1,0c-0.1,0-0.2,0-0.3,0
					c0,0,0,0,0,0c-0.1,0-0.2,0-0.3,0L29.5,53c-0.3,0-0.6,0.1-0.9,0.1c-0.2,0-0.5,0-0.8,0l0,0c0,0,0,0,0,0c-0.1,0-0.3,0-0.4,0
					c-0.4,0-0.9,0-1.2,0c-0.4,0-0.9,0-1.3-0.1c-0.8-0.1-1.9-0.2-2.7-0.3c-0.6-0.1-1.4-0.3-2-0.4c-0.2,0-0.3-0.1-0.5-0.1
					c-0.5-0.1-1-0.3-1.4-0.4l-0.5-0.2c-0.4-0.1-0.8-0.3-1.1-0.4l-0.2-0.1c-0.4-0.2-1-0.4-1.5-0.7c-0.2-0.1-0.4-0.2-0.6-0.3
					c-0.6-0.3-1.2-0.6-1.7-1c0,0,0,0-0.1,0c-0.5-0.3-0.9-0.6-1.3-0.9c-0.1-0.1-0.2-0.1-0.3-0.2l-0.5-0.4c-0.1-0.1-0.3-0.2-0.4-0.3
					l-0.7-0.6c-0.3-0.2-0.8-0.6-1.5-1.4c0,0,0,0,0,0l-0.1-0.1C7.4,45,7,44.5,6.5,44c0,0,0,0,0,0c0,0,0,0,0,0c-0.5-0.6-1.3-1.6-2.2-3
					c0,0,0,0,0,0c-0.2-0.3-0.4-0.7-0.6-1c0,0,0,0,0,0c0,0,0,0,0,0c-0.1-0.2-0.2-0.3-0.3-0.5c-0.1-0.2-0.2-0.5-0.3-0.7
					c0-0.1-0.1-0.1-0.1-0.2c-0.2-0.4-0.3-0.7-0.5-1.1c-0.2-0.5-0.5-1-0.6-1.5c0,0,0,0,0,0c-0.3-0.8-0.6-1.6-0.8-2.5
					c-0.1-0.5-0.3-1.1-0.4-1.6c-0.1-0.5-0.2-1.1-0.3-1.7c0-0.1,0-0.3-0.1-0.4C0.1,29,0,28.3,0,27.4c0,0,0-0.1,0-0.1l0-0.1
					C0,26.7,0,26,0,25.3z"/>
			</defs>
			<clipPath id="SVGID_00000096762505529486277390000002915783810900576647_">
				<use xlink:href="#SVGID_00000011022011286322440240000007876020496939409816_"  style="overflow:visible;"/>
			</clipPath>
			<g style="opacity:0.8;clip-path:url(#SVGID_00000096762505529486277390000002915783810900576647_);">
				
					<image style="overflow:visible;" width="793" height="767" xlink:href="319A0763.html"  transform="matrix(0.24 0 0 0.24 -62.5882 -54.2991)">
				</image>
			</g>
			<g style="clip-path:url(#SVGID_00000096762505529486277390000002915783810900576647_);">
				<g>
					
						<image style="overflow:visible;" width="764" height="740" xlink:href="319A0761.html"  transform="matrix(0.24 0 0 0.24 -74.3482 -75.6591)">
					</image>
				</g>
				<g class="st3">
					
						<image style="overflow:visible;" width="666" height="661" xlink:href="319A0767.html"  transform="matrix(0.24 0 0 0.24 -39.7882 -67.0191)">
					</image>
				</g>
			</g>
		</g>
	</g>
</g>
</svg>
