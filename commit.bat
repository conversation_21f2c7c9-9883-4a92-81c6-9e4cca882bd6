@echo off
rem -- ------------------------------------------------------------------
rem -- ------------------------------------------------------------------
rem -- the following commands do a commit and push to remote repository
rem -- ------------------------------------------------------------------
rem -- ------------------------------------------------------------------


rem -- first of all, you need a correctly configured git client to procede
rem -- if you're having troubles with git, please open the specifically
rem -- designed git-cmd.exe shell


rem -- check if current folder contains a git repository
if not exist ".\.git\*" goto :norep

rem -- check if a comment is present
if [%1]==[] goto :nocomment

rem -- check if a comment is present
if [%2]==[] goto :ok
goto :noquotes
:ok

rem -- add newly or removed files and folders
git add -A

rem -- committ all
git commit -a -m %1

rem -- push changes
git push -u origin master

goto :end


rem -- errors
:norep
echo sorry, no rep folder found
goto :end

:nocomment
echo sorry, no comment like "add - some new feature"
goto :end

:noquotes
echo sorry, wrap comment between brackets like "add - some new feature"
goto :end

rem -- end
:end
pause
